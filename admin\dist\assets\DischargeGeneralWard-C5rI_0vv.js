import{a5 as p,ak as u,a1 as x,al as h,a2 as e,a7 as g,am as b,a9 as y,aa as j,ac as s,an as v,ab as n,ao as f}from"./index-ClX9RVH0.js";const D=()=>{const[c,a]=p.useState(1),m=u.map(t=>({...t,isActive:t.step===c})),r=x({initialValues:{patientInformation:[{patientId:"",fullName:"",contactNumber:"",admissionDate:"",department:"",dischargeDate:""}],treatmentSummary:[{primaryDiagnosis:"",secondaryDiagnosis:"",treatmentProvided:"",surgery:""}],homecareInstructions:[{lifestyleRecommendations:"",dietaryAdvice:"",exerciseSuggestions:"",precautionaryMeasures:"",otherAdvice:"",medicineName:"",dose:"",frequencey:""}],followUp:[{recommendedFollowUpDate:"",revisitReason:"",departmentVisit:""}],dischargeApproval:[{doctorName:"",nurseName:""}]},enableReinitialize:!0,onSubmit:t=>{h.success("Form submitted successfully!"),history.back(),console.log(t)}}),{handleSubmit:o,values:l}=r;return e.jsxs("div",{children:[e.jsx(g,{listTitle:"Discharge Form",hideHeader:!0}),e.jsxs("div",{className:"relative flex w-full gap-6",children:[e.jsx("div",{className:"w-auto h-full",children:e.jsx(b,{steps:m})}),e.jsx("div",{className:"w-full",children:e.jsx(y,{value:r,children:e.jsx(j,{onSubmit:o,children:e.jsxs("div",{className:"flex flex-col w-full gap-5 pb-4",children:[e.jsx("div",{className:"flex flex-col gap-4 p-4 bg-white rounded-sm",onClick:()=>a(1),children:e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(s,{label:"Paitent Id",type:"text",placeholder:"Enter",name:"patientInformation.paitentId",onFocus:()=>a(1)}),e.jsx(s,{label:"Full Name",type:"text",placeholder:"Enter",name:"patientInformation.fullName",onFocus:()=>a(1)}),e.jsx(s,{label:"Contact Number",type:"text",placeholder:"Enter",name:"patientInformation.contactNumber",onFocus:()=>a(1)}),e.jsx(s,{label:"Admission Date",type:"date",placeholder:"Enter",name:"patientInformation.admissionDate",onFocus:()=>a(1)}),e.jsx(s,{label:"Department",type:"text",placeholder:"Enter",name:"patientInformation.department",onFocus:()=>a(1)}),e.jsx(s,{label:"Discharge Date",type:"date",placeholder:"Enter",name:"patientInformation.dischargeDate",onFocus:()=>a(1)})]})}),e.jsx("div",{className:"flex flex-col gap-4 p-4 bg-white rounded-sm",onClick:()=>a(2),children:e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(s,{label:"Primary Diagnosis",type:"text",placeholder:"Enter",name:"treatmentSummary.primaryDiagnosis",onFocus:()=>a(2)}),e.jsx(s,{label:"Secondary Diagnosis",type:"text",placeholder:"Enter",name:"treatmentSummary.secondaryDiagnosis",onFocus:()=>a(2)}),e.jsx(s,{label:"Treatment Provided",type:"text",placeholder:"Enter",name:"treatmentSummary.treatmentProvided",onFocus:()=>a(2)}),e.jsx(s,{label:"Surgery",type:"text",placeholder:"Enter",name:"treatmentSummary.surgery",onFocus:()=>a(2)})]})}),e.jsxs("div",{className:"flex flex-col gap-8 p-4 bg-white rounded-sm",onClick:()=>a(5),children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(s,{label:"Lifestyle Recommendations",type:"text",placeholder:"Enter",name:"homecareInstructions.lifestyleRecommendations",onFocus:()=>a(3)}),e.jsx(s,{label:"Dietary Advice",type:"text",placeholder:"Enter",name:"homecareInstructions.dietaryAdvice",onFocus:()=>a(3)}),e.jsx(s,{label:"Exercise Suggestions",type:"text",placeholder:"Enter",name:"homecareInstructions.exerciseSuggestions",onFocus:()=>a(3)})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(s,{label:"Precautionary Measures",type:"text",placeholder:"Enter",name:"homecareInstructions.precautionaryMeasures",onFocus:()=>a(3)}),e.jsx(s,{label:"Other Advice",type:"text",placeholder:"Enter",name:"homecareInstructions.otherAdvice",onFocus:()=>a(3)})]}),e.jsx(v,{name:"homecareInstructions",children:({push:t,remove:d})=>e.jsx("div",{children:l.homecareInstructions.map((N,i)=>e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(n,{label:"Medicine Name",options:[{value:"Pentafast",label:"pentafast"},{value:"Sinex",label:"sinex"}],name:"homecareInstructions.medicineName",onFocus:()=>a(3)}),e.jsx(n,{label:"dose",options:[{value:"Pentafast",label:"pentafast"},{value:"Sinex",label:"sinex"}],name:"homecareInstructions.dose",onFocus:()=>a(3)}),e.jsx(s,{label:"Frequency",type:"text",placeholder:"Enter",name:"homecareInstructions.frequencey",onFocus:()=>a(3)})]}),e.jsxs("div",{className:"flex justify-center gap-4 mt-2",children:[l.homecareInstructions.length>1&&e.jsxs("button",{type:"button",onClick:()=>d(i),className:"flex items-center px-4 py-2 transition-colors bg-gray-200 rounded-md hover:bg-gray-300",children:[e.jsx("span",{className:"mr-2",children:"−"})," Remove"]}),i===l.homecareInstructions.length-1&&e.jsxs("button",{type:"button",onClick:()=>t({surgeonName:"",assistantSurgeon:"",anesthesiologist:"",scrubNurse:"",circulatingNurse:"",otherStaff:""}),className:"flex items-center px-4 py-2 text-white transition-colors rounded-md bg-primary",children:[e.jsx("span",{className:"mr-2",children:"+"})," Prescribed Medications"]})]})]}))})})]}),e.jsx("div",{className:"flex flex-col gap-4 p-4 bg-white rounded-sm",onClick:()=>a(4),children:e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(s,{label:"Recommended Follow-Up Date",type:"date",placeholder:"Enter",name:"followUp.recommendedFollowUpDate",onFocus:()=>a(4)}),e.jsx(s,{label:"Reason for Revisit",type:"text",placeholder:"Routine Checkup",name:"followUp.revisitReason",onFocus:()=>a(4)}),e.jsx(n,{label:"Department Visit",options:[{value:"Pentafast",label:"pentafast"},{value:"Sinex",label:"sinex"}],name:"followUp.departmentVisit",onFocus:()=>a(4)})]})}),e.jsx("div",{className:"flex flex-col gap-4 p-4 bg-white rounded-sm",onClick:()=>a(5),children:e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(n,{label:"Doctor's Name",options:[{value:"Dr Shishir Thapa",label:"Dr Shishir Thapa"},{value:"Dr Dipak Kumal",label:"Dr Dipak Kumal"}],name:"dischargeApproval.doctorName",onFocus:()=>a(5)}),e.jsx(n,{label:"Nurse's Name",options:[{value:"Dr Shishir Thapa",label:"Dr Shishir Thapa"},{value:"Dr Dipak Kumal",label:"Dr Dipak Kumal"}],name:"dischargeApproval.nurseName",onFocus:()=>a(5)})]})}),e.jsx(f,{onCancel:()=>history.back(),onSubmit:o})]})})})})]})]})};export{D as DischargeGeneralWard};
