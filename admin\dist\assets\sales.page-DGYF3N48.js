import{a5 as f,c6 as W,a1 as X,bo as Z,a2 as e,b3 as _,a9 as D,aa as ee,c7 as se,ao as le,aM as Q,bB as K,aP as G,ct as ne,af as O,ah as oe,aj as $,aR as z,a4 as ce,dr as ae,bM as te,bN as ie,aQ as de,c3 as re}from"./index-ClX9RVH0.js";const xe=({editData:s,onClose:i})=>{var j,p,c,u,g,A,w,a,R,M,I,P,T,C,B;console.log(s);const[d,m]=f.useState(0),{mutateAsync:y}=W(),n=X({initialValues:{selectedMedicine:(j=s==null?void 0:s.productList)==null?void 0:j.map(l=>({quantity:l.quantity,item:l})),returnAmount:0,paymentMethod:"",remarks:"",bank:"",accountNo:""},onSubmit:async l=>{var k,F;console.log(l.selectedMedicine);const x=(k=n.values.selectedMedicine)==null?void 0:k.map(t=>{var E,q,L,H,V,U;return{pProduct:(q=(E=t==null?void 0:t.item)==null?void 0:E.pProduct)==null?void 0:q._id,pBatch:(H=(L=t==null?void 0:t.item)==null?void 0:L.pBatch)==null?void 0:H._id,quantity:t==null?void 0:t.quantity,totalAmount:Number((t==null?void 0:t.quantity)*((U=(V=t==null?void 0:t.item)==null?void 0:V.pBatch)==null?void 0:U.mrpRate))}}),S={inventoryFor:"PHARMACY",category:"SALERETURN",date:new Date().toISOString().split("T")[0],remarks:l.remarks,paymentMethod:l.paymentMethod,returnAmount:l.returnAmount,predefinedBillNo:s==null?void 0:s.invoiceNo,supervisor:s==null?void 0:s.supervisor,totalAmount:n.values.returnAmount,paymentStatus:"PAID",invoiceNo:`SR-${s==null?void 0:s.invoiceNo}-${Math.random().toString(36).slice(2)}`,billingAgainst:(F=s==null?void 0:s.billingAgainst)==null?void 0:F._id,walkInCustomer:s==null?void 0:s.walkInCustomer,productList:x};n.values.paymentMethod==="BANK"&&(S.bank=l.bank),await y(S)}}),{data:h}=Z(),r=(c=(p=h==null?void 0:h.data)==null?void 0:p.banks)==null?void 0:c.map(l=>({value:l._id,label:l.bankName,accountNumber:l.accountNumber}));f.useEffect(()=>{if(n.values.selectedMedicine){const l=n.values.selectedMedicine.reduce((x,S)=>{var k;return x+(((k=S.item.pBatch)==null?void 0:k.mrpRate)||0)*S.quantity},0);m(l),n.setFieldValue("returnAmount",l)}},[n.values.selectedMedicine]);const N=[{title:"Invoice no. :",value:s==null?void 0:s.invoiceNo},{title:"Date. :",value:s==null?void 0:s.date}],b=[{title:"Patient name. :",value:((u=s==null?void 0:s.walkInCustomer)==null?void 0:u.name)??((w=(A=(g=s==null?void 0:s.billingAgainst)==null?void 0:g.commonInfo)==null?void 0:A.personalInfo)==null?void 0:w.fullName)},{title:"Email :",value:((a=s==null?void 0:s.billingAgainst)==null?void 0:a.email)??"N/A"},{title:"Phone :",value:((R=s==null?void 0:s.walkInCustomer)==null?void 0:R.contact)??((T=(P=(I=(M=s==null?void 0:s.billingAgainst)==null?void 0:M.commonInfo)==null?void 0:I.contactInfo)==null?void 0:P.phone)==null?void 0:T.primaryPhone)}],o=[{label:"Cash",value:"CASH"},{label:"Bank",value:"BANK"}];f.useEffect(()=>{if(n.values.bank){const l=r==null?void 0:r.find(x=>x.value===n.values.bank);n.setFieldValue("accountNo",l==null?void 0:l.accountNumber)}},[n.values.bank]);const v=[{type:"select",field:"paymentMethod",label:"Payment Method",options:o},{type:"select",field:"bank",label:"Bank",options:r,isVisible:n.values.paymentMethod==="BANK"},{type:"text",field:"accountNo",label:"Account Number",isVisible:n.values.paymentMethod==="BANK"},{type:"text",field:"remarks",label:"Remarks"}];return e.jsxs("div",{className:"flex flex-col gap-10",children:[e.jsxs("section",{className:"grid grid-cols-2 justify-between",children:[e.jsx("div",{className:"flex flex-col gap-1",children:N.map((l,x)=>e.jsxs("div",{className:"flex gap-2",children:[e.jsx("span",{className:"font-semibold",children:l.title}),e.jsx("span",{children:l.value})]},x))}),e.jsx("div",{children:b.map((l,x)=>e.jsxs("div",{className:"flex gap-2",children:[e.jsx("span",{className:"font-semibold",children:l.title}),e.jsx("span",{children:l.value})]},x))})]}),e.jsxs("section",{children:[e.jsxs("div",{className:"flex border-b pb-2 text-sm text-gray-500",children:[e.jsx("div",{className:"w-1/4",children:"Product"}),e.jsx("div",{className:"w-1/6 text-center",children:"Batch no."}),e.jsx("div",{className:"w-1/6 text-center",children:"Price"}),e.jsx("div",{className:"w-1/6 text-center",children:"QTY"}),e.jsx("div",{className:"w-1/6 text-center",children:"Sub Total"}),e.jsx("div",{className:"w-10"})]}),((B=(C=n.values)==null?void 0:C.selectedMedicine)==null?void 0:B.length)>0?n.values.selectedMedicine.map((l,x)=>{var S,k,F,t,E,q,L,H,V,U;return e.jsxs("div",{className:"flex items-center py-4 border-b",children:[e.jsx("div",{className:"w-1/4 text-xs",children:(k=(S=l.item)==null?void 0:S.pProduct)==null?void 0:k.name}),e.jsx("div",{className:"w-1/5 text-center",children:e.jsxs("div",{className:"relative",children:["B-",(E=(t=(F=l.item)==null?void 0:F.pBatch)==null?void 0:t.batchNo)==null?void 0:E.split("BATCH")[1]]})}),e.jsx("div",{className:"w-1/5 text-center",children:(L=(q=l.item)==null?void 0:q.pBatch)==null?void 0:L.mrpRate}),e.jsx("div",{className:"w-1/6 text-center",children:e.jsx("input",{type:"number",min:"1",value:l.quantity,onChange:Y=>{const J=Number.parseInt(Y.target.value)||0;n.setFieldValue(`selectedMedicine.${x}.quantity`,J)},className:"w-16 p-1 text-center border rounded"})}),e.jsx("div",{className:"w-1/6 text-center",children:(((V=(H=l.item)==null?void 0:H.pBatch)==null?void 0:V.mrpRate)||0)*l.quantity}),e.jsx("div",{className:"w-10 flex justify-center",children:e.jsx("button",{onClick:()=>{const Y=[...n.values.selectedMedicine];Y.splice(x,1),n.setFieldValue("selectedMedicine",Y)},className:"text-red-400 hover:text-red-600",children:e.jsx(_,{icon:"fluent:delete-24-regular"})})})]},(U=l.item)==null?void 0:U._id)}):e.jsx("div",{className:"text-center mt-2",children:"No Product"})]}),e.jsx("section",{className:"flex flex-col gap-3",children:e.jsxs("div",{className:"flex justify-end gap-40",children:[e.jsx("p",{className:"",children:"Return Amount"}),e.jsx("p",{className:"",children:d.toFixed(2)})]})}),e.jsx("section",{className:"-mt-2",children:e.jsx(D,{value:n,children:e.jsxs(ee,{onSubmit:n.handleSubmit,className:"grid grid-cols-2 gap-5",children:[e.jsx(se,{formDatails:v,getFieldProps:n.getFieldProps,touched:n.touched,errors:n.errors}),e.jsx("div",{className:"col-span-2 mt-4",children:e.jsx(le,{onCancel:i,onSubmit:()=>{n.submitForm(),i()}})})]})})})]})},me=()=>{var p;const s=Q({category:"SALE",inventoryFor:"PHARMACY",paymentStatus:["PAID","PARTIALLY-PAID"]}),[i,d]=f.useState({viewModal:!1,printModal:!1,editModal:!1}),[m,y]=f.useState(void 0),[n,h]=f.useState(!1),r=f.useRef(null),{data:N}=K(s),b=(p=N==null?void 0:N.data)==null?void 0:p.invoices,o=G(()=>d({viewModal:!1,printModal:!1,editModal:!1})),v=ne.useReactToPrint({contentRef:r});f.useEffect(()=>{n&&m&&i.printModal&&setTimeout(()=>{v(),h(!1)},100)},[n,m,i.printModal,v]);const j={columns:[{title:"S.N",key:"sn"},{title:"Invoice no",key:"invoiceNo"},{title:"Date",key:"date"},{title:"Customer",key:"customer"},{title:"Amount Due",key:"due"},{title:"Total",key:"total"},{title:"Payment Status",key:"status"},{title:"Actions",key:"action"}],rows:b==null?void 0:b.map((c,u)=>{var g,A,w,a,R,M;return{sn:u+1,invoiceNo:c==null?void 0:c.invoiceNo,date:c==null?void 0:c.date,customer:((g=c==null?void 0:c.walkInCustomer)==null?void 0:g.name)??((a=(w=(A=c==null?void 0:c.billingAgainst)==null?void 0:A.commonInfo)==null?void 0:w.personalInfo)==null?void 0:a.fullName)??"-",due:(R=c==null?void 0:c.dueAmount)==null?void 0:R.toFixed(2),total:(M=c==null?void 0:c.payableAmount)==null?void 0:M.toFixed(2),status:e.jsx(oe,{status:c==null?void 0:c.paymentStatus}),action:e.jsx(O,{onPrint:()=>{y(c),d({printModal:!0,viewModal:!1,editModal:!1}),h(!0)},onReturn:()=>{y(c),d({printModal:!1,viewModal:!1,editModal:!0})}})}})};return e.jsxs("div",{children:[e.jsx($,{columns:j.columns,rows:j.rows,loading:!1}),i.editModal&&e.jsxs(z,{ref:o,classname:"p-8  overflow-scroll flex flex-col gap-2",children:[e.jsx(ce,{as:"h3",size:"body-lg-lg",variant:"primary-blue",className:"mt-6 px-2 text-primary",children:"Sales Return"}),e.jsx(xe,{editData:m,onClose:()=>d({printModal:!1,viewModal:!1,editModal:!1})})]}),e.jsx("div",{style:{display:"none"},children:e.jsx(ae,{ref:r,invoice:m})})]})},he=({returnData:s})=>{var i,d,m,y,n,h,r,N,b,o,v,j,p,c,u,g,A,w;return console.log(s),e.jsxs("div",{className:"max-w-6xl mx-auto p-6 bg-white",children:[e.jsx("div",{className:"border-b-2 border-gray-200 pb-6 mb-6",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Sales Return"}),e.jsxs("p",{className:"text-gray-600",children:["Return processed for invoice #",s==null?void 0:s.invoiceNo]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium mb-2",children:"RETURN"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Date: ",new Date((s==null?void 0:s.date)??"").toLocaleDateString()]})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx(_,{icon:"mdi:user-outline",className:"h-5 w-5 text-gray-600 mr-2"}),e.jsx("h3",{className:"font-semibold text-gray-900",children:"Customer Details"})]}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Name:"}),e.jsx("span",{className:"font-medium",children:((i=s==null?void 0:s.walkInCustomer)==null?void 0:i.name)??((y=(m=(d=s==null?void 0:s.billingAgainst)==null?void 0:d.commonInfo)==null?void 0:m.personalInfo)==null?void 0:y.fullName)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Contact:"}),e.jsx("span",{className:"font-medium",children:((n=s==null?void 0:s.walkInCustomer)==null?void 0:n.contact)??((b=(N=(r=(h=s==null?void 0:s.billingAgainst)==null?void 0:h.commonInfo)==null?void 0:r.contactInfo)==null?void 0:N.phone)==null?void 0:b.primaryPhone)})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Address:"}),e.jsx("span",{className:"font-medium text-right max-w-32 truncate",children:((o=s==null?void 0:s.walkInCustomer)==null?void 0:o.address)??((c=(p=(j=(v=s==null?void 0:s.billingAgainst)==null?void 0:v.commonInfo)==null?void 0:j.contactInfo)==null?void 0:p.address)==null?void 0:c.currentAddress)})]})]})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxs("div",{className:"flex items-center mb-3",children:[e.jsx(_,{icon:"mdi:file-outline",className:"h-5 w-5 text-gray-600 mr-2"}),e.jsx("h3",{className:"font-semibold text-gray-900",children:"Invoice Details"})]}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Invoice No:"}),e.jsx("span",{className:"font-medium",children:s==null?void 0:s.invoiceNo})]}),(s==null?void 0:s.transactionNo)&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Transaction No:"}),e.jsx("span",{className:"font-medium",children:s==null?void 0:s.transactionNo})]})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("div",{className:"flex items-center mb-4",children:e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"Products Returned"})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full border-collapse border border-gray-200",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-50",children:[e.jsx("th",{className:"border border-gray-200 px-4 py-3 text-left text-sm font-semibold text-gray-900",children:"Product Name"}),e.jsx("th",{className:"border border-gray-200 px-4 py-3 text-left text-sm font-semibold text-gray-900",children:"Batch No"}),e.jsx("th",{className:"border border-gray-200 px-4 py-3 text-center text-sm font-semibold text-gray-900",children:"Quantity Returned"}),e.jsx("th",{className:"border border-gray-200 px-4 py-3 text-right text-sm font-semibold text-gray-900",children:"Unit Price (RS.)"}),e.jsx("th",{className:"border border-gray-200 px-4 py-3 text-right text-sm font-semibold text-gray-900",children:"Total Amount (RS.)"})]})}),e.jsx("tbody",{children:(u=s==null?void 0:s.productList)==null?void 0:u.map((a,R)=>{var M,I,P,T,C,B;return e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"border border-gray-200 px-4 py-3",children:e.jsx("div",{children:e.jsx("div",{className:"font-medium text-gray-900",children:(M=a==null?void 0:a.pProduct)==null?void 0:M.name})})}),e.jsxs("td",{className:"border border-gray-200 px-4 py-3 text-sm text-gray-600",children:["B-",(P=(I=a==null?void 0:a.pBatch)==null?void 0:I.batchNo)==null?void 0:P.split("BATCH")[1]]}),e.jsx("td",{className:"border border-gray-200 px-4 py-3 text-center",children:e.jsx("span",{className:"bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm font-medium",children:a==null?void 0:a.quantity})}),e.jsx("td",{className:"border border-gray-200 px-4 py-3 text-right text-sm text-gray-900",children:(C=(T=a==null?void 0:a.pBatch)==null?void 0:T.mrpRate)==null?void 0:C.toFixed(0)}),e.jsx("td",{className:"border border-gray-200 px-4 py-3 text-right font-medium text-gray-900",children:(B=a==null?void 0:a.totalAmount)==null?void 0:B.toFixed(2)})]},a._id||R)})})]})})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:e.jsxs("div",{className:"bg-gray-50 p-6 rounded-lg",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Return Summary"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Subtotal:"}),e.jsx("span",{className:"font-medium",children:((g=s==null?void 0:s.subTotal)==null?void 0:g.toFixed(0))||(s==null?void 0:s.totalAmount.toFixed(0))})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Discount:"}),e.jsxs("span",{className:"font-medium text-green-600",children:["-RS. ",s==null?void 0:s.discount.toFixed(0)]})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Tax:"}),e.jsxs("span",{className:"font-medium",children:["RS. ",(A=s==null?void 0:s.tax)==null?void 0:A.toFixed(2)]})]}),e.jsx("div",{className:"border-t pt-3",children:e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"font-semibold text-gray-900",children:"Total Return Amount:"}),e.jsxs("span",{className:"font-bold text-lg text-red-600",children:["RS. ",(w=s==null?void 0:s.payableAmount)==null?void 0:w.toFixed(2)]})]})})]})]})})]})},Ne=()=>{var b;const s=Q({category:ie.SALERETURN,inventoryFor:te.PHARMACY}),{data:i}=K(s),d=(b=i==null?void 0:i.data)==null?void 0:b.invoices,[m,y]=f.useState(!1),n=G(()=>y(!1)),[h,r]=f.useState(),N={columns:[{title:"S.N",key:"sn"},{title:"Invoice no",key:"invoiceNo"},{title:"Date",key:"date"},{title:"Customer",key:"customer"},{title:"Transaction ID",key:"transactionID"},{title:"Payment Method",key:"paymentMethod"},{title:"Total Amount (RS.)",key:"total"},{title:"Action",key:"action"}],rows:d==null?void 0:d.map((o,v)=>{var j,p,c,u;return{sn:v+1,invoiceNo:o==null?void 0:o.invoiceNo,date:o==null?void 0:o.date,customer:((j=o==null?void 0:o.walkInCustomer)==null?void 0:j.name)??((u=(c=(p=o==null?void 0:o.billingAgainst)==null?void 0:p.commonInfo)==null?void 0:c.personalInfo)==null?void 0:u.fullName),due:o==null?void 0:o.dueAmount,transactionID:(o==null?void 0:o.transactionNo)??"-",total:o==null?void 0:o.payableAmount,paymentMethod:o==null?void 0:o.paymentMethod,action:e.jsx(O,{onShow:()=>{r(o),y(!0)}})}})};return e.jsxs("div",{children:[e.jsx($,{columns:N.columns,rows:N.rows,loading:!1}),m&&e.jsx(z,{ref:n,classname:"h-[550px] overflow-scroll p-4",children:e.jsx(he,{returnData:h})})]})},je=()=>{const[s,i]=f.useState("sales");return e.jsxs("div",{className:"flex flex-col",children:[e.jsx(de,{headerTitle:"Total Sales",onSearch:!0}),e.jsxs("section",{className:"flex flex-col bg-white gap-3",children:[e.jsx(re,{tabs:[{label:"Sales",value:"sales"},{label:"Sales Return",value:"salesReturn"}],defaultTab:s,onTabChange:d=>{i(d)}}),e.jsx("section",{children:s==="sales"?e.jsx(me,{}):e.jsx(Ne,{})})]})]})};export{je as SalesPage};
