import { useState } from "react";
import Medicalsilders from "../Consultations/component/Medicalsilders";
import AdministerMedication from "./pages/AdministerMedication";
import AdministerVaccination from "./pages/AdministerVaccination";
import AdministerBlood from "./pages/AdministerBlood";
import AdministerMedicalDevices from "./pages/AdministerMedicalDevices";

const medicalRecordTab = [
  "Administer Medication",
  "Administer Vaccination",
  "Administer Blood",
  "Administer Medical Devices",
];

const TreatmentRecordSideBar = () => {
  const [activeSection, setActiveSection] = useState("Administer Medication");

  const renderContent = () => {
    switch (activeSection) {
      case "Administer Medication":
        return (
          <div>
            <AdministerMedication />
          </div>
        );
      case "Administer Vaccination":
        return (
          <div>
            <AdministerVaccination />
          </div>
        );
      case "Administer Blood":
        return (
          <div>
            <AdministerBlood />
          </div>
        );

      case "Administer Medical Devices":
        return (
          <div>
            <AdministerMedicalDevices />
          </div>
        );
    }
  };

  return (
    <div className='flex h-screen bg-[#F8F8F8]'>
      <Medicalsilders
        tabs={medicalRecordTab}
        defaultTab='Administer Medication'
        onTabChange={setActiveSection}
      />
      <div className='flex-1 overflow-auto'>{renderContent()}</div>
    </div>
  );
};

export default TreatmentRecordSideBar;
