{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/api/mutation.ts", "../../src/api/query.ts", "../../src/api/request.ts", "../../src/interface/global.interface.ts", "../../src/assets/svg/attendancesvg.tsx", "../../src/assets/svg/svg.tsx", "../../src/components/actionbutton.tsx", "../../src/components/areafield.tsx", "../../src/components/boolean-field.tsx", "../../src/components/breadcrumb.tsx", "../../src/components/button.tsx", "../../src/components/checkbox.tsx", "../../src/components/collapsablesection.tsx", "../../src/components/customtab.tsx", "../../src/components/customtabwithextrafield.tsx", "../../src/components/datepicker.tsx", "../../src/components/deletedialog.tsx", "../../src/components/drawer.tsx", "../../src/components/dropdownfield.tsx", "../../src/components/dropdownfieldtags.tsx", "../../src/components/dynamictab.tsx", "../../src/components/exportactions.tsx", "../../src/components/filterdropdown.tsx", "../../src/components/formikmultiselect.tsx", "../../src/components/globalform.tsx", "../../src/components/header.tsx", "../../src/components/input-field.tsx", "../../src/components/loader.tsx", "../../src/components/mainnavigation.tsx", "../../src/components/multiselectwithchips.tsx", "../../src/components/multivalueinputfield.tsx", "../../src/components/renderprinatabledata.tsx", "../../src/components/routerenderer.tsx", "../../src/components/sampledata.ts", "../../src/components/searchdropdown.tsx", "../../src/components/searchabledropdown.tsx", "../../src/components/searchableselect.tsx", "../../src/components/secondarynavbar.tsx", "../../src/components/selectinput.tsx", "../../src/components/shortstatus.tsx", "../../src/components/sidebarprops.tsx", "../../src/components/status.tsx", "../../src/components/statusbadge.tsx", "../../src/components/stepper.tsx", "../../src/components/submenu.tsx", "../../src/components/text.tsx", "../../src/components/textarea.tsx", "../../src/components/uploadfile.tsx", "../../src/components/uploadsinglefile.tsx", "../../src/components/index.ts", "../../src/components/modifycustomtab.tsx", "../../src/components/alternativeheader/alternativeheader.tsx", "../../src/components/calendar/customcalendar.tsx", "../../src/components/department/departmentheader.tsx", "../../src/components/fourboxheader/fourboxheader.tsx", "../../src/components/modal/calculateage.tsx", "../../src/components/modal/notificationpopup.tsx", "../../src/components/modal/popup-modal.tsx", "../../src/components/modal/side-modal.tsx", "../../src/components/modal/srollablemodal.tsx", "../../src/components/performancedashboard/performancedashboard.tsx", "../../src/components/performancedashboard/performancemonitorbutton.tsx", "../../src/components/performancedashboard/index.ts", "../../src/components/examples/mastertablewithcheckboxexample.tsx", "../../src/components/loader/skeleton-loader.tsx", "../../src/components/loader/table-skeleton-loader.tsx", "../../src/constant/loadermessage.ts", "../../src/constant/constant.ts", "../../src/context/permissioncontext.tsx", "../../src/context/sidebarcontext.tsx", "../../src/context/appcontext.tsx", "../../src/hooks/sidebarhooks.tsx", "../../src/hooks/useoutsideclick.tsx", "../../src/hooks/index.ts", "../../src/hooks/useauth.tsx", "../../src/hooks/usebuildquery.tsx", "../../src/hooks/usedepartment.tsx", "../../src/hooks/usefilteredroutes.tsx", "../../src/hooks/usenotification.tsx", "../../src/hooks/usepaymentvouchers.ts", "../../src/layouts/navbar.tsx", "../../src/layouts/notificationbell.tsx", "../../src/layouts/sidebar.tsx", "../../src/layouts/index.ts", "../../src/layouts/table/mastertable.tsx", "../../src/layouts/table/pagination.tsx", "../../src/layouts/table/shimmerloader.tsx", "../../src/layouts/table/tableaction.tsx", "../../src/layouts/table/tablebody.tsx", "../../src/layouts/table/tablehead.tsx", "../../src/pages/loader.tsx", "../../src/pages/notfoundpage.tsx", "../../src/pages/testform.tsx", "../../src/pages/index.ts", "../../src/pages/accounting/index.ts", "../../src/pages/accounting/balancesheet/balancesheet.tsx", "../../src/pages/accounting/balancesheet/balancesheet.page.tsx", "../../src/pages/accounting/balancesheet/components/printbalancesheet.component.tsx", "../../src/pages/accounting/balancesheet/components/printingbalancesheet.tsx", "../../src/pages/accounting/generalledgerbalance/generalledgerbalance.tsx", "../../src/pages/accounting/generalledgerbalance/components/commonheader.tsx", "../../src/pages/accounting/generalledgerbalance/components/printgeneralledger.tsx", "../../src/pages/accounting/incomestatement/incomestatement.tsx", "../../src/pages/accounting/incomestatement/components/printincomestatement.component.tsx", "../../src/pages/accounting/paymentvoucher/paymentvouchers.tsx", "../../src/pages/accounting/paymentvoucher/index.ts", "../../src/pages/accounting/paymentvoucher/payment.voucher.page.tsx", "../../src/pages/accounting/paymentvoucher/components/createpaymentvouchermodal.tsx", "../../src/pages/accounting/paymentvoucher/components/paymentvoucherpaymodal.tsx", "../../src/pages/accounting/paymentvoucher/components/paymentvoucherprintout.tsx", "../../src/pages/accounting/paymentvoucher/components/printpaymentvoucher.tsx", "../../src/pages/accounting/paymentvoucher/components/index.ts", "../../src/pages/accounting/purchasereturn/purchasereturnivoice.tsx", "../../src/pages/accounting/purchasereturn/components/printpurchasereturn.tsx", "../../src/pages/accounting/salesreturn/salesreturn.tsx", "../../src/pages/accounting/salesreturn/components/printsalesreturn.tsx", "../../src/pages/accounting/salesvoucher/salesvoucher.tsx", "../../src/pages/accounting/salesvoucher/components/printsalesvoucher.tsx", "../../src/pages/accounting/transactionlist/transactionslist.tsx", "../../src/pages/accounting/transactionlist/components/printtransaction.tsx", "../../src/pages/ambulance/index.ts", "../../src/pages/ambulance/addambulancetype/addambulancetype.tsx", "../../src/pages/ambulance/addambulancetype/components/dropdown.tsx", "../../src/pages/ambulance/addambulancetype/components/multiselect.tsx", "../../src/pages/ambulance/ambulanceinquiry/ambulanceinquiry.tsx", "../../src/pages/ambulance/ambulanceinquiry/components/addambulanceinquiryform.tsx", "../../src/pages/ambulance/ambulanceinquiry/components/ambulancebilling.tsx", "../../src/pages/ambulance/ambulanceinquiry/components/dashedarrow.tsx", "../../src/pages/ambulance/ambulanceinquiry/components/editambulanceinquiryform.tsx", "../../src/pages/ambulance/ambulanceinquiry/components/headerbreadcrumb.tsx", "../../src/pages/ambulance/ambulanceinquiry/components/map.tsx", "../../src/pages/ambulance/ambulanceinquiry/components/modalcontent.tsx", "../../src/pages/ambulance/ambulanceinquiry/components/picklocationinput.tsx", "../../src/pages/ambulance/ambulanceinquiry/components/viewdetails.tsx", "../../src/pages/ambulance/ambulanceinquiry/components/ambulanceinquiryinterface.ts", "../../src/pages/ambulance/ambulancelist/ambulancelist.tsx", "../../src/pages/ambulance/ambulancelist/components/addambulanceform.tsx", "../../src/pages/ambulance/ambulancetype/ambulancetype.tsx", "../../src/pages/ambulance/ambulancetype/components/sampledata.ts", "../../src/pages/ambulance/ambulancetype/components/viewambulancetypedetails.tsx", "../../src/pages/ambulance/priceconfig/priceconfig.tsx", "../../src/pages/ambulance/components/modal.tsx", "../../src/pages/attendance/attendancelist.tsx", "../../src/pages/attendance/index.ts", "../../src/pages/attendance/components/addattendance.tsx", "../../src/pages/attendance/components/viewattendance.tsx", "../../src/pages/auth/changepassword.component.tsx", "../../src/pages/auth/emailverification.component.tsx", "../../src/pages/auth/forgotpage.tsx", "../../src/pages/auth/loginpage.tsx", "../../src/pages/auth/otp.component.tsx", "../../src/pages/auth/verificationpage.tsx", "../../src/pages/auth/index.ts", "../../src/pages/bank/bank.page.tsx", "../../src/pages/bank/index.ts", "../../src/pages/bank/transaction-details.page.tsx", "../../src/pages/bank/components/addbank.component.tsx", "../../src/pages/bank/components/adjusttransaction.tsx", "../../src/pages/bank/components/transactiondetails.component.tsx", "../../src/pages/bank/components/transactiontabledata.tsx", "../../src/pages/bedallocation/index.ts", "../../src/pages/bedallocation/bedallocationlist/bedallocation.tsx", "../../src/pages/bedallocation/bedallocationlist/components/assignbed.tsx", "../../src/pages/bedallocation/bedallocationlist/components/bedallocationdetail.component.tsx", "../../src/pages/bedallocation/bedallocationlist/components/editbedallocation.tsx", "../../src/pages/bedallocation/bedcategory/bedcategory.tsx", "../../src/pages/bedallocation/bedlist/bedlist.tsx", "../../src/pages/bedallocation/roomsdetail/roomdetails.tsx", "../../src/pages/bedallocation/roomsdetail/components/addroom.tsx", "../../src/pages/certificate/addcertificate.tsx", "../../src/pages/certificate/certificate.page.tsx", "../../src/pages/certificate/index.ts", "../../src/pages/certificate/components/birthcertificate.tsx", "../../src/pages/certificate/components/birthtable.tsx", "../../src/pages/certificate/components/createbirthcertifcate.tsx", "../../src/pages/certificate/components/createdeathcertificate.tsx", "../../src/pages/certificate/components/deathcertificate.tsx", "../../src/pages/certificate/components/deathtable.tsx", "../../src/pages/dashboard/dashboardindex.tsx", "../../src/pages/dashboard/roledashboard.tsx", "../../src/pages/dashboard/index.ts", "../../src/pages/dashboard/accountdashboard/accountdashboard.tsx", "../../src/pages/dashboard/accountdashboard/components/datacard.tsx", "../../src/pages/dashboard/accountdashboard/components/departmentrevenue.tsx", "../../src/pages/dashboard/accountdashboard/components/expensestracker.tsx", "../../src/pages/dashboard/accountdashboard/components/financialreports.tsx", "../../src/pages/dashboard/accountdashboard/components/invoicemanagement.tsx", "../../src/pages/dashboard/accountdashboard/components/overallincomechart.tsx", "../../src/pages/dashboard/accountdashboard/components/patientbilling.tsx", "../../src/pages/dashboard/accountdashboard/components/paymentgatewaystatus.tsx", "../../src/pages/dashboard/accountdashboard/components/recentexpenses.tsx", "../../src/pages/dashboard/accountdashboard/components/recenttransactions.tsx", "../../src/pages/dashboard/accountdashboard/components/revenuevsexpenseschart.tsx", "../../src/pages/dashboard/accountdashboard/components/weeklyrevenueexpensesbarchart.tsx", "../../src/pages/dashboard/admindashboard/admindashboard.tsx", "../../src/pages/dashboard/admindashboard/components/appointmentdata.tsx", "../../src/pages/dashboard/admindashboard/components/bedfacilitymanagement.tsx", "../../src/pages/dashboard/admindashboard/components/billings.tsx", "../../src/pages/dashboard/admindashboard/components/chart.tsx", "../../src/pages/dashboard/admindashboard/components/dashreport.tsx", "../../src/pages/dashboard/admindashboard/components/doctorsschedule.tsx", "../../src/pages/dashboard/admindashboard/components/recentactivity.tsx", "../../src/pages/dashboard/admindashboard/components/revenueinsights.tsx", "../../src/pages/dashboard/admindashboard/components/staffcountavailability.tsx", "../../src/pages/dashboard/admindashboard/components/statcard.tsx", "../../src/pages/dashboard/admindashboard/components/admin.obj.ts", "../../src/pages/dashboard/admindashboard/components1/billing.tsx", "../../src/pages/dashboard/admindashboard/components1/statcard.tsx", "../../src/pages/dashboard/doctordashboard/doctordashboard.tsx", "../../src/pages/dashboard/doctordashboard/components1/appointmentinsights.tsx", "../../src/pages/dashboard/doctordashboard/components1/appointmenttracker.tsx", "../../src/pages/dashboard/doctordashboard/components1/doctorcard.tsx", "../../src/pages/dashboard/doctordashboard/components1/overallappointments.tsx", "../../src/pages/dashboard/doctordashboard/components1/patientlist.component.tsx", "../../src/pages/dashboard/doctordashboard/components1/patientvolumeanalytics.tsx", "../../src/pages/dashboard/doctordashboard/components1/reportlist.component.tsx", "../../src/pages/dashboard/doctordashboard/components1/reportresult.component.tsx", "../../src/pages/dashboard/doctordashboard/components1/upcommingappointments.tsx", "../../src/pages/dashboard/doctordashboard/components/horizontalcalender.tsx", "../../src/pages/dashboard/doctordashboard/components/infocard.tsx", "../../src/pages/dashboard/doctordashboard/components/mycalender.tsx", "../../src/pages/dashboard/doctordashboard/components/nextpatient.tsx", "../../src/pages/dashboard/doctordashboard/components/profile.tsx", "../../src/pages/dashboard/doctordashboard/components/sampledata.ts", "../../src/pages/dashboard/doctordashboard/components/welcome.tsx", "../../src/pages/dashboard/headnursedashboard/headnursedashboard.tsx", "../../src/pages/dashboard/headnursedashboard/components/headnursecard.tsx", "../../src/pages/dashboard/headnursedashboard/components/headnursetestrequesttable.tsx", "../../src/pages/dashboard/headnursedashboard/components/horizontalcalendar.tsx", "../../src/pages/dashboard/headnursedashboard/components/nurselist.tsx", "../../src/pages/dashboard/headnursedashboard/components/recentpatientlist.tsx", "../../src/pages/dashboard/headnursedashboard/components/shiftcalendar.tsx", "../../src/pages/dashboard/labdashboard/labdashboard.tsx", "../../src/pages/dashboard/labdashboard/components/billing.tsx", "../../src/pages/dashboard/labdashboard/components/chemicalexpiryinventory.tsx", "../../src/pages/dashboard/labdashboard/components/departmentrequests.tsx", "../../src/pages/dashboard/labdashboard/components/lowstockproducts.tsx", "../../src/pages/dashboard/labdashboard/components/recentreport.tsx", "../../src/pages/dashboard/labdashboard/components/summarycards.tsx", "../../src/pages/dashboard/labdashboard/components/testlist.tsx", "../../src/pages/dashboard/labdashboard/components/testrequesttable.tsx", "../../src/pages/dashboard/labdashboard/components/testvolumetrends.tsx", "../../src/pages/dashboard/labdashboard/components/toporderedtests.tsx", "../../src/pages/dashboard/labdashboard/components/totaltestchart.tsx", "../../src/pages/dashboard/labdashboard/components/uncollectedreports.tsx", "../../src/pages/dashboard/nursedashboaed/nursedashboard.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/patientdetail.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/component/nursesectionobj.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/consultation.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/component/medicalsidebar.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/component/medicalsilders.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/advice.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/allergies.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/complaintshistory.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/currentmedication.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/diagnosis.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/investigation.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/navbartab.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/prescription.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/presentingcomplaints.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/vitalsigns.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations/pages/samplemastertabledata.ts", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/doctorconsultation.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/component/medicalsidebar.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/component/medicalsilders.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/d.advice.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/d.allergies.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/d.currentmedication.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/d.diagnosis.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/d.historyandexaminations.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/d.investigation.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/d.medicalcertificate.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/d.prescription.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/d.presentingcomplaints.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/d.vitalsigns.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/consultations copy/pages/component/sampledata.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/medicalrecord.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/medicalrecordsidebar.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/pages/allergies.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/pages/chronicconditions.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/pages/familyhistory.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/pages/immunizations.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/pages/medicaladministration.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/pages/socialhistory.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/pages/surgicalhistory.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/pages/visithistory.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/medical records/pages/vitalsigns.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/patientinformation/patientinformation.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/treatmentrecords/treatmentrecordsidebar.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/treatmentrecords/treatmentrecords.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/treatmentrecords/pages/administerblood.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/treatmentrecords/pages/administermedicaldevices.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/treatmentrecords/pages/administermedication.tsx", "../../src/pages/dashboard/nursedashboaed/component-1/maincomponent/treatmentrecords/pages/administervaccination.tsx", "../../src/pages/dashboard/nursedashboaed/componentmain/calender.tsx", "../../src/pages/dashboard/nursedashboaed/componentmain/nursecard.tsx", "../../src/pages/dashboard/nursedashboaed/componentmain/recentactivity.tsx", "../../src/pages/dashboard/nursedashboaed/componentmain/reminders.tsx", "../../src/pages/dashboard/nursedashboaed/inventrymanag/inventrymanag.tsx", "../../src/pages/dashboard/nursedashboaed/inventrymanag/consumables/consumables.tsx", "../../src/pages/dashboard/nursedashboaed/inventrymanag/equipments/equipments.tsx", "../../src/pages/dashboard/nursedashboaed/inventrymanag/medications/medications.tsx", "../../src/pages/dashboard/nursedashboaed/inventrymanag/safety/safety.tsx", "../../src/pages/dashboard/nursedashboaed/shiftoverview/shiftoverview.tsx", "../../src/pages/dashboard/nursedashboaed/shiftoverview/components/alerts.tsx", "../../src/pages/dashboard/nursedashboaed/shiftoverview/components/shiftoverviewchart.tsx", "../../src/pages/dashboard/radiologydashboard/radiologydashboard.page.tsx", "../../src/pages/dashboard/radiologydashboard/components/equipmentstatus.tsx", "../../src/pages/dashboard/radiologydashboard/components/radiologistdutyschedule.tsx", "../../src/pages/dashboard/radiologydashboard/components/radiologylowstockproducts.tsx", "../../src/pages/dashboard/radiologydashboard/components/radiologyrecentreport.tsx", "../../src/pages/dashboard/radiologydashboard/components/radiologysummarycards.tsx", "../../src/pages/dashboard/radiologydashboard/components/totaltestradiologychart.tsx", "../../src/pages/dashboard/pharmacydashboard/pharmacydashboard.tsx", "../../src/pages/dashboard/pharmacydashboard/components/expiryalerts.tsx", "../../src/pages/dashboard/pharmacydashboard/components/inventoryvalue.tsx", "../../src/pages/dashboard/pharmacydashboard/components/lowstockalerts.tsx", "../../src/pages/dashboard/pharmacydashboard/components/lowstockreporttable.tsx", "../../src/pages/dashboard/pharmacydashboard/components/machinealerts.tsx", "../../src/pages/dashboard/pharmacydashboard/components/mostsolditems.tsx", "../../src/pages/dashboard/pharmacydashboard/components/pendingorders.tsx", "../../src/pages/dashboard/pharmacydashboard/components/pharmacycards.tsx", "../../src/pages/dashboard/pharmacydashboard/components/prescriptionstatus.tsx", "../../src/pages/dashboard/pharmacydashboard/components/profitchart.tsx", "../../src/pages/dashboard/pharmacydashboard/components/saleschart.tsx", "../../src/pages/dashboard/pharmacydashboard/components/salesoverviewchart.tsx", "../../src/pages/dashboard/pharmacydashboard/components/salesperformancechart.tsx", "../../src/pages/dashboard/pharmacydashboard/components/salesreturntracking.tsx", "../../src/pages/dashboard/pharmacydashboard/components/sampledata.ts", "../../src/pages/dashboard/pharmacydashboard/components/stocklevelsbycategory.tsx", "../../src/pages/dashboard/pharmacydashboard/components/summarycard.tsx", "../../src/pages/dashboard/pharmacydashboard/components/topsellingproducts.tsx", "../../src/pages/donor/index.ts", "../../src/pages/donor/bloodbank/bloodbank.tsx", "../../src/pages/donor/bloodbank/components/sampledata.ts", "../../src/pages/donor/blooddonorlist/blooddonorlist.tsx", "../../src/pages/donor/blooddonorlist/components/adddonor.tsx", "../../src/pages/donor/blooddonorlist/components/sampledata.ts", "../../src/pages/donor/blooddonorlist/components/viewdetaildonor.tsx", "../../src/pages/donor/blooddonorlist/components/detailpages/donordetail.tsx", "../../src/pages/donor/blooddonorlist/components/detailpages/patientcard.tsx", "../../src/pages/donor/cashequipment/cashequipment.tsx", "../../src/pages/donor/cashequipment/components/donationmodal.tsx", "../../src/pages/donor/cashequipment/components/dynamiccashfield.tsx", "../../src/pages/donor/cashequipment/components/dynamicequipmentform.tsx", "../../src/pages/donor/cashequipment/components/cash.obj.ts", "../../src/pages/donor/organdonorlist/organdonorlist.tsx", "../../src/pages/donor/organdonorlist/components/sampledata.ts", "../../src/pages/donor/receiever/receiver.tsx", "../../src/pages/donor/receiever/viewdetaildonor.tsx", "../../src/pages/donor/receiever/customform/customform.tsx", "../../src/pages/emergency/emergencyobj.ts", "../../src/pages/emergency/index.ts", "../../src/pages/emergency/addpatient/addpatient.tsx", "../../src/pages/emergency/emergencyroom/emergencyroompage.tsx", "../../src/pages/emergency/emergencyroom/components/doctor.tsx", "../../src/pages/emergency/emergencyroom/components/nurse.tsx", "../../src/pages/emergency/emergencyroom/components/patient.tsx", "../../src/pages/emergency/emergencyroom/components/staff.tsx", "../../src/pages/events/add_events.tsx", "../../src/pages/events/events.tsx", "../../src/pages/events/index.ts", "../../src/pages/events/calendar/customtoolbar.tsx", "../../src/pages/events/components/eventcard.tsx", "../../src/pages/events/components/headerbar.tsx", "../../src/pages/expenses/expensecategory.tsx", "../../src/pages/expenses/expenseobj.tsx", "../../src/pages/expenses/expenseslist.tsx", "../../src/pages/expenses/index.ts", "../../src/pages/expenses/sampledataexp/sampleexpenses.tsx", "../../src/pages/expenses/voucher/voucherindex.tsx", "../../src/pages/expenses/voucher/components/voucherpay.tsx", "../../src/pages/expenses/voucher/components/voucherprintout.tsx", "../../src/pages/expenses/voucher/components/voucher.obj.ts", "../../src/pages/expenses/components/addexpense.tsx", "../../src/pages/expenses/components/expensemodal.tsx", "../../src/pages/expenses/components/expensetypes.tsx", "../../src/pages/expenses/components/fileupload.tsx", "../../src/pages/expenses/components/receiptupload.tsx", "../../src/pages/expenses/components/validationschema.tsx", "../../src/pages/expenses/components/category/addexpensecategory.tsx", "../../src/pages/financialops/index.ts", "../../src/pages/financialops/invoice/createinvoice.tsx", "../../src/pages/financialops/invoice/invoicepage.tsx", "../../src/pages/financialops/invoice/components/canteen.tsx", "../../src/pages/financialops/invoice/components/ipd.tsx", "../../src/pages/financialops/invoice/components/lab.tsx", "../../src/pages/financialops/invoice/components/opd.tsx", "../../src/pages/financialops/invoice/components/pharmacy.tsx", "../../src/pages/financialops/invoice/components/invoiceinterfaces.ts", "../../src/pages/financialops/payment/advancepaymentform.page.tsx", "../../src/pages/financialops/payment/paymentpage.tsx", "../../src/pages/financialops/payment/index.ts", "../../src/pages/financialops/payment/components/canteen.tsx", "../../src/pages/financialops/payment/components/ipd.tsx", "../../src/pages/financialops/payment/components/lab.tsx", "../../src/pages/financialops/payment/components/opd.tsx", "../../src/pages/financialops/payment/components/pharmacy.tsx", "../../src/pages/ipd/transferpatient.tsx", "../../src/pages/ipd/index.ts", "../../src/pages/ipd/daycareunit/daycareunit.tsx", "../../src/pages/ipd/generalward/generalwardpage.tsx", "../../src/pages/ipd/generalward/globalform.tsx", "../../src/pages/ipd/generalward/generalwardobj.ts", "../../src/pages/ipd/generalward/components/dischargegeneralward.tsx", "../../src/pages/ipd/generalward/components/dynamicfields.tsx", "../../src/pages/ipd/generalward/components/editpatientform.tsx", "../../src/pages/ipd/generalward/components/generalheader.tsx", "../../src/pages/ipd/generalward/components/ipdpatientform.tsx", "../../src/pages/ipd/generalward/components/surgerygeneralward.tsx", "../../src/pages/ipd/generalward/components/transfergeneralward.tsx", "../../src/pages/ipd/generalward/components/treatmentgeneralward.tsx", "../../src/pages/ipd/generalward/types/users.types.ts", "../../src/pages/ipd/gyneobservationward/gyneobservation.tsx", "../../src/pages/ipd/highdependencyunit/highdependency.tsx", "../../src/pages/ipd/icu/icu.tsx", "../../src/pages/ipd/nicu/nicu.tsx", "../../src/pages/ipd/postoperativeward/postoperativeward.tsx", "../../src/pages/inventorymanagement/index.ts", "../../src/pages/inventorymanagement/categorylist/categorylist.tsx", "../../src/pages/inventorymanagement/categorylist/components/addcategory.tsx", "../../src/pages/inventorymanagement/categorylist/components/hospinvproductcategoryinterface.ts", "../../src/pages/inventorymanagement/categorylist/components/select.tsx", "../../src/pages/inventorymanagement/inventory/inventorypage.tsx", "../../src/pages/inventorymanagement/inventory/components/viewinventorycard.tsx", "../../src/pages/inventorymanagement/productlist/productlist.tsx", "../../src/pages/inventorymanagement/productlist/components/addproductlistform.tsx", "../../src/pages/inventorymanagement/productlist/components/productmultiselect.tsx", "../../src/pages/inventorymanagement/productlist/components/productlistinterface.ts", "../../src/pages/inventorymanagement/subcategorylist/subcategory.tsx", "../../src/pages/inventorymanagement/subcategorylist/components/addsubcategories.tsx", "../../src/pages/labdashboard/index.ts", "../../src/pages/labdashboard/labbilling/duelist.tsx", "../../src/pages/labdashboard/labbilling/invoicelist.tsx", "../../src/pages/labdashboard/labbilling/paymentlabmodal.tsx", "../../src/pages/labdashboard/labbilling/paymenttestlist.tsx", "../../src/pages/labdashboard/labbilling/components/invoicedetails.tsx", "../../src/pages/labdashboard/labdepartment/labdepartment.tsx", "../../src/pages/labdashboard/labdepartment/labsubdepartment.tsx", "../../src/pages/labdashboard/labinventory/labinventory.tsx", "../../src/pages/labdashboard/labinventory/components/inventorydetails.tsx", "../../src/pages/labdashboard/labinventory/components/productdetails.tsx", "../../src/pages/labdashboard/labpatient/labpatientlist.tsx", "../../src/pages/labdashboard/labproductlist/labproductlist.tsx", "../../src/pages/labdashboard/labproductlist/components/addproduct.tsx", "../../src/pages/labdashboard/labpurchase/labpurchase.tsx", "../../src/pages/labdashboard/labpurchase/components/purchasedetails.tsx", "../../src/pages/labdashboard/labtestconfig/parameterconfig.tsx", "../../src/pages/labdashboard/labtestconfig/testtypeconfig.tsx", "../../src/pages/labdashboard/labtestconfig/components/addparameter.tsx", "../../src/pages/labdashboard/labtestconfig/components/addtesttype.tsx", "../../src/pages/labdashboard/labtestconfig/components/testparameters.tsx", "../../src/pages/labdashboard/labtestresult/labtestresult.tsx", "../../src/pages/labdashboard/labtestresult/components/resultfilter.tsx", "../../src/pages/labdashboard/samplecollection/samplecollection.tsx", "../../src/pages/labdashboard/testrequestlist/tablebodycopy.tsx", "../../src/pages/labdashboard/testrequestlist/testrequestlist.tsx", "../../src/pages/labdashboard/testrequestlist/components/addlabtestrequest.tsx", "../../src/pages/labdashboard/testrequestlist/components/editreport.tsx", "../../src/pages/labdashboard/testrequestlist/components/mastertablecopy.tsx", "../../src/pages/labdashboard/testrequestlist/components/tableheadcopy.tsx", "../../src/pages/labdashboard/testrequestlist/components/testlist.tsx", "../../src/pages/labdashboard/testrequestlist/components/testlistform.tsx", "../../src/pages/labdashboard/components/editsamplereport.tsx", "../../src/pages/labdashboard/components/formfield.tsx", "../../src/pages/labdashboard/components/headingpopup.tsx", "../../src/pages/labdashboard/components/labreport.tsx", "../../src/pages/labdashboard/components/popup-modal.tsx", "../../src/pages/labdashboard/components/status.tsx", "../../src/pages/labdashboard/components/statussize.tsx", "../../src/pages/odp/index.ts", "../../src/pages/odp/appointment/appointmentpage.tsx", "../../src/pages/odp/appointment/opdappointmentpage.tsx", "../../src/pages/odp/appointment/paitentcard.tsx", "../../src/pages/odp/appointment/components/addappointmentpage.tsx", "../../src/pages/odp/appointment/components/appointmentdetailsstep.tsx", "../../src/pages/odp/appointment/components/appointmentsummarycards.tsx", "../../src/pages/odp/appointment/components/doctorselectionstep.tsx", "../../src/pages/odp/appointment/components/newpaitentform.tsx", "../../src/pages/odp/appointment/components/opdappointmentform.tsx", "../../src/pages/odp/appointment/components/oldpaitentform.tsx", "../../src/pages/odp/appointment/components/patientinformationstep.tsx", "../../src/pages/odp/appointment/components/paymentdetailsstep.tsx", "../../src/pages/odp/appointment/components/stepcards.tsx", "../../src/pages/odp/appointment/components/steppernavigation.tsx", "../../src/pages/odp/appointment/components/todayappointmentlist.tsx", "../../src/pages/odp/appointment/components/viewappointment.tsx", "../../src/pages/odp/appointment/components/appointmentformvalidation.ts", "../../src/pages/odp/cardiology/cardiologypage.tsx", "../../src/pages/odp/cardiology/components/addcardiologypage.tsx", "../../src/pages/odp/dentalcare/dentalcarepage.tsx", "../../src/pages/odp/dentalcare/components/adddentalcarepage.tsx", "../../src/pages/odp/dermatology/dermatologypage.tsx", "../../src/pages/odp/dermatology/components/adddermotologypage.tsx", "../../src/pages/odp/doctors/doctors.tsx", "../../src/pages/odp/doctors/components/adddoctorappointmentform.tsx", "../../src/pages/odp/doctors/components/doctorcard.tsx", "../../src/pages/odp/doctors/components/loadingcard.tsx", "../../src/pages/odp/doctors/components/newpatientform.tsx", "../../src/pages/odp/doctors/components/oldpatientform.tsx", "../../src/pages/odp/doctors/components/statusdoctorcard.tsx", "../../src/pages/odp/ent/entdepartmentpage.tsx", "../../src/pages/odp/ent/components/addentpage.tsx", "../../src/pages/odp/generalcheckup/generalcheckuppage.tsx", "../../src/pages/odp/generalcheckup/components/addgeneralcheckupform.tsx", "../../src/pages/odp/generalcheckup/components/addgeneralprecheckupform.tsx", "../../src/pages/odp/generalcheckup/components/advicesection.tsx", "../../src/pages/odp/generalcheckup/components/allergiessection.tsx", "../../src/pages/odp/generalcheckup/components/chronicdiseasesection.tsx", "../../src/pages/odp/generalcheckup/components/currentlymedicationsection.tsx", "../../src/pages/odp/generalcheckup/components/flowoffsection.tsx", "../../src/pages/odp/generalcheckup/components/formikcreatablemultiselect.tsx", "../../src/pages/odp/generalcheckup/components/generalcheckupsummarycards.tsx", "../../src/pages/odp/generalcheckup/components/medicalhistorysection.tsx", "../../src/pages/odp/generalcheckup/components/referralsection.tsx", "../../src/pages/odp/generalcheckup/components/statusconfirm.tsx", "../../src/pages/odp/generalcheckup/components/symptomssection.tsx", "../../src/pages/odp/generalcheckup/components/testlaboratorysection.tsx", "../../src/pages/odp/generalcheckup/components/testradiologysection.tsx", "../../src/pages/odp/generalcheckup/components/viewpatientdetails/patientviewsdetails.tsx", "../../src/pages/odp/generalcheckup/components/viewpatientdetails/printsection.tsx", "../../src/pages/odp/generalphysics/generalphysicianpage.tsx", "../../src/pages/odp/generalphysics/components/addgeneralphysicspage.tsx", "../../src/pages/odp/gynecology/gynecologypage.tsx", "../../src/pages/odp/gynecology/components/addgynecologypage.tsx", "../../src/pages/odp/neurology/neurologypage.tsx", "../../src/pages/odp/neurology/components/addneurologypage.tsx", "../../src/pages/odp/orthomology/orthomologypage.tsx", "../../src/pages/odp/orthomology/components/addorthomologypage.tsx", "../../src/pages/odp/orthopedics/orthopedicspage.tsx", "../../src/pages/odp/orthopedics/components/addorthopedicspage.tsx", "../../src/pages/odp/pediatrics/pediatricspage.tsx", "../../src/pages/odp/pediatrics/components/addorthopedicspage.tsx", "../../src/pages/operationtheater/index.ts", "../../src/pages/operationtheater/general/generalot.tsx", "../../src/pages/operationtheater/otconfiguration/operation.tsx", "../../src/pages/operationtheater/otconfiguration/otconfiguration.tsx", "../../src/pages/operationtheater/otconfiguration/components/addoperation.tsx", "../../src/pages/operationtheater/otconfiguration/components/addsurgery.tsx", "../../src/pages/operationtheater/otconfiguration/components/ot.tsx", "../../src/pages/operationtheater/otconfiguration/components/otmodal.tsx", "../../src/pages/operationtheater/otconfiguration/components/surgery.tsx", "../../src/pages/operationtheater/otconfiguration/components/surgerydepartment.tsx", "../../src/pages/operationtheater/otconfiguration/components/surgerysubdepatment.tsx", "../../src/pages/operationtheater/otconfiguration/components/ot.obj.ts", "../../src/pages/operationtheater/pre-operativeform/pof.tsx", "../../src/pages/operationtheater/components/editpage.tsx", "../../src/pages/operationtheater/components/headertable.tsx", "../../src/pages/operationtheater/components/operationlayout.tsx", "../../src/pages/operationtheater/components/patientlayout.tsx", "../../src/pages/operationtheater/components/reschedule.tsx", "../../src/pages/operationtheater/components/canceloperation.tsx", "../../src/pages/operationtheater/components/reschedulecomponent/rescheduleform.tsx", "../../src/pages/operationtheater/components/detailcomponents/aditionalinformationdetail.tsx", "../../src/pages/operationtheater/components/detailcomponents/emergency.tsx", "../../src/pages/operationtheater/components/detailcomponents/moreinformation.tsx", "../../src/pages/operationtheater/components/detailcomponents/patientcard.tsx", "../../src/pages/operationtheater/components/detailcomponents/patientgeneral.tsx", "../../src/pages/operationtheater/components/detailcomponents/previoussurgery.tsx", "../../src/pages/operationtheater/surgery availability/availability.tsx", "../../src/pages/operationtheater/surgery availability/component/availabilitydetail.tsx", "../../src/pages/operationtheater/surgery availability/component/imageviewer.tsx", "../../src/pages/operationtheater/surgery availability/component/profilecard.tsx", "../../src/pages/operationtheater/surgery availability/component/profilegeneral.tsx", "../../src/pages/operationtheater/surgery availability/component/profilegeneralinfo.tsx", "../../src/pages/patient/index.ts", "../../src/pages/patient/patient-details/patientdetails.tsx", "../../src/pages/patient/patient-details/patientdetailsa.tsx", "../../src/pages/patient/patient-details/components/billinginformation.tsx", "../../src/pages/patient/patient-details/components/buttonlist.tsx", "../../src/pages/patient/patient-details/components/customtabs.tsx", "../../src/pages/patient/patient-details/components/detailsreceipt.tsx", "../../src/pages/patient/patient-details/components/generaldataobj.ts", "../../src/pages/patient/patient-details/components/generalinfocard.tsx", "../../src/pages/patient/patient-details/components/header.tsx", "../../src/pages/patient/patient-details/components/hospitalcard.tsx", "../../src/pages/patient/patient-details/components/infocard.tsx", "../../src/pages/patient/patient-details/components/labdetails.tsx", "../../src/pages/patient/patient-details/components/patientcard.tsx", "../../src/pages/patient/patient-details/components/patientdetailsmodal.tsx", "../../src/pages/patient/patient-details/components/patientdiet.tsx", "../../src/pages/patient/patient-details/components/patientfile.tsx", "../../src/pages/patient/patient-details/components/patientgeneral.tsx", "../../src/pages/patient/patient-details/components/patienthistory.tsx", "../../src/pages/patient/patient-details/components/paymentmodal.tsx", "../../src/pages/patient/patient-details/components/pharmacydetails.tsx", "../../src/pages/patient/patient-details/components/sampledata.ts", "../../src/pages/patient/patient-details/components/verticaltabs.tsx", "../../src/pages/patient/patient-details/components/warddetails.tsx", "../../src/pages/patient/patient-details/components/patientobj.ts", "../../src/pages/patient/patientlist/inpatientlist.tsx", "../../src/pages/patient/patientlist/patientlist.tsx", "../../src/pages/patient/patientlist/components/pdfheader.tsx", "../../src/pages/patient/patientlist/components/sampledata.ts", "../../src/pages/payroll/payrollconfig.tsx", "../../src/pages/payroll/payrolllist.tsx", "../../src/pages/payroll/index.ts", "../../src/pages/payroll/sampledata/sampledataallowance.ts", "../../src/pages/payroll/sampledata/samplepayrolltabledata.ts", "../../src/pages/payroll/components/addallowance.tsx", "../../src/pages/payroll/components/adddeduction.tsx", "../../src/pages/payroll/components/addpayroll.tsx", "../../src/pages/payroll/components/generateallpayroll.components.tsx", "../../src/pages/payroll/components/pay.component.tsx", "../../src/pages/payroll/components/payrollheader.tsx", "../../src/pages/payroll/components/payrolllistheader.tsx", "../../src/pages/payroll/components/printdata.tsx", "../../src/pages/payroll/components/viewpayrollcard.tsx", "../../src/pages/payroll/components/payrollinterfaces.ts", "../../src/pages/pharmacy/add_prescriptions.tsx", "../../src/pages/pharmacy/prescriptions.tsx", "../../src/pages/pharmacy/index.ts", "../../src/pages/pharmacy/finance/expense/expense.page.tsx", "../../src/pages/pharmacy/finance/expense/components/addexpense.tsx", "../../src/pages/pharmacy/finance/expense/components/addexpensecategory.component.tsx", "../../src/pages/pharmacy/finance/expense/components/expensecategorytable.tsx", "../../src/pages/pharmacy/finance/expense/components/expensetable.tsx", "../../src/pages/pharmacy/finance/reports/reports.page.tsx", "../../src/pages/pharmacy/finance/reports/components/reportbill.tsx", "../../src/pages/pharmacy/finance/reports/components/reporttable.component.tsx", "../../src/pages/pharmacy/finance/sales/sales.page.tsx", "../../src/pages/pharmacy/finance/sales/components/sale-return-table.component.tsx", "../../src/pages/pharmacy/finance/sales/components/sales-bill.component.tsx", "../../src/pages/pharmacy/finance/sales/components/sales-return.component.tsx", "../../src/pages/pharmacy/finance/sales/components/sales.component.tsx", "../../src/pages/pharmacy/finance/sales/components/salesreturndetails.component.tsx", "../../src/pages/pharmacy/medicine-request/medicinerequest.page.tsx", "../../src/pages/pharmacy/medicine-request/[id]/medicinerequestdetails.page.tsx", "../../src/pages/pharmacy/medicine-request/[id]/components/customerinformation.tsx", "../../src/pages/pharmacy/medicine-request/[id]/components/medicinerecipet.tsx", "../../src/pages/pharmacy/medicine-request/[id]/components/newpharmacybill.tsx", "../../src/pages/pharmacy/inventory/pharmacyinventory.page.tsx", "../../src/pages/pharmacy/inventory/pharmacyproductlist.page.tsx", "../../src/pages/pharmacy/inventory/components/addpharmacyproduct.component.tsx", "../../src/pages/pharmacy/inventory/components/beautyinformation.tsx", "../../src/pages/pharmacy/inventory/components/druginformation.tsx", "../../src/pages/pharmacy/inventory/components/inventorydetails.tsx", "../../src/pages/pharmacy/inventory/components/inventoryproductdetails.tsx", "../../src/pages/pharmacy/pos/pharmacypos.page.tsx", "../../src/pages/pharmacy/pos/pos.page.tsx", "../../src/pages/pharmacy/pos/components/cart.component.tsx", "../../src/pages/pharmacy/pos/components/medicincard.component.tsx", "../../src/pages/pharmacy/pos/components/medicinelist.component.tsx", "../../src/pages/pharmacy/pos/components/medicinelist.tsx", "../../src/pages/pharmacy/pos/components/payment.tsx", "../../src/pages/pharmacy/pos/components/tabledata.tsx", "../../src/pages/pharmacy/pos/components/transactionheld.component.tsx", "../../src/pages/pharmacy/pos/components/transactionholddialog.component.tsx", "../../src/pages/pharmacy/pos/utils/findnearestbatch.utils.ts", "../../src/pages/pharmacy/types/formdata.types.ts", "../../src/pages/pharmacy/types/formdropdowndata.types.ts", "../../src/pages/pharmacy/types/medcinecart.types.ts", "../../src/pages/pharmacy/types/pharmacypos.types.ts", "../../src/pages/pharmacy/types/pharmacyproducttabledata.tsx", "../../src/pages/pharmacy/types/tabdata.types.ts", "../../src/pages/pharmacy/types/useroptions.types.ts", "../../src/pages/pricingconfig/index..ts", "../../src/pages/pricingconfig/categorylist/category.tsx", "../../src/pages/pricingconfig/categorylist/components/addpricingcategory.tsx", "../../src/pages/pricingconfig/categorylist/components/categoryaddschema.ts", "../../src/pages/pricingconfig/serviceitem/serviceitemlist.tsx", "../../src/pages/pricingconfig/serviceitem/components/addservice.tsx", "../../src/pages/pricingconfig/serviceitem/components/serviceschema.ts", "../../src/pages/profile/components/password.tsx", "../../src/pages/profile/components/profileindex.tsx", "../../src/pages/profile/components/profile.obj.ts", "../../src/pages/purchase/index.ts", "../../src/pages/purchasemanagement/index.ts", "../../src/pages/purchasemanagement/purchaselist/purchaselist.tsx", "../../src/pages/purchasemanagement/purchaselist/components/addnewpurchase.tsx", "../../src/pages/purchasemanagement/purchaselist/components/details.tsx", "../../src/pages/purchasemanagement/purchaselist/components/purchasedetails.tsx", "../../src/pages/purchasemanagement/purchaselist/components/purchasereturnform.tsx", "../../src/pages/purchasemanagement/purchaseorder/purchaseorder.tsx", "../../src/pages/purchasemanagement/purchaseorder/purchaseobj.ts", "../../src/pages/purchasemanagement/purchaseorder/components/basicinformation.tsx", "../../src/pages/purchasemanagement/purchaseorder/components/editorder.tsx", "../../src/pages/purchasemanagement/purchaseorder/components/newpurchaseorder.tsx", "../../src/pages/purchasemanagement/purchaseorder/components/productdetails.tsx", "../../src/pages/purchasemanagement/purchaseorder/components/productdetailsform.tsx", "../../src/pages/purchasemanagement/purchaseorder/components/producttable.tsx", "../../src/pages/purchasemanagement/purchaseorder/components/reusablehooks.tsx", "../../src/pages/purchasemanagement/purchasereturn/purchasereturn.tsx", "../../src/pages/purchasemanagement/purchasereturn/components/addpurchase.tsx", "../../src/pages/purchasemanagement/vendorbilling/vendorbilling.page.tsx", "../../src/pages/purchasemanagement/vendorlist/vendorlistpage.tsx", "../../src/pages/purchasemanagement/vendorlist/vendorobj.ts", "../../src/pages/purchasemanagement/vendorlist/components/addvendor.tsx", "../../src/pages/purchasemanagement/vendorlist/components/editvendor.tsx", "../../src/pages/purchasemanagement/vendorlist/components/invoicescard.tsx", "../../src/pages/purchasemanagement/vendorlist/components/vdetails.tsx", "../../src/pages/purchasemanagement/vendorlist/components/vfinancial.tsx", "../../src/pages/purchasemanagement/vendorlist/components/vendorpayment.tsx", "../../src/pages/purchasemanagement/vendorlist/components/vendorproducts.component.tsx", "../../src/pages/purchasemanagement/vendorlist/components/viewvendor.tsx", "../../src/pages/purchasemanagement/vendororder/vendororder.page.tsx", "../../src/pages/purchasemanagement/vendororder/components/orderdetails.component.tsx", "../../src/pages/purchasemanagement/vendororder/components/vendororderalert.component.tsx", "../../src/pages/purchasemanagement/vendororder/components/vendororderlist.component.tsx", "../../src/pages/purchasemanagement/vendorproductlist/vendorproductlist.page.tsx", "../../src/pages/radiology/index.ts", "../../src/pages/radiology/billingandconfig/invoicedetails.tsx", "../../src/pages/radiology/billingandconfig/paymentradmodal.tsx", "../../src/pages/radiology/billingandconfig/paymentradtestlist.tsx", "../../src/pages/radiology/billingandconfig/radiologyinvoicedetails.tsx", "../../src/pages/radiology/billingandconfig/radiologyinvoicelist.tsx", "../../src/pages/radiology/billingandconfig/duellist/radiologyduelist.tsx", "../../src/pages/radiology/purchase/radiologyinvoicedetails.tsx", "../../src/pages/radiology/purchase/radiologypurchase.tsx", "../../src/pages/radiology/radiologyinventory/inventory/inventorydetails.tsx", "../../src/pages/radiology/radiologyinventory/inventory/radiologyinventory.tsx", "../../src/pages/radiology/radiologyinventory/inventory/radiologyinventoryproductdetails.tsx", "../../src/pages/radiology/radiologyinventory/productlist/addproduct.tsx", "../../src/pages/radiology/radiologyinventory/productlist/radiologyproductlist.tsx", "../../src/pages/radiology/report/radiologyreport.tsx", "../../src/pages/radiology/report/components/editsamplereport.tsx", "../../src/pages/radiology/report/components/formfield.tsx", "../../src/pages/radiology/report/components/headingpopup.tsx", "../../src/pages/radiology/report/components/labreport.tsx", "../../src/pages/radiology/report/components/popup-modal.tsx", "../../src/pages/radiology/report/components/status.tsx", "../../src/pages/radiology/report/components/statussize.tsx", "../../src/pages/radiology/testconfig/department/radiologydepartment.tsx", "../../src/pages/radiology/testconfig/servicetype/servicetype.tsx", "../../src/pages/radiology/testconfig/servicetype/testlistconfig.tsx", "../../src/pages/radiology/testconfig/components/addparameter.tsx", "../../src/pages/radiology/testconfig/components/addtesttype.tsx", "../../src/pages/radiology/testconfig/components/testparameters.tsx", "../../src/pages/radiology/testrequest/radiologytestrequest.tsx", "../../src/pages/radiology/testrequest/components/addradiologytestrequest.tsx", "../../src/pages/radiology/testrequest/components/mastertablecopy.tsx", "../../src/pages/radiology/testrequest/components/radiologyeditreport.tsx", "../../src/pages/radiology/testrequest/components/tableheadcopy.tsx", "../../src/pages/radiology/testrequest/components/testrlist.tsx", "../../src/pages/radiology/testrequest/components/testrlistform.tsx", "../../src/pages/settings/index.ts", "../../src/pages/settings/commissionmgt/commissionconfig/addcommission/addcommission.tsx", "../../src/pages/settings/commissionmgt/commissionconfig/addcommission/components/bankreseller.tsx", "../../src/pages/settings/commissionmgt/commissionconfig/addcommission/components/reseller.tsx", "../../src/pages/settings/commissionmgt/commissionconfig/addcommission/components/resellerdetailedit.tsx", "../../src/pages/settings/commissionmgt/commissionconfig/addcommission/components/types.ts", "../../src/pages/settings/commissionmgt/commissionconfig/commissionlist/commissionlist.tsx", "../../src/pages/settings/commissionmgt/commissionconfig/commissionlist/detailcommission/bankdetail.tsx", "../../src/pages/settings/commissionmgt/commissionconfig/commissionlist/detailcommission/resellercard.tsx", "../../src/pages/settings/commissionmgt/commissionconfig/commissionlist/detailcommission/resellergeneral.tsx", "../../src/pages/settings/commissionmgt/commissionconfig/commissionlist/detailcommission/viewdetailsreseller.tsx", "../../src/pages/settings/commissionmgt/commissionconfig/commissionlist/detailcommission/resellercomission.tsx/addresellerdetail.tsx", "../../src/pages/settings/commissionmgt/commissionconfig/commissionlist/detailcommission/resellercomission.tsx/resellerdetaillist.tsx", "../../src/pages/settings/commissionmgt/reseller/type & service/typeservice.tsx", "../../src/pages/settings/commissionmgt/reseller/type & service/component/addservice.tsx", "../../src/pages/settings/commissionmgt/reseller/type & service/component/addtype.tsx", "../../src/pages/settings/commissionmgt/reseller/type & service/component/service.tsx", "../../src/pages/settings/commissionmgt/reseller/type & service/component/typelist.tsx", "../../src/pages/settings/departmentconfig/addalldepartment.tsx", "../../src/pages/settings/departmentconfig/departmentconfigpage.tsx", "../../src/pages/settings/departmentconfig/components/addcategory.tsx", "../../src/pages/settings/departmentconfig/components/adddepartment.tsx", "../../src/pages/settings/departmentconfig/components/addspecialization.tsx", "../../src/pages/settings/departmentconfig/components/department.component.tsx", "../../src/pages/settings/departmentconfig/components/diagnostics.tsx", "../../src/pages/settings/departmentconfig/components/emergency.tsx", "../../src/pages/settings/departmentconfig/components/ipd.tsx", "../../src/pages/settings/departmentconfig/components/opd.tsx", "../../src/pages/settings/departmentconfig/components/specialist.tsx", "../../src/pages/settings/departmentconfig/components/subdepartment.component.tsx", "../../src/pages/settings/notificationsetup/notificationsetup.tsx", "../../src/pages/settings/notificationsetup/addnotification/addnotification.tsx", "../../src/pages/settings/notificationsetup/addnotification/components/multiselect.tsx", "../../src/pages/settings/report/report.tsx", "../../src/pages/settings/rolemanagement/rolemanagementindex.tsx", "../../src/pages/settings/rolemanagement/components/permissiontable.tsx", "../../src/pages/settings/rolemanagement/components/rolemanagementform.tsx", "../../src/pages/settings/usermanagement/usermanagement.tsx", "../../src/pages/settings/usermanagement/adduser/adduser.tsx", "../../src/pages/settings/usermanagement/adduser/components/multiselect.tsx", "../../src/pages/settings/usermanagement/components/sampledata.ts", "../../src/pages/settings/ward-config/page.tsx", "../../src/pages/settings/ward-config/components/addward.tsx", "../../src/pages/staff/index.ts", "../../src/pages/staff/addstaff/addstaff.tsx", "../../src/pages/staff/addstaff/components/employmentdetails.tsx", "../../src/pages/staff/addstaff/components/imageupload.tsx", "../../src/pages/staff/addstaff/components/multiselect.tsx", "../../src/pages/staff/addstaff/components/paymentdetails.tsx", "../../src/pages/staff/addstaff/components/personalinformation.tsx", "../../src/pages/staff/addstaff/components/qualificationdetails.tsx", "../../src/pages/staff/addstaff/components/stafftype.ts", "../../src/pages/staff/addstaff/components/index.ts", "../../src/pages/staff/addstaff/components/validationschema.ts", "../../src/pages/staff/staffdetails/staffdetails.tsx", "../../src/pages/staff/staffdetails/components/horizontalcalender.tsx", "../../src/pages/staff/staffdetails/components/staffbank.tsx", "../../src/pages/staff/staffdetails/components/staffcard.tsx", "../../src/pages/staff/staffdetails/components/stafffile.tsx", "../../src/pages/staff/staffdetails/components/staffgeneral.tsx", "../../src/pages/staff/staffdetails/components/staffgeneralinfo.tsx", "../../src/pages/staff/staffdetails/components/workexperience.tsx", "../../src/pages/staff/stafflist/stafflist.tsx", "../../src/pages/staff/stafflist/components/sampledata.ts", "../../src/pages/tokenmanagement/index.ts", "../../src/pages/tokenmanagement/account/accounttokenlist.tsx", "../../src/pages/tokenmanagement/account/components/accounttokendetails.tsx", "../../src/pages/tokenmanagement/account/components/viewtokenlist.tsx", "../../src/pages/tokenmanagement/appointment/appointmenttokenlist.tsx", "../../src/pages/tokenmanagement/appointment/tokenconfig.tsx", "../../src/pages/tokenmanagement/appointment/components/appointmenttokendetails.tsx", "../../src/pages/vendor/index.ts", "../../src/pages/canteen/index.ts", "../../src/pages/canteen/assigndietplan/page.tsx", "../../src/pages/canteen/assigndietplan/components/adddiet.tsx", "../../src/pages/canteen/assigndietplan/mock/patient-diet.ts", "../../src/pages/canteen/dietplan/page.tsx", "../../src/pages/canteen/dietplan/components/adddietplan.tsx", "../../src/pages/canteen/dietplan/mock/dietplan.ts", "../../src/pages/canteen/dietplan/types/diet.types.ts", "../../src/pages/canteen/stocklist/stocklist.tsx", "../../src/pages/canteen/inventory/product/product-list.page.tsx", "../../src/pages/canteen/inventory/product/components/addcanteenproduct.tsx", "../../src/pages/canteen/inventory/product/types/canteenproductcategory.tsx", "../../src/pages/errorhandler/errorhandler.tsx", "../../src/pages/laboratory/index.ts", "../../src/pages/laboratory/testrequest/page.tsx", "../../src/pages/laboratory/testrequest/components/addtestrequest.tsx", "../../src/pages/laboratory/testrequest/mock/testrequest.ts", "../../src/pages/laboratory/testresults/page.tsx", "../../src/pages/laboratory/testresults/components/addtestresult.tsx", "../../src/pages/laboratory/testresults/mock/testresult.ts", "../../src/pages/people/index.ts", "../../src/pages/people/medical-staff/doctor/index.ts", "../../src/pages/people/medical-staff/doctor/adddoctor/adddoctor.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/components/contactinformation.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/components/doctortype.ts", "../../src/pages/people/medical-staff/doctor/adddoctor/components/employmentdetails.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/components/extradocuments.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/components/imageupload.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/components/initialvalues.ts", "../../src/pages/people/medical-staff/doctor/adddoctor/components/multiselect.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/components/paymentdetails.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/components/personalinformation.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/components/professionalinformation.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/components/workexperience.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/components/index.ts", "../../src/pages/people/medical-staff/doctor/adddoctor/components/validationschema.ts", "../../src/pages/people/medical-staff/doctor/adddoctor/helper/isuserrole.ts", "../../src/pages/people/medical-staff/doctor/adddoctor/hooks/formsection.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/hooks/usedoctorform.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/hooks/usemutation.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/hooks/usevalidationscroller.tsx", "../../src/pages/people/medical-staff/doctor/adddoctor/types/doctor_nurse.types.ts", "../../src/pages/people/medical-staff/doctor/doctoravailability/doctoravailability.tsx", "../../src/pages/people/medical-staff/doctor/doctoravailability/index.ts", "../../src/pages/people/medical-staff/doctor/doctorlist/doctorlist.tsx", "../../src/pages/people/medical-staff/doctor/doctorreviews/doctorreviews.tsx", "../../src/pages/people/medical-staff/doctor/doctorreviews/components/deletereview.tsx", "../../src/pages/people/medical-staff/doctor/doctorreviews/components/doctordetails.tsx", "../../src/pages/people/medical-staff/doctor/doctorreviews/components/review.tsx", "../../src/pages/people/medical-staff/doctor/doctorreviews/components/reviewdetails.tsx", "../../src/pages/people/medical-staff/doctor/doctorreviews/components/sampledata.ts", "../../src/pages/people/medical-staff/doctor/components/horizontalcalender.tsx", "../../src/pages/people/medical-staff/doctor/components/infocard.tsx", "../../src/pages/people/medical-staff/lab-technicain/labtechnicianlist.page.tsx", "../../src/pages/people/medical-staff/nurse/nurselist.tsx", "../../src/pages/people/medical-staff/pharmacists/pharamacists-list.page.tsx", "../../src/pages/people/medical-staff/[id]/staff-details.page.tsx", "../../src/pages/people/medical-staff/[id]/doctordetails/doctordetailspage.tsx", "../../src/pages/people/medical-staff/[id]/doctordetails/components/appointments.tsx", "../../src/pages/people/medical-staff/[id]/doctordetails/components/doctorheader.tsx", "../../src/pages/people/medical-staff/[id]/doctordetails/components/monthlyschedule.tsx", "../../src/pages/people/medical-staff/[id]/nursedetails/nursedetailspage.tsx", "../../src/pages/people/medical-staff/[id]/components/aboutsection.tsx", "../../src/pages/people/medical-staff/[id]/components/bankdetails.tsx", "../../src/pages/people/medical-staff/[id]/components/contactinfo.tsx", "../../src/pages/people/medical-staff/[id]/components/documents.tsx", "../../src/pages/people/medical-staff/[id]/components/imageviewer.tsx", "../../src/pages/people/medical-staff/[id]/components/monthlyschedule.tsx", "../../src/pages/people/medical-staff/[id]/components/patientreviews.tsx", "../../src/pages/people/medical-staff/[id]/components/personalprofessionaldetails.tsx", "../../src/pages/people/medical-staff/[id]/components/staffheader.tsx", "../../src/pages/people/medical-staff/[id]/components/index.ts", "../../src/pages/people/medical-staff/components/feedback.tsx", "../../src/pages/people/medical-staff/components/staff-bank.tsx", "../../src/pages/people/medical-staff/components/staff-card.tsx", "../../src/pages/people/medical-staff/components/staff-document.tsx", "../../src/pages/people/medical-staff/components/staff-file.tsx", "../../src/pages/people/medical-staff/components/staff-general-info.tsx", "../../src/pages/people/medical-staff/components/staff-general.tsx", "../../src/pages/people/medical-staff/components/staff-work-experience.tsx", "../../src/pages/people/medical-staff/components/index.tsx", "../../src/pages/shift/shiftassign.tsx", "../../src/pages/shift/shiftlist.tsx", "../../src/pages/shift/index.ts", "../../src/pages/shift/components/addshift.tsx", "../../src/pages/shift/components/assignshift.tsx", "../../src/pages/shift/components/sampledatashift.ts", "../../src/pages/shift/daily-shift/page.tsx", "../../src/pages/unauthorized/unauthorized.page.tsx", "../../src/routes/frontendroutes.ts", "../../src/routes/protectedlayout.tsx", "../../src/routes/protectedloginpage.tsx", "../../src/routes/routes-config-optimized.tsx", "../../src/routes/routes-config.tsx", "../../src/routes/sidebar-routes-config.tsx", "../../src/routes/implementation-example.tsx", "../../src/routes/index.ts", "../../src/routes/route-config.ts", "../../src/server-action/api/donorapi.ts", "../../src/server-action/api/donorinventryapi.ts", "../../src/server-action/api/otassignment.ts", "../../src/server-action/api/radiologybill.ts", "../../src/server-action/api/testconfigapi.ts", "../../src/server-action/api/advancedpayment.api.ts", "../../src/server-action/api/allusers.ts", "../../src/server-action/api/allergy.api.ts", "../../src/server-action/api/ambulanceapi.ts", "../../src/server-action/api/analytics.api.ts", "../../src/server-action/api/appointmentapi.ts", "../../src/server-action/api/attendance.api.ts", "../../src/server-action/api/auth.ts", "../../src/server-action/api/balancesheetapi.ts", "../../src/server-action/api/bank.api.ts", "../../src/server-action/api/bedassing.ts", "../../src/server-action/api/canteen-inventory.ts", "../../src/server-action/api/canteen-product.api.ts", "../../src/server-action/api/categoryapi.ts", "../../src/server-action/api/certificate.api.ts", "../../src/server-action/api/chronicdisease.api.ts", "../../src/server-action/api/dasboard.api.ts", "../../src/server-action/api/department-category.api.ts", "../../src/server-action/api/department-subcat.api.ts", "../../src/server-action/api/departmentapi.ts", "../../src/server-action/api/donationandequipment.api.ts", "../../src/server-action/api/enhanced-paymentvoucher.api.ts", "../../src/server-action/api/equipmentinventory.ts", "../../src/server-action/api/equipmentsapi.ts", "../../src/server-action/api/expense.api.ts", "../../src/server-action/api/financebankapi.ts", "../../src/server-action/api/financetransaction.ts", "../../src/server-action/api/financialopsapi.ts", "../../src/server-action/api/hospitalapi.ts", "../../src/server-action/api/hospitalproduct.ts", "../../src/server-action/api/incomestatement.ts", "../../src/server-action/api/inventories.api.ts", "../../src/server-action/api/inventoryapi.ts", "../../src/server-action/api/labdepartment.ts", "../../src/server-action/api/labreportbill.ts", "../../src/server-action/api/labreports.ts", "../../src/server-action/api/masterinvoice.api.ts", "../../src/server-action/api/medicalproduct.api.ts", "../../src/server-action/api/notification.api.ts", "../../src/server-action/api/operation.api.ts", "../../src/server-action/api/operationcosting.ts", "../../src/server-action/api/operationtreature.ts", "../../src/server-action/api/patienthistory.api.ts", "../../src/server-action/api/paymentvoucher.api.ts", "../../src/server-action/api/payrollapi.ts", "../../src/server-action/api/permission.api.ts", "../../src/server-action/api/pharmacy-inventory.api.ts", "../../src/server-action/api/pharmacy-product.api.ts", "../../src/server-action/api/reseller.api.ts", "../../src/server-action/api/serviceapi.ts", "../../src/server-action/api/shiftapi.ts", "../../src/server-action/api/shiftassignapi.ts", "../../src/server-action/api/shiftlist.ts", "../../src/server-action/api/symptoms.api.ts", "../../src/server-action/api/tokenapi.ts", "../../src/server-action/api/tokenconfigapi.ts", "../../src/server-action/api/transaction-adjustment.api.ts", "../../src/server-action/api/transaction.api.ts", "../../src/server-action/api/user.ts", "../../src/server-action/api/ward.api.ts", "../../src/server-action/api/services/roomapi.ts", "../../src/server-action/api/website/application.api.ts", "../../src/server-action/api/website/blog.ts", "../../src/server-action/api/website/bod.api.ts", "../../src/server-action/api/website/campaign.ts", "../../src/server-action/api/website/gallery.ts", "../../src/server-action/api/website/job.api.ts", "../../src/server-action/api/website/training.ts", "../../src/server-action/config/api-config.ts", "../../src/server-action/config/enhanced-api-config.ts", "../../src/server-action/config/firebaseconfig.ts", "../../src/server-action/config/nextapi.config.ts", "../../src/server-action/config/notification-service.tsx", "../../src/server-action/config/service-worker-manager.ts", "../../src/server-action/error-config/errorconfig.tsx", "../../src/server-action/interceptors/api-interceptors.ts", "../../src/server-action/providers/querryclientproviders.tsx", "../../src/server-action/types/financialtranaction.types.ts", "../../src/server-action/types/advancedpayment.types.ts", "../../src/server-action/types/balancesheet.types.ts", "../../src/server-action/types/bank.types.ts", "../../src/server-action/types/certifcate.types.ts", "../../src/server-action/types/dashboard.types.ts", "../../src/server-action/types/expense.types.ts", "../../src/server-action/types/master-invoice.types.ts", "../../src/server-action/types/medical-inventory.types.ts", "../../src/server-action/types/medical-product.type.ts", "../../src/server-action/types/notification.types.ts", "../../src/server-action/types/payroll.types.ts", "../../src/server-action/types/permission.types.ts", "../../src/server-action/types/rooms-beds-wards.types.ts", "../../src/server-action/types/shift.types.ts", "../../src/server-action/types/user.types.ts", "../../src/server-action/utils/apigateway.ts", "../../src/services/mastra.config.ts", "../../src/services/permissionutils.ts", "../../src/services/routepermissions.ts", "../../src/store/bedtransfer.store.ts", "../../src/store/pharmacyinventory.store.ts", "../../src/types/api.types.ts", "../../src/types/html2pdf.d.ts", "../../src/types/routes.types.ts", "../../src/utils/calculateage.ts", "../../src/utils/filtervalue.ts", "../../src/utils/month.ts", "../../src/utils/numbertoword.ts", "../../src/utils/secure.ts", "../../src/utils/cache-strategies.ts", "../../src/utils/enhanced-api-setup.ts", "../../src/utils/export.utils.ts", "../../src/utils/performance-monitor.ts", "../../src/utils/performance-testing.ts", "../../src/utils/routeprocessor.ts", "../../src/utils/voucher.utils.ts", "../../src/website/components/customcard.tsx", "../../src/website/components/dateformater.tsx", "../../src/website/components/deleteconfirmmodal.tsx", "../../src/website/components/formfield.tsx", "../../src/website/components/genericcard.components.tsx", "../../src/website/components/imageupload.tsx", "../../src/website/components/websiteheader.tsx", "../../src/website/layout/websitenavbar.tsx", "../../src/website/layout/websitesidebar.tsx", "../../src/website/layout/websitemenuitems.ts", "../../src/website/pages/blog/blogpageindex.tsx", "../../src/website/pages/blog/components/blogcreateform.tsx", "../../src/website/pages/career/application.page.tsx", "../../src/website/pages/career/career.page.tsx", "../../src/website/pages/career/components/createjob.component.tsx", "../../src/website/pages/career/components/joblist.component.tsx", "../../src/website/pages/career/components/applicant-details.component.tsx", "../../src/website/pages/career/components/applicant-table.component.tsx", "../../src/website/pages/career/components/filter-bar.tsx", "../../src/website/pages/career/components/jobcardconfig.component.tsx", "../../src/website/pages/career/lib/utils.ts", "../../src/website/pages/compaign/compaignpage.tsx", "../../src/website/pages/compaign/components/campaigncard.tsx", "../../src/website/pages/compaign/components/campaigncreateformprops.tsx", "../../src/website/pages/compaign/components/campaignlistcomponent.tsx", "../../src/website/pages/compaign/components/pagination.tsx", "../../src/website/pages/contact/contactpageindex.tsx", "../../src/website/pages/gallery/gallerypageindex.tsx", "../../src/website/pages/gallery/components/gallerieslistcomponent.tsx", "../../src/website/pages/gallery/components/galleryform.tsx", "../../src/website/pages/team-management/team-management.page.tsx", "../../src/website/pages/team-management/components/addteammember.component.tsx", "../../src/website/pages/team-management/components/team-management-config.component.tsx", "../../src/website/pages/team-management/components/team-management-list.component.tsx", "../../src/website/pages/training/trainingindex.tsx", "../../src/website/pages/training/components/trainingform.tsx", "../../src/website/types/application.types.ts", "../../src/website/types/blogs.types.ts", "../../src/website/types/bod.types.ts", "../../src/website/types/campaign.types.ts", "../../src/website/types/jobs.types.ts"], "version": "5.7.3"}