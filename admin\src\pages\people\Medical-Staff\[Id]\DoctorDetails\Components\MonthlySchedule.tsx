import React, { useState, useMemo, useRef, useEffect } from "react";
import { Icon } from "@iconify/react";
import dayjs from "dayjs";

interface ScheduleAppointment {
  id: string;
  time: string;
  appointmentCount: number;
}

interface MonthlyScheduleProps {
  schedule?: {
    currentMonth: string;
    currentYear: number;
    days: any[];
  };
  todaySchedule?: ScheduleAppointment[];
  shiftData?: any;
  doctorId?: string;
  doctorData?: any; // Add doctor data prop to access surgery details
}

const MonthlySchedule: React.FC<MonthlyScheduleProps> = ({
  schedule,
  todaySchedule,
  shiftData,
  doctorId,
  doctorData,
}) => {
  const [activeTab, setActiveTab] = useState<"Today" | "Monthly">("Today");
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [showYearMonthPicker, setShowYearMonthPicker] = useState(false);
  const pickerRef = useRef<HTMLDivElement>(null);

  // Close picker when clicking outside and handle keyboard events
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current &&
        !pickerRef.current.contains(event.target as Node)
      ) {
        setShowYearMonthPicker(false);
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setShowYearMonthPicker(false);
      }
    };

    if (showYearMonthPicker) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [showYearMonthPicker]);

  // Generate year options (from 2000 to current year + 10)
  const yearOptions = useMemo(() => {
    const currentYear = dayjs().year();
    const years = [];
    for (let i = 2000; i <= currentYear + 10; i++) {
      years.push(i);
    }
    return years;
  }, []);

  // Month options
  const monthOptions = [
    { value: 0, label: "January" },
    { value: 1, label: "February" },
    { value: 2, label: "March" },
    { value: 3, label: "April" },
    { value: 4, label: "May" },
    { value: 5, label: "June" },
    { value: 6, label: "July" },
    { value: 7, label: "August" },
    { value: 8, label: "September" },
    { value: 9, label: "October" },
    { value: 10, label: "November" },
    { value: 11, label: "December" },
  ];

  // Handle year/month change
  const handleDateChange = (year: number, month: number) => {
    setSelectedDate(dayjs().year(year).month(month));
    setShowYearMonthPicker(false);
  };

  const processShiftData = useMemo(() => {
    if (
      !doctorData?.surgeryDetails ||
      !Array.isArray(doctorData.surgeryDetails)
    ) {
      return {};
    }

    // Create a map of date -> schedules
    const scheduleMap: { [key: string]: any[] } = {};

    doctorData.surgeryDetails.forEach((surgery: any, index: number) => {
      if (!surgery.date) {
        return;
      }

      const date = dayjs(surgery.date).format("YYYY-MM-DD");

      // Check if schedules exist and have length > 0
      if (
        surgery.schedules &&
        Array.isArray(surgery.schedules) &&
        surgery.schedules.length > 0
      ) {
        // Process each schedule within the surgery
        const processedSchedules = surgery.schedules.map((schedule: any) => ({
          timeFrame: schedule.timeFrame || "Time not specified",
          availabilityStatus: schedule.availabilityStatus || false,
          id: schedule.id || schedule._id,
          startTime: schedule.timeFrame?.split("-")[0]?.trim() || "09:00",
          endTime: schedule.timeFrame?.split("-")[1]?.trim() || "17:00",
        }));

        scheduleMap[date] = processedSchedules;
      } else {
      }
    });

    return scheduleMap;
  }, [doctorData]);

  const todayAppointments: ScheduleAppointment[] = useMemo(() => {
    if (todaySchedule && todaySchedule.length > 0) {
      return todaySchedule;
    }
    const today = dayjs().format("YYYY-MM-DD");
    const todaySchedules = processShiftData[today] || [];

    if (todaySchedules.length === 0) {
      return [
        {
          id: "no-schedules",
          time: "No schedules for today",
          appointmentCount: 0,
        },
      ];
    }

    return todaySchedules.map((schedule: any, index: number) => ({
      id: `schedule-${index}`,
      time: schedule.timeFrame || "Time not specified",
      appointmentCount: schedule.availabilityStatus ? 2 : 0,
    }));
  }, [todaySchedule, processShiftData]);

  // Generate calendar data
  const generateCalendar = useMemo(() => {
    const startOfMonth = selectedDate.startOf("month");
    const endOfMonth = selectedDate.endOf("month");
    const startOfWeek = startOfMonth.startOf("week");
    const endOfWeek = endOfMonth.endOf("week");

    const days = [];
    let current = startOfWeek;

    while (current.isBefore(endOfWeek) || current.isSame(endOfWeek, "day")) {
      const dateStr = current.format("YYYY-MM-DD");
      const hasSchedule =
        processShiftData[dateStr] && processShiftData[dateStr].length > 0;

      days.push({
        date: current,
        day: current.date(),
        isCurrentMonth: current.month() === selectedDate.month(),
        isToday: current.isSame(dayjs(), "day"),
        hasShift: hasSchedule,
        shifts: processShiftData[dateStr] || [],
      });

      current = current.add(1, "day");
    }

    return days;
  }, [selectedDate, processShiftData]);

  const currentDate = new Date();
  const formattedDate = currentDate.toLocaleDateString("en-US", {
    weekday: "short",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="bg-white rounded-md shadow-sm border overflow-hidden w-full">
      {/* Header with blue background */}
      <div className="bg-blue text-white px-4 py-3">
        <div className="flex items-center justify-between">
          <h2 className="text-base font-semibold">
            {activeTab === "Today" ? "Today's Schedule" : "Monthly Schedule"}
          </h2>
          <div className="flex bg-[#E0E0E0] rounded-sm">
            <button
              onClick={() => setActiveTab("Today")}
              className={`px-3 py-1 text-xs rounded-sm font-medium transition-colors ${
                activeTab === "Today" ? "bg-white text-black" : "text-black "
              }`}
            >
              Today
            </button>
            <button
              onClick={() => setActiveTab("Monthly")}
              className={`px-3 py-1 text-xs rounded-sm font-medium transition-colors ${
                activeTab === "Monthly" ? "bg-white text-black" : "text-black "
              }`}
            >
              Monthly
            </button>
          </div>
        </div>
        <p className="text-white/90 text-sm">{formattedDate}</p>
      </div>

      {/* Content */}
      <div className="p-2">
        {activeTab === "Today" ? (
          <div className="space-y-2">
            {todayAppointments.map((appointment, index) => (
              <div key={appointment.id} className="flex items-start gap-3">
                {/* Timeline dot */}
                <div className="flex flex-col items-center pt-1">
                  <div className="w-2.5 h-2.5 rounded-full border-2 border-blue "></div>
                  {index < todayAppointments.length - 1 && (
                    <div className="w-0.5 h-6 bg-gray-300 mt-1"></div>
                  )}
                </div>

                {/* Appointment details */}
                <div className="flex-1 bg-blue/15 rounded-sm p-3">
                  <div className="font-medium text-gray-900 text-sm">
                    {appointment.time}
                  </div>
                  <div className="text-gray-800 text-xs mt-1">
                    {appointment.appointmentCount} appointments
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div>
            {/* Monthly Calendar Header */}
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={() =>
                  setSelectedDate(selectedDate.subtract(1, "month"))
                }
                className="p-1 hover:bg-gray-100 rounded"
              >
                <Icon icon="mdi:chevron-left" className="w-4 h-4" />
              </button>

              <div className="relative" ref={pickerRef}>
                <button
                  onClick={() => setShowYearMonthPicker(!showYearMonthPicker)}
                  className="font-semibold text-blue text-base hover:bg-blue-50 px-3 py-1 rounded flex items-center gap-2"
                >
                  {selectedDate.format("MMMM YYYY")}
                  <Icon icon="mdi:calendar" className="w-4 h-4" />
                </button>

                {showYearMonthPicker && (
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 bg-white border border-gray-200 rounded-md shadow-lg p-3 z-50 w-64 sm:w-72 max-h-80 overflow-y-auto">
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          Year
                        </label>
                        <select
                          value={selectedDate.year()}
                          onChange={(e) =>
                            handleDateChange(
                              parseInt(e.target.value),
                              selectedDate.month()
                            )
                          }
                          className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        >
                          {yearOptions.map((year) => (
                            <option key={year} value={year}>
                              {year}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Month Selection */}
                      <div>
                        <label className="block text-xs font-medium text-gray-600 mb-1">
                          Month
                        </label>
                        <select
                          value={selectedDate.month()}
                          onChange={(e) =>
                            handleDateChange(
                              selectedDate.year(),
                              parseInt(e.target.value)
                            )
                          }
                          className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        >
                          {monthOptions.map((month) => (
                            <option key={month.value} value={month.value}>
                              {month.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="flex justify-between items-center mt-3 pt-2 border-t border-gray-100">
                      <button
                        onClick={() =>
                          handleDateChange(dayjs().year(), dayjs().month())
                        }
                        className="text-xs text-blue-600 hover:text-blue-800 font-medium px-2 py-1 rounded hover:bg-blue-50"
                      >
                        Today
                      </button>
                      <button
                        onClick={() => setShowYearMonthPicker(false)}
                        className="text-xs text-gray-800 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-50"
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <button
                onClick={() => setSelectedDate(selectedDate.add(1, "month"))}
                className="p-1 hover:bg-gray-100 rounded"
              >
                <Icon icon="mdi:chevron-right" className="w-4 h-4" />
              </button>
            </div>

            {/* Days of week header */}
            <div className="grid grid-cols-7 gap-1 mb-3">
              {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map(
                (day, index) => (
                  <div
                    key={`${day}-${index}`}
                    className="text-center text-xs font-medium text-blue py-2"
                  >
                    {day}
                  </div>
                )
              )}
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-2">
              {generateCalendar.map((day, index) => (
                <button
                  key={index}
                  className={`aspect-square flex items-center justify-center text-xs rounded-md transition-colors font-medium
                    ${
                      day.hasShift
                        ? "bg-blue text-white hover:bg-blue-600"
                        : day.isCurrentMonth
                        ? "text-gray-700 hover:bg-gray-100"
                        : "text-gray-400"
                    }
                    ${day.isToday ? "ring-2 ring-blue ring-offset-1" : ""}
                  `}
                  title={
                    day.hasShift
                      ? `${day.shifts.length} shift(s) scheduled`
                      : "No shifts"
                  }
                >
                  {day.day < 10 ? `0${day.day}` : day.day}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MonthlySchedule;
