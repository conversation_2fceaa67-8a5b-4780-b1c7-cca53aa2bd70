import { Icon } from "@iconify/react/dist/iconify.js";
import React from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { useFormik } from "formik";
import * as Yup from "yup";

//# Data for the "Medical Devices History" table
const medicalDevicesTableData = {
  columns: [
    { title: "Device Name", key: "deviceName" },
    { title: "Model", key: "model" },
    { title: "Size", key: "size" },
    { title: "Site", key: "site" },
    { title: "Apply Time", key: "applyTime" },
    { title: "Status", key: "status" },
    { title: "Remove Time", key: "removeTime" },
    { title: "Reason", key: "reason" },
    { title: "Observation", key: "observation" },
    { title: "Remarks", key: "remarks" },
  ],
  rows: [
    {
      id: "1",
      deviceName: "Foley Catheter",
      model: "Bard Model X",
      size: "14 Fr",
      site: "Urethra",
      applyTime: "10:00 AM",
      status: "Applied",
      removeTime: "—",
      reason: "—",
      observation: "No irritation",
      remarks: "To be reviewed after 3 days",
    },
    {
      id: "2",
      deviceName: "Central Venous Catheter",
      model: "Arrow CVC-220",
      size: "7 Fr",
      site: "Right Subclavian",
      applyTime: "2:00 PM",
      status: "Applied",
      removeTime: "—",
      reason: "—",
      observation: "Site clean, no signs of infection",
      remarks: "Daily site inspection required",
    },
    {
      id: "3",
      deviceName: "Nasogastric Tube",
      model: "Ryles Tube 16",
      size: "16 Fr",
      site: "Nasal",
      applyTime: "8:30 AM",
      status: "Removed",
      removeTime: "5:00 PM",
      reason: "Completed feeding course",
      observation: "No complications",
      remarks: "Tube removed successfully",
    },
    {
      id: "4",
      deviceName: "Endotracheal Tube",
      model: "Portex ET 7.0",
      size: "7.0 mm",
      site: "Trachea",
      applyTime: "6:00 AM",
      status: "Applied",
      removeTime: "—",
      reason: "—",
      observation: "Breath sounds equal, tube secure",
      remarks: "Reposition to be checked every shift",
    },
  ],
};

const AdministerMedicalDevices: React.FC = () => {
  const initialValues = {
    deviceName: "",
    model: "",
    size: "",
    applyTime: "",
    site: "",
    status: "",
    removeTime: "",
    reason: "",
    observation: "",
    remarks: "",
  };

  //# validation schema
  const medicalDeviceValidationSchema = Yup.object({
    deviceName: Yup.string().required("Device Name is required"),
    model: Yup.string().required("Model is required"),
    size: Yup.string(),
    applyTime: Yup.string().required("Apply Time is required"),
    site: Yup.string().required("Site is required"),
    status: Yup.string().required("Status is required"),
    removeTime: Yup.string().nullable(),
    reason: Yup.string().when("status", {
      is: "Removed",
      then: (schema) =>
        schema.required("Reason is required if device is removed"),
      otherwise: (schema) => schema.nullable(),
    }),
    observation: Yup.string(),
    remarks: Yup.string(),
  });

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: medicalDeviceValidationSchema,
    onSubmit: (values, { resetForm }) => {
      console.log("Medical Device Administration Form submitted", values);

      resetForm();
    },
  });

  return (
    <div className='max-w-full p-4 mx-auto'>
      <h2 className='mb-4 text-lg font-semibold text-center'>
        Administer Medical Devices
      </h2>

      <form
        onSubmit={formik.handleSubmit}
        className='w-full p-4 mb-8 space-y-4 border rounded-md'
      >
        {/* First Row: Device Name, Model, Size */}
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Device Name */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='deviceName' className='whitespace-nowrap'>
                Device Name
              </label>
              <input
                type='text'
                id='deviceName'
                name='deviceName'
                placeholder='Device Name'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.deviceName}
              />
            </div>
            {formik.touched.deviceName && formik.errors.deviceName && (
              <p className='text-xs text-red-500'>{formik.errors.deviceName}</p>
            )}
          </div>

          {/* Model */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='model' className='whitespace-nowrap'>
                Model
              </label>
              <input
                type='text'
                id='model'
                name='model'
                placeholder='Model'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.model}
              />
            </div>
            {formik.touched.model && formik.errors.model && (
              <p className='text-xs text-red-500'>{formik.errors.model}</p>
            )}
          </div>

          {/* Size */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='size' className='whitespace-nowrap'>
                Size
              </label>
              <input
                type='text'
                id='size'
                name='size'
                placeholder='Size'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.size}
              />
            </div>
            {formik.touched.size && formik.errors.size && (
              <p className='text-xs text-red-500'>{formik.errors.size}</p>
            )}
          </div>
        </div>

        {/* Second Row: Apply Time, Site, Status */}
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Apply Time */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='applyTime' className='whitespace-nowrap'>
                Apply Time
              </label>
              <input
                type='datetime-local'
                id='applyTime'
                name='applyTime'
                placeholder='Applied Time'
                className='w-full p-1.5 text-[#3a3a3a] border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.applyTime}
              />
            </div>
            {formik.touched.applyTime && formik.errors.applyTime && (
              <p className='text-xs text-red-500'>{formik.errors.applyTime}</p>
            )}
          </div>

          {/* Site */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='site' className='whitespace-nowrap'>
                Site
              </label>
              <input
                type='text'
                id='site'
                name='site'
                placeholder='Site'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.site}
              />
            </div>
            {formik.touched.site && formik.errors.site && (
              <p className='text-xs text-red-500'>{formik.errors.site}</p>
            )}
          </div>

          {/* Status */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='status' className='whitespace-nowrap'>
                Status
              </label>
              <select
                id='status'
                name='status'
                className='w-full p-1.5 text-[#3a3a3a] border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.status}
              >
                <option value='' className='text-[#3a3a3a]'>
                  Status
                </option>
                <option value='Applied'>Applied</option>
                <option value='In Use'>In Use</option>
                <option value='Removed'>Removed</option>
              </select>
            </div>
            {formik.touched.status && formik.errors.status && (
              <p className='text-xs text-red-500'>{formik.errors.status}</p>
            )}
          </div>
        </div>

        {/* Third Row: Remove Time, Reason, Observation */}
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Remove Time */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='removeTime' className='whitespace-nowrap'>
                Remove Time
              </label>
              <input
                type='datetime-local'
                id='removeTime'
                name='removeTime'
                placeholder='Removed Time'
                className='w-full p-1.5 text-[#3a3a3a] border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.removeTime}
              />
            </div>
            {formik.touched.removeTime && formik.errors.removeTime && (
              <p className='text-xs text-red-500'>{formik.errors.removeTime}</p>
            )}
          </div>

          {/* Reason */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='reason' className='whitespace-nowrap'>
                Reason
              </label>
              <input
                type='text'
                id='reason'
                name='reason'
                placeholder='Reason'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.reason}
              />
            </div>
            {formik.touched.reason && formik.errors.reason && (
              <p className='text-xs text-red-500'>{formik.errors.reason}</p>
            )}
          </div>

          {/* Observation */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='observation' className='whitespace-nowrap'>
                Observation
              </label>
              <input
                type='text'
                id='observation'
                name='observation'
                placeholder='Observation'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.observation}
              />
            </div>
            {formik.touched.observation && formik.errors.observation && (
              <p className='text-xs text-red-500'>
                {formik.errors.observation}
              </p>
            )}
          </div>
        </div>

        {/* Fourth Row: Remarks, Save Button */}
        <div className='grid items-end grid-cols-12 gap-4'>
          {/* Remarks Textarea */}
          <div className='flex  col-span-12 gap-2 items-center md:col-span-9'>
            <label htmlFor='remarks' className='whitespace-nowrap'>
              Remarks
            </label>
            <textarea
              id='remarks'
              name='remarks'
              placeholder='Remarks'
              rows={2}
              className='w-full p-2 border border-gray-300 rounded resize-none focus:outline-none focus:border-blue-400'
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.remarks}
            />
            {formik.touched.remarks && formik.errors.remarks && (
              <p className='text-xs text-red-500'>{formik.errors.remarks}</p>
            )}
          </div>

          {/* Save Button */}
          <div className='flex items-end justify-end col-span-12 md:col-span-3'>
            <button
              type='submit'
              className='flex items-center justify-center px-6 py-2 gap-3 text-white text-md bg-[#116aef] rounded-lg hover:bg-blue-700 h-fit'
            >
              <Icon
                icon='fa6-solid:floppy-disk'
                width='18'
                height='18'
                color='white'
              />
              Save
            </button>
          </div>
        </div>
      </form>

      {/* Medical Devices History Table */}
      <h2 className='mb-4 text-lg font-semibold text-center'>
        Medical Devices History
      </h2>
      <div className='overflow-x-auto'>
        <MasterTable
          columns={medicalDevicesTableData.columns}
          rows={medicalDevicesTableData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default AdministerMedicalDevices;
