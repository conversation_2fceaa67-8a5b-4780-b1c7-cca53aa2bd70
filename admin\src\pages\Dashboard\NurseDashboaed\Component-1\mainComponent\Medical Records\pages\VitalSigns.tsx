import { useState } from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { VitalData } from "../../Consultations/pages/sampleMasterTableData";
import { get } from "lodash";
import { TableAction } from "../../../../../../../layouts/Table/TableAction";

interface VitalSignsProps {
  data: {
    _id: string;
    data: {
      users: any[];
    };
  };
}
const VitalSigns: React.FC<VitalSignsProps> = ({ data }) => {
  const [selectedDate, setSelectedDate] = useState("");
  console.log(data, "sdfasdfasdfasdfasdfasdfasd");

  // const tableData = {
  //   columns: [
  //     { title: "Date & Time", key: "visitNo" },
  //     { title: "Temp", key: "temperature" },
  //     { title: "systolic BP", key: "systolicBP" },
  //     { title: "distolic BP", key: "distolicBP" },
  //     { title: "bmi", key: "bmi" },
  //     { title: "Action", key: "action" },
  //   ],

  //   rows: get(data, "vitalSigns", []).map((item: any, index: number) => ({
  //     key: index,
  //     date: item.date,
  //     time: item.time,
  //     temperature: item.temperature,
  //     systolicBP: item.systolicBP,
  //     distolicBP: item.distolicBP,
  //     bmi: item.bmi,
  //     action: <TableAction onShow={() => {}} />,
  //   })),
  // };
  const tableData = {
    columns: [
      { title: "Date & Time", key: "visitNo" },
      { title: "Temp", key: "temperature" },
      { title: "Systolic BP", key: "systolicBP" },
      { title: "Distolic BP", key: "distolicBP" },
      { title: "BMI", key: "bmi" },
    ],
    rows: [
      {
        visitNo: "2025-07-04 10:00 AM",
        temperature: "98.4°F",
        systolicBP: "120 mmHg",
        distolicBP: "80 mmHg",
        bmi: "23.5",
      },
      {
        visitNo: "2025-07-03 03:45 PM",
        temperature: "99.1°F",
        systolicBP: "125 mmHg",
        distolicBP: "85 mmHg",
        bmi: "24.2",
      },
      {
        visitNo: "2025-07-02 09:15 AM",
        temperature: "97.9°F",
        systolicBP: "118 mmHg",
        distolicBP: "76 mmHg",
        bmi: "22.8",
      },
    ],
  };

  return (
    <div className='w-full p-4 mx-auto'>
      <h2 className='mb-4 text-lg font-semibold text-center'>Vital Signs</h2>

      {/* Date Filter */}
      <div className='flex justify-end mb-2'>
        <input
          type='date'
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
          className='w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none'
        />
      </div>

      {/* Vital Signs Table */}
      <div className='overflow-x-auto'>
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default VitalSigns;
