import{a2 as e,b3 as r,a5 as g,aP as v,aM as w,bB as D,av as o,aK as P,af as S,ah as C,aj as A,aR as O,ai as k}from"./index-ClX9RVH0.js";const I=({orderId:i,onClose:n})=>{const a={orderId:"MED-1234",orderDate:"March 01, 2024",totalAmount:2305,currentStatus:"Shipped",deliveryAddress:{name:"Hospital Central",address:"Pharmacy, 2nd Floor",city:"Ave. Main St. 123",zipCode:"20230, USA"},department:"Internal Medicine",requestedBy:"Dr. <PERSON>",statusHistory:[{status:"Order Placed",date:"2024-02-28 10:30 AM",description:"Order confirmed and sent to warehouse for processing."},{status:"Processing",date:"2024-03-01 09:15 AM",description:"Items being picked and packed for shipment."},{status:"Shipped",date:"2024-03-01 02:00 PM",description:"Package with tracking number ABC123XYZ. Estimated delivery in 2 days."},{status:"In Transit",date:"2024-03-02 08:00 AM",description:"Package en route to delivery address. Scan at regional distribution center."},{status:"Out for Delivery",date:"2024-03-03 06:00 AM",description:"Package is out for delivery and expected to arrive today."}],orderedItems:[{id:1,name:"Sterile Surgical Gown",brand:"MedPro",sku:"MSG-001",quantity:50,unitPrice:15,total:750},{id:2,name:"Medical Syringes",brand:"SafeInject",sku:"SYR-10",quantity:200,unitPrice:.75,total:150},{id:3,name:"Disposable Face Masks N95",brand:"ProtectMax",sku:"N95-MASK",quantity:100,unitPrice:1.2,total:120},{id:4,name:"Alcohol Wipes (Box of 200)",brand:"CleanCare",sku:"AW-200",quantity:10,unitPrice:8.5,total:85},{id:5,name:"IV Solution Dextrose 5%",brand:"FluidTech",sku:"IV-D5",quantity:20,unitPrice:25,total:500},{id:6,name:"Digital Thermometer",brand:"TempCheck",sku:"DT-PRO",quantity:5,unitPrice:35,total:175},{id:7,name:"Stethoscope Professional",brand:"CardioSound",sku:"STETH-PRO",quantity:2,unitPrice:150,total:300},{id:8,name:"Surgical Gloves (Disposable)",brand:"SafeHands",sku:"SG-DISP",quantity:30,unitPrice:7.5,total:225}],relatedDocuments:[{name:"Purchase Order",id:"#PO-7890",type:"PDF"},{name:"Delivery Receipt (Draft)",id:"DRAFT",type:"DOCX"},{name:"Medical Supplies Catalog",id:"PDF",type:"PDF"},{name:"Product Images for Item 1",id:"LINK",type:"LINK"}]},c=t=>{switch(t.toLowerCase()){case"shipped":return"bg-blue text-white";case"processing":return"bg-yellow-100 text-yellow-800";case"order placed":return"bg-green-100 text-green-800";case"in transit":return"bg-purple-100 text-purple-800";case"out for delivery":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},m=t=>{switch(t.toLowerCase()){case"shipped":return"material-symbols:local-shipping";case"processing":return"material-symbols:settings";case"order placed":return"material-symbols:check-circle";case"in transit":return"material-symbols:flight";case"out for delivery":return"material-symbols:delivery-truck";default:return"material-symbols:info"}};return e.jsxs("div",{className:"bg-white rounded shadow-xl",children:[e.jsxs("div",{className:"flex items-center justify-between py-1 px-6 border-b border-gray-200",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"Order Details:"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-base font-medium text-gray-700",children:a.orderId}),e.jsx("span",{className:`px-3 py-1 rounded text-sm font-medium ${c(a.currentStatus)}`,children:a.currentStatus})]})]}),e.jsx("button",{onClick:n,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(r,{icon:"material-symbols:close",className:"text-xl text-gray-500"})})]}),e.jsxs("div",{className:"flex h-[calc(90vh-120px)]",children:[e.jsxs("div",{className:" flex flex-col gap-2 px-4 py-2 overflow-y-auto",children:[e.jsxs("div",{className:"border rounded px-1 py-2  mb-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Order Summary"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{icon:"material-symbols:receipt",className:"text-blue-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Order ID"}),e.jsx("p",{className:"font-medium",children:a.orderId})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{icon:"material-symbols:attach-money",className:"text-green-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total Amount"}),e.jsxs("p",{className:"font-medium",children:["$",a.totalAmount.toFixed(2)]})]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{icon:"material-symbols:calendar-today",className:"text-purple-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Order Date"}),e.jsx("p",{className:"font-medium",children:a.orderDate})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{icon:"material-symbols:local-shipping",className:"text-blue-600"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Current Status"}),e.jsx("p",{className:"font-medium",children:a.currentStatus})]})]})]})]})]}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg py-2 px-4 ",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-3",children:"Delivery Address:"}),e.jsxs("div",{className:"text-gray-700",children:[e.jsx("p",{className:"font-medium",children:a.deliveryAddress.name}),e.jsx("p",{children:a.deliveryAddress.address}),e.jsx("p",{children:a.deliveryAddress.city}),e.jsx("p",{children:a.deliveryAddress.zipCode})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 ",children:[e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-2",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Department:"}),e.jsx("p",{className:"font-medium text-gray-900",children:a.department})]}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-2",children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Requested By:"}),e.jsx("p",{className:"font-medium text-gray-900",children:a.requestedBy})]})]}),e.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200",children:e.jsx("h4",{className:"font-semibold text-gray-900",children:"Ordered Items"})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Text"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Brand/Model"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"SKU"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Quantity"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Unit Price"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Total"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-200",children:a.orderedItems.map(t=>e.jsxs("tr",{className:"hover:bg-gray-50",children:[e.jsx("td",{className:"px-4 py-3",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded flex items-center justify-center",children:e.jsx(r,{icon:"material-symbols:medical-services",className:"text-blue-600 text-sm"})}),e.jsx("span",{className:"font-medium text-gray-900",children:t.name})]})}),e.jsx("td",{className:"px-4 py-3 text-gray-700",children:t.brand}),e.jsx("td",{className:"px-4 py-3 text-gray-700",children:t.sku}),e.jsx("td",{className:"px-4 py-3 text-gray-700",children:t.quantity}),e.jsxs("td",{className:"px-4 py-3 text-gray-700",children:["$",t.unitPrice.toFixed(2)]}),e.jsxs("td",{className:"px-4 py-3 font-medium text-gray-900",children:["$",t.total.toFixed(2)]})]},t.id))})]})})]})]}),e.jsxs("div",{className:"w-80 border-l border-gray-200 p-6 overflow-y-auto bg-gray-50",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Order Status History"}),e.jsx("div",{className:"space-y-4",children:a.statusHistory.map((t,d)=>e.jsxs("div",{className:"relative",children:[d<a.statusHistory.length-1&&e.jsx("div",{className:"absolute left-4 top-8 w-0.5 h-16 bg-gray-300"}),e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${c(t.status)}`,children:e.jsx(r,{icon:m(t.status),className:"text-sm"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"flex items-center justify-between mb-1",children:e.jsx("h5",{className:"font-medium text-gray-900",children:t.status})}),e.jsx("p",{className:"text-xs text-gray-500 mb-2",children:t.date}),e.jsx("p",{className:"text-sm text-gray-600",children:t.description})]})]})]},d))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-4",children:"Related Documents"}),e.jsx("div",{className:"space-y-3",children:a.relatedDocuments.map((t,d)=>e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200 hover:shadow-sm transition-shadow cursor-pointer",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded flex items-center justify-center",children:e.jsx(r,{icon:t.type==="PDF"?"material-symbols:picture-as-pdf":t.type==="DOCX"?"material-symbols:description":"material-symbols:link",className:"text-blue-600 text-sm"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"font-medium text-gray-900 text-sm",children:t.name}),e.jsxs("p",{className:"text-xs text-gray-500",children:["(",t.id,")"]})]}),e.jsx(r,{icon:"material-symbols:download",className:"text-gray-400 text-sm"})]},d))})]})]})]}),e.jsx("div",{className:"border-t border-gray-200 p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:["Expected delivery by ",e.jsx("span",{className:"font-medium",children:"2024-03-04"})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2",children:[e.jsx(r,{icon:"material-symbols:print",className:"text-sm"}),"Print Invoice"]}),e.jsxs("button",{className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2",children:[e.jsx(r,{icon:"material-symbols:local-shipping",className:"text-sm"}),"Track Shipment"]}),e.jsxs("button",{className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2",children:[e.jsx(r,{icon:"material-symbols:support-agent",className:"text-sm"}),"Contact Support"]})]})]})})]})},y=()=>{const[i,n]=g.useState({page:1,limit:10,search:"",status:""}),[a,c]=g.useState(null),[m,t]=g.useState(!1),d=v(()=>t(!1)),s=l=>{c(l),t(!0)},h=()=>{c(null),t(!1)},x=w(i),{data:u,isLoading:b}=D({...x,category:"PURCHASE"}),j=o.get(u,"data.invoices",[]),f=[{title:"S.N.",key:"serialNo"},{title:"Order ID",key:"orderId"},{title:"Hospital Name",key:"hospitalName"},{title:"Order Date",key:"orderDate"},{title:"Total Amount",key:"totalAmount"},{title:"Status",key:"status"},{title:"Priority",key:"priority"},{title:"Action",key:"action"}],N=j.map((l,p)=>({key:l._id||p,serialNo:(i.page-1)*i.limit+p+1,orderId:o.get(l,"predefinedBillNo","-"),hospitalName:o.get(l,"vendor.commonInfo.personalInfo.fullName","Hospital Name"),orderDate:P(o.get(l,"date","")).format("MMM DD, YYYY"),totalAmount:`Rs. ${o.get(l,"totalAmount",0).toLocaleString()}`,status:e.jsx(C,{status:o.get(l,"status","pending")}),priority:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${o.get(l,"priority","medium")==="high"?"bg-red-100 text-red-800":o.get(l,"priority","medium")==="medium"?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:o.get(l,"priority","medium").toUpperCase()}),action:e.jsx(S,{onShow:()=>s(l._id),onDelete:()=>console.log("Delete order:",l._id)})}));return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-white p-2 rounded shadow-sm",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[e.jsx("div",{className:"flex-1  max-w-md",children:e.jsxs("div",{className:"relative",children:[e.jsx(r,{icon:"material-symbols:search",className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),e.jsx("input",{type:"text",placeholder:"Search orders...",className:"w-full pl-10 pr-4 py-1 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",value:i.search,onChange:l=>n({...i,search:l.target.value,page:1})})]})}),e.jsx("div",{className:"flex gap-3",children:e.jsxs("select",{className:"px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 outline-none",value:i.status,onChange:l=>n({...i,status:l.target.value,page:1}),children:[e.jsx("option",{value:"",children:"All Status"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"processing",children:"Processing"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"cancelled",children:"Cancelled"})]})})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm",children:e.jsx(A,{columns:f,rows:N,loading:b,pagination:{currentPage:i.page,totalPage:o.get(u,"data.pagination.pages",1),limit:i.limit,onClick:({page:l,limit:p})=>{l&&n({...i,page:l}),p&&n({...i,limit:p})}}})})]}),m&&a&&e.jsx(O,{ref:d,classname:" w-full max-w-6xl max-h-[80vh] overflow-scroll",children:e.jsx(I,{orderId:a,onClose:h})})]})},M=()=>{const[i,n]=g.useState({page:1,limit:20,priority:"",category:"purchase_order"}),a=[{id:"PO-2024-001",title:"Urgent: Medical Supplies Order",message:"New purchase order for medical supplies requires immediate attention",hospital:"City General Hospital",priority:"high",category:"Medical Supplies",amount:15e3,timestamp:new Date(Date.now()-30*60*1e3),isRead:!1,orderItems:["Surgical Masks","Gloves","Syringes"]},{id:"PO-2024-002",title:"Equipment Purchase Request",message:"Laboratory equipment order pending vendor confirmation",hospital:"Metro Medical Center",priority:"medium",category:"Laboratory Equipment",amount:45e3,timestamp:new Date(Date.now()-2*60*60*1e3),isRead:!1,orderItems:["Centrifuge","Microscope","Test Tubes"]},{id:"PO-2024-003",title:"Pharmacy Stock Replenishment",message:"Regular pharmacy inventory restock order",hospital:"Regional Health Center",priority:"low",category:"Pharmaceuticals",amount:8500,timestamp:new Date(Date.now()-4*60*60*1e3),isRead:!0,orderItems:["Antibiotics","Pain Relievers","Vitamins"]},{id:"PO-2024-004",title:"Emergency Equipment Order",message:"Critical equipment needed for emergency department",hospital:"Emergency Medical Center",priority:"high",category:"Emergency Equipment",amount:25e3,timestamp:new Date(Date.now()-6*60*60*1e3),isRead:!1,orderItems:["Defibrillator","Ventilator Parts","Monitors"]}],c=s=>{switch(s){case"high":return"bg-blue text-white border-blue";case"medium":return"bg-[#83BDF7] text-whites-100 border-[#83BDF7]";case"low":return"bg-[#7CD7F7] text-grey border-[#7CD7F7]";default:return"bg-gray-100 text-gray-800 border-gray-200"}},m=s=>{switch(s){case"high":return e.jsx(r,{icon:"material-symbols:priority-high",className:"text-blue"});case"medium":return e.jsx(r,{icon:"material-symbols:remove",className:"text-[#83BDF7]"});case"low":return e.jsx(r,{icon:"material-symbols:keyboard-arrow-down",className:"text-[#7CD7F7]"});default:return e.jsx(r,{icon:"material-symbols:info",className:"text-gray-600"})}},t=s=>{const x=Math.floor((new Date().getTime()-s.getTime())/(1e3*60));if(x<60)return`${x} minutes ago`;if(x<1440){const u=Math.floor(x/60);return`${u} hour${u>1?"s":""} ago`}else{const u=Math.floor(x/1440);return`${u} day${u>1?"s":""} ago`}},d=a.filter(s=>!(i.priority&&s.priority!==i.priority));return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"High Priority"}),e.jsx("p",{className:"text-xl font-bold text-blue",children:a.filter(s=>s.priority==="high"&&!s.isRead).length})]}),e.jsx(r,{icon:"material-symbols:priority-high",className:"text-blue text-xl"})]})}),e.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#83BDF7]",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Medium Priority"}),e.jsx("p",{className:"text-xl font-bold text-[#83BDF7]",children:a.filter(s=>s.priority==="medium"&&!s.isRead).length})]}),e.jsx(r,{icon:"material-symbols:remove",className:"text-[#83BDF7] text-2xl"})]})}),e.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#7CD7F7]",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Low Priority"}),e.jsx("p",{className:"text-xl font-bold text-[#7CD7F7]",children:a.filter(s=>s.priority==="low"&&!s.isRead).length})]}),e.jsx(r,{icon:"material-symbols:keyboard-arrow-down",className:"text-[#7CD7F7] text-2xl"})]})}),e.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#A5E49D]",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total Value"}),e.jsxs("p",{className:"text-xl font-bold text-[#A5E49D]",children:["Rs.",a.reduce((s,h)=>s+h.amount,0).toLocaleString()]})]}),e.jsx(r,{icon:"material-symbols:attach-money",className:"text-[#A5E49D] text-2xl"})]})})]}),e.jsx("div",{className:"bg-white py-1 px-5  rounded shadow-sm",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4  w-full items-center justify-between",children:[e.jsx("h3",{className:"text-sm font-semibold text-gray-800",children:"Purchase Order Alerts"}),e.jsxs("div",{className:"flex gap-3 place-items-center",children:[e.jsxs("select",{className:"px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500",value:i.priority,onChange:s=>n({...i,priority:s.target.value}),children:[e.jsx("option",{value:"",children:"All Priorities"}),e.jsx("option",{value:"high",children:"High Priority"}),e.jsx("option",{value:"medium",children:"Medium Priority"}),e.jsx("option",{value:"low",children:"Low Priority"})]}),e.jsxs("button",{className:"px-4 py-[4.5px] bg-blue text-white rounded hover:bg-blue-700 transition-colors",children:[e.jsx(r,{icon:"material-symbols:refresh",className:"inline mr-2"}),"Refresh"]})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 gap-3",children:[d.map(s=>e.jsx("div",{className:`bg-white rounded shadow-sm border-l-4 p-[10.5px] transition-all hover:shadow-md ${s.isRead?"opacity-75":""} ${s.priority==="high"?"border-blue":s.priority==="medium"?"border-[#83bdf7]":"border-[#7cd7f7]"}`,children:e.jsx("div",{className:"flex items-start justify-between",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[m(s.priority),e.jsx("h4",{className:`font-semibold ${s.isRead?"text-gray-600":"text-gray-900"}`,children:s.title}),e.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium border ${c(s.priority)}`,children:s.priority.toUpperCase()}),!s.isRead&&e.jsx("span",{className:"w-2 h-2 bg-blue-600 rounded-full"})]}),e.jsx("p",{className:"text-gray-600 mb-3",children:s.message}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Hospital"}),e.jsx("p",{className:"text-sm text-gray-900",children:s.hospital})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Category"}),e.jsx("p",{className:"text-sm text-gray-900",children:s.category})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-500",children:"Order Value"}),e.jsxs("p",{className:"text-sm font-semibold text-green-600",children:["Rs. ",s.amount.toLocaleString()]})]})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-500 mb-1",children:"Order Items"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s.orderItems.map((h,x)=>e.jsx("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs",children:h},x))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-xs text-gray-500",children:t(s.timestamp)}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{className:"px-3 py-1 bg-blue text-white rounded text-sm hover:bg-blue-700 transition-colors",children:"View Details"}),e.jsx("button",{className:"px-3 py-1 bg-green text-white rounded text-sm hover:bg-green transition-colors",children:"Accept Order"})]})]})]})})},s.id)),d.length===0&&e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-8 text-center",children:[e.jsx(r,{icon:"material-symbols:notifications-off",className:"text-gray-400 text-4xl mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-600 mb-2",children:"No alerts found"}),e.jsx("p",{className:"text-gray-500",children:"There are no purchase order alerts matching your current filters."})]})]})]})},T=()=>{const[i,n]=g.useState("Order List"),a=[{title:"Order List",value:"orders",id:1},{title:"New Purchase Alerts",value:"alerts",id:2}],c=()=>{switch(i){case"Order List":return e.jsx(y,{});case"New Purchase Alerts":return e.jsx(M,{});default:return e.jsx(y,{})}};return e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsx("div",{className:"",children:e.jsx(k,{tabs:a.map(m=>m.title),defaultTab:i,onTabChange:m=>{const t=a.find(d=>d.title===m);t&&n(t.title)}})}),e.jsx("div",{className:"transition-all duration-300",children:c()})]})};export{T as VendorOrderPage};
