import{bR as G,a5 as a,cr as M,cs as U,av as g,a2 as e,d3 as q,ct as z,aM as K,ah as Q,a7 as W,b3 as J,aj as V}from"./index-ClX9RVH0.js";const X=G("incomestatement","Income Statement"),Y=X.useGetAll,Z=a.forwardRef(({data:s,type:h,status:n},x)=>{var i,u,y;const l=(i=s==null?void 0:s.data)==null?void 0:i.totalTransaction;M(Math.abs(l??0),{currency:"NRS",locale:"IN"});const{data:d,isSuccess:m}=U();return console.log(d,"hospitalInfo"),g.get(d,"data.hospital"),console.log(n,"status"),e.jsxs("div",{className:" p-2 bg-white",ref:x,children:[e.jsx(q,{}),e.jsx("div",{className:"text-center mb-6",children:e.jsxs("h2",{className:"text-lg font-semibold text-gray-800",children:[h," (",n,")"]})}),e.jsx("div",{className:"overflow-x-auto border border-gray-800 mb-4 rounded-md",children:e.jsxs("table",{className:"min-w-full text-sm text-left text-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 border-b border-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Account Code"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Description"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Category"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Invoice Date"}),e.jsx("th",{className:"px-4 py-2 text-center font-medium",children:"Transaction Amount"})]})}),e.jsx("tbody",{children:g.get(s,"data.incomeStatements",[]).map((r,N)=>{var b;return e.jsxs("tr",{className:"border-b border-gray-200 hover:bg-gray-100 break-inside-avoid-page",children:[e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center whitespace-nowrap w-[100px]",children:(r==null?void 0:r.accCode)||"-"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center max-w-[200px]",children:(r==null?void 0:r.accDescription)||"-"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center w-[80px] whitespace-nowrap",children:(r==null?void 0:r.category)||"-"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center w-[110px] whitespace-nowrap",children:(r==null?void 0:r.date)||"-"}),e.jsx("td",{className:"px-4 py-2 text-right w-[120px]",children:((b=r==null?void 0:r.txdAmount)==null?void 0:b.toFixed(2))??"0.00"})]},N)})})]})}),e.jsxs("div",{className:"flex justify-around items-center rounded-md border border-gray-800 bg-gray-50 text-sm font-semibold",children:[e.jsx("div",{className:"px-4 py-2 text-right border-gray-800",children:"Total"}),e.jsxs("div",{className:"px-4 py-2  border-l border-gray-800",children:[e.jsxs("p",{children:["Total Expenses: Rs.",(u=s==null?void 0:s.data)==null?void 0:u.totalLoss]}),e.jsxs("p",{children:["Total Income: Rs.",(y=s==null?void 0:s.data)==null?void 0:y.totalProfit]})]})]}),e.jsxs("div",{className:"flex justify-between mt-16",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm font-medium mb-16",children:"Prepared By:"}),e.jsx("div",{className:"border-t border-gray-800 w-48 pt-2 text-sm"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm font-medium mb-16",children:"Approved By:"}),e.jsx("div",{className:"border-t border-gray-800 w-48 pt-2 text-sm"})]})]})]})}),te=()=>{const[s,h]=a.useState(""),[n,x]=a.useState(""),[l,d]=a.useState(""),[m,i]=a.useState(""),[u,y]=a.useState(""),[r,N]=a.useState(""),[b,A]=a.useState(!1),[f,o]=a.useState([]),C=a.useRef(null),E=z.useReactToPrint({contentRef:C,pageStyle:`
        @page {
          size: A4;
          margin: 0.5in;
        }
        @media print {
          body {
            -webkit-print-color-adjust: exact;
          }
        }
      `}),j=n||l||s,P=j?K({page:1,limit:1e3,startDate:n||void 0,endDate:l||void 0,category:s&&s!=="ALL"?s:void 0}):null,{data:v,isLoading:D}=Y(P),S=j?g.get(v,"data",{}):{};console.log(v,"dataraja"),a.useEffect(()=>{o([])},[]),a.useEffect(()=>{o([])},[n,l,s]);const B=()=>{const t=m||(u||r?"ALL":"");x(u),d(r),h(t),i(t),o([]),A(!0)},H=()=>{x(""),d(""),h(""),y(""),N(""),i(""),o([]),A(!0)},O=(t,c)=>{o(t?c:[])},_=(t,c)=>{o(c?p=>[...p,t]:p=>p.filter(w=>w!==t))},k=()=>{const t=g.get(S,"incomeStatements",[]);f.length>0&&t.filter(c=>f.includes(c._id)),setTimeout(()=>{E()},100)},I={columns:[{title:"Transaction ID",key:"accCode"},{title:"Date",key:"date"},{title:"Name",key:"fullName"},{title:"Description",key:"accDescription"},{title:"Category",key:"category"},{title:"Amount",key:"amount"},{title:"Created At",key:"createdAt"}],rows:g.get(S,"incomeStatements",[]).map(t=>{var c,p,w,L,F,R;return{_id:t._id,accCode:t==null?void 0:t.accCode,date:new Date(t.date).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),accDescription:t.accDescription||"N/A",category:e.jsx(Q,{status:t.category,className:`px-2 py-1 rounded-full text-xs font-medium ${t.category==="PROFIT"?"bg-green-100 text-green-800":t.category==="LOSS"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`}),fullName:((w=(p=(c=t==null?void 0:t.createdBy)==null?void 0:c.commonInfo)==null?void 0:p.personalInfo)==null?void 0:w.fullName)||((R=(F=(L=t==null?void 0:t.user)==null?void 0:L.commonInfo)==null?void 0:F.personalInfo)==null?void 0:R.fullName)||"N/A",amount:`Rs. ${t.txdAmount.toLocaleString()}`,remark:t.remark||"N/A",createdAt:new Date(t.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}})},$=j?g.get(S,"incomeStatements",[]).length:0,T=n||l||s;return e.jsxs("div",{className:"h-screen overflow-y-auto",children:[e.jsx(W,{listTitle:"Income Statement",hideHeader:!0}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-end md:space-x-4 space-y-4 md:space-y-0",children:[e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:u,onChange:t=>y(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:r,onChange:t=>N(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category"}),e.jsxs("select",{value:m,onChange:t=>i(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",disabled:!0,children:"Select Category"}),e.jsx("option",{value:"ALL",children:"All"}),e.jsx("option",{value:"PROFIT",children:"Profit"}),e.jsx("option",{value:"LOSS",children:"Loss"})]})]}),e.jsxs("div",{className:"w-full md:w-auto flex gap-4 mt-2 md:mt-0",children:[e.jsx("button",{onClick:B,className:"w-full md:w-auto px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 transition-colors font-medium",children:"Apply"}),e.jsx("button",{onClick:H,className:"w-full md:w-auto px-4 py-2 bg-rose-500 text-white rounded-md hover:bg-rose-600 transition-colors font-medium",children:"Clear"}),e.jsx("button",{onClick:k,disabled:!b||!m,className:`w-full md:w-auto px-4 py-2  text-white rounded-md transition-colors font-medium 
                ${b&&m?"bg-sky-500 text-white hover:bg-sky-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:e.jsx(J,{icon:"material-symbols-light:print-outline",width:"24",height:"24"})})]})]}),T&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[e.jsx("span",{className:"text-sm text-gray-600 font-medium",children:"Active filters:"}),n&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["From: ",n,e.jsx("button",{onClick:()=>{x(""),x("")},className:"ml-1 text-green-600 hover:text-green-800",children:"×"})]}),l&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["To: ",l,e.jsx("button",{onClick:()=>{d(""),d("")},className:"ml-1 text-green-600 hover:text-green-800",children:"×"})]}),s&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:["Category: ",s,e.jsx("button",{onClick:()=>{i(""),h("")},className:"ml-1 text-purple-600 hover:text-purple-800",children:"×"})]})]})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm",children:[e.jsx("div",{className:"p-6 border-b border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Transaction Details"}),e.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:[j?D?"Loading...":`Showing ${$} transactions`:"Apply filters to view transactions",T&&j&&" (filtered)"]})]}),f.length>0&&e.jsxs("div",{className:"text-sm text-gray-600",children:[f.length," transaction(s) selected"]})]})}),e.jsx(V,{columns:I.columns,rows:I.rows,loading:D,color:"bg-gray-50",textcolor:"text-gray-600",selectedIds:f,onSelectAll:O,onSelectRow:_,primaryKey:"_id",onBulkAction:k,bulkActionLabel:"Print Selected",showBulkActions:!0})]}),e.jsx("section",{className:"hidden",children:e.jsx(Z,{ref:C,data:v,type:"Income Statement",status:m})})]})};export{te as default};
