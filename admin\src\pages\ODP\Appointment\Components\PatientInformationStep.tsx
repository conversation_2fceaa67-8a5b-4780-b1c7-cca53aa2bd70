import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { InputField } from "../../../../components/Input-Field";
import { DropdownField } from "../../../../components/DropDownField";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

// Options from existing patient forms
const genderOptions = [
  { value: "Male", label: "Male" },
  { value: "Female", label: "Female" },
  { value: "Other", label: "Other" },
];

const bloodGroupOptions = [
  { value: "A+", label: "A+" },
  { value: "A-", label: "A-" },
  { value: "B+", label: "B+" },
  { value: "B-", label: "B-" },
  { value: "AB+", label: "AB+" },
  { value: "AB-", label: "AB-" },
  { value: "O+", label: "O+" },
  { value: "O-", label: "O-" },
];

const maritalStatusOptions = [
  { value: "Single", label: "Single" },
  { value: "Married", label: "Married" },
  { value: "Divorced", label: "Divorced" },
  { value: "Widowed", label: "Widowed" },
];

// No validation for now - as requested
const patientValidationSchema = yup.object().shape({});

interface PatientInformationStepProps {
  onNext: (data: any) => void;
  onBack?: () => void;
  initialData?: any;
  showBack?: boolean;
}

const PatientInformationStep: React.FC<PatientInformationStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = false,
}) => {
  const [isOldPatient, setIsOldPatient] = useState(false);

  const formik = useFormik({
    initialValues: {
      patientId: initialData?.patientId || "",
      fullName: initialData?.fullName || "",
      gender: initialData?.gender || "",
      bloodGroup: initialData?.bloodGroup || "",
      dateOfBirth: initialData?.dateOfBirth || "",
      maritalStatus: initialData?.maritalStatus || "",
      language: initialData?.language || "",
      currentAddress: initialData?.currentAddress || "",
      permanentAddress: initialData?.permanentAddress || "",
      contactNumber: initialData?.contactNumber || "",
      alternatePhoneNumber: initialData?.alternatePhoneNumber || "",
      email: initialData?.email || "",
      citizenNumber: initialData?.citizenNumber || "",
      tokenNumber: initialData?.tokenNumber || "",
    },
    enableReinitialize: true,
    validationSchema: patientValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    setFieldValue,
    getFieldProps,
  } = formik;

  useEffect(() => {
    if (initialData) {
      setIsOldPatient(!!initialData.patientId);
    }
  }, [initialData]);

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:account" className="w-5 h-5 text-teal-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Patient Information
            </h2>
            <p className="text-sm text-gray-600">
              Please provide your personal and medical details
            </p>
          </div>
        </div>

        {/* Patient Type Selection */}
        <div className="mb-6">
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="patientType"
                checked={!isOldPatient}
                onChange={() => setIsOldPatient(false)}
                className="w-4 h-4 text-teal-600"
              />
              <span className="text-sm font-medium text-gray-700">
                I am a new patient
              </span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="patientType"
                checked={isOldPatient}
                onChange={() => setIsOldPatient(true)}
                className="w-4 h-4 text-teal-600"
              />
              <span className="text-sm font-medium text-gray-700">
                I am an old patient
              </span>
            </label>
          </div>
          {!isOldPatient && (
            <div className="mt-2 flex items-center space-x-2 text-sm text-gray-600">
              <span>Token Number:</span>
              <span className="font-medium text-gray-800">Token Number</span>
            </div>
          )}
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-5">
            {/* Patient ID */}
            <div>
              <InputField
                required
                type="text"
                label="Patient ID"
                placeholder="Patient identity number"
                disabled={!isOldPatient}
                {...getFieldProps("patientId")}
              />
              {errors.patientId && touched.patientId && (
                <p className="text-red-500 text-sm">
                  {String(errors.patientId)}
                </p>
              )}
            </div>

            {/* Gender */}
            <div>
              <DropdownField
                label="Gender"
                options={genderOptions}
                name="gender"
                required
                value={values.gender}
                onChange={(e: any) => setFieldValue("gender", e.target.value)}
              />
              {errors.gender && touched.gender && (
                <p className="text-red-500 text-sm">{String(errors.gender)}</p>
              )}
            </div>

            {/* Blood Group */}
            <div>
              <DropdownField
                label="Blood Group"
                options={bloodGroupOptions}
                name="bloodGroup"
                required
                value={values.bloodGroup}
                onChange={(e: any) =>
                  setFieldValue("bloodGroup", e.target.value)
                }
              />
              {errors.bloodGroup && touched.bloodGroup && (
                <p className="text-red-500 text-sm">
                  {String(errors.bloodGroup)}
                </p>
              )}
            </div>

            {/* Full Name */}
            <div>
              <InputField
                required
                type="text"
                label="Full Name"
                placeholder="Enter full name"
                {...getFieldProps("fullName")}
              />
              {errors.fullName && touched.fullName && (
                <p className="text-red-500 text-sm">
                  {String(errors.fullName)}
                </p>
              )}
            </div>

            {/* Date of Birth */}
            <div>
              <InputField
                required
                type="date"
                label="Date of Birth"
                placeholder="DD/MM/YYYY"
                {...getFieldProps("dateOfBirth")}
              />
              {errors.dateOfBirth && touched.dateOfBirth && (
                <p className="text-red-500 text-sm">
                  {String(errors.dateOfBirth)}
                </p>
              )}
            </div>

            {/* Marital Status */}
            <div>
              <DropdownField
                label="Marital Status"
                options={maritalStatusOptions}
                name="maritalStatus"
                value={values.maritalStatus}
                onChange={(e: any) =>
                  setFieldValue("maritalStatus", e.target.value)
                }
              />
            </div>

            {/* Language */}
            <div>
              <InputField
                type="text"
                label="Language"
                placeholder="Enter preferred language"
                {...getFieldProps("language")}
              />
              {errors.language && touched.language && (
                <p className="text-red-500 text-sm">
                  {String(errors.language)}
                </p>
              )}
            </div>

            {/* Contact Number */}
            <div>
              <InputField
                required
                type="tel"
                label="Phone Number"
                placeholder="Phone Number"
                {...getFieldProps("contactNumber")}
              />
              {errors.contactNumber && touched.contactNumber && (
                <p className="text-red-500 text-sm">
                  {String(errors.contactNumber)}
                </p>
              )}
            </div>

            {/* Alternate Phone Number */}
            <div>
              <InputField
                type="tel"
                label="Alternate Phone Number"
                placeholder="Alternate Phone Number"
                {...getFieldProps("alternatePhoneNumber")}
              />
              {errors.alternatePhoneNumber && touched.alternatePhoneNumber && (
                <p className="text-red-500 text-sm">
                  {String(errors.alternatePhoneNumber)}
                </p>
              )}
            </div>

            {/* Email Address */}
            <div>
              <InputField
                required
                type="email"
                label="Email Address"
                placeholder="Email Address"
                {...getFieldProps("email")}
              />
              {errors.email && touched.email && (
                <p className="text-red-500 text-sm">{String(errors.email)}</p>
              )}
            </div>

            {/* Current Address */}
            <div>
              <InputField
                type="text"
                label="Current Address"
                placeholder="Enter current address"
                {...getFieldProps("currentAddress")}
              />
            </div>

            {/* Permanent Address */}
            <div>
              <InputField
                type="text"
                label="Permanent Address"
                placeholder="Enter permanent address"
                {...getFieldProps("permanentAddress")}
              />
            </div>

            {/* Citizenship Number */}
            <div>
              <InputField
                type="text"
                label="Citizenship No"
                placeholder="Enter Citizenship number"
                {...getFieldProps("citizenNumber")}
              />
              {errors.citizenNumber && touched.citizenNumber && (
                <p className="text-red-500 text-sm">
                  {String(errors.citizenNumber)}
                </p>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between mt-8">
            {showBack ? (
              <Button
                type="button"
                onClick={onBack}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Icon icon="mdi:arrow-left" className="w-4 h-4" />
                <span>Back</span>
              </Button>
            ) : (
              <div></div>
            )}

            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            >
              <span>Next</span>
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default PatientInformationStep;
