import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";

interface PatientInformationStepProps {
  onNext: (data: any) => void;
  onBack?: () => void;
  initialData?: any;
  showBack?: boolean;
}

const PatientInformationStep: React.FC<PatientInformationStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = false,
}) => {
  const [isOldPatient, setIsOldPatient] = useState(false);
  const [formData, setFormData] = useState({
    patientId: "",
    fullName: "",
    gender: "",
    bloodGroup: "",
    dateOfBirth: "",
    maritalStatus: "",
    language: "",
    currentAddress: "",
    permanentAddress: "",
    phoneNumber: "",
    alternatePhoneNumber: "",
    emailAddress: "",
    citizenshipId: "",
    tokenNumber: "",
  });

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
      setIsOldPatient(!!initialData.patientId);
    }
  }, [initialData]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext(formData);
  };

  const isFormValid = () => {
    const requiredFields = ['fullName', 'gender', 'bloodGroup', 'dateOfBirth', 'phoneNumber'];
    return requiredFields.every(field => formData[field as keyof typeof formData]);
  };

  return (
    <div>
      {/* Step Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center">
          <Icon icon="mdi:account" className="w-5 h-5 text-teal-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-800">Patient Information</h2>
          <p className="text-sm text-gray-600">Please provide your personal and medical details</p>
        </div>
      </div>

      {/* Patient Type Selection */}
      <div className="mb-6">
        <div className="flex items-center space-x-4">
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="radio"
              name="patientType"
              checked={!isOldPatient}
              onChange={() => setIsOldPatient(false)}
              className="w-4 h-4 text-teal-600"
            />
            <span className="text-sm font-medium text-gray-700">I am a new patient</span>
          </label>
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="radio"
              name="patientType"
              checked={isOldPatient}
              onChange={() => setIsOldPatient(true)}
              className="w-4 h-4 text-teal-600"
            />
            <span className="text-sm font-medium text-gray-700">I am an old patient</span>
          </label>
        </div>
        {!isOldPatient && (
          <div className="mt-2 flex items-center space-x-2 text-sm text-gray-600">
            <span>Token Number:</span>
            <span className="font-medium text-gray-800">Token Number</span>
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Patient ID */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Patient ID *
            </label>
            <input
              type="text"
              value={formData.patientId}
              onChange={(e) => handleInputChange('patientId', e.target.value)}
              placeholder="Patient identity number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
              disabled={!isOldPatient}
            />
          </div>

          {/* Gender */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Gender *
            </label>
            <select
              value={formData.gender}
              onChange={(e) => handleInputChange('gender', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="">Auto Filled</option>
              <option value="Male">Male</option>
              <option value="Female">Female</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Blood Group */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Blood Group *
            </label>
            <select
              value={formData.bloodGroup}
              onChange={(e) => handleInputChange('bloodGroup', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              <option value="">Auto Filled</option>
              <option value="A+">A+</option>
              <option value="A-">A-</option>
              <option value="B+">B+</option>
              <option value="B-">B-</option>
              <option value="AB+">AB+</option>
              <option value="AB-">AB-</option>
              <option value="O+">O+</option>
              <option value="O-">O-</option>
            </select>
          </div>

          {/* Date of Birth */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date of Birth *
            </label>
            <input
              type="date"
              value={formData.dateOfBirth}
              onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            />
          </div>

          {/* Full Name */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name *
            </label>
            <input
              type="text"
              value={formData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value)}
              placeholder="Enter full name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            />
          </div>

          {/* Phone Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number *
            </label>
            <input
              type="tel"
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
              placeholder="Enter phone number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            />
          </div>

          {/* Email Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address *
            </label>
            <input
              type="email"
              value={formData.emailAddress}
              onChange={(e) => handleInputChange('emailAddress', e.target.value)}
              placeholder="Enter email address"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between mt-8">
          {showBack ? (
            <Button
              type="button"
              onClick={onBack}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <Icon icon="mdi:arrow-left" className="w-4 h-4" />
              <span>Back</span>
            </Button>
          ) : (
            <div></div>
          )}

          <Button
            type="submit"
            disabled={!isFormValid()}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
          >
            <span>Next</span>
            <Icon icon="mdi:arrow-right" className="w-4 h-4" />
          </Button>
        </div>
      </form>
    </div>
  );
};

export default PatientInformationStep;
