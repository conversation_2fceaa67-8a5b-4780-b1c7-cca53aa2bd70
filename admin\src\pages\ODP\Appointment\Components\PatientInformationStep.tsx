import React, { useState, useMemo } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { InputField } from "../../../../components/Input-Field";
import { DropdownField } from "../../../../components/DropDownField";
import { SearchableDropdown } from "../../../../components/SearchDropDown";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";
import { useGetUser } from "../../../../server-action/api/user";
import { buildQueryParams } from "../../../../hooks/useBuildQuery";
import { userRole } from "../../../../constant/constant";

// Options from existing patient forms
const genderOptions = [
  { value: "MALE", label: "Male" },
  { value: "FEMALE", label: "Female" },
  { value: "OTHER", label: "Other" },
];

const bloodGroupOptions = [
  { value: "A+", label: "A+" },
  { value: "A-", label: "A-" },
  { value: "B+", label: "B+" },
  { value: "B-", label: "B-" },
  { value: "AB+", label: "AB+" },
  { value: "AB-", label: "AB-" },
  { value: "O+", label: "O+" },
  { value: "O-", label: "O-" },
];

const maritalStatusOptions = [
  { value: "SINGLE", label: "Single" },
  { value: "MARRIED", label: "Married" },
  { value: "DIVORCED", label: "Divorced" },
  { value: "WIDOWED", label: "Widowed" },
];

// No validation for now
const patientValidationSchema = yup.object().shape({});

interface PatientInformationStepProps {
  onNext: (data: any) => void;
  onBack?: () => void;
  initialData?: any;
  showBack?: boolean;
}

const PatientInformationStep: React.FC<PatientInformationStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = false,
}) => {
  const [isOldPatient, setIsOldPatient] = useState(false);

  const queryParams = buildQueryParams({
    role: userRole.PATIENT,
  });
  const { data: userPatientDetails } = useGetUser(queryParams);

  // Patient options for searchable dropdown (handle API response structure)
  const getAllPatient = useMemo(() => {
    // Handle different API response structures
    let patientsArray: any[] = [];

    if (Array.isArray(userPatientDetails)) {
      patientsArray = userPatientDetails;
    } else if (
      userPatientDetails &&
      typeof userPatientDetails === "object" &&
      "data" in userPatientDetails
    ) {
      const data = (userPatientDetails as any).data;
      if (data?.users && Array.isArray(data.users)) {
        patientsArray = data.users;
      } else if (Array.isArray(data)) {
        patientsArray = data;
      }
    }

    return patientsArray.map((patient: any) => ({
      value: patient._id,
      label:
        patient.patientInfo?.patientId ||
        patient.commonInfo?.personalInfo?.fullName,
      name: patient.commonInfo?.personalInfo?.fullName,
      phone: patient.commonInfo?.contactInfo?.phone?.primaryPhone,
    }));
  }, [userPatientDetails]);

  // Handle patient selection for old patient form
  const handlePatientChange = (field: string, value: string) => {
    const selectedUser = getAllPatient.find(
      (user: any) => user.value === value
    );
    if (selectedUser) {
      formik.setFieldValue("contactNumber", selectedUser.phone);
      formik.setFieldValue("name", selectedUser.name);
    }
    formik.setFieldValue("patientId", value);
  };

  const formik = useFormik({
    initialValues: {
      // Old patient fields
      patientId: initialData?.patientId || "",
      name: initialData?.name || "",

      // New patient fields (matching NewPatientForm exactly)
      fullName: initialData?.fullName || "",
      gender: initialData?.gender || "",
      bloodGroup: initialData?.bloodGroup || "",
      age: initialData?.age || "", // DOB field in NewPatientForm
      maritalStatus: initialData?.maritalStatus || "",
      language: initialData?.language || "",
      currentAddress: initialData?.currentAddress || "",
      permanentAddress: initialData?.permanentAddress || "",
      contactNumber: initialData?.contactNumber || "",
      alternatePhoneNumber: initialData?.alternatePhoneNumber || "",
      email: initialData?.email || "",
      citizenNumber: initialData?.citizenNumber || "",
    },
    enableReinitialize: true,
    validationSchema: patientValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, errors, touched, setFieldValue, getFieldProps } = formik;

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:account" className="w-5 h-5 text-teal-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Patient Information
            </h2>
            <p className="text-sm text-gray-600">Personal & Medical Details</p>
          </div>
        </div>

        {/* Patient Type Selection */}
        <div className="mb-6">
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="patientType"
                checked={!isOldPatient}
                onChange={() => setIsOldPatient(false)}
                className="w-4 h-4 text-teal-600"
              />
              <span className="text-sm font-medium text-gray-700">
                I am a new patient
              </span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="patientType"
                checked={isOldPatient}
                onChange={() => setIsOldPatient(true)}
                className="w-4 h-4 text-teal-600"
              />
              <span className="text-sm font-medium text-gray-700">
                I am an old patient
              </span>
            </label>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5">
            {isOldPatient ? (
              // Old Patient Form - Only Patient ID, Name, Contact Number
              <>
                <div>
                  <SearchableDropdown
                    required
                    label="Patient ID"
                    field="patientId"
                    getFieldProps={getFieldProps}
                    options={getAllPatient}
                    value={values.patientId}
                    onValueChange={handlePatientChange}
                  />
                  {errors.patientId && touched.patientId && (
                    <p className="text-red text-sm">
                      {String(errors.patientId)}
                    </p>
                  )}
                </div>

                <div>
                  <InputField
                    type="text"
                    label="Name"
                    placeholder="Auto"
                    name="name"
                    value={values.name}
                    disabled
                  />
                </div>

                <div>
                  <InputField
                    type="text"
                    label="Contact Number"
                    placeholder="Auto"
                    name="contactNumber"
                    value={values.contactNumber}
                    disabled
                  />
                </div>
              </>
            ) : (
              // New Patient Form - All fields from NewPatientForm
              <>
                {/* Full Name */}
                <div>
                  <InputField
                    required
                    type="text"
                    label="Full Name"
                    placeholder="Full Name"
                    {...getFieldProps("fullName")}
                  />
                  {errors.fullName && touched.fullName && (
                    <p className="text-red text-sm">
                      {String(errors.fullName)}
                    </p>
                  )}
                </div>

                {/* Gender */}
                <div>
                  <DropdownField
                    label="Gender"
                    options={genderOptions}
                    name="gender"
                    required
                    value={values.gender}
                    onChange={(e: any) =>
                      setFieldValue("gender", e.target.value)
                    }
                  />
                  {errors.gender && touched.gender && (
                    <p className="text-red text-sm">{String(errors.gender)}</p>
                  )}
                </div>

                {/* Blood Group */}
                <div>
                  <DropdownField
                    label="Blood Group"
                    name="bloodGroup"
                    required
                    options={bloodGroupOptions}
                    value={values.bloodGroup}
                    onChange={(e: any) =>
                      setFieldValue("bloodGroup", e.target.value)
                    }
                  />
                  {errors.bloodGroup && touched.bloodGroup && (
                    <p className="text-red text-sm">
                      {String(errors.bloodGroup)}
                    </p>
                  )}
                </div>

                {/* DOB */}
                <div>
                  <InputField
                    required
                    type="date"
                    label="DOB"
                    placeholder="DD/MM/YYYY"
                    {...getFieldProps("age")}
                  />
                  {errors.age && touched.age && (
                    <p className="text-red text-sm">{String(errors.age)}</p>
                  )}
                </div>

                {/* Marital Status */}
                <div>
                  <DropdownField
                    label="Marital Status"
                    options={maritalStatusOptions}
                    name="maritalStatus"
                    required
                    value={values.maritalStatus}
                    onChange={(e: any) =>
                      setFieldValue("maritalStatus", e.target.value)
                    }
                  />
                  {errors.maritalStatus && touched.maritalStatus && (
                    <p className="text-red text-sm">
                      {String(errors.maritalStatus)}
                    </p>
                  )}
                </div>

                {/* Language */}
                <div>
                  <InputField
                    type="text"
                    label="Language"
                    placeholder="Language"
                    value={values.language}
                    onChange={(e: any) =>
                      setFieldValue("language", e.target.value)
                    }
                  />
                  {errors.language && touched.language && (
                    <p className="text-red text-sm">
                      {String(errors.language)}
                    </p>
                  )}
                </div>

                {/* Current Address */}
                <div>
                  <InputField
                    type="text"
                    label="Current Address"
                    placeholder="Current Address"
                    value={values.currentAddress}
                    onChange={(e: any) =>
                      setFieldValue("currentAddress", e.target.value)
                    }
                    required
                  />
                  {errors.currentAddress && touched.currentAddress && (
                    <p className="text-red text-sm">
                      {String(errors.currentAddress)}
                    </p>
                  )}
                </div>

                {/* Permanent Address */}
                <div>
                  <InputField
                    type="text"
                    label="Permanent Address"
                    placeholder="Permanent Address"
                    value={values.permanentAddress}
                    onChange={(e: any) =>
                      setFieldValue("permanentAddress", e.target.value)
                    }
                  />
                  {errors.permanentAddress && touched.permanentAddress && (
                    <p className="text-red text-sm">
                      {String(errors.permanentAddress)}
                    </p>
                  )}
                </div>

                {/* Phone Number */}
                <div>
                  <InputField
                    type="text"
                    label="Phone Number"
                    placeholder="Phone Number"
                    {...getFieldProps("contactNumber")}
                    required
                  />
                  {errors.contactNumber && touched.contactNumber && (
                    <p className="text-red text-sm">
                      {String(errors.contactNumber)}
                    </p>
                  )}
                </div>

                {/* Alternate Phone Number */}
                <div>
                  <InputField
                    type="text"
                    label="Alternate Phone Number"
                    placeholder="Alternate Phone Number"
                    {...getFieldProps("alternatePhoneNumber")}
                  />
                  {errors.alternatePhoneNumber &&
                    touched.alternatePhoneNumber && (
                      <p className="text-red text-sm">
                        {String(errors.alternatePhoneNumber)}
                      </p>
                    )}
                </div>

                {/* Email Address */}
                <div>
                  <InputField
                    required
                    type="email"
                    label="Email Address"
                    placeholder="Email Address"
                    {...getFieldProps("email")}
                  />
                  {errors.email && touched.email && (
                    <p className="text-red text-sm">{String(errors.email)}</p>
                  )}
                </div>

                {/* Citizenship No */}
                <div>
                  <InputField
                    type="text"
                    label="Citizenship No"
                    placeholder="Enter Citizenship number"
                    {...getFieldProps("citizenNumber")}
                  />
                  {errors.citizenNumber && touched.citizenNumber && (
                    <p className="text-red text-sm">
                      {String(errors.citizenNumber)}
                    </p>
                  )}
                </div>
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-6">
            {showBack && (
              <Button
                type="button"
                onClick={onBack}
                className="bg-gray-500 hover:bg-gray-600 flex items-center gap-2"
              >
                <Icon icon="mdi:arrow-left" className="w-4 h-4" />
                <span>Back</span>
              </Button>
            )}
            <Button
              type="submit"
              className="bg-teal-600 hover:bg-teal-700 flex items-center gap-2 px-6 py-3"
            >
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
              <span>Next</span>
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default PatientInformationStep;
