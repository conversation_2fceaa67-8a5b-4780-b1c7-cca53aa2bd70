import React, { useState } from "react";
import SearchableSelect from "../../../../components/SearchableSelect";
import Button from "../../../../components/Button";
import { useNavigate } from "react-router-dom";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { Status } from "../../../../components/Status";

type Department = "IPD" | "OPD" | "OT" | "Emergency";

interface Nurse {
  nurseID: string;
  nurseName: string;
  department: string;
  shift: string;
  status: "Available" | "Unavailable" | "On Break";
}

const NurseList: React.FC = () => {
  const [department, setDepartment] = useState<Department>("IPD");
  const navigate = useNavigate();

  const testRequestData: Record<Department, Nurse[]> = {
    IPD: [
      {
        nurseID: "N-001",
        nurseName: "<PERSON>",
        department: "OT",
        shift: "Morning",
        status: "Unavailable",
      },

      {
        nurseID: "N-003",
        nurseName: "<PERSON>",
        department: "Cardiology",
        shift: "Night",
        status: "Unavailable",
      },
    ],
    OPD: [
      {
        nurseID: "N-006",
        nurseName: "<PERSON> <PERSON>",
        department: "Cardiology",
        shift: "Morning",
        status: "Available",
      },
    ],
    Emergency: [
      {
        nurseID: "N-008",
        nurseName: "Emma Thompson",
        department: "Orthopedics",
        shift: "Evening",
        status: "On Break",
      },
      {
        nurseID: "N-009",
        nurseName: "Daniel Garcia",
        department: "Emergency",
        shift: "Morning",
        status: "Available",
      },
    ],
    OT: [
      {
        nurseID: "N-010",
        nurseName: "George Hall",
        department: "Surgery",
        shift: "Afternoon",
        status: "Unavailable",
      },
      {
        nurseID: "N-011",
        nurseName: "Linda Martinez",
        department: "OT",
        shift: "Morning",
        status: "Available",
      },
    ],
  };

  const currentData = testRequestData[department];

  const totalNurses = currentData.length;
  const availableCount = currentData.filter(
    (n) => n.status === "Available"
  ).length;
  const unavailableCount = currentData.filter(
    (n) => n.status === "Unavailable"
  ).length;

  const tableData = {
    columns: [
      { title: "Nurse ID", key: "nurseID" },
      { title: "Nurse Name", key: "nurseName" },
      { title: "Department ", key: "department" },
      { title: "Shift", key: "shift" },
      { title: "Status", key: "status" },
    ],
    rows: currentData.map((nurse) => ({
      nurseID: nurse.nurseID,
      nurseName: nurse.nurseName,
      department: nurse.department,
      shift: nurse.shift,
      status: <Status status={nurse.status} />,
    })),
  };

  return (
    <div className="w-full bg-white  rounded-xl p-4 transition-all duration-300">
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center gap-4">
          <div>
            <h3 className="text-xl font-bold text-gray-800">List of Nurses</h3>
            <p className="text-sm text-gray-500 mt-1">
              {totalNurses} Total • {availableCount} Available •{" "}
              {unavailableCount} Unavailable
            </p>
          </div>
        </div>
        <div className=" flex items-center gap-3">
          <Button
            variant="outline"
            title="View All"
            className="!text-blue-600 !border-blue-500 hover:!bg-blue-50 !px-4 !py-2 !text-sm !font-medium !rounded-lg transition-all duration-200"
            onClick={() => navigate("/nurse")}
          />
        </div>
      </div>

      <div className="flex flex-wrap gap-3 mb-3 ">
        <div className="bg-white flex gap-1 text-[#2FC0A1] border border-[#2FC0A1] rounded-md shadow-sm hover:shadow-md transition-all duration-200 w-28 mx-2 ">
          <SearchableSelect
            value={department}
            options={[
              { label: "IPD", value: "IPD" },
              { label: "OPD", value: "OPD" },
              { label: "OT", value: "OT" },
              { label: "Emergency", value: "Emergency" },
            ]}
            onChange={(value) => setDepartment(value as Department)}
          />
        </div>
      </div>

      <div className="overflow-x-auto">
        <MasterTable
          className="p-0"
          rows={tableData.rows}
          columns={tableData.columns}
          loading={false}
        />
      </div>
    </div>
  );
};

export default NurseList;
