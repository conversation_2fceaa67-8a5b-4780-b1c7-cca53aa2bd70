import{cu as L,a2 as e,ad as F,a5 as i,aM as P,cv as E,cw as I,ag as T,av as B,af as V,aQ as q,ai as U,aj as Q}from"./index-ClX9RVH0.js";const _=({data:o,setOpen:g,open:p})=>{var v,s,c,t,h,N,u,y,r;const x=()=>g(!1);if(!p)return null;const{data:a}=L(o),l=a==null?void 0:a.receiverType;return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",onClick:x,children:e.jsxs("div",{className:"relative w-11/12 max-w-[60vw] max-h-[90vh] p-6 bg-white rounded-lg shadow-lg overflow-y-auto print:max-w-full print:max-h-full print:rounded-none print:shadow-none",onClick:d=>d.stopPropagation(),children:[e.jsxs("div",{className:"flex justify-between border rounded-md px-2 py-3 border-b-gray-300 pb-2",children:[e.jsx("h1",{className:"text-lg font-bold",children:"Receiver Details"}),e.jsx("button",{className:"no-print",onClick:x,children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M18 6L6 18M6 6L18 18",stroke:"#0D0D0D",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsxs("div",{className:"py-2 px-2 mt-2 print:mt-0",children:[e.jsx("div",{className:"grid grid-cols-3 gap-y-8 print:grid-cols-2 print:gap-y-4",children:[{label:"Receiver Name",value:(v=a==null?void 0:a.receiverDetails)==null?void 0:v.name},{label:"Receiver Age",value:(s=a==null?void 0:a.receiverDetails)==null?void 0:s.age},{label:"Blood Group",value:a==null?void 0:a.bloodGroup},{label:"Contact No.",value:(c=a==null?void 0:a.receiverDetails)==null?void 0:c.contact},{label:"Address",value:(t=a==null?void 0:a.receiverDetails)==null?void 0:t.address}].map(({label:d,value:b})=>e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("span",{className:"text-gray-500",children:d}),e.jsx("span",{children:b||"N/A"})]},d))}),e.jsxs("h2",{className:"text-lg mt-6 font-semibold mb-2",children:["Receive Type:"," ",l==="BLOOD"?"Blood Receiver":l==="ORGAN"?"Organ Receiver":"N/A"]}),e.jsxs("div",{className:"grid grid-cols-3 gap-y-8 print:grid-cols-2 print:gap-y-4",children:[l==="BLOOD"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("span",{className:"text-gray-500",children:"Receive Date"}),e.jsx("span",{children:((h=a==null?void 0:a.bloodReceivingDetails)==null?void 0:h.donationDate)||"N/A"})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("span",{className:"text-gray-500",children:"Blood Amount"}),e.jsx("span",{children:(N=a==null?void 0:a.bloodReceivingDetails)!=null&&N.quantityInUnits?`${a.bloodReceivingDetails.quantityInUnits} ML`:"N/A"})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("span",{className:"text-gray-500",children:"Expiry Date"}),e.jsx("span",{children:((u=a==null?void 0:a.bloodReceivingDetails)==null?void 0:u.expiryDate)||"N/A"})]})]}),l==="ORGAN"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("span",{className:"text-gray-500",children:"Receive Date"}),e.jsx("span",{children:((y=a==null?void 0:a.organReceivingDetails)==null?void 0:y.donationDate)||"N/A"})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("span",{className:"text-gray-500",children:"Organ Name"}),e.jsx("span",{children:((r=a==null?void 0:a.organReceivingDetails)==null?void 0:r.organType)||"N/A"})]})]})]})]})]})})},z=()=>{const o=F(),[g,p]=i.useState(null),[x,a]=i.useState(!1),[l,v]=i.useState("Blood Receiver"),[s,c]=i.useState({limit:5,page:1}),[t,h]=i.useState({onContactNumber:"",search:""}),N=!!(t.search||t.onContactNumber),u={"Blood Receiver":"BLOOD","Organ Receiver":"ORGAN"},y=P({search:t.search,contact:t.onContactNumber,...N?{}:{page:s.page,limit:s.limit},receiverType:u[l]}),{data:r}=E(y);console.log(r,"data");const{mutateAsync:d}=I({id:JSON.stringify([g])}),b=i.useCallback(n=>o(`${T.CUSTOMFORM}/${n}`),[o]),G=i.useCallback(()=>o(T.CUSTOMFORM),[o]),M=i.useCallback(n=>{p(n),a(!0)},[]),m={columns:[{title:"S.N.",key:"key"},{title:"Receiver Name",key:"name"},{title:"Age",key:"age"},{title:"Blood Group",key:"bloodGroup"},{title:"Receive Date",key:"donationDate"},{title:"Expiry Date",key:"expiryDate"},{title:"Contact No",key:"contact"},{title:"Action",key:"action"}],rows:B.get(r,"data.receiver",[]).map((n,j)=>{var f,R,A,w,D,k,O,C,S;return{key:j+1,name:((f=n==null?void 0:n.receiverDetails)==null?void 0:f.name)||"N/A",age:((A=(R=n==null?void 0:n.receiverDetails)==null?void 0:R.age)==null?void 0:A.toString())||"N/A",gender:((w=n==null?void 0:n.receiverDetails)==null?void 0:w.gender)||"N/A",contact:((D=n==null?void 0:n.receiverDetails)==null?void 0:D.contact)||"N/A",bloodGroup:n.bloodGroup||"N/A",donationDate:((k=n.bloodReceivingDetails)==null?void 0:k.donationDate)||((O=n.organReceivingDetails)==null?void 0:O.donationDate)||"N/A",expiryDate:((C=n.bloodReceivingDetails)==null?void 0:C.expiryDate)||((S=n.organReceivingDetails)==null?void 0:S.expiryDate)||"N/A",action:e.jsx("div",{className:"relative",children:e.jsx(V,{onShow:()=>M(n._id),onEdit:()=>b(n._id),onDelete:async()=>{p(n._id),await d()}})})}})};return e.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[e.jsx(q,{headerTitle:"Receiver List",onSearch:!0,onContactNumber:!0,onFilter:!0,button:!0,toSearch:"Receiver Name",buttonText:"Add Receiver",buttonAction:G,onSearchFunc:n=>h({...t,search:n})}),e.jsxs("div",{className:"bg-white rounded-md",children:[e.jsx(U,{tabs:["Blood Receiver","Organ Receiver"],defaultTab:l,onTabChange:n=>{v(n),c(j=>({...j,page:1}))}}),e.jsx(Q,{columns:m.columns,rows:m.rows,loading:!1,pagination:{currentPage:s.page,totalPage:B.get(r,"data.pagination.pages",1),limit:s.limit,onClick:n=>{n.page&&c({...s,page:n.page}),n.limit&&c({...s,limit:n.limit})}}}),g&&e.jsx(_,{data:g,setOpen:a,open:x})]})]})};export{z as default};
