import{a5 as S,aM as x,br as w,da as j,db as N,ad as b,a2 as s,af as f,ag as T,aj as v,dc as C,aU as E,dd as _,bs as k,de as m,df as P,aQ as I,ai as $}from"./index-ClX9RVH0.js";const U=({queryProps:i})=>{var p,g,u;const[e,o]=S.useState({limit:5,page:1}),d=x({search:i.search,"departmentDetails.department":i.selectedDepartment,"departmentDetails.speciality":i.selectedSpecialist,...i.search?{}:{page:e.page,limit:e.limit}}),{data:n}=w(d),c=(p=n==null?void 0:n.data)==null?void 0:p.departmentCategory,{mutateAsync:l}=j(),h=async(a,t)=>{try{await l({_id:a,entityData:{...t,isActive:!(t!=null&&t.isActive)}})}catch(D){console.error("Failed to update status:",D)}},{mutateAsync:y}=N(),A=b(),r={columns:[{title:"Department Id",key:"id"},{title:"Department Name",key:"name"},{title:"Action",key:"action"}],rows:c==null?void 0:c.map(a=>{var t;return{id:`D-${(t=a==null?void 0:a._id)==null?void 0:t.slice(-5)}`,name:a==null?void 0:a.name,action:s.jsx(f,{onEdit:()=>{A(`${T.SETTINGSADDDEPARTMENT}/${a==null?void 0:a._id}/Department`)},switchStatus:a==null?void 0:a.isActive,onApiSwitch:()=>{h(a._id??"",a)},onDelete:async()=>{await y({id:JSON.stringify([a==null?void 0:a._id])})}})}})};return s.jsx("div",{children:s.jsx(v,{columns:r.columns,rows:r.rows,loading:!1,pagination:{currentPage:e.page,totalPage:(u=(g=n==null?void 0:n.data)==null?void 0:g.pagination)==null?void 0:u.pages,limit:e.limit,onClick:a=>{a.page&&o({...e,page:a.page}),a.limit&&o({...e,limit:a.limit})}}})})},G=({queryProps:i})=>{var p,g,u;const[e,o]=S.useState({limit:5,page:1}),d=x({search:i.search,"departmentDetails.department":i.selectedDepartment,"departmentDetails.speciality":i.selectedSpecialist,...i.search?{}:{page:e.page,limit:e.limit}}),{mutateAsync:n}=C(),c=async(a,t)=>{try{await n({_id:a,entityData:{...t,isActive:!(t!=null&&t.isActive)}})}catch(D){console.error("Failed to update status:",D)}},{data:l}=E(d),h=(p=l==null?void 0:l.data)==null?void 0:p.departments,{mutateAsync:y}=_(),A=b(),r={columns:[{title:"Department Id",key:"id"},{title:"Department Name",key:"name"},{title:"Upper Hirachy",key:"upper"},{title:"Action",key:"action"}],rows:h==null?void 0:h.map(a=>{var t,D;return{id:`D-${(t=a==null?void 0:a._id)==null?void 0:t.slice(-5)}`,name:a==null?void 0:a.name,upper:(D=a==null?void 0:a.upperHirachy)==null?void 0:D.name,action:s.jsx(f,{onEdit:()=>{A(`${T.SETTINGSADDDEPARTMENT}/${a==null?void 0:a._id}/Sub Department`)},switchStatus:a==null?void 0:a.isActive,onApiSwitch:()=>{c(a._id??"",a)},onDelete:async()=>{await y({id:JSON.stringify([a==null?void 0:a._id])})}})}})};return s.jsx("div",{children:s.jsx(v,{columns:r.columns,rows:r.rows,loading:!1,pagination:{currentPage:e.page,totalPage:(u=(g=l==null?void 0:l.data)==null?void 0:g.pagination)==null?void 0:u.pages,limit:e.limit,onClick:a=>{a.page&&o({...e,page:a.page}),a.limit&&o({...e,limit:a.limit})}}})})},M=({queryProps:i})=>{var p,g,u;const[e,o]=S.useState({limit:5,page:1}),d=x({search:i.search,"departmentDetails.department":i.selectedDepartment,"departmentDetails.speciality":i.selectedSpecialist,...i.search?{}:{page:e.page,limit:e.limit}}),{data:n}=k(d),c=(p=n==null?void 0:n.data)==null?void 0:p.departmentSubCat,{mutateAsync:l}=m(),{mutateAsync:h}=P(),y=async(a,t)=>{try{await l({_id:a,entityData:{...t,isActive:!(t!=null&&t.isActive)}})}catch(D){console.error("Failed to update status:",D)}},A=b(),r={columns:[{title:"Department Id",key:"id"},{title:"Department Name",key:"name"},{title:"Action",key:"action"}],rows:c==null?void 0:c.map(a=>{var t;return{id:`D-${(t=a==null?void 0:a._id)==null?void 0:t.slice(-5)}`,name:a==null?void 0:a.name,action:s.jsx(f,{onEdit:()=>{A(`${T.SETTINGSADDDEPARTMENT}/${a==null?void 0:a._id}/Specialists`)},switchStatus:a==null?void 0:a.isActive,onApiSwitch:()=>{y(a._id??"",a)},onDelete:async()=>{await h({id:JSON.stringify([a==null?void 0:a._id])})}})}})};return s.jsx("div",{children:s.jsx(v,{columns:r.columns,rows:r.rows,loading:!1,pagination:{currentPage:e.page,totalPage:(u=(g=n==null?void 0:n.data)==null?void 0:g.pagination)==null?void 0:u.pages,limit:e.limit,onClick:a=>{a.page&&o({...e,page:a.page}),a.limit&&o({...e,limit:a.limit})}}})})},R=()=>{const i=b(),[e,o]=S.useState(()=>sessionStorage.getItem("departmentConfigTab")||"Department"),[d,n]=S.useState({selectedDepartment:"",selectedSpecialist:"",search:""});S.useEffect(()=>{sessionStorage.setItem("departmentConfigTab",e)},[e]);const c=()=>{switch(e){case"Department":return s.jsx(U,{queryProps:d});case"Sub Department":return s.jsx(G,{queryProps:d});case"Specialists":return s.jsx(M,{queryProps:d})}};return s.jsxs("div",{className:"",children:[s.jsx(I,{onSearchFunc:l=>{n({...d,search:l})},onSearch:!0,button:!0,headerTitle:"Department Configuration",buttonText:"Add Department",buttonAction:()=>i(T.SETTINGSADDDEPARTMENT)}),s.jsxs("div",{className:"bg-white pt-4 mt-4",children:[s.jsx("div",{className:"px-4",children:s.jsx($,{tabs:["Department","Sub Department","Specialists"],defaultTab:e,onTabChange:l=>{o(l)}})}),s.jsx("div",{className:"flex-1",children:c()})]})]})};export{R as default};
