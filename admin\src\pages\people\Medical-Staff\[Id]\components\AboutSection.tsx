import React from "react";

interface AboutSectionProps {
  about: {
    description: string;
    specializedIn?: string[];
  } | null;
}

const AboutSection: React.FC<AboutSectionProps> = ({ about }) => {
  if (!about) {
    return (
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="px-4 py-3 border-b">
          <h2 className="text-base font-semibold text-blue">About</h2>
        </div>
        <div className="p-4">
          <p className="text-gray-500 text-sm">No information available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="px-4 py-3 border-b">
        <h2 className="text-base font-semibold text-blue">About</h2>
      </div>
      <div className="p-4 space-y-4">
        <p className="text-gray-900 leading-relaxed text-sm">
          {about.description}
        </p>
        {/* 
          {about.specializedIn && about.specializedIn.length > 0 && (
            <div>
              <h3 className="text-xs font-medium text-gray-900 uppercase tracking-wide mb-2">
                Specialized In
              </h3>
              <div className="flex flex-wrap gap-2">
                {about.specializedIn.map((specialty, index) => (
                  <span
                    key={index}
                    className="bg-blue text-white px-2 py-1 rounded text-xs font-medium"
                  >
                    {specialty}
                  </span>
                ))}
              </div>
            </div>
          )} */}
      </div>
    </div>
  );
};

export default AboutSection;
