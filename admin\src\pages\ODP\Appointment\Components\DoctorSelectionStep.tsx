import React, { useState, useEffect, useMemo } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { DropdownField } from "../../../../components/DropDownField";
import { InputField } from "../../../../components/Input-Field";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";
import { useGetUser } from "../../../../server-action/api/user";
import { useGetAllDepartments } from "../../../../server-action/api/departmentApi";
import { useGetShiftAssign } from "../../../../server-action/api/shiftAssignApi";
import { userRole } from "../../../../constant/constant";

// Helper function to get current date
const getCurrentDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const doctorValidationSchema = yup.object().shape({
  department: yup.string().required("Department is required"),
  doctor: yup.string().required("Doctor is required"),
  appointmentDate: yup.string().required("Appointment date is required"),
  availableTimeSlot: yup.string().required("Time slot is required"),
});

interface DoctorSelectionStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const DoctorSelectionStep: React.FC<DoctorSelectionStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const { data: departmentData } = useGetAllDepartments() as any;
  const departmentList = departmentData?.data?.departmentCategory;
  const departmentDropdown =
    departmentList?.map((item: any) => ({
      label: item?.name,
      value: item?._id,
    })) || [];

  const { data: userDoctorDetails } = useGetUser(
    { role: userRole.DOCTOR },
    {
      enabled: true,
      refetchOnMount: true,
      refetchOnWindowFocus: true,
    }
  ) as any;

  const [filteredDoctors, setFilteredDoctors] = useState([]);

  const getDoctorsArray = () => {
    if (Array.isArray(userDoctorDetails)) {
      return userDoctorDetails;
    }
    if (
      userDoctorDetails?.data?.users &&
      Array.isArray(userDoctorDetails.data.users)
    ) {
      return userDoctorDetails.data.users;
    }
    return [];
  };

  const formik = useFormik({
    initialValues: {
      department: initialData?.department || "",
      doctor: initialData?.doctor || "",
      appointmentDate: initialData?.appointmentDate || getCurrentDate(),
      availableTimeSlot: initialData?.availableTimeSlot || "",
      doctorDutyTime: initialData?.doctorDutyTime || "",
    },
    enableReinitialize: true,
    validationSchema: doctorValidationSchema,
    onSubmit: (values) => {
      // Get display names for StepCards
      const selectedDepartment = departmentDropdown?.find(
        (dept: any) => dept.value === values.department
      );
      const selectedDoctor = (filteredDoctors as any[])?.find(
        (doc: any) => doc.value === values.doctor
      );

      // Determine if this department is IPD or OPD
      const departmentType = selectedDepartment?.label
        ?.toLowerCase()
        .includes("ipd")
        ? "IPD"
        : "OPD";

      // Get the selected time slot info
      const selectedTimeSlot = timeSlotOptions.find(
        (slot: any) => slot.value === values.availableTimeSlot
      );

      const enhancedData = {
        ...values,
        departmentName: selectedDepartment?.label || values.department,
        doctorName: selectedDoctor?.label || values.doctor,
        ipdOpd: departmentType,
        timeSlotDepartment: selectedTimeSlot?.department || departmentType,
        timeSlotLabel:
          selectedTimeSlot?.originalLabel || values.availableTimeSlot,
      };

      onNext(enhancedData);
    },
  });

  const { values, setFieldValue } = formik;

  const { data: AllShiftDate } = useGetShiftAssign(
    {},
    {
      enabled: Boolean(values.appointmentDate),
      refetchOnMount: true,
      refetchOnWindowFocus: true,
    }
  ) as any;

  const shiftList = AllShiftDate?.data?.shiftassigned ?? [];
  console.log(shiftList, "shfitlist");

  // Get selected department type (IPD or OPD)
  const selectedDepartmentType = useMemo(() => {
    if (!values.department) return null;

    const selectedDept = departmentDropdown?.find(
      (dept: any) => dept.value === values.department
    );

    console.log("Selected Department:", selectedDept); // Debug log

    // More flexible department type detection
    const deptName = selectedDept?.label?.toLowerCase() || "";
    if (deptName.includes("ipd") || deptName.includes("inpatient")) {
      return "IPD";
    } else if (deptName.includes("opd") || deptName.includes("outpatient")) {
      return "OPD";
    }

    // If you want to show both IPD and OPD when department type is unclear, return null
    return null; // This will show both IPD and OPD slots
  }, [values.department, departmentDropdown]);

  // Filter doctors based on appointment date, department, and shifts
  useEffect(() => {
    const doctorsArray = getDoctorsArray();
    if (!values.appointmentDate || !doctorsArray.length) {
      setFilteredDoctors([]);
      return;
    }

    // If department is selected, filter by department type
    let filteredByDepartment = shiftList;
    if (selectedDepartmentType) {
      filteredByDepartment = shiftList.filter((entry: any) =>
        entry.shiftAssignment.some(
          (sa: any) =>
            sa.date === values.appointmentDate &&
            sa.department === selectedDepartmentType
        )
      );
    } else {
      // If no department selected, show doctors with shifts on the selected date
      filteredByDepartment = shiftList.filter((entry: any) =>
        entry.shiftAssignment.some(
          (sa: any) => sa.date === values.appointmentDate
        )
      );
    }

    const userIdsWithShiftOnDate = filteredByDepartment.map(
      (entry: any) => entry.user?._id
    );

    const options = doctorsArray
      .filter((doc: any) => userIdsWithShiftOnDate.includes(doc._id))
      .map((doc: any) => ({
        value: doc._id,
        label: doc?.commonInfo?.personalInfo?.fullName,
      }));

    setFilteredDoctors(options);

    // Reset doctor selection if current doctor is not in filtered list
    if (
      values.doctor &&
      !options.some((opt: any) => opt.value === values.doctor)
    ) {
      setFieldValue("doctor", "");
      setFieldValue("availableTimeSlot", "");
      setFieldValue("doctorDutyTime", "");
    }
  }, [
    values.appointmentDate,
    values.department,
    selectedDepartmentType,
    userDoctorDetails?.data,
    shiftList,
  ]);

  const { data: shiftAssignData } = useGetShiftAssign(
    values.doctor ? { user: values.doctor } : undefined,
    {
      enabled: Boolean(values.doctor),
      refetchOnMount: true,
      refetchOnWindowFocus: true,
    }
  ) as any;

  const flattenedShiftAssignments = useMemo(() => {
    if (!shiftAssignData?.data?.shiftassigned) return [];

    return shiftAssignData.data.shiftassigned.flatMap((entry: any) => {
      return (
        entry?.shiftAssignment?.flatMap(
          (sa: any) =>
            sa?.shifts?.map((shift: any) => ({
              userId: entry.user?._id,
              date: sa?.date,
              department: sa?.department,
              shiftId: shift?.shift,
              startTime: shift?.startTime,
              endTime: shift?.endTime,
              slots: shift?.slots,
            })) || []
        ) || []
      );
    });
  }, [shiftAssignData]);

  // Filter time slots by selected department
  const filteredTimeSlots = useMemo(() => {
    if (
      !values.appointmentDate ||
      !values.department ||
      !flattenedShiftAssignments.length
    )
      return [];

    const padDate = (dateStr: string) => {
      if (!dateStr) return "";
      const [year, month, day] = dateStr.split("-");
      return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
    };

    const selectedDate = padDate(values.appointmentDate.toString());

    // Get the selected department info
    const selectedDepartment = departmentDropdown?.find(
      (dept: any) => dept.value === values.department
    );

    console.log("Selected Department:", selectedDepartment);
    console.log("Flattened Shift Assignments:", flattenedShiftAssignments);

    // Show BOTH IPD and OPD time slots - don't filter by department type
    console.log("Showing both IPD and OPD time slots for selected date");

    // Filter time slots by date ONLY - show both IPD and OPD
    return flattenedShiftAssignments
      .filter((item: any) => {
        const matchesDate = item?.date && padDate(item.date) === selectedDate;

        console.log(
          "Shift item:",
          item,
          "Matches date:",
          matchesDate,
          "Department:",
          item?.department
        );

        return matchesDate; // Only filter by date, show both IPD and OPD
      })
      .flatMap(
        (item: any) =>
          item?.slots?.filter((slot: any) => slot?.isAvailiable) || []
      );
  }, [
    values.appointmentDate,
    values.department,
    flattenedShiftAssignments,
    departmentDropdown,
  ]);

  const timeSlotOptions =
    filteredTimeSlots?.map((slot: any) => {
      const slotWithDept = flattenedShiftAssignments.find((item: any) =>
        item?.slots?.some((s: any) => s?.timeRange === slot?.timeRange)
      );
      const department = slotWithDept?.department || "OPD";

      return {
        label: `${slot?.timeRange || "No time specified"} (${department})`,
        value: slot?.timeRange || "",
        department: department,
        originalLabel: slot?.timeRange || "No time specified",
      };
    }) || [];

  // Handle department change
  const handleDepartmentChange = (e: any) => {
    setFieldValue("department", e.target.value);
    // Reset dependent fields when department changes
    setFieldValue("doctor", "");
    setFieldValue("availableTimeSlot", "");
    setFieldValue("doctorDutyTime", "");
  };

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-blue/10 rounded-full flex items-center justify-center">
            <Icon icon="mdi:doctor" className="w-5 h-5" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Doctor Selection
            </h2>
            <p className="text-sm text-gray-600">
              Choose your preferred department, doctor, and appointment time
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-2 gap-5 mx-auto justify-between items-start">
            <div className="grid grid-cols-2 gap-4 items-center">
              <div>
                <DropdownField
                  required
                  label="Department"
                  options={departmentDropdown || []}
                  firstInput="Select Department"
                  name="department"
                  value={values.department}
                  onChange={handleDepartmentChange}
                />
              </div>

              <div>
                <DropdownField
                  required
                  label="Choose Doctor"
                  options={filteredDoctors}
                  firstInput="Select Doctor"
                  name="doctor"
                  value={values.doctor}
                  onChange={(e: any) => {
                    setFieldValue("doctor", e.target.value);
                    // Reset time slot when doctor changes
                    setFieldValue("availableTimeSlot", "");
                    setFieldValue("doctorDutyTime", "");
                  }}
                  disabled={!values.department || !values.appointmentDate}
                />
              </div>

              <div>
                <InputField
                  type="date"
                  required
                  label="Appointment Date"
                  placeholder="yy/mm/dd"
                  name="appointmentDate"
                  min={getCurrentDate()}
                  onChange={(e: any) => {
                    setFieldValue("appointmentDate", e.target.value);
                    setFieldValue("doctor", "");
                    setFieldValue("availableTimeSlot", "");
                    setFieldValue("doctorDutyTime", "");
                  }}
                  value={values.appointmentDate}
                />
              </div>

              <div>
                <InputField
                  required
                  type="time"
                  label="Doctor Duty Time"
                  placeholder="Time"
                  name="doctorDutyTime"
                  value={values.doctorDutyTime || ""}
                  disabled
                />
              </div>
            </div>

            <div className="">
              <label className="block text-sm text-black">
                Available Times <span className="text-red">*</span>
              </label>
              <div className="grid grid-cols-6 gap-3 border p-2 rounded-sm">
                {timeSlotOptions.length > 0 ? (
                  timeSlotOptions.map((slot: any) => (
                    <button
                      key={slot.value}
                      type="button"
                      onClick={() => {
                        setFieldValue("availableTimeSlot", slot.value);
                        const [startTime] = slot.value.split("-");
                        setFieldValue("doctorDutyTime", startTime.trim());
                      }}
                      className={`border rounded px-1 py-1 text-sm border-[#116AEF] font-medium ${
                        values.availableTimeSlot === slot.value
                          ? "bg-[#E9F2FF] text-black border-blue-600"
                          : " text-black"
                      }`}
                    >
                      {slot.label}
                    </button>
                  ))
                ) : (
                  <p className="text-sm text-gray-500 col-span-6">
                    {!values.department
                      ? "Please select a department first"
                      : !values.doctor
                      ? "Please select a doctor first"
                      : "No available time slots"}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-6">
            {showBack && (
              <Button
                type="button"
                onClick={onBack}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Icon icon="mdi:arrow-left" className="w-4 h-4" />
                <span>Back</span>
              </Button>
            )}

            <Button
              type="submit"
              variant="outline"
              className="flex items-center gap-2"
              disabled={
                !values.department ||
                !values.doctor ||
                !values.availableTimeSlot
              }
            >
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
              <span>Next</span>
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default DoctorSelectionStep;
