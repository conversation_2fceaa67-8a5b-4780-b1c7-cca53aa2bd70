import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { InputField } from "../../../../components/Input-Field";
import { DropdownField } from "../../../../components/DropDownField";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

// Mock data - replace with real API data
const departmentOptions = [
  { value: "cardiology", label: "Cardiology" },
  { value: "neurology", label: "Neurology" },
  { value: "orthopedics", label: "Orthopedics" },
  { value: "pediatrics", label: "Pediatrics" },
  { value: "general", label: "General Medicine" },
];

const doctorOptions = [
  { value: "dr-smith", label: "Dr. <PERSON>" },
  { value: "dr-johnson", label: "<PERSON>. <PERSON>" },
  { value: "dr-brown", label: "<PERSON>. <PERSON>" },
  { value: "dr-davis", label: "<PERSON>. <PERSON>" },
];

const timeSlotOptions = [
  { value: "09:00-09:30", label: "09:00 AM - 09:30 AM" },
  { value: "09:30-10:00", label: "09:30 AM - 10:00 AM" },
  { value: "10:00-10:30", label: "10:00 AM - 10:30 AM" },
  { value: "10:30-11:00", label: "10:30 AM - 11:00 AM" },
  { value: "11:00-11:30", label: "11:00 AM - 11:30 AM" },
  { value: "11:30-12:00", label: "11:30 AM - 12:00 PM" },
];

// No validation for now
const doctorValidationSchema = yup.object().shape({});

// Helper function to get current date
const getCurrentDate = () => {
  const today = new Date();
  return today.toISOString().split("T")[0];
};

interface DoctorSelectionStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const DoctorSelectionStep: React.FC<DoctorSelectionStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const formik = useFormik({
    initialValues: {
      department: initialData?.department || "",
      doctor: initialData?.doctor || "",
      appointmentDate: initialData?.appointmentDate || "",
      availableTimeSlot: initialData?.availableTimeSlot || "",
      doctorDutyTime: initialData?.doctorDutyTime || "",
      availablestatus: initialData?.availablestatus || "None",
      totalCharge: initialData?.totalCharge || "",
      resellerName: initialData?.resellerName || "none",
      remark: initialData?.remark || "",
    },
    enableReinitialize: true,
    validationSchema: doctorValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue, getFieldProps } = formik;

  // Mock departments and doctors data
  const departments = [
    "Cardiology",
    "Neurology",
    "Orthopedics",
    "Pediatrics",
    "General Medicine",
  ];

  const doctors = [
    { id: "1", name: "Dr. John Smith", department: "Cardiology" },
    { id: "2", name: "Dr. Sarah Johnson", department: "Neurology" },
    { id: "3", name: "Dr. Michael Brown", department: "Orthopedics" },
    { id: "4", name: "Dr. Emily Davis", department: "Pediatrics" },
    { id: "5", name: "Dr. Robert Wilson", department: "General Medicine" },
  ];

  const filteredDoctors = doctorOptions.filter(
    (doctor) => !values.department || doctor.value.includes(values.department)
  );

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:doctor" className="w-5 h-5 text-red-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Doctor Selection
            </h2>
            <p className="text-sm text-gray-600">
              Choose your preferred department, doctor, and appointment time
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
            {/* Department */}
            <div>
              <DropdownField
                label="Department"
                options={departmentOptions}
                firstInput="Select Department"
                name="department"
                value={values.department}
                onChange={(e: any) =>
                  setFieldValue("department", e.target.value)
                }
              />
            </div>

            {/* Choose Doctor */}
            <div>
              <DropdownField
                label="Choose Doctor"
                options={filteredDoctors}
                firstInput="Select Doctor"
                name="doctor"
                value={values.doctor}
                onChange={(e: any) => setFieldValue("doctor", e.target.value)}
                disabled={!values.appointmentDate}
              />
            </div>

            {/* Available Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Available Status
              </label>
              <select
                value={formData.availableStatus}
                onChange={(e) =>
                  handleInputChange("availableStatus", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              >
                <option value="None">None</option>
                <option value="Available">Available</option>
                <option value="Busy">Busy</option>
                <option value="On Leave">On Leave</option>
              </select>
            </div>

            {/* Appointment Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Appointment Date *
              </label>
              <input
                type="date"
                value={formData.appointmentDate}
                onChange={(e) =>
                  handleInputChange("appointmentDate", e.target.value)
                }
                className="w-full px-3 py -2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Available Times */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Available Times
            </label>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
              {availableTimes.map((time) => (
                <button
                  key={time}
                  type="button"
                  onClick={() => setSelectedTime(time)}
                  className={`px-4 py-2 text-sm font-medium rounded-md border transition-colors ${
                    selectedTime === time
                      ? "bg-red-500 text-white border-red-500"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  {time}
                </button>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between mt-8">
            <Button
              type="button"
              onClick={onBack}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Icon icon="mdi:arrow-left" className="w-4 h-4" />
              <span>Back</span>
            </Button>

            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            >
              <span>Next</span>
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default DoctorSelectionStep;
