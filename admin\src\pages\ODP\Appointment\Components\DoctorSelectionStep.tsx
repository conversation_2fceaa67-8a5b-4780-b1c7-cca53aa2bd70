"use client";

import React, { useState, useEffect, useMemo } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { DropdownField } from "../../../../components/DropDownField";
import { InputField } from "../../../../components/Input-Field";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";
import { useGetUser } from "../../../../server-action/api/user";
import { useGetAllDepartments } from "../../../../server-action/api/departmentApi";
import { useGetShiftAssign } from "../../../../server-action/api/shiftAssignApi";
import { userRole } from "../../../../constant/constant";

const getCurrentDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const doctorValidationSchema = yup.object().shape({
  department: yup.string().required("Department is required"),
  doctor: yup.string().required("Doctor is required"),
  appointmentDate: yup.string().required("Appointment date is required"),
  availableTimeSlot: yup.string().required("Time slot is required"),
});

const DoctorSelectionStep = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const { data: departmentData } = useGetAllDepartments();
  const departmentList = departmentData?.data?.departmentCategory;
  const departmentDropdown =
    departmentList?.map((item) => ({ label: item?.name, value: item?._id })) ||
    [];

  const { data: userDoctorDetails } = useGetUser({ role: userRole.DOCTOR });
  const [filteredDoctors, setFilteredDoctors] = useState<any[]>([]);
  const [allDoctors, setAllDoctors] = useState<any[]>([]); // All doctors for doctor-first selection

  const getDoctorsArray = () => {
    if (Array.isArray(userDoctorDetails)) return userDoctorDetails;
    if (Array.isArray(userDoctorDetails?.data?.users))
      return userDoctorDetails.data.users;
    return [];
  };

  const formik = useFormik({
    initialValues: {
      department: initialData?.department || "",
      doctor: initialData?.doctor || "",
      appointmentDate: initialData?.appointmentDate || getCurrentDate(),
      availableTimeSlot: initialData?.availableTimeSlot || "",
      doctorDutyTime: initialData?.doctorDutyTime || "",
    },
    enableReinitialize: true,
    validationSchema: doctorValidationSchema,
    onSubmit: (values) => {
      const selectedDepartment = departmentDropdown?.find(
        (dept) => dept.value === values.department
      );
      const selectedDoctor = filteredDoctors?.find(
        (doc) => doc.value === values.doctor
      );

      const departmentType = selectedDepartment?.label
        ?.toLowerCase()
        .includes("ipd")
        ? "IPD"
        : "OPD";

      const selectedTimeSlot = timeSlotOptions.find(
        (slot) => slot.value === values.availableTimeSlot
      );

      const enhancedData = {
        ...values,
        departmentName: selectedDepartment?.label || values.department,
        doctorName: selectedDoctor?.label || values.doctor,
        ipdOpd: departmentType,
        timeSlotDepartment: selectedTimeSlot?.department || departmentType,
        timeSlotLabel:
          selectedTimeSlot?.originalLabel || values.availableTimeSlot,
      };

      onNext(enhancedData);
    },
  });

  const { values, setFieldValue } = formik;
  const { data: AllShiftDate } = useGetShiftAssign();
  const shiftList = AllShiftDate?.data?.shiftassigned ?? [];

  const selectedDepartmentType = useMemo(() => {
    const selectedDept = departmentDropdown?.find(
      (dept) => dept.value === values.department
    );
    const deptName = selectedDept?.label?.toLowerCase() || "";
    if (deptName.includes("ipd") || deptName.includes("inpatient"))
      return "IPD";
    if (deptName.includes("opd") || deptName.includes("outpatient"))
      return "OPD";
    return null;
  }, [values.department, departmentDropdown]);

  // Populate all doctors on component mount
  useEffect(() => {
    const doctorsArray = getDoctorsArray();
    if (doctorsArray.length) {
      const allDoctorOptions = doctorsArray.map((doc: any) => ({
        value: doc._id,
        label: doc?.commonInfo?.personalInfo?.fullName,
        department: doc?.department, // Include department info
      }));
      setAllDoctors(allDoctorOptions);
    }
  }, [userDoctorDetails]);

  useEffect(() => {
    const doctorsArray = getDoctorsArray();
    if (!values.appointmentDate || !doctorsArray.length) {
      setFilteredDoctors([]);
      return;
    }
    let filteredByDepartment = shiftList;
    if (selectedDepartmentType) {
      filteredByDepartment = shiftList.filter((entry) =>
        entry.shiftAssignment.some(
          (sa) =>
            sa.date === values.appointmentDate &&
            sa.department === selectedDepartmentType
        )
      );
    } else {
      filteredByDepartment = shiftList.filter((entry) =>
        entry.shiftAssignment.some((sa) => sa.date === values.appointmentDate)
      );
    }
    const userIds = filteredByDepartment.map((entry) => entry.user?._id);
    const options = doctorsArray
      .filter((doc) => userIds.includes(doc._id))
      .map((doc) => ({
        value: doc._id,
        label: doc?.commonInfo?.personalInfo?.fullName,
      }));
    setFilteredDoctors(options);
    if (values.doctor && !options.some((opt) => opt.value === values.doctor)) {
      setFieldValue("doctor", "");
      setFieldValue("availableTimeSlot", "");
      setFieldValue("doctorDutyTime", "");
    }
  }, [
    values.appointmentDate,
    values.department,
    selectedDepartmentType,
    userDoctorDetails?.data,
    shiftList,
  ]);

  const { data: shiftAssignData } = useGetShiftAssign(
    values.doctor ? { user: values.doctor } : undefined
  );

  const filteredTimeSlots = useMemo(() => {
    // Require BOTH doctor and appointment date to be selected
    if (
      !(shiftAssignData as any)?.data?.shiftassigned ||
      !values.appointmentDate ||
      !values.doctor
    )
      return [];
    const padDate = (dateStr: string) => {
      const [year, month, day] = dateStr.split("-");
      return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
    };
    const selectedDate = padDate(values.appointmentDate);
    const slots = (shiftAssignData as any)?.data?.shiftassigned?.flatMap(
      (entry: any) =>
        entry?.shiftAssignment?.flatMap(
          (sa: any) =>
            sa?.shifts?.flatMap(
              (shift: any) =>
                shift?.slots?.map((slot: any) => ({
                  ...slot,
                  timeRange: slot?.timeRange,
                  department: sa?.department,
                  userId: entry.user?._id,
                  date: sa?.date,
                  shiftId: shift?.shift,
                })) || []
            ) || []
        ) || []
    );

    // Remove duplicates based on timeRange + department
    return Array.from(
      new Map(
        slots
          .filter(
            (item) =>
              item?.date &&
              padDate(item.date) === selectedDate &&
              item?.isAvailiable
          )
          .map((slot) => [`${slot.timeRange}-${slot.department}`, slot])
      ).values()
    );
  }, [shiftAssignData, values.appointmentDate, values.doctor]);

  const timeSlotOptions = useMemo(() => {
    return filteredTimeSlots.map((slot) => ({
      label: `${slot.timeRange} (${slot.department})`,
      value: `${slot.timeRange}-${slot.department}`,
      department: slot.department,
      originalLabel: slot.timeRange,
    }));
  }, [filteredTimeSlots]);

  return (
    <FormikProvider value={formik}>
      <div>
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-blue/10 rounded-full flex items-center justify-center">
            <Icon icon="mdi:doctor" className="w-5 h-5" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Doctor Selection
            </h2>
            <p className="text-sm text-gray-600">
              Choose your preferred department, doctor, and appointment time
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-2 gap-5 mx-auto justify-between items-start">
            <div className="grid grid-cols-2 gap-4 items-center">
              <DropdownField
                required
                label="Department"
                options={departmentDropdown}
                firstInput={
                  values.doctor
                    ? "Auto-filled from Doctor"
                    : "Select Department"
                }
                name="department"
                value={values.department}
                onChange={(e) => {
                  setFieldValue("department", e.target.value);
                  setFieldValue("doctor", "");
                  setFieldValue("availableTimeSlot", "");
                  setFieldValue("doctorDutyTime", "");
                }}
                disabled={!!values.doctor} // Disable when doctor is selected
              />
              <DropdownField
                required
                label="Choose Doctor"
                options={allDoctors} // Use all doctors, not filtered
                firstInput="Select Doctor"
                name="doctor"
                value={values.doctor}
                onChange={(e) => {
                  const selectedDoctorId = e.target.value;
                  setFieldValue("doctor", selectedDoctorId);

                  // Auto-fill department based on selected doctor
                  const selectedDoctor = allDoctors?.find(
                    (doc: any) => doc.value === selectedDoctorId
                  );
                  if (selectedDoctor?.department) {
                    setFieldValue("department", selectedDoctor.department);
                  }

                  // Reset dependent fields
                  setFieldValue("availableTimeSlot", "");
                  setFieldValue("doctorDutyTime", "");
                }}
                // No disabled condition - doctor can be selected first
              />
              <InputField
                type="date"
                required
                label="Appointment Date"
                placeholder="yy/mm/dd"
                name="appointmentDate"
                min={getCurrentDate()}
                onChange={(e) => {
                  setFieldValue("appointmentDate", e.target.value);
                  setFieldValue("doctor", "");
                  setFieldValue("availableTimeSlot", "");
                  setFieldValue("doctorDutyTime", "");
                }}
                value={values.appointmentDate}
              />
              <InputField
                required
                type="time"
                label="Doctor Duty Time"
                name="doctorDutyTime"
                value={values.doctorDutyTime || ""}
                disabled
              />
            </div>

            <div>
              <label className="block text-sm text-black">
                Available Times <span className="text-red">*</span>
              </label>
              {["IPD", "OPD"].map((dept) => {
                const slots = timeSlotOptions.filter(
                  (slot) => slot.department?.toUpperCase() === dept
                );
                return slots.length > 0 ? (
                  <div key={dept} className="mb-4">
                    <h4 className="font-semibold text-sm text-gray-700 mb-2">
                      {dept} Slots
                    </h4>
                    <div className="grid grid-cols-6 gap-2 border p-2 rounded-sm">
                      {slots.map((slot) => (
                        <button
                          key={slot.value}
                          type="button"
                          onClick={() => {
                            setFieldValue("availableTimeSlot", slot.value);
                            const [startTime] = slot.originalLabel.split("-");
                            setFieldValue("doctorDutyTime", startTime.trim());
                          }}
                          className={`border rounded px-1 py-1 text-sm border-[#116AEF] font-medium ${
                            values.availableTimeSlot === slot.value
                              ? "bg-[#E9F2FF] text-black border-blue-600"
                              : " text-black"
                          }`}
                        >
                          {slot.originalLabel}
                        </button>
                      ))}
                    </div>
                  </div>
                ) : null;
              })}
              {timeSlotOptions.length === 0 && (
                <p className="text-sm text-gray-500 mt-2">
                  {!values.doctor
                    ? "Please select a doctor first"
                    : !values.appointmentDate
                    ? "Please select an appointment date"
                    : "No available time slots"}
                </p>
              )}
            </div>
          </div>

          <div className="flex justify-between pt-6">
            {showBack && (
              <Button
                type="button"
                onClick={onBack}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Icon icon="mdi:arrow-left" className="w-4 h-4" />
                <span>Back</span>
              </Button>
            )}
            <Button
              type="submit"
              variant="outline"
              className="flex items-center gap-2"
              onClick={onNext}
            >
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
              <span>Next</span>
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default DoctorSelectionStep;
