import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { InputField } from "../../../../components/Input-Field";
import { DropdownField } from "../../../../components/DropDownField";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

// Mock data - replace with real API data
const departmentOptions = [
  { value: "cardiology", label: "Cardiology" },
  { value: "neurology", label: "Neurology" },
  { value: "orthopedics", label: "Orthopedics" },
  { value: "pediatrics", label: "Pediatrics" },
  { value: "general", label: "General Medicine" },
];

const doctorOptions = [
  { value: "dr-smith", label: "Dr. <PERSON>" },
  { value: "dr-johnson", label: "<PERSON>. <PERSON>" },
  { value: "dr-brown", label: "<PERSON>. <PERSON>" },
  { value: "dr-davis", label: "<PERSON>. <PERSON>" },
];

const timeSlotOptions = [
  { value: "09:00-09:30", label: "09:00 AM - 09:30 AM" },
  { value: "09:30-10:00", label: "09:30 AM - 10:00 AM" },
  { value: "10:00-10:30", label: "10:00 AM - 10:30 AM" },
  { value: "10:30-11:00", label: "10:30 AM - 11:00 AM" },
  { value: "11:00-11:30", label: "11:00 AM - 11:30 AM" },
  { value: "11:30-12:00", label: "11:30 AM - 12:00 PM" },
];

// No validation for now
const doctorValidationSchema = yup.object().shape({});

// Helper function to get current date
const getCurrentDate = () => {
  const today = new Date();
  return today.toISOString().split("T")[0];
};

interface DoctorSelectionStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const DoctorSelectionStep: React.FC<DoctorSelectionStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const formik = useFormik({
    initialValues: {
      department: initialData?.department || "",
      doctor: initialData?.doctor || "",
      appointmentDate: initialData?.appointmentDate || "",
      availableTimeSlot: initialData?.availableTimeSlot || "",
      doctorDutyTime: initialData?.doctorDutyTime || "",
      availablestatus: initialData?.availablestatus || "None",
      totalCharge: initialData?.totalCharge || "",
      resellerName: initialData?.resellerName || "none",
      remark: initialData?.remark || "",
    },
    enableReinitialize: true,
    validationSchema: doctorValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue, getFieldProps } = formik;

  const filteredDoctors = doctorOptions.filter(
    (doctor) => !values.department || doctor.value.includes(values.department)
  );

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:doctor" className="w-5 h-5 text-red-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Doctor Selection
            </h2>
            <p className="text-sm text-gray-600">
              Choose your preferred department, doctor, and appointment time
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
            {/* Department */}
            <div>
              <DropdownField
                label="Department"
                options={departmentOptions}
                firstInput="Select Department"
                name="department"
                value={values.department}
                onChange={(e: any) =>
                  setFieldValue("department", e.target.value)
                }
              />
            </div>
            <div>
              <DropdownField
                label="Choose Doctor"
                options={filteredDoctors}
                firstInput="Select Doctor"
                name="doctor"
                value={values.doctor}
                onChange={(e: any) => setFieldValue("doctor", e.target.value)}
                disabled={!values.appointmentDate}
              />
            </div>
            <div>
              <InputField
                type="date"
                label="Appointment Date"
                placeholder="yy/mm/dd"
                min={getCurrentDate()}
                {...getFieldProps("appointmentDate")}
                onChange={(e: any) => {
                  setFieldValue("appointmentDate", e.target.value);
                  setFieldValue("doctor", "");
                }}
              />
            </div>
            <div>
              <InputField
                type="text"
                label="Available Status"
                placeholder="None"
                {...getFieldProps("availablestatus")}
                disabled
              />
            </div>
            <div>
              <InputField
                type="text"
                label="Total Charge"
                placeholder="Auto"
                {...getFieldProps("totalCharge")}
                disabled
              />
            </div>
            <div>
              <DropdownField
                label="Available Time Slot"
                options={timeSlotOptions}
                firstInput="Select Time Slot"
                name="availableTimeSlot"
                value={values.availableTimeSlot}
                onChange={(e: any) => {
                  const selectedTime = e.target.value;
                  if (selectedTime.includes("-")) {
                    const [startTime] = selectedTime.split("-");
                    setFieldValue("availableTimeSlot", selectedTime);
                    setFieldValue("doctorDutyTime", startTime.trim());
                  } else {
                    setFieldValue("availableTimeSlot", selectedTime);
                    setFieldValue("doctorDutyTime", "");
                  }
                }}
                disabled={!values.doctor}
              />
            </div>
            <div>
              <InputField
                type="time"
                label="Doctor Duty Time"
                placeholder="Time"
                {...getFieldProps("doctorDutyTime")}
                disabled
              />
            </div>
            <div>
              <DropdownField
                label="Referral Name"
                options={[
                  { value: "none", label: "None" },
                  { value: "referral1", label: "Referral 1" },
                  { value: "referral2", label: "Referral 2" },
                ]}
                name="resellerName"
                value={values.resellerName}
                onChange={(e: any) =>
                  setFieldValue("resellerName", e.target.value)
                }
              />
            </div>
            <div className="md:col-span-3">
              <InputField
                type="text"
                label="Remark"
                placeholder="Remarks"
                {...getFieldProps("remark")}
              />
            </div>
          </div>

          <div className="flex justify-between mt-8">
            <Button
              type="button"
              onClick={onBack}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Icon icon="mdi:arrow-left" className="w-4 h-4" />
              <span>Back</span>
            </Button>

            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            >
              <span>Next</span>
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default DoctorSelectionStep;
