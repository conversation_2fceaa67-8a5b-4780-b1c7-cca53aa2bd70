import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { DropdownField } from "../../../../components/DropDownField";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

// Static data for dropdowns
const departmentOptions = [
  { value: "cardiology", label: "Cardiology" },
  { value: "neurology", label: "Neurology" },
  { value: "orthopedics", label: "Orthopedics" },
  { value: "pediatrics", label: "Pediatrics" },
  { value: "general", label: "General Medicine" },
  { value: "emergency", label: "Emergency" },
  { value: "surgery", label: "Surgery" },
  { value: "dermatology", label: "Dermatology" },
];

const doctorOptions = [
  { value: "dr-smith", label: "<PERSON>. <PERSON>" },
  { value: "dr-johnson", label: "<PERSON>. <PERSON>" },
  { value: "dr-brown", label: "<PERSON>. <PERSON>" },
  { value: "dr-davis", label: "<PERSON>. <PERSON>" },
  { value: "dr-wilson", label: "Dr. <PERSON>" },
  { value: "dr-anderson", label: "<PERSON>. <PERSON>" },
  { value: "dr-miller", label: "Dr. <PERSON>" },
  { value: "dr-taylor", label: "Dr. <PERSON>" },
];

const timeSlotOptions = [
  "11:00 AM",
  "11:30 AM",
  "12:00 PM",
  "12:30 PM",
  "01:00 PM",
  "01:30 PM",
  "02:00 PM",
  "02:30 PM",
  "03:00 PM",
  "03:30 PM",
  "04:00 PM",
  "04:30 PM",
  "05:00 PM",
  "05:30 PM",
  "06:00 PM",
];

// No validation for now
const doctorValidationSchema = yup.object().shape({});

// Helper function to get current date
const getCurrentDate = () => {
  const today = new Date();
  return today.toISOString().split("T")[0];
};

interface DoctorSelectionStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const DoctorSelectionStep: React.FC<DoctorSelectionStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const formik = useFormik({
    initialValues: {
      department: initialData?.department || "",
      doctor: initialData?.doctor || "",
      appointmentDate: initialData?.appointmentDate || "",
      availableTimeSlot: initialData?.availableTimeSlot || "",
      doctorDutyTime: initialData?.doctorDutyTime || "",
      availablestatus: initialData?.availablestatus || "None",
      totalCharge: initialData?.totalCharge || "",
      resellerName: initialData?.resellerName || "none",
      remark: initialData?.remark || "",
    },
    enableReinitialize: true,
    validationSchema: doctorValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue, getFieldProps } = formik;

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:doctor" className="w-5 h-5 text-red-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Doctor Selection
            </h2>
            <p className="text-sm text-gray-600">
              Choose your preferred department, doctor, and appointment time
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Department */}
            <div>
              <DropdownField
                label="Department"
                options={departmentOptions}
                firstInput="Patient identity number"
                name="department"
                value={values.department}
                onChange={(e: any) =>
                  setFieldValue("department", e.target.value)
                }
                required
              />
            </div>

            {/* Choose Doctor */}
            <div>
              <DropdownField
                label="Choose Doctor"
                options={doctorOptions}
                firstInput="Select doctor"
                name="doctor"
                value={values.doctor}
                onChange={(e: any) => setFieldValue("doctor", e.target.value)}
                required
              />
            </div>

            {/* Available Status */}
            <div>
              <DropdownField
                label="Available Status"
                options={[
                  { value: "none", label: "None" },
                  { value: "available", label: "Available" },
                  { value: "busy", label: "Busy" },
                ]}
                firstInput="None"
                name="availablestatus"
                value={values.availablestatus}
                onChange={(e: any) =>
                  setFieldValue("availablestatus", e.target.value)
                }
                disabled
              />
            </div>

            {/* Appointment Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Appointment Date *
              </label>
              <input
                type="date"
                placeholder="DD/MM/YYYY"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min={getCurrentDate()}
                {...getFieldProps("appointmentDate")}
                onChange={(e: any) => {
                  setFieldValue("appointmentDate", e.target.value);
                  setFieldValue("doctor", "");
                }}
              />
            </div>
          </div>

          {/* Available Times */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Available Times
            </label>
            <div className="grid grid-cols-5 gap-3">
              {timeSlotOptions.map((slot) => (
                <button
                  key={slot}
                  type="button"
                  onClick={() => setFieldValue("availableTimeSlot", slot)}
                  className={`px-4 py-2 text-sm font-medium rounded-md border transition-colors ${
                    values.availableTimeSlot === slot
                      ? "bg-blue-500 text-white border-blue-500"
                      : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                  }`}
                >
                  {slot}
                </button>
              ))}
            </div>
          </div>

          <div className="flex justify-between mt-8">
            <Button
              type="button"
              onClick={onBack}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Icon icon="mdi:arrow-left" className="w-4 h-4" />
              <span>Back</span>
            </Button>

            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            >
              <span>Next</span>
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default DoctorSelectionStep;
