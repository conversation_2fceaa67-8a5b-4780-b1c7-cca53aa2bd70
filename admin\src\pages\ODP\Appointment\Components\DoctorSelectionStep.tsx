import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";

interface DoctorSelectionStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const DoctorSelectionStep: React.FC<DoctorSelectionStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const [formData, setFormData] = useState({
    department: "",
    doctor: "",
    doctorName: "",
    availableStatus: "None",
    appointmentDate: "",
  });

  const [availableTimes] = useState([
    "11:00 AM",
    "11:30 AM",
    "12:00 PM",
    "12:30 PM",
    "1:00 PM",
    "1:30 PM",
    "2:00 PM",
    "2:30 PM",
    "3:00 PM",
    "3:30 PM",
  ]);

  const [selectedTime, setSelectedTime] = useState("");

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
      setSelectedTime(initialData.selectedTime || "");
    }
  }, [initialData]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext({
      ...formData,
      selectedTime,
    });
  };

  const isFormValid = () => {
    return (
      formData.department &&
      formData.doctor &&
      formData.appointmentDate &&
      selectedTime
    );
  };

  // Mock departments and doctors data
  const departments = [
    "Cardiology",
    "Neurology",
    "Orthopedics",
    "Pediatrics",
    "General Medicine",
  ];

  const doctors = [
    { id: "1", name: "Dr. John Smith", department: "Cardiology" },
    { id: "2", name: "Dr. Sarah Johnson", department: "Neurology" },
    { id: "3", name: "Dr. Michael Brown", department: "Orthopedics" },
    { id: "4", name: "Dr. Emily Davis", department: "Pediatrics" },
    { id: "5", name: "Dr. Robert Wilson", department: "General Medicine" },
  ];

  const filteredDoctors = doctors.filter(
    (doctor) =>
      !formData.department || doctor.department === formData.department
  );

  return (
    <div>
      {/* Step Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
          <Icon icon="mdi:doctor" className="w-5 h-5 text-red-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-800">
            Doctor Selection
          </h2>
          <p className="text-sm text-gray-600">
            Choose your preferred department, doctor, and appointment time
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Department */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Department *
            </label>
            <select
              value={formData.department}
              onChange={(e) => handleInputChange("department", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            >
              <option value="">Patient identity number</option>
              {departments.map((dept) => (
                <option key={dept} value={dept}>
                  {dept}
                </option>
              ))}
            </select>
          </div>

          {/* Choose Doctor */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Choose Doctor *
            </label>
            <select
              value={formData.doctor}
              onChange={(e) => {
                const selectedDoctor = filteredDoctors.find(
                  (doc) => doc.id === e.target.value
                );
                handleInputChange("doctor", e.target.value);
                handleInputChange("doctorName", selectedDoctor?.name || "");
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            >
              <option value="">Select doctor</option>
              {filteredDoctors.map((doctor) => (
                <option key={doctor.id} value={doctor.id}>
                  {doctor.name}
                </option>
              ))}
            </select>
          </div>

          {/* Available Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Available Status
            </label>
            <select
              value={formData.availableStatus}
              onChange={(e) =>
                handleInputChange("availableStatus", e.target.value)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            >
              <option value="None">None</option>
              <option value="Available">Available</option>
              <option value="Busy">Busy</option>
              <option value="On Leave">On Leave</option>
            </select>
          </div>

          {/* Appointment Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Appointment Date *
            </label>
            <input
              type="date"
              value={formData.appointmentDate}
              onChange={(e) =>
                handleInputChange("appointmentDate", e.target.value)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Available Times */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Available Times
          </label>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
            {availableTimes.map((time) => (
              <button
                key={time}
                type="button"
                onClick={() => setSelectedTime(time)}
                className={`px-4 py-2 text-sm font-medium rounded-md border transition-colors ${
                  selectedTime === time
                    ? "bg-red-500 text-white border-red-500"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                }`}
              >
                {time}
              </button>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            onClick={onBack}
            variant="outline"
            className="flex items-center space-x-2"
          >
            <Icon icon="mdi:arrow-left" className="w-4 h-4" />
            <span>Back</span>
          </Button>

          <Button
            type="submit"
            disabled={!isFormValid()}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
          >
            <span>Next</span>
            <Icon icon="mdi:arrow-right" className="w-4 h-4" />
          </Button>
        </div>
      </form>
    </div>
  );
};

export default DoctorSelectionStep;
