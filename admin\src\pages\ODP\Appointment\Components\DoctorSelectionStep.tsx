import React, { useState, useEffect, useMemo } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { DropdownField } from "../../../../components/DropDownField";
import { InputField } from "../../../../components/Input-Field";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";
import { useGetUser } from "../../../../server-action/api/user";
import { useGetAllDepartmentCategory } from "../../../../server-action/api/department-category.api";
import { useGetShiftAssign } from "../../../../server-action/api/shiftAssignApi";

// Helper function to get current date
const getCurrentDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const departmentOptions = [
  { value: "cardiology", label: "Cardiology" },
  { value: "neurology", label: "Neurology" },
  { value: "orthopedics", label: "Orthopedics" },
  { value: "pediatrics", label: "Pediatrics" },
  { value: "general", label: "General Medicine" },
  { value: "emergency", label: "Emergency" },
  { value: "surgery", label: "Surgery" },
  { value: "dermatology", label: "Dermatology" },
];

const doctorOptions = [
  { value: "dr-smith", label: "Dr. John Smith" },
  { value: "dr-johnson", label: "Dr. Sarah Johnson" },
  { value: "dr-brown", label: "Dr. Michael Brown" },
  { value: "dr-davis", label: "Dr. Emily Davis" },
  { value: "dr-wilson", label: "Dr. Robert Wilson" },
  { value: "dr-anderson", label: "Dr. Lisa Anderson" },
  { value: "dr-miller", label: "Dr. David Miller" },
  { value: "dr-taylor", label: "Dr. Jennifer Taylor" },
];

const timeSlotOptions = [
  "11:00 AM",
  "11:30 AM",
  "12:00 PM",
  "12:30 PM",
  "01:00 PM",
  "01:30 PM",
  "02:00 PM",
  "02:30 PM",
  "03:00 PM",
  "03:30 PM",
  "04:00 PM",
  "04:30 PM",
  "05:00 PM",
  "05:30 PM",
  "06:00 PM",
];

const doctorValidationSchema = yup.object().shape({});

interface DoctorSelectionStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const DoctorSelectionStep: React.FC<DoctorSelectionStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  // API hooks for real data
  const { data: userDoctorDetails } = useGetUser({ role: "DOCTOR" });
  const { data: departmentData } = useGetAllDepartmentCategory();
  const { data: shiftList } = useGetShiftAssign();

  // State for filtered data
  const [filteredDoctors, setFilteredDoctors] = useState<any[]>([]);

  // Real API data options
  const departmentDropdown = useMemo(() => {
    return (
      departmentData?.data?.map((dept: any) => ({
        value: dept._id,
        label: dept.name,
      })) || []
    );
  }, [departmentData]);

  const getDoctorsArray = () => {
    return userDoctorDetails || [];
  };

  const doctorDropdown = useMemo(() => {
    return filteredDoctors.map((doctor: any) => ({
      value: doctor._id,
      label: `Dr. ${doctor.commonInfo?.personalInfo?.fullName || "Unknown"}`,
    }));
  }, [filteredDoctors]);

  const formik = useFormik({
    initialValues: {
      department: initialData?.department || "",
      doctor: initialData?.doctor || "",
      appointmentDate: initialData?.appointmentDate || getCurrentDate(),
      availableTimeSlot: initialData?.availableTimeSlot || "",
      doctorDutyTime: initialData?.doctorDutyTime || "",
      availablestatus: initialData?.availablestatus || "None",
      totalCharge: initialData?.totalCharge || "500",
      resellerName: initialData?.resellerName || "none",
      remark: initialData?.remark || "",
    },
    enableReinitialize: true,
    validationSchema: doctorValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue, getFieldProps } = formik;

  // Filter doctors based on selected department
  useEffect(() => {
    if (values.department && userDoctorDetails) {
      const filtered = userDoctorDetails.filter(
        (doctor: any) =>
          doctor.commonInfo?.professionalInfo?.department === values.department
      );
      setFilteredDoctors(filtered);
    } else {
      setFilteredDoctors([]);
    }
  }, [values.department, userDoctorDetails]);

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:doctor" className="w-5 h-5 text-red-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Doctor Selection
            </h2>
            <p className="text-sm text-gray-600">
              Choose your preferred department, doctor, and appointment time
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5">
            <div>
              <DropdownField
                required
                label="Department"
                options={
                  departmentDropdown.length > 0
                    ? departmentDropdown
                    : departmentOptions
                }
                firstInput="Select Department"
                name="department"
                value={values.department}
                onChange={(e: any) =>
                  setFieldValue("department", e.target.value)
                }
              />
            </div>

            <div>
              <DropdownField
                required
                label="Choose Doctor"
                options={
                  doctorDropdown.length > 0 ? doctorDropdown : doctorOptions
                }
                firstInput="Select Doctor"
                name="doctor"
                value={values.doctor}
                onChange={(e: any) => setFieldValue("doctor", e.target.value)}
                disabled={!values.appointmentDate}
              />
            </div>

            <div>
              <InputField
                type="date"
                required
                label="Appointment Date"
                placeholder="yy/mm/dd"
                name="appointmentDate"
                min={getCurrentDate()}
                onChange={(e: any) => {
                  setFieldValue("appointmentDate", e.target.value);
                  setFieldValue("doctor", "");
                }}
                value={values.appointmentDate}
              />
            </div>

            <div>
              <DropdownField
                label="Available Time Slot"
                options={timeSlotOptions.map((slot) => ({
                  value: slot,
                  label: slot,
                }))}
                firstInput="Select Time Slot"
                name="availableTimeSlot"
                value={values.availableTimeSlot}
                onChange={(e: any) => {
                  const selectedTime = e.target.value;
                  setFieldValue("availableTimeSlot", selectedTime);
                  setFieldValue("doctorDutyTime", selectedTime);
                }}
                disabled={!values.doctor}
              />
            </div>

            <div>
              <InputField
                type="text"
                label="Available Status"
                placeholder="None"
                value={values.availablestatus || "None"}
                disabled
              />
            </div>

            <div>
              <InputField
                type="time"
                label="Doctor Duty Time"
                placeholder="Time"
                value={values.doctorDutyTime}
                disabled
              />
            </div>

            <div>
              <DropdownField
                label="Referral Name"
                options={[
                  { value: "none", label: "None" },
                  { value: "referral1", label: "Referral 1" },
                  { value: "referral2", label: "Referral 2" },
                ]}
                firstInput="Select Referral"
                name="resellerName"
                value={values.resellerName}
                onChange={(e: any) =>
                  setFieldValue("resellerName", e.target.value)
                }
              />
            </div>

            <div>
              <InputField
                type="text"
                label="Total Charge"
                placeholder="Auto"
                value={values.totalCharge || "500"}
                disabled
              />
            </div>

            <div>
              <InputField
                type="text"
                label="Remark"
                placeholder="Remarks"
                value={values.remark}
                onChange={(e: any) => setFieldValue("remark", e.target.value)}
              />
            </div>
          </div>

          <div className="flex justify-between mt-8">
            <Button
              type="button"
              onClick={onBack}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Icon icon="mdi:arrow-left" className="w-4 h-4" />
              <span>Back</span>
            </Button>

            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            >
              <span>Next</span>
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default DoctorSelectionStep;
