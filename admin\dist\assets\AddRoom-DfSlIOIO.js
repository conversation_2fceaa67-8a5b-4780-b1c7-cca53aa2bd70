import{cI as V,ba as D,cJ as L,aU as O,cK as _,ad as I,a1 as $,aV as z,bm as F,aX as y,aM as G,ax as M,a5 as T,a2 as e,a9 as H,bx as K,aa as U,a7 as W,ab as B,ac as p,a4 as Z,cL as q,aZ as J}from"./index-ClX9RVH0.js";function k(b,m={}){const{keepZero:c=!0,keepEmptyArrays:t=!1,keepEmptyObjects:g=!1}=m,o={};for(const[d,a]of Object.entries(b))if(a!=null){if(typeof a=="string"){a.trim()!==""&&(o[d]=a);continue}if(typeof a=="number"){(c||a!==0)&&(o[d]=a);continue}if(typeof a=="boolean"){o[d]=a;continue}if(Array.isArray(a)){(t||a.length>0)&&(o[d]=a);continue}if(typeof a=="object"){const N=Object.keys(a).length>0;if(g||N){const h=k(a,m);(Object.keys(h).length>0||g)&&(o[d]=h)}continue}a&&(o[d]=a)}return o}const X=()=>{var j,C,E,R;const b=V(),{id:m}=D(),c=!!m,{data:t,isLoading:g}=L(m??""),{data:o}=O({limit:100}),{mutateAsync:d,isPending:a}=_(),N=(C=(j=o==null?void 0:o.data)==null?void 0:j.departments)==null?void 0:C.map(l=>({value:l._id,label:l.name})),h=I(),u=$({initialValues:{department:"",categoryName:"",roomNo:"",startNumber:0,endNumber:0,bedCapacity:"",floor:"",notes:"",facilities:[],rate:0},enableReinitialize:!0,validationSchema:z({department:y(),categoryName:y(),roomNo:y(),bedCapacity:y(),floor:F(),rate:F().min(0,"Rate cannot be negative")}),onSubmit:async l=>{const f=k(l);c?await d({_id:m??"",entityData:f}):await b.mutateAsync(f),h(-1)}}),w=G({department:u.values.department}),{data:v}=M(w),A=(R=(E=v==null?void 0:v.data)==null?void 0:E.wardCategory)==null?void 0:R.map(l=>({value:l._id,label:l.categoryName}));T.useEffect(()=>{var l,f;c&&t&&u.setValues({department:((l=t.department)==null?void 0:l._id)||"",categoryName:((f=t.categoryName)==null?void 0:f._id)||"",roomNo:t.roomNo||"",bedCapacity:t.bedCapacity||"",startNumber:t.startNumber||0,endNumber:t.endNumber||0,floor:t.floor||"",notes:t.notes||"",facilities:t.facilities||[],rate:t.rate||0})},[t,c]);const{handleSubmit:S,values:r,errors:s,touched:x,handleChange:i,handleBlur:n,setFieldValue:P}=u;return c&&g?e.jsx("div",{children:"Loading..."}):e.jsxs(H,{value:u,children:[(b.isPending||a)&&e.jsx(K,{isLoading:b.isPending||a}),e.jsx(U,{onSubmit:l=>{l.preventDefault(),S()},children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsx(W,{listTitle:`${c?"Edit Room":"Add Room"} `,hideHeader:!0}),e.jsxs("div",{className:"grid grid-cols-3 gap-5 bg-white py-8 px-5 rounded-lg",children:[e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(B,{label:"Department Name",options:N,name:"department",onChange:i,onBlur:n,value:r.department}),x.department&&s.department&&e.jsx("div",{className:"text-red text-xs",children:s.department})]}),e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(B,{label:"Ward Name",options:A,name:"categoryName",onChange:i,onBlur:n,value:r.categoryName}),x.categoryName&&s.categoryName&&e.jsx("div",{className:"text-red text-xs",children:s.categoryName})]}),e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(p,{type:"text",label:"Room No.",placeholder:"Enter Room No.",name:"roomNo",onChange:i,onBlur:n,value:r.roomNo}),x.roomNo&&s.roomNo&&e.jsx("div",{className:"text-red text-xs",children:s.roomNo})]}),e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(p,{type:"number",label:"Rate",placeholder:"Enter Room rate",name:"rate",onChange:i,onBlur:n,value:r.rate,min:0}),x.rate&&s.rate&&e.jsx("div",{className:"text-red text-xs",children:s.rate})]})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-5 bg-white py-8 px-5 rounded-lg",children:[e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(p,{type:"text",label:"Total Beds",placeholder:"Enter total number of beds",name:"bedCapacity",onChange:i,onBlur:n,value:r.bedCapacity}),x.bedCapacity&&s.bedCapacity&&e.jsx("div",{className:"text-red text-xs",children:s.bedCapacity})]}),e.jsx("div",{className:"flex flex-col gap-1",children:e.jsx(p,{type:"number",label:"Starting bed number",placeholder:"Enter starting bed number",name:"startNumber",onChange:i,onBlur:n,value:r.startNumber})}),e.jsx("div",{className:"flex flex-col gap-1",children:e.jsx(p,{type:"number",label:"Ending bed number",placeholder:"Enter ending number of beds",name:"endNumber",onChange:i,onBlur:n,value:r.endNumber})}),e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx(p,{type:"number",label:"Floor",placeholder:"5",name:"floor",onChange:i,onBlur:n,value:r.floor}),x.floor&&s.floor&&e.jsx("div",{className:"text-red text-xs",children:s.floor})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx(Z,{variant:"grey-500",size:"body-sm-lg",className:"tracking-wide capitalize",children:"Facilities"}),e.jsx(q,{field:"facilities",label:"Facilities",placeholder:"Enter Facilities",onValueChange:P,defaultValue:r.facilities})]})]}),e.jsx("div",{className:"w-full rounded-lg bg-white py-8 px-5",children:e.jsx("div",{className:"col-span-3 ",children:e.jsx(J,{label:"Notes",placeholder:"Add any specific details about the room",name:"notes",onChange:i,onBlur:n,value:r.notes})})}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsx("button",{type:"submit",disabled:u.isSubmitting,className:"bg-primary text-white py-2 px-6 rounded-md hover:bg-primary-dark disabled:opacity-50",children:u.isSubmitting?"Submitting...":"Submit"})})]})})]})};export{X as default};
