import{a5 as t,aM as G,ad as H,a2 as a,af as J,ag as u,ah as W,aQ as z,ai as K,aj as X}from"./index-ClX9RVH0.js";import{u as Y,a as Z}from"./attendance.api-CVfqyNC8.js";const te=()=>{var g,D,f;const[n,I]=t.useState(()=>{const e=localStorage.getItem("tabValue");return e==="Doctor"||e==="Nurse"||e==="Staff"?e:"Doctor"});t.useEffect(()=>{localStorage.setItem("tabValue",n)},[n]);const[d,j]=t.useState(""),[r,v]=t.useState(null),[l,L]=t.useState(null),[P,U]=t.useState(""),[s,i]=t.useState({limit:20,page:1}),V=d,O=new Date().toISOString().split("T")[0],$=G({role:n.toUpperCase(),startDate:r,endDate:l,status:P,...V?{}:{search:d,limit:s.limit,page:s.page}}),{data:o}=Y($),c=H(),[Q,_]=t.useState(null),{mutateAsync:M}=Z({id:JSON.stringify([Q])}),R=e=>{c(`${u.ADDATTENDANCE}/${e}`)},p={columns:[{title:"S.N.",key:"key"},{title:"Name",key:"doctorName"},{title:"Department",key:"department"},{title:"Date",key:"date"},{title:"Entry Time",key:"entryTime"},{title:"Exit Time",key:"exitTime"},{title:"Status",key:"status"},{title:"role",key:"role"},{title:"Action",key:"action"}],rows:(((g=o==null?void 0:o.data)==null?void 0:g.attendances)||[]).filter(e=>!r&&!l?new Date(e==null?void 0:e.date).toISOString().split("T")[0]===O:!0).map((e,A)=>{var S,y,T,N,b,h,x,F,C,E,k,w;return{key:A+1,doctorName:((T=(y=(S=e==null?void 0:e.user)==null?void 0:S.commonInfo)==null?void 0:y.personalInfo)==null?void 0:T.fullName.charAt(0).toUpperCase())+((h=(b=(N=e==null?void 0:e.user)==null?void 0:N.commonInfo)==null?void 0:b.personalInfo)==null?void 0:h.fullName.slice(1).toLowerCase()),department:((F=(x=e==null?void 0:e.user)==null?void 0:x.commonInfo)==null?void 0:F.ipdOpd.charAt(0).toUpperCase())+((E=(C=e==null?void 0:e.user)==null?void 0:C.commonInfo)==null?void 0:E.ipdOpd.slice(1).toLowerCase()),entryTime:e==null?void 0:e.entryTime,exitTime:e==null?void 0:e.exitTime,role:((k=e==null?void 0:e.user)==null?void 0:k.role.charAt(0).toUpperCase())+((w=e==null?void 0:e.user)==null?void 0:w.role.slice(1).toLowerCase()),date:e==null?void 0:e.date,status:a.jsx(W,{status:(e==null?void 0:e.status.charAt(0).toUpperCase())+(e==null?void 0:e.status.slice(1).toLowerCase())}),action:a.jsx(J,{onShow:()=>c(`${u.VIEWATTENDANCE}/${e._id}`),onEdit:()=>R(e._id),onDelete:async()=>{_(e==null?void 0:e._id),await M()}})}})},q=e=>{U(e.target.value)},B=()=>{c(`${u.ADDATTENDANCE}`)};return a.jsxs("div",{children:[a.jsx(z,{headerTitle:"Attendance List",onSearch:!0,onDatePicker:!0,onDateFrom:!0,onSearchFunc:e=>j(e),date:r??void 0,setDate:v,setDateFrom:L,dateFrom:l??void 0,setFilterValue:e=>q({target:{value:e.toUpperCase()}}),onFilter:!0,button:!0,buttonAction:B,buttonText:"Add Attendance"}),a.jsxs("div",{className:"bg-white rounded-md",children:[a.jsx("div",{className:"flex items-center justify-between pt-2 pb-1 pr-4 mt-3",children:a.jsx(K,{tabs:["Doctor","Nurse","Staff"],defaultTab:n,onTabChange:e=>I(e)})}),a.jsx(X,{columns:p.columns,rows:p.rows,loading:!1,pagination:{currentPage:s.page,totalPage:((f=(D=o==null?void 0:o.data)==null?void 0:D.pagination)==null?void 0:f.pages)||1,limit:s.limit,onClick:e=>{e.page&&i({...s,page:e.page}),e.limit&&i({...s,limit:e.limit})}}})]})]})};export{te as AttendanceList};
