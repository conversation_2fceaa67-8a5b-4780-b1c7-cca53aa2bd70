import{a5 as u,ae as p,a2 as e,ah as g,ai as x,aj as h}from"./index-ClX9RVH0.js";import{N as y,D as b}from"./Svg-BMTGOzwv.js";import{D as j}from"./DepartmentHeader-Aj6XBXn4.js";const k=()=>{const[o,s]=u.useState("Patient"),a={columns:[{title:"Paitent Id",key:"tokenid"},{title:"Patient Name",key:"patientName"},{title:"Date Assigned",key:"date"},{title:"Contact Number",key:"treatment"},{title:"Appointment Date",key:"doctorName"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:p.map(({tokenId:t,patientName:i,date:r,doctorName:l,status:c,treatment:m},d)=>({key:d,tokenid:t,patientName:i,date:r,doctorName:l,status:e.jsx(g,{status:c}),treatment:m,action:e.jsx("button",{className:"px-4 py-2 text-white rounded bg-primary",children:"Checkup"})}))},n=t=>{console.log(t,"onSearch")};return e.jsxs(e.Fragment,{children:[e.jsx(j,{title:"Neurology",headerTitle:"ODP",doctorName:"Dr. John Smith",services:["Diagnostic","Interventional","Electrophysiology","Cardiac Rehabilitation","Preventive Cardiology"],patientServed:100,doctorImage:e.jsx(b,{}),followUpPatient:110,newPatient:20,revenueGenerated:4900,icon:e.jsx(y,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pb-2 mt-5",children:[e.jsx(x,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:o,onTabChange:t=>s(t)}),e.jsx("div",{children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name id",onChange:t=>n(t.target.value),className:"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(h,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{k as NeurologyDepartmentPage};
