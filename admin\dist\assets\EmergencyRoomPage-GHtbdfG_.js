import{ad as I,a5 as s,aw as R,av as e,a2 as a,aK as S,bS as k,cg as P,a7 as j,ag as i,aj as b,af as h,aL as r}from"./index-ClX9RVH0.js";import{T as C}from"./TransferPatient-qcC_AtDM.js";const D=()=>{const n=I(),[c,T]=s.useState(""),[o,g]=s.useState({state:!1,patientId:""}),[l,E]=s.useState({page:1,limit:10}),{data:d}=R({role:"PATIENT","commonInfo.emergency":!0,...c!==""&&{search:c},page:l.page,limit:l.limit}),u=t=>a.jsx(h,{onShow:()=>n(`${i.PATIENTDETAIL}/${t}`),onMore:()=>{},onMoreList:[{title:"Transfer Bed",onClick:()=>{r(t),n(i.TRANSFER_GENERALWARD)},index:1},{title:"Transfer Hospital",onClick:()=>g({state:!0,patientId:t}),index:4},{title:"Treatment",onClick:()=>{r(t),n(i.TREATMENT_GENERALWARD)},index:2},{title:"Surgery",onClick:()=>{r(t),n(i.SURGERY_GENERALWARD)},index:3}]}),p=e.get(d,"data.pagination"),x=e.get(d,"data.users",[]).map(t=>({bedNo:e.get(t,"emergencyInfo.bedNumber","-"),patientId:e.get(t,"patientInfo.patientId",""),patientName:e.get(t,"commonInfo.personalInfo.fullName",""),allocatedTime:S(e.get(t,"createdAt","")).format("MMM-DD-YYYY HH:mm A"),reason:e.get(t,"emergencyInfo.reason","-"),status:a.jsx("div",{className:k("py-1 px-2 rounded-full text-xs items-center flex  justify-center",P(e.get(t,"patientInfo.patientStatus","-"))),children:e.get(t,"patientInfo.patientStatus","-")}),action:u(e.get(t,"_id",""))})),A=s.useMemo(()=>[{title:"Bed No.",key:"bedNo"},{title:"Patient ID",key:"patientId"},{title:"Patient Name",key:"patientName"},{title:"Allocated Time",key:"allocatedTime"},{title:"Reason",key:"reason"},{title:"Status",key:"status"},{title:"Action",key:"action"}],[]),N=({page:t,limit:m})=>{E(f=>({page:t??(m?1:f.page),limit:m??f.limit}))},y=s.useCallback(e.debounce(t=>T(t),500),[]);return a.jsxs("div",{className:"space-y-4",children:[a.jsx(j,{title:"Patient",onSearch:y,onAddClick:()=>{n(i.EMERGENCYADDPATIENT)},listTitle:"Emergency"}),a.jsx(b,{columns:A,rows:x,loading:!1,pagination:{totalPage:e.get(p,"pages",1),currentPage:e.get(p,"page",1),limit:e.get(l,"limit",10),onClick:N}}),a.jsx(C,{isOpen:o.state,onClose:()=>{g({...o,state:!1})},patientId:o.patientId})]})},G=()=>a.jsx(D,{});export{G as default};
