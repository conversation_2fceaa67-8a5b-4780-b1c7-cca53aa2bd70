import{dj as q,a2 as s,aj as A,dl as k,dm as F,af as C,cC as P,dn as B,a1 as v,aV as _,a5 as m,a9 as S,aa as w,c7 as T,ao as M,bm as V,aX as f,aU as O,dp as R,dq as G,aP as K,aQ as L,c3 as $,aR as E,a4 as j}from"./index-ClX9RVH0.js";const I=()=>{var l;const{data:t}=q(),r=(l=t==null?void 0:t.data)==null?void 0:l.expenses,o={columns:[{title:"Date",key:"date"},{title:"Expense ID",key:"id"},{title:"Expense Item",key:"item"},{title:"Category",key:"category"},{title:"Amount (NPR)",key:"amount"},{title:"Paid By",key:"paidBy"},{title:"Remarks",key:"remarks"}],rows:r==null?void 0:r.map(e=>{var c,d,n,i;return{id:`E-${(c=e==null?void 0:e._id)==null?void 0:c.slice(-5)}`,date:e==null?void 0:e.date,category:(n=(d=e==null?void 0:e.expensesList[0])==null?void 0:d.categoryName)==null?void 0:n.categoryName,item:(i=e==null?void 0:e.expensesList[0])==null?void 0:i.details,amount:e==null?void 0:e.totalAmount,remarks:e==null?void 0:e.remark}})};return s.jsx("div",{children:s.jsx(A,{columns:o.columns,rows:o.rows,loading:!1})})},U=({setSelectedData:t,setViewModal:r})=>{var c,d;const{data:o}=k(),{mutateAsync:l}=F(),e={columns:[{title:"S.N",key:"sn"},{title:"Category",key:"category"},{title:"Description",key:"desc"},{title:"Actions",key:"action"}],rows:(d=(c=o==null?void 0:o.data)==null?void 0:c.expensescategories)==null?void 0:d.map((n,i)=>({sn:i+1,category:n==null?void 0:n.categoryName,desc:n==null?void 0:n.details,action:s.jsx(C,{onEdit:()=>{t(n),r(!0)},onDelete:async()=>{await l({id:JSON.stringify([n==null?void 0:n._id])})}})}))};return s.jsx("div",{children:s.jsx(A,{columns:e.columns,rows:e.rows,loading:!1})})},z=()=>_().shape({expenseCategory:f().required("Expense category is required"),expenseName:f().required("Expense name is required"),paymentMethod:f().required("Payment method is required"),totalAmount:V().required("Total amount is required")}),H=({editData:t,onClose:r})=>{var h,N;const{data:o}=k(),l=(h=o==null?void 0:o.data)==null?void 0:h.expensescategories,e=l==null?void 0:l.map(a=>({label:a==null?void 0:a.categoryName,value:a==null?void 0:a._id})),{data:c}=P(),d=(N=c==null?void 0:c.data)==null?void 0:N.banks,n=d==null?void 0:d.map(a=>({value:a==null?void 0:a._id,label:a==null?void 0:a.bankName,accountNumber:a==null?void 0:a.accountNumber})),{mutateAsync:i}=B(),u=v({initialValues:{expenseCategory:"",expenseName:"",paymentMethod:"",totalAmount:"",remarks:"",bankName:"",accountNumber:""},validationSchema:z,onSubmit:a=>{console.log(a);const b={date:new Date().toISOString().split("T")[0],expensesList:[{categoryName:a.expenseCategory,details:a.expenseName,amount:Number(a.totalAmount)}],amount:Number(a.totalAmount),paymentMethod:a.paymentMethod,remark:a.remarks};a.paymentMethod==="BANK"&&(b.bank=a.bankName),t||i(b),r==null||r()}});m.useEffect(()=>{if(!t&&u.values.paymentMethod){const a=n==null?void 0:n.find(b=>b.value===u.values.bankName);u.setFieldValue("accountNumber",a==null?void 0:a.accountNumber)}},[u.values.bankName]);const x=[{type:"select",field:"expenseCategory",label:"Expense Category",options:e,required:!0},{type:"text",field:"expenseName",label:"Expense Name",required:!0},{type:"select",field:"paymentMethod",label:"Payment Method",options:[{label:"Cash",value:"CASH"},{label:"Bank",value:"BANK"}],required:!0},{type:"select",field:"bankName",label:"Bank Name",isVisible:u.values.paymentMethod==="BANK",options:n},{type:"text",field:"accountNumber",label:"Account Number",isVisible:u.values.paymentMethod==="BANK"},{type:"text",field:"totalAmount",label:"Total Amount",required:!0},{type:"text",field:"remarks",label:"Remarks"}],{touched:p,errors:y}=u;return s.jsx("div",{children:s.jsx(S,{value:u,children:s.jsxs(w,{onSubmit:u.handleSubmit,className:"grid grid-cols-2 gap-5",children:[s.jsx(T,{getFieldProps:u.getFieldProps,formDatails:x,errors:y,touched:p}),s.jsx("div",{className:"mt-6 col-span-2",children:s.jsx(M,{onCancel:()=>{u.resetForm(),r==null||r()},onSubmit:u.handleSubmit})})]})})})},D=({editData:t,onClose:r})=>{var g,x;const{data:o}=O(),l=(g=o==null?void 0:o.data)==null?void 0:g.departments,e=l==null?void 0:l.map(p=>({label:p==null?void 0:p.name,value:p==null?void 0:p._id})),{mutateAsync:c}=R(),d=[{type:"text",field:"categoryName",label:"Category Name"},{type:"text",field:"description",label:"Description"},{type:"select",field:"subDepartment",label:"Sub Department",options:e}],{mutateAsync:n}=G(),i=v({initialValues:{categoryName:(t==null?void 0:t.categoryName)??"",description:(t==null?void 0:t.details)??"",subDepartment:((x=t==null?void 0:t.department)==null?void 0:x._id)??""},onSubmit:async p=>{const y={categoryName:p.categoryName,details:p.description,department:p.subDepartment};t?await c({_id:(t==null?void 0:t._id)??"",entityData:y}):await n(y),r==null||r()}}),{handleSubmit:u}=i;return s.jsx("div",{children:s.jsx(S,{value:i,children:s.jsxs(w,{className:"grid grid-cols-2 border border-whites-200 rounded-lg gap-4 p-6",children:[s.jsx(T,{formDatails:d,getFieldProps:i.getFieldProps}),s.jsx("div",{className:"mt-3 col-span-2",children:s.jsx(M,{onCancel:()=>{i.resetForm(),r==null||r()},submitLabel:"Save",submitlabelButton:!0,onSubmit:u})})]})})})},Q=()=>{const[t,r]=m.useState("expense"),[o,l]=m.useState(!1),[e,c]=m.useState(void 0),d=K(()=>l(!1));return s.jsxs("div",{className:"flex flex-col",children:[s.jsx(L,{headerTitle:"Expenses",buttonText:t==="expense"?"Add Expense":"Add Expense Category",button:!0,onSearch:!0,buttonAction:()=>{c(void 0),l(!0)}}),s.jsxs("section",{className:"flex flex-col bg-white gap-3",children:[s.jsx($,{tabs:[{label:"Expenses",value:"expense"},{label:"Expenses Cateogry",value:"category"}],defaultTab:t,onTabChange:n=>{r(n),l(!1)}}),s.jsx("section",{children:t==="expense"?s.jsx(I,{}):s.jsx(U,{setSelectedData:c,setViewModal:l})}),t==="expense"&&o&&s.jsxs(E,{ref:d,classname:"p-5  overflow-scroll",children:[s.jsx(j,{as:"h3",size:"body-lg-lg",variant:"primary-blue",className:"mt-6 pb-4 text-primary",children:e?"Edit Expense":"Add Expense"}),s.jsx(H,{editData:e,onClose:()=>l(!1)})]}),t==="category"&&o&&s.jsxs(E,{ref:d,classname:"p-5  overflow-scroll",children:[s.jsx(j,{as:"h3",size:"body-lg-lg",variant:"primary-blue",className:"mt-6 px-2 text-primary",children:e?"Edit Expense Category":"Add Expense Category"}),s.jsx(D,{editData:e,onClose:()=>l(!1)})]})]})]})};export{Q as PharmacyExpense};
