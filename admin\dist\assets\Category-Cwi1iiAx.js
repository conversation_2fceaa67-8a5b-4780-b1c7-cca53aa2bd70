import{ad as k,a5 as s,a1 as I,a2 as t,af as P,a7 as R,ab as T,ag as E,aj as F,bA as G}from"./index-ClX9RVH0.js";import{u as _,a as L}from"./categoryApi-75124ePN.js";const J=()=>{var p,C;const r=k(),[f,o]=s.useState(!1),[c,i]=s.useState(null),[g,n]=s.useState(1),[d,O]=s.useState(20),[x,u]=s.useState(""),{data:a,isLoading:h,refetch:v}=_({page:g,limit:d,search:x}),{mutateAsync:N}=L({id:JSON.stringify([c])}),m=((p=a==null?void 0:a.data)==null?void 0:p.serviceCategory)||[],l=((C=a==null?void 0:a.data)==null?void 0:C.pagination)||{page:1,pages:1,limit:20},{values:S,handleSubmit:b,handleChange:j,submitForm:A}=I({initialValues:{categoryName:""},onSubmit:e=>{u(e.categoryName),n(1)}}),w=async()=>{if(c)try{await N(),o(!1),i(null),v()}catch(e){console.log(e)}},y={columns:[{title:"S.N",key:"sn"},{title:"Categories",key:"categories"},{title:"Description",key:"description"},{title:"Example Service/Items",key:"services"},{title:"Action",key:"action"}],rows:m.map((e,D)=>({sn:(g-1)*d+(D+1),categories:e.categoryName||"-",description:e.description||"-",services:e.example||"-",action:t.jsx(P,{onEdit:()=>r(`/pricing-config/add-category/${e._id}`),onDelete:()=>{i(e._id),o(!0)}})}))};return t.jsxs("div",{children:[t.jsx(R,{title:"New Category",onSearch:e=>{u(e),n(1)},onAddClick:()=>r(E.PRICINGADDPRICINGCATEGORY),listTitle:"Pricing Category List",FilterSection:()=>t.jsx("form",{onSubmit:b,className:"flex gap-5",children:t.jsx(T,{label:"",name:"categoryName",firstInput:"Category",value:S.categoryName,onChange:e=>{j(e),A()},options:[{value:"",label:"All"},...m.map(e=>({value:e.categoryName,label:e.categoryName}))]})})}),t.jsxs("div",{className:"py-4",children:[t.jsx(F,{columns:y.columns,rows:y.rows,loading:h,pagination:{currentPage:l.page,totalPage:l.pages,limit:l.limit,onClick:({page:e})=>{e&&n(e)}}}),t.jsx(G,{confirmAction:f,title:"Do you want to delete this record?",des:"This action cannot be undone.",onClose:()=>{o(!1),i(null)},onConfirm:w})]})]})};export{J as default};
