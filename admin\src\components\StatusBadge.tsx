import React from "react";

interface StatusBadgeProps {
  statusText: string;
}

interface StatusColors {
  bgColor: string;
  textColor: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ statusText }) => {
  const getStatusColors = (status: string): StatusColors => {
    switch (status.toLowerCase()) {
      case "active":
        return {
          bgColor: "",
          textColor: "text-[#28A745]",
        };
      case "inactive":
        return {
          bgColor: "",
          textColor: "text-[#17a2b8]",
        };
      case "critical":
        return {
          bgColor: "",
          textColor: "text-[#DC3545]",
        };
      case "resolved":
        return {
          bgColor: "",
          textColor: "text-[#6c757d]",
        };
      case "error":
        return {
          bgColor: "bg-red-600",
          textColor: "text-white",
        };
      case "cancelled":
        return {
          bgColor: "bg-red-400",
          textColor: "text-white",
        };
      case "draft":
        return {
          bgColor: "bg-gray-300",
          textColor: "text-white",
        };
      default:
        return {
          bgColor: "bg-gray-400",
          textColor: "text-white",
        };
    }
  };

  const { bgColor, textColor } = getStatusColors(statusText);

  return (
    <span
      className={` px-1.5 py-1 rounded-md font-medium uppercase text-xs  ${bgColor} ${textColor}`}
    >
      {statusText}
    </span>
  );
};
