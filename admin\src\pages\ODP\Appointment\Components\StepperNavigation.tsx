import React from "react";
import { Icon } from "@iconify/react";

interface Step {
  id: number;
  title: string;
  subtitle: string;
  icon: string;
  color: string;
}

interface StepperNavigationProps {
  steps: Step[];
  currentStep: number;
  isStepCompleted: (step: number) => boolean;
}

const StepperNavigation: React.FC<StepperNavigationProps> = ({
  steps,
  currentStep,
  isStepCompleted,
}) => {
  const getStepStatus = (step: Step) => {
    if (isStepCompleted(step.id)) {
      return "completed";
    } else if (step.id === currentStep) {
      return "active";
    } else {
      return "inactive";
    }
  };

  const getStepColors = (step: Step, status: string) => {
    const colorMap = {
      teal: {
        active: "bg-teal-500 text-white border-teal-500",
        completed: "bg-teal-500 text-white border-teal-500",
        inactive: "bg-gray-200 text-gray-400 border-gray-200",
        line: "bg-teal-500",
      },
      red: {
        active: "bg-red-500 text-white border-red-500",
        completed: "bg-red-500 text-white border-red-500",
        inactive: "bg-gray-200 text-gray-400 border-gray-200",
        line: "bg-red-500",
      },
      blue: {
        active: "bg-blue-500 text-white border-blue-500",
        completed: "bg-blue-500 text-white border-blue-500",
        inactive: "bg-gray-200 text-gray-400 border-gray-200",
        line: "bg-blue-500",
      },
      purple: {
        active: "bg-purple-500 text-white border-purple-500",
        completed: "bg-purple-500 text-white border-purple-500",
        inactive: "bg-gray-200 text-gray-400 border-gray-200",
        line: "bg-purple-500",
      },
    };

    return colorMap[step.color as keyof typeof colorMap] || colorMap.teal;
  };

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between relative">
        {steps.map((step, index) => {
          const status = getStepStatus(step);
          const colors = getStepColors(step, status);
          const isLast = index === steps.length - 1;

          return (
            <div key={step.id} className="flex items-center flex-1">
              {/* Step Circle */}
              <div className="relative flex flex-col items-center">
                <div
                  className={`w-12 h-12 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${colors[status]}`}
                >
                  {status === "completed" ? (
                    <Icon icon="mdi:check" className="w-6 h-6" />
                  ) : (
                    <Icon icon={step.icon} className="w-6 h-6" />
                  )}
                </div>
                
                {/* Step Info */}
                <div className="mt-3 text-center">
                  <div
                    className={`text-sm font-medium ${
                      status === "active" || status === "completed"
                        ? "text-gray-800"
                        : "text-gray-400"
                    }`}
                  >
                    {step.title}
                  </div>
                  <div
                    className={`text-xs ${
                      status === "active" || status === "completed"
                        ? "text-gray-600"
                        : "text-gray-400"
                    }`}
                  >
                    {step.subtitle}
                  </div>
                </div>
              </div>

              {/* Connecting Line */}
              {!isLast && (
                <div className="flex-1 h-0.5 mx-4 relative">
                  <div className="absolute inset-0 bg-gray-200"></div>
                  <div
                    className={`absolute inset-0 transition-all duration-300 ${
                      isStepCompleted(step.id) ? colors.line : "bg-gray-200"
                    }`}
                    style={{
                      width: isStepCompleted(step.id) ? "100%" : "0%",
                    }}
                  ></div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StepperNavigation;
