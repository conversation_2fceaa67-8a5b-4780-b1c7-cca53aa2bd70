import type React from "react";
import { Icon } from "@iconify/react";

interface Step {
  id: number;
  title: string;
  subtitle: string;
  icon: string;
}

interface StepperNavigationProps {
  steps: Step[];
  currentStep: number;
  isStepCompleted: (step: number) => boolean;
}

export const StepperNavigation: React.FC<StepperNavigationProps> = ({
  steps,
  currentStep,
  isStepCompleted,
}) => {
  const getStepStatus = (step: Step) => {
    if (isStepCompleted(step.id)) return "completed";
    if (step.id === currentStep) return "active";
    return "inactive";
  };

  const getProgressWidth = () => {
    // Calculate progress based on current step, not just completed steps
    const progressSteps = Math.max(0, currentStep - 1);
    return (progressSteps / (steps.length - 1)) * 100;
  };

  return (
    <div className="w-full py-8 bg-white">
      <div className="relative  mx-auto">
        <div className="absolute top-6 left-0 right-0 h-0.5 bg-gray-300 mx-6"></div>

        {/* Progress line */}
        <div
          className="absolute top-6 left-6 h-0.5 bg-blue transition-all duration-500 ease-in-out"
          style={{
            width: `calc(${getProgressWidth()}% - 24px)`,
          }}
        ></div>

        <div className="flex items-start justify-between relative">
          {steps.map((step, index) => {
            const status = getStepStatus(step);
            const isCompleted = status === "completed";
            const isActive = status === "active";
            const isActiveOrCompleted = isCompleted || isActive;

            return (
              <div
                key={step.id}
                className="flex flex-col items-center relative z-10 flex-1"
                style={{ maxWidth: `${100 / steps.length}%` }}
              >
                {/* Circle with icon */}
                <div
                  className={`w-12 h-12 rounded-full border-2 flex items-center justify-center transition-all duration-300 ${
                    isActiveOrCompleted
                      ? "bg-blue border-blue text-white"
                      : "bg-white border-gray-300 text-gray-400"
                  }`}
                >
                  {isCompleted ? (
                    <Icon icon="mdi:check" className="w-5 h-5" />
                  ) : (
                    <Icon icon={step.icon} className="w-5 h-5" />
                  )}
                </div>

                {/* Text content */}
                <div className="mt-3 text-center max-w-[120px]">
                  <div
                    className={`text-sm font-semibold leading-tight ${
                      isActiveOrCompleted ? "text-blue-500" : "text-gray-500"
                    }`}
                  >
                    {step.title}
                  </div>
                  <div
                    className={`text-xs mt-1 leading-tight ${
                      isActiveOrCompleted ? "text-blue-500" : "text-gray-400"
                    }`}
                  >
                    {step.subtitle}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
