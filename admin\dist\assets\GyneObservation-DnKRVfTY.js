import{a5 as u,ae as g,a2 as e,af as p,ah as x,ai as h,aj as b}from"./index-ClX9RVH0.js";import{C as y,D as f}from"./Svg-BMTGOzwv.js";import{D as j}from"./DepartmentHeader-Aj6XBXn4.js";const v=()=>{const[s,i]=u.useState("Patient"),a={columns:[{title:"Patient Id",key:"tokenid"},{title:"Patient Name",key:"patientName"},{title:"Age",key:"date"},{title:"Maritial Status",key:"date"},{title:"Diagnosis",key:"treatment"},{title:"Bed No.",key:"date"},{title:"Addmission Date",key:"date"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:g.map(({tokenId:t,patientName:o,date:r,doctorName:l,status:c,treatment:d},m)=>({key:m,tokenid:t,patientName:o,date:r,doctorName:l,status:e.jsx(x,{status:c}),treatment:d,action:e.jsx(p,{onEdit:()=>{},onMore:()=>{}})}))},n=t=>{console.log(t,"onSearch")};return e.jsxs("div",{children:[e.jsx(j,{title:"Gyne Observation Ward (GOW)",doctorName:"Dr. Shishir Thapa",services:["Maternal Monitoring","Pre & Postnatal Care","High-Risk Care","Pain Management","Fetal Monitoring","Emergency Care","24/7 Medical Team"],totalPatients:200,currentPatients:50,highRiskCases:50,dischargedPatients:10,doctorImage:e.jsx(f,{}),headerTitle:"Inpatient Department",icon:e.jsx(y,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pt-2 pb-1 pr-4 mt-5",children:[e.jsx(h,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:s,onTabChange:t=>i(t)}),e.jsx("div",{className:"flex flex-row gap-10",children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name, id",onChange:t=>n(t.target.value),className:"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(b,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{v as GyneObservationWard};
