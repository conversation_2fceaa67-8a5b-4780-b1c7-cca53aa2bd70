import{a2 as t,a5 as f,b3 as E,aK as T,ad as S,aE as A,av as g,ag as P,ah as L,a7 as F,ai as G,aj as H}from"./index-ClX9RVH0.js";const R=({date:n,setDate:x,disabled:d=!1,min:r,max:p})=>t.jsx("input",{type:"date",value:n,onChange:a=>x(a.target.value),disabled:d,min:r,max:p,className:`border border-gray-300 px-4 py-[6px] text-sm rounded-sm w-64 ${d?"bg-gray-100 cursor-not-allowed":""}`,style:{color:n?"black":"gray"},placeholder:"Select a date"}),$=({patientHistoryData:n,isLoading:x,tabValue:d="All",selectedDate:r})=>{const p=f.useMemo(()=>{var c;if(!n)return[];const s=d.toLowerCase()==="today"?"Today":r?T(r).format("dddd"):"All",l=((c=n==null?void 0:n.data)==null?void 0:c.patientHistory)||[],h=l.length,b=l.filter(o=>o.status==="NEW").length,y=l.filter(o=>o.status==="CHECKED").length,w=l.filter(o=>o.status==="PRE-CHECKED").length,N=l.filter(o=>o.status==="DISCHARGED").length,k=l.filter(o=>o.status==="IN-PROGRESS").length;return[{title:"Total Patients",value:h,iconColor:"#3B82F6",topIcon:"mdi:account-group",bgColor:"bg-white",color:"#ffffff",status:"Total",dayName:s},{title:"New",value:b,iconColor:"#10B981",topIcon:"mdi:account-plus",bgColor:"bg-white",color:"#ffffff",status:"New",dayName:s},{title:"Pre-Checked",value:w,iconColor:"#F59E0B",topIcon:"mdi:clipboard-check-outline",bgColor:"bg-white",color:"#ffffff",status:"Pre-Checked",dayName:s},{title:"Checked",value:y,iconColor:"#3B82F6",topIcon:"mdi:check-circle",bgColor:"bg-white",color:"#ffffff",status:"Checked",dayName:s},{title:"In-Progress",value:k,iconColor:"#8B5CF6",topIcon:"mdi:progress-clock",bgColor:"bg-white",color:"#ffffff",status:"In-Progress",dayName:s},{title:"Discharged",value:N,iconColor:"#EF4444",topIcon:"mdi:account-arrow-right",bgColor:"bg-white",color:"#ffffff",status:"Discharged",dayName:s}]},[n,d,r]);return t.jsx("div",{className:"w-full py-1 bg-gray-100",children:t.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-1.5",children:p.map((a,s)=>t.jsxs("div",{className:`p-3 rounded-xl shadow-sm ${a.bgColor} h-20 sm:h-22 md:h-24 flex flex-col justify-between transition-all duration-300 border border-gray-200`,children:[t.jsxs("div",{className:"flex items-center justify-between mb-2 sm:mb-3",children:[t.jsx("div",{className:"flex-1 min-w-0",children:t.jsx("h1",{className:"text-xs sm:text-sm text-gray-600 truncate",children:a.title})}),t.jsx("div",{className:"bg-white rounded-md p-0.5 flex-shrink-0",children:t.jsx(E,{icon:a.topIcon,width:"20",height:"20",className:"sm:w-6 sm:h-6",color:a.iconColor})})]}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("p",{className:"text-sm sm:text-md md:text-lg font-semibold text-gray-800",children:a.value}),t.jsx("div",{className:"flex text-xs",children:t.jsx("span",{className:"text-gray-500 text-xs sm:text-sm",children:a.dayName})})]})]},s))})})},O=()=>{const n=S(),[x,d]=f.useState(""),[r,p]=f.useState("All"),[a,s]=f.useState(""),[l,h]=f.useState({page:1,limit:20}),b=()=>new Date().toISOString().split("T")[0],y=e=>{if(r!==e){switch(p(e),e.toLowerCase()){case"today":s(b());break;case"upcoming":s("");break;case"history":s("");break;default:s("");break}h(i=>({...i,page:1}))}},w=()=>{if(a)return a;if(r.toLowerCase()==="today")return b()},N=()=>({ipdOpd:"OPD",page:l.page,limit:l.limit,user:x||void 0,date:w()}),k=()=>{const e=new Date,i=new Date(e);i.setDate(i.getDate()+1);const u=i.toISOString().split("T")[0],m=new Date(e);m.setDate(m.getDate()-1);const I=m.toISOString().split("T")[0];switch(r.toLowerCase()){case"today":return{disabled:!0,min:void 0,max:void 0};case"upcoming":return{disabled:!1,min:u,max:void 0};case"history":return{disabled:!1,min:void 0,max:I};default:return{disabled:!1,min:void 0,max:void 0}}},{data:c,isLoading:o}=A(N()),D=g.get(c,"data.patientHistory",[]),v=g.get(c,"data.pagination.pages",1),C=(()=>{if(!D)return[];const e=new Date;return e.setHours(0,0,0,0),D.filter(i=>{const u=new Date(i.date);if(u.setHours(0,0,0,0),a){const m=new Date(a);return m.setHours(0,0,0,0),u.getTime()===m.getTime()}switch(r.toLowerCase()){case"today":return u.getTime()===e.getTime();case"upcoming":return u.getTime()>e.getTime();case"history":return u.getTime()<e.getTime();default:return!0}})})(),j={columns:[{title:"Patient ID",key:"patientId"},{title:"Patient Name",key:"patientName"},{title:"Contact Number",key:"contactNumber"},{title:"Checkup Date",key:"checkupDate"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:C==null?void 0:C.map((e,i)=>({key:i,patientId:g.get(e,"user.patientInfo.patientId","N/A"),patientName:g.get(e,"user.commonInfo.personalInfo.fullName","N/A"),contactNumber:g.get(e,"user.commonInfo.contactInfo.phone.primaryPhone","N/A"),checkupDate:T(g.get(e,"date","")).format("MMM-DD-YYYY"),status:t.jsx(L,{status:e==null?void 0:e.status}),action:t.jsxs("div",{className:"space-x-2",children:[t.jsx("button",{className:"py-0.5 px-1 text-primary rounded border-2 border-gray-400",onClick:()=>n(`${P.GENERALPRECHECKUPADD}/${e._id}`),children:"Pre-Checkup"}),t.jsx("button",{className:"px-1 py-1 text-white rounded bg-primary",onClick:()=>n(`${P.GENERALCHECKUPADD}/${e._id}`),children:"Checkup"}),t.jsx("button",{className:"px-2 py-1 text-white rounded bg-primary",onClick:()=>n(`${P.GENERALPATIENTDETAILS}/${e._id}`),children:"View"})]})}))};return t.jsxs(t.Fragment,{children:[t.jsx(F,{onSearch:e=>{d(e)},listTitle:"General Checkup List",FilterSection:()=>{const e=k();return t.jsx("div",{className:"flex gap-5",children:t.jsx("div",{children:t.jsx(R,{date:a,setDate:s,disabled:e.disabled,min:e.min,max:e.max})})})}}),t.jsx("div",{className:"mt-0.5 mb-0.5",children:t.jsx($,{patientHistoryData:{...c,data:{...c==null?void 0:c.data,patientHistory:C}},isLoading:o,tabValue:r,selectedDate:a})}),t.jsx(G,{tabs:["All","Today","UpComing","History"],defaultTab:r,onTabChange:y}),t.jsx(H,{columns:j.columns,rows:j.rows,loading:o,pagination:{currentPage:l.page,totalPage:v,limit:l.limit,onClick:e=>{h(i=>({page:e.page??i.page,limit:e.limit??i.limit}))}}})]})},B=Object.freeze(Object.defineProperty({__proto__:null,GeneralCheckupPage:O},Symbol.toStringTag,{value:"Module"}));export{R as D,O as G,B as a};
