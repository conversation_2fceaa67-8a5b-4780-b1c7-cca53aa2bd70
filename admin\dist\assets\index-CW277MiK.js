import{a5 as N,cs as he,di as je,aP as ge,a2 as e,dY as fe,bF as Ne,ad as ye,aM as ee,dZ as be,d_ as pe,av as Y,af as W,ah as Z,aQ as se,aj as X,c2 as ve,d$ as Ae,ai as Se,b3 as Te,e0 as te,bZ as me,bq as _,a1 as we,e1 as Re,aV as ke,bm as Ie,aX as Pe,e2 as oe,a9 as Le,aa as Ce,b_ as re,e3 as Ee,e4 as Be,e5 as Ue,al as De}from"./index-ClX9RVH0.js";import{J as Ke,G as Je,H as ze,z as Qe,y as We,F as Ze,K as Xe,M as es}from"./index-ClX9RVH0.js";import{A as ue}from"./AddProduct-DEXONU3r.js";function xe({reportData:a,onClose:i,popup:j}){var y,R,S,x,L,C,I,k,p,T,v,c,b,n,t,r,f,A,B,U,M,O,D;const s=N.useRef(null),{data:u}=he(),o=(R=(y=u==null?void 0:u.data)==null?void 0:y.hospital)==null?void 0:R[4],P=()=>{var $;const w=($=s.current)==null?void 0:$.innerHTML;if(!w)return;const E=window.open("","_blank","width=800,height=600");E&&(E.document.write(`
      <html>
        <head>
          <title>Lab Report</title>
          <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
          <style>
            @media print {
              .no-print { display: none; }
              body { margin: 0; padding: 0; }
            }
          </style>
        </head>
        <body class="p-4">
          ${w}
        </body>
      </html>
    `),E.document.close(),E.onload=()=>{E.focus(),E.print(),i==null||i()})},g=(L=(x=(S=a==null?void 0:a.user)==null?void 0:S.commonInfo)==null?void 0:x.personalInfo)!=null&&L.dob?je(a.user.commonInfo.personalInfo.dob):((C=a==null?void 0:a.noAuthUser)==null?void 0:C.age)||"N/A",d=((I=a==null?void 0:a.reportOutcome)==null?void 0:I.map(w=>({name:w.investigations,result:w.result,referenceRange:w.referenceRange,unit:w.unit,comment:w.comment})))||[],h=ge(()=>{i==null||i()});return e.jsxs(fe,{onClose:()=>i,ref:h,children:[e.jsx("div",{className:"max-w-4xl mx-auto bg-white",children:j==="print"&&e.jsx("div",{className:"mb-4 print:hidden no-print flex justify-end items-cente mt-12 mr-5",children:e.jsx(Ne,{onClick:P,title:"Print Bill",className:"flex items-center gap-2 bg-gray-600 hover:bg-gray-700 "})})}),e.jsxs("div",{ref:s,className:"bg-white",children:[e.jsxs("div",{className:"bg-blue-600 text-white p-4 relative",children:[e.jsx("div",{className:"absolute top-2 right-4 text-sm",children:"PAN No.: 608502595"}),e.jsxs("div",{className:"flex items-center gap-4 mt-4",children:[e.jsx("img",{src:"/ReportLogo.png",className:"h-20 w-auto rounded-full",alt:"hospital logo"}),e.jsxs("div",{className:"text-black",children:[e.jsx("h1",{className:"text-3xl font-bold mt-3",children:(o==null?void 0:o.hospitalName)||"Aarogya Niketan Hospital Pvt. Ltd"}),e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx("p",{className:"text-xl",children:"(आरोग्य निकेतन अस्पताल प्रा. लि.)"}),e.jsxs("p",{className:"text-sm",children:["Mo. ",(o==null?void 0:o.contactNo)||"9818832882, 9854029882"]}),e.jsx("p",{className:"text-sm",children:o==null?void 0:o.address})]})]})]}),e.jsx("div",{className:"absolute bottom-2 right-4",children:e.jsx("div",{className:"flex gap-1",children:[...Array(20)].map((w,E)=>e.jsx("div",{className:"w-1 h-6 bg-white",style:{height:Math.random()*20+10}},E))})})]}),e.jsx("div",{className:"p-4 border-b-2 border-gray-300",children:e.jsxs("div",{className:"grid grid-cols-12 gap-4 text-sm",children:[e.jsxs("div",{className:"col-span-5 space-y-2",children:[e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-24",children:"Patient Name"}),e.jsx("span",{className:"mr-4",children:((k=a==null?void 0:a.noAuthUser)==null?void 0:k.name)||((v=(T=(p=a==null?void 0:a.user)==null?void 0:p.commonInfo)==null?void 0:T.personalInfo)==null?void 0:v.fullName)||"N/A"})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-24",children:"Doctor Name"}),e.jsx("span",{className:"mr-4",children:(n=(b=(c=a==null?void 0:a.doctor)==null?void 0:c.commonInfo)==null?void 0:b.personalInfo)!=null&&n.fullName?`Dr. ${a.doctor.commonInfo.personalInfo.fullName}`:"SELF"})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-24",children:"Ref Hospital"}),e.jsx("span",{className:"mr-4",children:(o==null?void 0:o.hospitalName)||"Aarogya Niketan Hospital Pvt. Ltd"})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-24",children:"Lab No."}),e.jsx("span",{className:"mr-4",children:((r=(t=a==null?void 0:a.user)==null?void 0:t.patientInfo)==null?void 0:r.patientId)||"66"})]})]}),e.jsxs("div",{className:"col-span-5 space-y-2",children:[e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-24",children:"Sex/Age"}),e.jsxs("span",{className:"mr-4",children:[g," Year / ",((f=a==null?void 0:a.noAuthUser)==null?void 0:f.gender)||((U=(B=(A=a==null?void 0:a.user)==null?void 0:A.commonInfo)==null?void 0:B.personalInfo)==null?void 0:U.gender)||"Male"]})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-24",children:"Sample Coll at"}),e.jsx("span",{className:"mr-4",children:((M=a==null?void 0:a.createdAt)==null?void 0:M.split("T")[0])||"N/A"})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-24",children:"Reporting Date"}),e.jsx("span",{className:"mr-4",children:((O=a==null?void 0:a.updatedAt)==null?void 0:O.split("T")[0])||"N/A"})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-24",children:"Address"}),e.jsx("span",{className:"mr-4",children:"Janakpur"})]})]})]})}),(a==null?void 0:a.test)&&e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"bg-gray-200 text-center py-2 font-bold text-lg mb-2",children:((D=a==null?void 0:a.department)==null?void 0:D.name)||"TEST RESULTS"}),e.jsxs("table",{className:"w-full text-sm border-collapse",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100",children:[e.jsx("th",{className:"text-left p-2 border border-gray-300 font-semibold",children:"Tests"}),e.jsx("th",{className:"text-center p-2 border border-gray-300 font-semibold",children:"Result"}),e.jsx("th",{className:"text-center p-2 border border-gray-300 font-semibold",children:"Reference Range"}),e.jsx("th",{className:"text-center p-2 border border-gray-300 font-semibold",children:"Unit"})]})}),e.jsx("tbody",{children:d.map((w,E)=>e.jsxs("tr",{className:E%2===0?"bg-white":"bg-gray-50",children:[e.jsx("td",{className:"p-2 border border-gray-300",children:w.name}),e.jsx("td",{className:"p-2 border border-gray-300 text-center font-medium",children:w.result}),e.jsx("td",{className:"p-2 border border-gray-300 text-center",children:w.referenceRange}),e.jsx("td",{className:"p-2 border border-gray-300 text-center",children:w.unit})]},E))})]})]}),e.jsxs("div",{className:"p-4 border-t-2 border-gray-300",children:[e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"border-t border-gray-400 w-32 mb-2"}),e.jsx("p",{className:"font-semibold",children:"LAB ASSISTANT"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"border-t border-gray-400 w-32 mb-2 mt-7"}),e.jsx("p",{className:"font-semibold",children:"Medical Lab Technician"}),e.jsx("p",{children:"CMLT(CTEVT)"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"border-t border-gray-400 w-32 mb-2 mt-11"}),e.jsx("p",{className:"font-semibold",children:"MR. SANTOSH KUMAR YADAV"}),e.jsx("p",{children:"Medical Lab Technologist"}),e.jsx("p",{children:"NHPC No. A-1229 MLT"})]})]}),e.jsx("div",{className:"text-center mt-6",children:e.jsx("div",{className:"inline-block bg-blue-600 text-white px-4 py-1 text-sm font-semibold",children:'"Please correlate clinically"'})})]})]})]})}const qe=()=>{var v,c,b,n;const a=ye(),[i,j]=N.useState(1),[s,u]=N.useState({date:"",search:"",status:"",page:1,limit:50}),[o,P]=N.useState(50),g=s.search,d=ee({reportType:"RADIOLOGY",search:s.search,date:s.date,status:s.status,...g?{}:{page:i,limit:o}}),{data:h,isLoading:y}=be(d);console.log(h,"reportsData");const[R,S]=N.useState(),[x,L]=N.useState(""),{mutate:C}=pe({id:JSON.stringify([x])}),I={columns:[{title:"S.N.",key:"sn"},{title:"Report ID",key:"reportId"},{title:"Patient Name",key:"patientName"},{title:"Test Type",key:"testType"},{title:"Date Tested",key:"dateTested"},{title:"Issue Date",key:"issueDate"},{title:"Doctor Name",key:"doctorName"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:(c=(v=h==null?void 0:h.data)==null?void 0:v.labreports)==null?void 0:c.map((t,r)=>{var f,A,B;return{sn:r+1,reportId:Y.get(t,"reportNumber"),patientName:Y.get(t,"noAuthUser.name")||Y.get(t,"user.commonInfo.personalInfo.fullName","Patient Not Found"),testType:t!=null&&t.test?(f=t==null?void 0:t.test)==null?void 0:f.specifiedTest:"Deleted Test",dateTested:t==null?void 0:t.updatedAt.slice(0,10),issueDate:t==null?void 0:t.date,result:(A=t==null?void 0:t.reportOutcome)==null?void 0:A.map(U=>e.jsxs("div",{children:[U.investigations," : ",U.comment,","]})),doctorName:`Dr. ${(B=t==null?void 0:t.doctor)==null?void 0:B.commonInfo.personalInfo.fullName}`,status:(t==null?void 0:t.status)==="DISPATCHED"?e.jsx(Z,{status:"SUBMITTED"}):(t==null?void 0:t.status)==="PUBLISHED"?e.jsx(Z,{status:"PUBLISHED"}):"",action:(t==null?void 0:t.status)==="DISPATCHED"?e.jsx(W,{onEdit:()=>{a(`/radiology/test-request/add-report/${t==null?void 0:t._id}-edit`,{replace:!0})},onShow:()=>{p("show"),S(t)},onDelete:async()=>{L(t._id),await C()}}):e.jsx(W,{onPrint:()=>{S(t),p("print")},onShow:()=>{p("show"),S(t)},onDelete:async()=>{L(t._id),await C()}})}})},[k,p]=N.useState(""),T=N.useCallback(()=>p(""),[]);return e.jsxs("div",{children:[e.jsx(se,{onSearch:!0,onDatePicker:!0,toSearch:"Search by Report ID",date:s.date,onReportStatus:!0,onReportStatusFunc:t=>u({...s,status:t}),setDate:t=>u({...s,date:t}),onSearchFunc:t=>u({...s,search:t})}),e.jsx(X,{rows:I.rows,columns:I.columns,loading:y,pagination:{currentPage:i,totalPage:((n=(b=h==null?void 0:h.data)==null?void 0:b.pagination)==null?void 0:n.pages)||1,limit:o,onClick:({page:t,limit:r})=>{t&&j(t),r&&P(r)}}}),k==="show"&&e.jsx(xe,{reportData:R,onClose:T}),k==="print"&&e.jsx(xe,{reportData:R,popup:k,onClose:T})]})},He=()=>{var v,c,b,n;const[a,i]=N.useState("All"),[j,s]=N.useState({search:"",page:1,limit:5}),u=j.search,o=ee({search:j.search,upperHirachy:"6823163b7abfaa19e7374883",...u?{}:{page:j.page,limit:j.limit}}),[P,g]=N.useState(""),[d,h]=N.useState(),y=N.useCallback(()=>g(""),[]),[R,S]=N.useState(""),{data:x,isSuccess:L,isLoading:C}=ve(o);console.log(x,"EquipmentData");const{mutate:I}=Ae({id:JSON.stringify([R])}),k=["Equipment","Chemical","Consumable","Glassware","Plasticware","Safety Supplies","Instruments","Kits"],p=(c=(v=x==null?void 0:x.data)==null?void 0:v.equipments)==null?void 0:c.filter(t=>a==="All"?!0:a==="Others"?!k.includes(t==null?void 0:t.category):(t==null?void 0:t.category)===a),T={columns:[{title:" Item Code",key:"itemCode"},{title:"Category",key:"category"},{title:"Name",key:"name"},{title:"Department",key:"department"},{title:"Short Discription",key:"shortDiscription"},{title:"Action",key:"action"}],rows:L?p.map((t,r)=>{var f;return{key:r,itemCode:t==null?void 0:t.equipmentCode,category:t==null?void 0:t.category,name:t==null?void 0:t.name,department:(f=t==null?void 0:t.department)==null?void 0:f.name,shortDiscription:t==null?void 0:t.description,action:e.jsx(W,{onEdit:()=>{h(t),g("edit")},onDelete:()=>{S(t==null?void 0:t._id),I()}})}}):[]};return e.jsxs("div",{children:[e.jsx("div",{className:"mb-4",children:e.jsx(se,{onSearch:!0,button:!0,onSearchFunc:t=>s({...j,search:t}),buttonText:"Add Product",buttonAction:()=>g("add")})}),e.jsx("div",{className:"bg-white px-5 rounded-t-md",children:e.jsx(Se,{tabs:["All","Equipment","Chemical","Consumable","Glassware","Plasticware","Safety Supplies","Instruments","Kits","Others"],defaultTab:a,onTabChange:t=>i(t)})}),e.jsx(X,{rows:T.rows,columns:T.columns,loading:C,pagination:{currentPage:j.page,totalPage:((n=(b=x==null?void 0:x.data)==null?void 0:b.pagination)==null?void 0:n.pages)||1,limit:j.limit,onClick:({page:t,limit:r})=>{t&&s({...j,page:t}),r&&s({...j,limit:r})}}}),P==="add"&&e.jsx(ue,{onClose:y}),P==="edit"&&e.jsx(ue,{EditData:d,onClose:y})]})},Ge=({modalProps:a,setModalProps:i})=>{var u,o,P,g,d,h,y,R,S,x,L,C,I,k,p,T,v,c,b,n,t,r,f,A,B,U,M,O;console.log(a.selectedData,"models after popup");const j=[{title:"Batch no.",value:(u=a==null?void 0:a.selectedData)==null?void 0:u.batchNo},{title:"Current Stock",value:(o=a==null?void 0:a.selectedData)==null?void 0:o.availableStock},{title:"Min. requirement stock",value:(P=a==null?void 0:a.selectedData)==null?void 0:P.minimumTreshHold},{title:"Unit price",value:(g=a==null?void 0:a.selectedData)==null?void 0:g.mrpRate},{title:"Total price",value:(d=a==null?void 0:a.selectedData)==null?void 0:d.purchaseRate},{title:"Expiry date",value:(h=a==null?void 0:a.selectedData)==null?void 0:h.expiryDate},{title:"Manufacture date",value:(y=a==null?void 0:a.selectedData)==null?void 0:y.manufacturedDate}],s=[{title:"Supplier name",value:(L=(x=(S=(R=a==null?void 0:a.selectedData)==null?void 0:R.vendor)==null?void 0:S.commonInfo)==null?void 0:x.personalInfo)==null?void 0:L.fullName},{title:"Email",value:(I=(C=a==null?void 0:a.selectedData)==null?void 0:C.vendor)==null?void 0:I.email},{title:"Phone",value:(c=(v=(T=(p=(k=a==null?void 0:a.selectedData)==null?void 0:k.vendor)==null?void 0:p.commonInfo)==null?void 0:T.contactInfo)==null?void 0:v.phone)==null?void 0:c.primaryPhone}];return e.jsxs("div",{className:"flex flex-col  px-2 gap-3 relative",children:[e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"text-2xl text-textGray font-medium",children:"Product Details"})}),e.jsx("div",{onClick:()=>i({...a,open:!1}),className:"absolute top-2 right-5",children:e.jsx(Te,{icon:"material-symbols:close-rounded",className:"hover:text-red duration-300 transition-all",fontSize:20})}),e.jsx("section",{className:"flex place-items-center justify-between border-b pb-[10px]  border-b-[#BDBDBD]",children:e.jsx("div",{className:"flex flex-col gap-1",children:e.jsxs("p",{className:"font-bold text-xl",children:["#",(b=a==null?void 0:a.selectedData)==null?void 0:b.invoiceNo]})})}),e.jsxs("section",{className:"flex flex-col gap-3 border-b border-b-[#BDBDBD] pb-[10px] py-2",children:[e.jsx("p",{className:"font-semibold text-sm",children:"Item"}),e.jsxs("div",{className:"flex justify-between place-items-center",children:[e.jsxs("section",{className:"flex flex-col gap-1",children:[e.jsx("p",{className:"text-base font-semibold",children:(t=(n=a.selectedData)==null?void 0:n.product)==null?void 0:t.name}),e.jsx("p",{className:"text-sm font-medium",children:(f=(r=a.selectedData)==null?void 0:r.product)==null?void 0:f.category}),e.jsx("p",{className:" text-sm font-medium text-[#656565] pt-5",children:"Description"}),e.jsx("p",{className:"text-sm",children:(B=(A=a.selectedData)==null?void 0:A.product)==null?void 0:B.description})]}),e.jsxs("section",{className:"flex place-items-center gap-1",children:[e.jsx("div",{className:`w-[8px] h-[8px] rounded-full ${((U=a.selectedData)==null?void 0:U.status)==="IN-STOCK"?"bg-[#17878e]":((M=a.selectedData)==null?void 0:M.status)==="LOW-STOCK"?"bg-[#ffcc00]":"bg-[#ff3b2f]"}`}),e.jsx("p",{className:" text-sm text-[#656565]",children:(O=a.selectedData)==null?void 0:O.status})]})]})]}),e.jsx("section",{className:"flex flex-col gap-5 border-b border-b-[#BDBDBD] pb-[10px]",children:j==null?void 0:j.map(D=>e.jsxs("div",{className:"flex place-items-center justify-between",children:[e.jsx("p",{children:D.title}),e.jsx("p",{className:"text-[#6B7290]",children:D.value})]}))}),e.jsx("section",{className:"flex flex-col gap-5 ",children:s==null?void 0:s.map(D=>e.jsxs("div",{className:"flex place-items-center justify-between",children:[e.jsx("p",{children:D.title}),e.jsx("p",{className:"text-[#6B7290]",children:D.value})]}))})]})},Fe=({onClose:a,editData:i})=>{var y,R,S,x,L,C,I,k,p,T,v,c,b,n,t,r,f,A,B,U,M,O,D,w,E,$,K,J,z,m,F,ae,ne,ce,le,ie,de;const j=()=>a(),s=i,u=(y=s==null?void 0:s.testList)==null?void 0:y.map(l=>{var H;return{testId:(H=l==null?void 0:l.test)==null?void 0:H._id,paymentStatus:l==null?void 0:l.paymentStatus}}),{data:o}=te({testFor:"RADIOLOGY"}),P=u==null?void 0:u.map(l=>l==null?void 0:l.testId),g=(S=(R=o==null?void 0:o.data)==null?void 0:R.laboratory)==null?void 0:S.filter(l=>P==null?void 0:P.includes(l._id)),d=((L=(x=u==null?void 0:u.filter(l=>l.paymentStatus==="PAID"))==null?void 0:x.map(l=>l.testId))==null?void 0:L.reduce((l,H)=>{const G=g==null?void 0:g.find(V=>V._id===H);return l+((G==null?void 0:G.rate)||0)},0))||0,h=s.totalCharge-d;return e.jsx(me,{onClose:j,heading:"Invoice Details",className:"h-[750px] overflow-scroll w-[750px]",children:e.jsxs("div",{className:"max-w-4xl mx-auto bg-white px-4 rounded-2xl",children:[e.jsxs("div",{className:"border-b pb-4 mb-4",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"Test Bill Summary"}),e.jsxs("p",{className:"text-gray-600",children:["Transaction No:"," ",e.jsx("span",{className:"font-medium",children:s==null?void 0:s.transactionNo})]}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Date: ",s!=null&&s.date?new Date(s.date).toLocaleDateString():"-"]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Patient Information"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Name:"})," ",(C=s==null?void 0:s.noAuthUser)!=null&&C.name?(I=s==null?void 0:s.noAuthUser)==null?void 0:I.name:((T=(p=(k=s==null?void 0:s.user)==null?void 0:k.commonInfo)==null?void 0:p.personalInfo)==null?void 0:T.fullName)||"-"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Gender:"})," ",(v=s==null?void 0:s.noAuthUser)!=null&&v.gender?(c=s==null?void 0:s.noAuthUser)==null?void 0:c.gender:((t=(n=(b=s==null?void 0:s.user)==null?void 0:b.commonInfo)==null?void 0:n.personalInfo)==null?void 0:t.gender)||"-"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"DOB:"})," ",(r=s==null?void 0:s.noAuthUser)!=null&&r.age?(f=s==null?void 0:s.noAuthUser)==null?void 0:f.age:((U=(B=(A=s==null?void 0:s.user)==null?void 0:A.commonInfo)==null?void 0:B.personalInfo)==null?void 0:U.dob)||"-"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Phone:"})," ",(M=s==null?void 0:s.noAuthUser)!=null&&M.contact?(O=s==null?void 0:s.noAuthUser)==null?void 0:O.contact:(($=(E=(w=(D=s==null?void 0:s.user)==null?void 0:D.commonInfo)==null?void 0:w.contactInfo)==null?void 0:E.phone)==null?void 0:$.primaryPhone)||"-"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Address:"})," ",(K=s==null?void 0:s.noAuthUser)!=null&&K.address?(J=s==null?void 0:s.noAuthUser)==null?void 0:J.address:((ae=(F=(m=(z=s==null?void 0:s.user)==null?void 0:z.commonInfo)==null?void 0:m.contactInfo)==null?void 0:F.address)==null?void 0:ae.currentAddress)||"-"]})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Tests"}),e.jsx("ul",{className:"space-y-3",children:(ne=s==null?void 0:s.testList)==null?void 0:ne.map(l=>{var H,G,V,Q,q;return e.jsxs("li",{className:"border p-3 rounded-lg bg-gray-50 list-none",children:[e.jsx("p",{className:"font-medium text-gray-800",children:((H=l==null?void 0:l.test)==null?void 0:H.specifiedTest)||"Unknown Test"}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Department: ",((G=l==null?void 0:l.department)==null?void 0:G.name)||"-"]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Doctor:"," ",((q=(Q=(V=l==null?void 0:l.doctor)==null?void 0:V.commonInfo)==null?void 0:Q.personalInfo)==null?void 0:q.fullName)||"N/A"]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Date:"," ",l!=null&&l.date?new Date(l.date).toLocaleDateString():"-"]}),e.jsxs("p",{className:"text-sm text-gray-600",children:["Status:"," ",e.jsx("span",{className:"font-semibold text-green-600",children:(l==null?void 0:l.resportStatus)||"N/A"})]})]},l==null?void 0:l._id)})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Payment Details"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Total Charge:"})," NPR ",(s==null?void 0:s.totalCharge)??0]}),e.jsxs("p",{children:[e.jsx("strong",{children:"PaidAmount:"})," NPR ",d??0]}),e.jsxs("p",{children:[e.jsx("strong",{children:"DueAmount:"})," ",h??0]}),e.jsxs("p",{className:"flex items-center ",children:[e.jsx("strong",{children:"Status:"}),(()=>{var V,Q;const l=(V=s==null?void 0:s.testList)==null?void 0:V.every(q=>(q==null?void 0:q.paymentStatus)===_.PAID),H=(Q=s==null?void 0:s.testList)==null?void 0:Q.some(q=>(q==null?void 0:q.paymentStatus)===_.PAID),G=l?_.PAID:H?_.PARTPAID:_.PENDING;return e.jsx(Z,{status:G===_.PAID?"PAID":G===_.PARTPAID?"PARTIALLY_PAID":"PENDING"})})()]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Method:"})," ",(s==null?void 0:s.paymentMethod)||"N/A"]})]})]}),(s==null?void 0:s.paymentMethod)==="BANK"&&(s==null?void 0:s.bank)&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"Bank Details"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Bank:"})," ",((ce=s==null?void 0:s.bank)==null?void 0:ce.bankName)||"-"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Branch:"})," ",((le=s==null?void 0:s.bank)==null?void 0:le.bankBranch)||"-"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Account Holder:"})," ",((ie=s==null?void 0:s.bank)==null?void 0:ie.accountHolderName)||"-"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Account No:"})," ",((de=s==null?void 0:s.bank)==null?void 0:de.accountNumber)||"-"]})]})]})]})})},_e=({onClose:a,paymentData:i})=>{var c,b;const[j,s]=N.useState({state:"",data:null});console.log(i,"pay");const[u,o]=N.useState({test:"",paymentStatus:"",amount:""}),[P,g]=N.useState(!1),[d,h]=N.useState([]),y=we({initialValues:{testList:(i==null?void 0:i.testList)||[]},enableReinitialize:!0,onSubmit:()=>{}}),{data:R}=te(),S=((b=(c=R==null?void 0:R.data)==null?void 0:c.laboratory)==null?void 0:b.map(n=>({value:n.rate,label:`${n.rate} (${n.specifiedTest}|${n.gender})`,testId:n._id})))||[],x=i==null?void 0:i.testList.filter(n=>{var t;return n.resportStatus!==Re.REPORTED&&((t=n==null?void 0:n.mainHirachy)==null?void 0:t._id)==="6823163b7abfaa19e7374883"}).map(n=>{var t,r;return{value:(t=n.test)==null?void 0:t._id,label:(r=n.test)==null?void 0:r.specifiedTest}}),L=ke({test:Pe().required("Test Type is required"),amount:Ie().required("Amount is required").positive("Amount must be positive")}),C=n=>{const t=n.target.value;o(f=>({...f,test:t,amount:""}));const r=S.find(f=>f.testId===t);r&&o(f=>({...f,test:t,amount:r.value.toString()}))},I=async()=>{try{await L.validate(u,{abortEarly:!1});const n=i==null?void 0:i.testList.find(A=>A.test._id===u.test);if(!n){g(!0);return}if(!S.find(A=>A.value===parseFloat(u.amount))){g(!0);return}const r={testId:n._id,testType:n.test.specifiedTest,amount:parseFloat(u.amount),status:n==null?void 0:n.paymentStatus};if(d.some(A=>A.testId===r.testId)){g(!0);return}h([...d,r]),o({test:"",paymentStatus:"",amount:""}),g(!1)}catch{g(!0)}},k=n=>{h(d.filter((t,r)=>r!==n))},p=d.reduce((n,t)=>n+t.amount,0),T={columns:[{title:"Test Type",key:"testType"},{title:"Amount",key:"amount"},{title:"Payment Status",key:"status"},{title:"Action",key:"action"}],rows:d.map((n,t)=>({testType:n.testType,amount:n.amount,status:n.status==="PENDING"?e.jsx("div",{className:"ml-40",children:e.jsx(oe,{status:"PENDING"})}):n.status==="PAID"?e.jsx("div",{className:"ml-40",children:e.jsx(oe,{status:"PAID"})}):"",action:e.jsx(W,{onDelete:()=>k(t)})}))},v=()=>a==null?void 0:a();return e.jsx(me,{onClose:v,heading:"Radiology Payment",className:"h-[750px] overflow-scroll w-[750px]",children:e.jsx(Le,{value:y,children:e.jsxs(Ce,{onSubmit:y.handleSubmit,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("h1",{children:["Name: ",Y.get(i,"noAuthUser.name")||Y.get(i,"user.commonInfo.personalInfo.fullName")]}),e.jsxs("h1",{children:["Date of Birth: ",Y.get(i,"noAuthUser.age")||Y.get(i,"user.commonInfo.personalInfo.dob")]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-x-4 gap-y-2",children:[e.jsx(re,{name:"test",label:"Test Type",type:"dropdown",formik:y,options:x,value:u.test,placeholder:"Select Test Type",onChange:C}),e.jsx(re,{name:"amount",label:"Test Type Amount",type:"dropdown",formik:y,options:S,value:u.amount,placeholder:"Select Test Type Amount",onChange:n=>o(t=>({...t,amount:n.target.value}))})]}),P&&e.jsx("p",{className:"text-red text-sm",children:"Test type already exists, fields are empty, or invalid input."}),e.jsx("div",{className:"py-5 flex justify-end space-x-3",children:e.jsx("button",{onClick:I,type:"button",className:"px-4 py-2 bg-primary text-white rounded",children:"Add Payment"})}),e.jsx(X,{loading:!1,columns:T.columns,rows:T.rows}),e.jsxs("div",{className:"flex justify-end items-center text-xl font-semibold",children:["Total Amount: ",p]}),e.jsxs("div",{className:"py-5 flex justify-end space-x-3",children:[e.jsx("button",{type:"button",onClick:v,className:"px-4 py-2 bg-gray-400 text-white rounded",children:"Cancel"}),e.jsx("button",{type:"button",onClick:()=>s(n=>({...n,state:"pay",data:{...i,testData:d,totalAmount:p,_id:i==null?void 0:i._id}})),className:"px-4 py-2 bg-teal-600 text-white rounded",children:"Pay Now"}),j.state==="pay"&&e.jsx(Ee,{onClose:v,data:j.data})]})]})})})},$e=()=>{var k,p,T,v;const[a,i]=N.useState(""),[j,s]=N.useState(""),[u,o]=N.useState({}),[P,g]=N.useState(""),[d,h]=N.useState({status:"",date:"",search:"",page:1,limit:5}),{data:y}=te({testFor:"RADIOLOGY"}),R=d.search,S=ee({search:d.search,date:d.date,paymentStatus:d.status,...R?{}:{page:d.page,limit:d.limit}}),{data:x,isLoading:L}=Be(S),{mutate:C}=Ue({id:JSON.stringify([a])}),I={columns:[{title:"S.N.",key:"sn"},{title:"Test No",key:"requestId"},{title:"Date",key:"dateReq"},{title:"Patient",key:"patientName"},{title:"Total",key:"total"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:((p=(k=x==null?void 0:x.data)==null?void 0:k.radiologytests)==null?void 0:p.map((c,b)=>{var O,D,w,E,$,K,J,z;const n=(c==null?void 0:c.testList)??[],t=n.every(m=>(m==null?void 0:m.paymentStatus)===_.PAID),r=n.some(m=>(m==null?void 0:m.paymentStatus)===_.PAID),f=t?_.PAID:r?_.PARTPAID:_.PENDING,A=n==null?void 0:n.map(m=>{var F;return{testId:(F=m==null?void 0:m.test)==null?void 0:F._id,paymentStatus:m==null?void 0:m.paymentStatus}}),B=A==null?void 0:A.map(m=>m==null?void 0:m.testId),U=(D=(O=y==null?void 0:y.data)==null?void 0:O.laboratory)==null?void 0:D.filter(m=>B==null?void 0:B.includes(m._id)),M=U==null?void 0:U.reduce((m,F)=>m+(F==null?void 0:F.rate),0);return{sn:b+1,requestId:c==null?void 0:c.predefinedBillNo,patientName:(w=c==null?void 0:c.noAuthUser)!=null&&w.name?(E=c==null?void 0:c.noAuthUser)==null?void 0:E.name:((J=(K=($=c==null?void 0:c.user)==null?void 0:$.commonInfo)==null?void 0:K.personalInfo)==null?void 0:J.fullName)||"",dateReq:(z=c==null?void 0:c.date)==null?void 0:z.slice(0,10),total:(M==null?void 0:M.toFixed(2))||"0.00",status:e.jsx(Z,{status:f}),action:e.jsx(W,{onPay:()=>{n.every(F=>(F==null?void 0:F.paymentStatus)===_.PAID)?De.info("All tests already marked as PAID"):(o(c),g("PaymentStatus"))},onShow:()=>{o(c),s("show")},onDelete:()=>{i(c==null?void 0:c._id),C()}})}}))||[]};return e.jsxs("div",{children:[e.jsx(se,{onSearch:!0,toSearch:"Search by Test No",onDatePicker:!0,date:d.date,setDate:c=>h({...d,date:c}),onPaymentStatus:!0,onSearchFunc:c=>h({...d,search:c}),onPaymentStatusFunc:c=>h({...d,status:c})}),e.jsx(X,{rows:I.rows,columns:I.columns,loading:L,pagination:{currentPage:d.page,totalPage:((v=(T=x==null?void 0:x.data)==null?void 0:T.pagination)==null?void 0:v.pages)||1,limit:d.limit,onClick:({page:c,limit:b})=>{c&&h({...d,page:c}),b&&h({...d,limit:b})}}}),j==="show"&&e.jsx(Fe,{editData:u,onClose:()=>s("")}),P==="PaymentStatus"&&e.jsx(_e,{onClose:()=>g(""),paymentData:u})]})};export{Ke as RadiologyDueList,Je as RadiologyInventory,ze as RadiologyInventoryDetails,Ge as RadiologyInventoryProductDetails,$e as RadiologyInvoice,Qe as RadiologyInvoiceDetails,He as RadiologyProductList,We as RadiologyPurchase,qe as RadiologyReport,Ze as RadiologyTestRequest,Xe as ServiceType,es as TestListConfig};
