import{bR as s,bV as u}from"./index-ClX9RVH0.js";const r=s(u.OPERATIONTHEATRE,"ot-assignment"),n=r.useGetAll,p=r.useGetById,e=s("surgery-department","Surgery Department"),g=e.useGetAll,m=e.useCreate,o=e.useUpdate,y=e.useDeleteWithQuery,t=s("surgery-sub-department","Surgery Sub Department"),D=t.useGetAll,l=t.useCreate,S=t.useUpdate,c=t.useDeleteWithQuery;export{g as a,D as b,y as c,m as d,o as e,c as f,l as g,S as h,p as i,n as u};
