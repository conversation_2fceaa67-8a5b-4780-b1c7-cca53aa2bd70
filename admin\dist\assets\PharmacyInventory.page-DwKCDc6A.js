import{a5 as c,ad as x,aP as C,aM as f,bO as N,a2 as o,af as T,bP as j,ag as I,ah as O,aQ as A,aj as M}from"./index-ClX9RVH0.js";const D=()=>{var i,u,d,g,p;const[k,v]=c.useState({open:!1,selectedData:{}}),b=x(),[t,l]=c.useState({limit:50,page:1}),[s,n]=c.useState({selectedDepartment:"",selectedSpecialist:"",search:""});C(()=>v({...k,open:!1}));const P=f({search:s.search,combinedData:1,productCategory:s.selectedSpecialist,...s.search||s.selectedSpecialist?{}:{page:t.page,limit:t.limit}}),{data:e}=N(P);console.log((i=e==null?void 0:e.data)==null?void 0:i.medicalProductsInventory);const r={columns:[{title:"Medicine Name",key:"medicineName"},{title:"Categories",key:"categories"},{title:"Total Stock ",key:"stock"},{title:"Available Stock",key:"availableStock"},{title:"Available Status",key:"status"},{title:"Action",key:"action"}],rows:(d=(u=e==null?void 0:e.data)==null?void 0:u.medicalProductsInventory)==null?void 0:d.map((a,h)=>{var y,S;return{key:h,medicineName:((y=a==null?void 0:a.product)==null?void 0:y.name)??"-",categories:((S=a==null?void 0:a.product)==null?void 0:S.productCategory)??"-",stock:(a==null?void 0:a.totalStock)??"-",availableStock:(a==null?void 0:a.availableStock)??"-",status:o.jsx(O,{status:(a==null?void 0:a.availableStock)>40?"IN-STOCK":(a==null?void 0:a.availableStock)===0?"OUT-STOCK":"LOW-STOCK"}),action:o.jsx(T,{onShow:()=>{j(a),b(`${I.PHARMACYINVENTORYDetails}`)}})}})};return o.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[o.jsx(A,{headerTitle:"Inventory List",onSearch:!0,onMedicineCategory:!0,onFilter:!0,onSearchFunc:a=>n({...s,search:a}),onMedicineCategorySelectFunc:a=>n({...s,selectedSpecialist:a})}),o.jsx("div",{className:"bg-white rounded-md",children:o.jsx(M,{columns:r.columns,rows:r.rows,loading:!1,color:"bg-white ",textcolor:"text-gray-400",pagination:{currentPage:t.page,totalPage:(p=(g=e==null?void 0:e.data)==null?void 0:g.pagination)==null?void 0:p.pages,limit:t.limit,onClick:a=>{a.page&&l({...t,page:a.page}),a.limit&&l({...t,limit:a.limit})}}})})]})};export{D as PharmacyInventoryPage};
