import{a5 as p,ct as _,a2 as e,b3 as z,aK as v,ad as W,aD as J,aw as $,bw as K,dW as Q,av as r,dX as X,af as Z,ah as q,a7 as ee,aI as te,ag as se,ai as oe,aj as re,bJ as ne}from"./index-ClX9RVH0.js";import{a as ye}from"./index-ClX9RVH0.js";import{D as ie}from"./GeneralCheckupPage-_HRWelc0.js";import{G as we}from"./GeneralCheckupPage-_HRWelc0.js";import{i as ae,j as le}from"./Svg-BMTGOzwv.js";import{T as ce}from"./TransferPatient-qcC_AtDM.js";import{AddAppointment as ve}from"./AddAppointmentPage-DGtYqV0Z.js";import{CardiologyDepartmentPage as Pe}from"./CardiologyPage-Dok4FuN3.js";import{AddCardiology as Te}from"./AddCardiologyPage-DLLoc9Hx.js";import{DentalCareDepartmentPage as Ee}from"./DentalCarePage-DAuvBd-H.js";import{AddDentalCarePage as Fe}from"./AddDentalCarePage-DyIvTm2J.js";import{DermatologyDepartmentPage as Re}from"./DermatologyPage-D_YGfd9J.js";import{AddDermatology as Ge}from"./AddDermotologyPage-CGl0JwpK.js";import{ENTDepartmentPage as Be}from"./EntDepartmentPage-DQaFG1dH.js";import{AddENT as Ue}from"./AddENTPage-BNwx9o__.js";import{AddGeneralPreCheckupForm as Ye}from"./AddGeneralPreCheckupForm-CdrXvRJq.js";import{GeneralPhysicianPage as ze}from"./GeneralPhysicianPage-Cp4nL3HN.js";import{AddPhysicsian as Je}from"./AddGeneralPhysicsPage-Cw5kOmrr.js";import{GynecologyDepartmentPage as Ke}from"./GynecologyPage-CHG0vzAY.js";import{AddGynecologyPage as Xe}from"./AddGynecologyPage-CHuvYwlQ.js";import{NeurologyDepartmentPage as qe}from"./NeurologyPage-_5iQnKMs.js";import{AddNeuroLogy as tt}from"./AddNeuroLogyPage-DUGSkLaq.js";import{OrthomologyDepartmentPage as ot}from"./OrthomologyPage-CTugP4OO.js";import{OrthopedicsDepartmentPage as nt}from"./OrthopedicsPage-IRZUjgkG.js";import{AddOrthopedics as at}from"./AddOrthopedicsPage-DFPQwwaP.js";import{PediatricDepartmentPage as ct}from"./PediatricsPage-0XGJ0lp8.js";import{default as pt}from"./PatientViewsDetails-BhBLXP1F.js";import"./DepartmentHeader-Aj6XBXn4.js";const de=({data:t,setOpen:m,open:c})=>{var l;const n=p.useRef(null),x=_.useReactToPrint({contentRef:n,documentTitle:"Appointment_Details",pageStyle:`
      @media print {
        body {
          margin: 0;
          padding: 0;
        }
        .print-content {
          width: 100%;
          padding: 4mm;
          background: white;
          box-sizing: border-box;
          page-break-after: avoid;
        }
        .no-print {
          display: none !important;
        }
        .section {
          page-break-inside: avoid;
          break-inside: avoid;
        }
        .section table {
          width: 100%;
          border-collapse: collapse;
        }
        @page {
          size: A4;
          margin: 4mm;
        }
      }
    `});if(p.useEffect(()=>{if(!c)return;const a=()=>{m(!1),window.removeEventListener("afterprint",a)};return window.addEventListener("afterprint",a),()=>{window.removeEventListener("afterprint",a)}},[c,m]),!c)return null;const o=()=>{m(!1)};return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",onClick:o,children:e.jsx("div",{className:"relative w-11/12 max-w-[60vw] max-h-[90vh] p-6 bg-white rounded-lg shadow-lg overflow-y-auto print:max-w-full print:max-h-full print:rounded-none print:shadow-none print-content",onClick:a=>a.stopPropagation(),ref:n,children:e.jsxs("div",{className:"my-6 space-y-4 print:my-2 print:space-y-2 print:p-0",children:[e.jsx("div",{className:"no-print flex justify-end  mb-2",children:e.jsxs("button",{className:"py-2 px-6 text-white rounded bg-primary flex gap-2 hover:bg-[#055B9C] transition-colors",onClick:x,children:[e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.00033 6.50033V0.666992H14.0003V6.50033M4.00033 14.0003H2.33366C1.89163 14.0003 1.46771 13.8247 1.15515 13.5122C0.842587 13.1996 0.666992 12.7757 0.666992 12.3337V8.16699C0.666992 7.72497 0.842587 7.30104 1.15515 6.98848C1.46771 6.67592 1.89163 6.50033 2.33366 6.50033H15.667C16.109 6.50033 16.5329 6.67592 16.8455 6.98848C17.1581 7.30104 17.3337 7.72497 17.3337 8.16699V12.3337C17.3337 12.7757 17.1581 13.1996 16.8455 13.5122C16.5329 13.8247 16.109 14.0003 15.667 14.0003H14.0003M4.00033 10.667H14.0003V17.3337H4.00033V10.667Z",stroke:"#fff",strokeLinecap:"round",strokeLinejoin:"round"})}),"Print"]})}),e.jsxs("div",{className:"section flex justify-between items-center border rounded-md px-2 py-1 border-b-gray-300 print:py-1",children:[e.jsx("img",{src:"/companyLogo.png",alt:"hospitalLogo",className:"h-16"}),e.jsx("h1",{className:"text-lg font-bold print:text-sm",children:"Appointment Details"}),e.jsx("button",{className:"no-print",onClick:o,children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M18 6L6 18M6 6L18 18",stroke:"#0D0D0D",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsxs("div",{className:"section flex items-center justify-between gap-4 px-2 py-4 print:py-1",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx("div",{className:"bg-[#0774C2] rounded-full p-2 text-white text-sm font-semibold print:text-xs print:p-1",children:((l=t.patientName)==null?void 0:l.slice(0,2).toUpperCase())||"ND"}),e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx("span",{className:"text-sm print:text-[10px]",children:"Patient Name"}),e.jsx("span",{className:"font-semibold text-sm print:text-[10px]",children:t.patientName})]})]}),e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx("span",{className:"p-1 bg-green rounded-full print:p-0.5"}),e.jsx("span",{className:"text-sm print:text-[10px]",children:t.status})]})]}),e.jsxs("div",{className:"section flex gap-2 flex-col p-2 print:p-1",children:[e.jsx("span",{className:"text-sm font-semibold print:text-[10px]",children:"Remarks"}),e.jsx("p",{className:"text-sm line-clamp-3 print:text-[10px]",children:(t==null?void 0:t.remark)||"No remarks"})]}),e.jsxs("div",{className:"section grid grid-cols-3 gap-8 py-4 print:grid-cols-2 print:gap-4 print:py-1",children:[e.jsxs("div",{className:"flex gap-4 px-1",children:[e.jsx(ae,{}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm print:text-[10px]",children:"Date"}),e.jsx("span",{className:"text-sm font-semibold print:text-[10px]",children:t==null?void 0:t.date})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(le,{}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm print:text-[10px]",children:"Doctor Name"}),e.jsx("span",{className:"text-sm font-semibold print:text-[10px]",children:t==null?void 0:t.doctorName})]})]})]}),e.jsx("div",{className:"section grid grid-cols-1 py-4 print:py-1",children:e.jsx("div",{className:"flex gap-4 px-1",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm print:text-[10px]",children:"Consultation Fee"}),e.jsx("span",{className:"text-sm font-semibold print:text-[10px]",children:t==null?void 0:t.consultationFee})]})})}),e.jsxs("div",{className:"section py-2 px-2 print:py-1 print:px-1 w-full",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2 print:text-sm print:mb-1",children:"General Info"}),e.jsx("table",{children:e.jsxs("tbody",{children:[e.jsxs("tr",{className:"border-b  border-gray-300 ",children:[e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:"Full Name"}),e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:t==null?void 0:t.patientName})]}),e.jsxs("tr",{className:"border-b border-gray-300",children:[e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:"Phone Number"}),e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:t==null?void 0:t.phoneNumber})]}),e.jsxs("tr",{className:"border-b border-gray-300",children:[e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:"Blood Group"}),e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:t==null?void 0:t.bloodGroup})]}),e.jsxs("tr",{className:"border-b border-gray-300",children:[e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:"Age"}),e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:t==null?void 0:t.dob})]}),e.jsxs("tr",{className:"border-b border-gray-300",children:[e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:"Gender"}),e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:t==null?void 0:t.gender})]}),e.jsxs("tr",{children:[e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:"Address"}),e.jsx("td",{className:"text-sm p-1 print:text-[10px] print:p-[0.3mm]",children:t==null?void 0:t.address})]})]})})]})]})})})};function pe(t,m,c){var a;if(!t)return[];const n=((a=t==null?void 0:t.data)==null?void 0:a.appointments)||[],o=m.toLowerCase()==="today"?"Today":c?v(c).format("dddd"):"All",l=u=>n.filter(h=>h.status===u).length;return[{title:"Total Appointments",value:n.length,iconColor:"#3B82F6",topIcon:"mdi:calendar-today",bgColor:"bg-white",color:"#ffffff",status:"Total",dayName:o},{title:"Pending",value:l("PENDING"),iconColor:"#F59E0B",topIcon:"mdi:clock-outline",bgColor:"bg-white",color:"#ffffff",status:"Pending",dayName:o},{title:"Confirmed",value:l("CONFIRMED"),iconColor:"#10B981",topIcon:"mdi:check-circle-outline",bgColor:"bg-white",color:"#ffffff",status:"Confirmed",dayName:o},{title:"In-Progress",value:l("IN-PROGRESS"),iconColor:"#3B82F6",topIcon:"mdi:play-circle-outline",bgColor:"bg-white",color:"#ffffff",status:"In-Progress",dayName:o},{title:"Cancelled",value:l("CANCELLED"),iconColor:"#EF4444",topIcon:"mdi:cancel",bgColor:"bg-white",color:"#ffffff",status:"Cancelled",dayName:o},{title:"Re-Scheduled",value:l("RE-SCHEDULED"),iconColor:"#8B5CF6",topIcon:"mdi:calendar-refresh-outline",bgColor:"bg-white",color:"#ffffff",status:"Re-Scheduled",dayName:o}]}const me=({appointmentsData:t,isLoading:m,tabValue:c,selectedDate:n})=>{const x=p.useMemo(()=>{var N;if(!t)return[];const o=((N=t==null?void 0:t.data)==null?void 0:N.appointments)||[],a=c.toLowerCase()==="today"?"Today":n?v(n).format("dddd"):"All",u=o.length,h=o.filter(d=>d.status==="PENDING").length,A=o.filter(d=>d.status==="CONFIRMED").length,b=o.filter(d=>d.status==="IN-PROGRESS").length,j=o.filter(d=>d.status==="CANCELLED").length,y=o.filter(d=>d.status==="RE-SCHEDULED").length;return[{title:"Total Appointments",value:u,iconColor:"#3B82F6",topIcon:"mdi:calendar-today",bgColor:"bg-white",color:"#ffffff",status:"Total",dayName:a},{title:"Pending",value:h,iconColor:"#F59E0B",topIcon:"mdi:clock-outline",bgColor:"bg-white",color:"#ffffff",status:"Pending",dayName:a},{title:"Confirmed",value:A,iconColor:"#10B981",topIcon:"mdi:check-circle-outline",bgColor:"bg-white",color:"#ffffff",status:"Confirmed",dayName:a},{title:"In-Progress",value:b,iconColor:"#3B82F6",topIcon:"mdi:play-circle-outline",bgColor:"bg-white",color:"#ffffff",status:"In-Progress",dayName:a},{title:"Cancelled",value:j,iconColor:"#EF4444",topIcon:"mdi:cancel",bgColor:"bg-white",color:"#ffffff",status:"Cancelled",dayName:a},{title:"Re-Scheduled",value:y,change:"3%",iconColor:"#8B5CF6",topIcon:"mdi:calendar-refresh-outline",bgColor:"bg-white",color:"#ffffff",status:"Re-Scheduled",dayName:a}]},[t,c,n]);return e.jsx("div",{className:"w-full py-1 bg-gray-100",children:e.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-1.5",children:x.map((o,l)=>e.jsxs("div",{className:`p-3 rounded-xl shadow-sm ${o.bgColor} h-20 sm:h-22 md:h-24 flex flex-col justify-between transition-all duration-300 border border-gray-200`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2 sm:mb-3",children:[e.jsx("div",{className:"flex-1 min-w-0",children:e.jsx("h1",{className:"text-xs sm:text-sm text-gray-600 truncate",children:o.title})}),e.jsx("div",{className:"bg-white rounded-md p-0.5 flex-shrink-0",children:e.jsx(z,{icon:o.topIcon,width:"20",height:"20",className:"sm:w-6 sm:h-6",color:o.iconColor})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm sm:text-md md:text-lg font-semibold text-gray-800",children:o.value}),e.jsx("div",{className:"flex text-xs",children:e.jsx("span",{className:"text-gray-500 text-xs sm:text-sm",children:o.dayName})})]})]},l))})})},Ne=()=>{const t=W(),[m,c]=p.useState({state:!1,patientId:""}),[n,x]=p.useState(""),[o,l]=p.useState("All"),{contentRef:a,exportToPDF:u,exportToExcel:h,handlePrint:A}=J(),b=()=>new Date().toISOString().split("T")[0],[j,y]=p.useState(!1),[N,d]=p.useState(null),[I,F]=p.useState(""),[C,T]=p.useState({page:1,limit:20}),k=s=>new Date(s).toISOString().split("T")[0],{data:L}=$({role:K.DOCTOR},{enabled:j===!0,refetchOnMount:!0,refetchOnWindowFocus:!0}),E=()=>{switch(o.toLowerCase()){case"new":return"PRESENT";case"today":return"PRESENT";case"history":return"PAST";case"upcoming":return n?void 0:"FUTURE";default:return}},R=s=>{if(o!==s)switch(l(s),s.toLowerCase()){case"today":x(b());break;case"upcoming":case"history":case"all":x("");break;default:x("");break}},M=()=>{const s=b(),i=new Date;i.setDate(i.getDate()+1);const g=i.toISOString().split("T")[0],f=new Date;f.setDate(f.getDate()-1);const P=f.toISOString().split("T")[0];switch(o.toLowerCase()){case"today":return{disabled:!0,value:s};case"upcoming":return{disabled:!1,min:g};case"history":return{disabled:!1,max:P};default:return{disabled:!1}}},G={page:C.page,limit:C.limit,search:I||void 0,date:n?k(n):void 0,aCondition:E()};console.log("API Params:",{tabValue:o,selectedDate:n,formattedDate:n?k(n):void 0,aCondition:E()});const{data:w,isLoading:S}=Q(G),O=r.get(w,"data.appointments",[]),B=r.get(w,"data.pagination.pages",1),{mutateAsync:H}=X(),U=s=>{const i=new Date(s),g=Date.now()-i.getTime(),f=new Date(g);return Math.abs(f.getUTCFullYear()-1970).toString()},V=s=>{const i=r.get(s,"user.commonInfo",{}),g=r.get(s,"doctor",{}),f=r.get(L,"data.users",[]).find(Y=>Y._id===g._id),P=r.get(f,"professionalDetails.consultationFee","N/A");return{patientName:r.get(i,"personalInfo.fullName","N/A"),date:v(r.get(s,"date","")).format("MMM-DD-YYYY"),treatment:r.get(s,"treatment","N/A"),doctorName:r.get(g,"commonInfo.personalInfo.fullName","N/A"),consultationFee:P,status:r.get(s,"status","N/A"),remark:r.get(s,"remark","N/A"),phoneNumber:r.get(i,"contactInfo.phone.primaryPhone","N/A"),dob:r.get(i,"personalInfo.dob")?U(r.get(i,"personalInfo.dob")):"N/A",gender:r.get(i,"personalInfo.gender","N/A"),bloodGroup:r.get(i,"personalInfo.bloodGroup","N/A"),address:r.get(i,"contactInfo.address.currentAddress","N/A")}},D={columns:[{title:"Patient Id",key:"patientId"},{title:"Patient Name",key:"patientName"},{title:"Appointment Date",key:"date"},{title:"Doctor Name",key:"doctorName"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:O.map((s,i)=>({key:i,patientId:r.get(s,"user.patientInfo.patientId","N/A"),patientName:r.get(s,"user.commonInfo.personalInfo.fullName","N/A"),date:v(r.get(s,"date")).format("MMMM-DD-YYYY")||"N/A",doctorName:r.get(s,"doctor.commonInfo.personalInfo.fullName","N/A"),status:e.jsx(q,{status:s.status}),action:e.jsx(Z,{onEdit:()=>{t(`/appointment/add-appointment/${s._id}`)},onMore:()=>{},onMoreList:[{title:"Transfer Hospital",onClick:()=>c({state:!0,patientId:s._id}),index:4}],onDelete:async()=>{await H({id:JSON.stringify([s._id])})},onShow:()=>{d(V(s)),y(!0)}})}))};return p.useEffect(()=>{T(s=>({...s,page:1}))},[o,I,n]),console.log("sldkfsdlfk"),e.jsxs(e.Fragment,{children:[e.jsx(ee,{title:"Appointment",onSearch:s=>{F(s)},onAddClick:()=>{t(se.ADDAPPOINTMENT)},listTitle:"Appointment List",FilterSection:()=>{const s=M();return e.jsx("div",{className:"flex gap-4",children:e.jsx("div",{children:e.jsx(ie,{date:n,setDate:x,disabled:s.disabled,min:s.min,max:s.max})})})},children:e.jsx(te,{className:"col-span-2",onExportExcel:()=>h(D.rows,"report.xlsx"),onExportPDF:()=>u(a.current,"report.pdf"),onPrint:A})}),e.jsx("div",{className:"mt-0.5 mb-0.5 ",children:e.jsx(me,{appointmentsData:w,isLoading:S,tabValue:o,selectedDate:n})}),e.jsx(oe,{tabs:["All","Today","UpComing","History"],defaultTab:o,onTabChange:R}),e.jsx(re,{columns:D.columns,rows:D.rows,loading:S,pagination:{currentPage:C.page,totalPage:B,limit:C.limit,onClick:s=>{T(i=>({page:s.page??i.page,limit:s.limit??i.limit}))}}}),e.jsx(ne,{ref:a,tableData:D,cardData:pe(w,o,n)}),N&&e.jsx(de,{data:N,setOpen:y,open:j}),e.jsx(ce,{isOpen:m.state,onClose:()=>{c({...m,state:!1})},patientId:m.patientId})]})};export{ve as AddAppointment,Te as AddCardiology,Fe as AddDentalCarePage,Ge as AddDermatology,Ue as AddENT,ye as AddGeneralCheckupForm,Ye as AddGeneralPreCheckupForm,Xe as AddGynecologyPage,tt as AddNeuroLogy,at as AddOrthopedics,Je as AddPhysicsian,Ne as AppointmentPage,Pe as CardiologyDepartmentPage,Ee as DentalCareDepartmentPage,Re as DermatologyDepartmentPage,Be as ENTDepartmentPage,we as GeneralCheckupPage,ze as GeneralPhysicianPage,Ke as GynecologyDepartmentPage,qe as NeurologyDepartmentPage,ot as OrthomologyDepartmentPage,nt as OrthopedicsDepartmentPage,pt as PatientViewsDetails,ct as PediatricDepartmentPage};
