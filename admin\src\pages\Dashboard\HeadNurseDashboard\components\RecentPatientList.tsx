/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from "react";
import SearchableSelect from "../../../../components/SearchableSelect";
import Button from "../../../../components/Button";
import { useNavigate } from "react-router-dom";

const RecentPatientList: React.FC = () => {
  const [timeFilter, setTimeFilter] = useState("today");
  const [patientTypeFilter, setPatientTypeFilter] = useState("all");
  const navigate = useNavigate();

  // Dummy data for test requests
  const testRequestData = {
    today: {
      all: [
        {
          patientID: "P-001",
          patientName: "<PERSON>",
          department: "Hospital Patient",
          nurse: "<PERSON>",
          bedNo: "Blood Test",
          time: "09:30 AM",
          medicine: "Paracetamol",
          priority: "Normal",
        },
        {
          patientID: "P-002",
          patientName: "<PERSON>",
          department: "Emergency",
          nurse: "<PERSON>",
          bedNo: "ICU-04",
          time: "10:15 AM",
          medicine: "Amoxicillin",
          priority: "Urgent",
        },
      ],
      hospital: [
        {
          patientID: "P-003",
          patientName: "<PERSON>",
          department: "Cardiology",
          nurse: "<PERSON>",
          bedNo: "Ward-12B",
          time: "11:00 AM",
          medicine: "Atenolol",
          priority: "Normal",
        },
        {
          patientID: "P-004",
          patientName: "Sophia Williams",
          department: "Maternity",
          nurse: "James Anderson",
          bedNo: "Room-205",
          time: "08:45 AM",
          medicine: "Iron Supplement",
          priority: "Urgent",
        },
      ],
      outpatient: [
        {
          patientID: "P-005",
          patientName: "Daniel Garcia",
          department: "Orthopedics",
          nurse: "Emma Thompson",
          bedNo: "Ortho-03",
          time: "01:30 PM",
          medicine: "Ibuprofen",
          priority: "Urgent",
        },
      ],
    },
  };

  const currentData =
    testRequestData[timeFilter as keyof typeof testRequestData][
      patientTypeFilter as keyof typeof testRequestData.today
    ] || [];

  const totalRequests = currentData.length;
  const urgentRequests = currentData.filter(
    (req) => req.priority === "Urgent"
  ).length;
  const normalRequests = currentData.filter(
    (req) => req.priority === "Normal"
  ).length;

  const getPriorityBadge = (priority: string) => {
    return priority === "Urgent"
      ? "bg-red text-white"
      : priority === "Normal"
      ? "bg-[#10B981] text-white"
      : "bg-gray text-white";
  };

  return (
    <div className="w-full bg-white shadow-sm rounded-xl p-4 border border-purple-100 transition-all duration-300">
      {/* Header */}
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center gap-4">
          <div>
            <h3 className="text-xl font-bold text-gray-800">Recent Patients</h3>
            <p className="text-sm text-gray-500 mt-1">
              {totalRequests} total • {urgentRequests} urgent • {normalRequests}{" "}
              normal
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            title="View All"
            className="!text-blue-600 !border-blue-500 hover:!bg-blue-50 !px-4 !py-2 !text-sm !font-medium !rounded-lg transition-all duration-200"
            onClick={() => {
              navigate("/test-requests");
            }}
          />
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-3 mb-3">
        <div className="bg-white flex gap-1 text-[#2FC0A1] border border-[#2FC0A1] rounded-md shadow-sm hover:shadow-md transition-all duration-200">
          <SearchableSelect
            value={patientTypeFilter}
            options={[
              { label: "All Patients", value: "all" },
              { label: "Hospital Patients", value: "hospital" },
              { label: "Out Patients", value: "outpatient" },
            ]}
            onChange={(value) => setPatientTypeFilter(value)}
          />
        </div>
      </div>

      {/* Table Container */}
      <div className="flex-1 bg-white rounded-xl border border-gray-200  p-2">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-gray-700 text-sm">
                  Patient ID
                </th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 text-sm">
                  Patient Name
                </th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 text-sm">
                  Department
                </th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 text-sm">
                  Nurse
                </th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 text-sm">
                  Bed No
                </th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 text-sm">
                  Time
                </th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 text-sm">
                  Medicine
                </th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 text-sm">
                  Priority
                </th>
              </tr>
            </thead>
            <tbody>
              {currentData.map((request) => (
                <tr
                  key={request.patientID}
                  className="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200"
                >
                  <td className="py-3 px-2 text-sm font-medium text-blue-600">
                    {request.patientID}
                  </td>
                  <td className="py-3 px-2 text-sm text-gray-800">
                    {request.patientName}
                  </td>
                  <td className="py-3 px-2 text-sm text-gray-600">
                    {request.department}
                  </td>
                  <td className="py-3 px-2 text-sm text-gray-800 font-medium">
                    {request.nurse || "N/A"}
                  </td>
                  <td className="py-3 px-2 text-sm text-gray-600">
                    {request.bedNo}
                  </td>
                  <td className="py-3 px-2 text-sm text-gray-600">
                    {request.time}
                  </td>
                  <td className="py-3 px-2 text-sm text-gray-600">
                    {request.medicine}
                  </td>
                  <td className="py-3 px-4">
                    <span
                      className={`px-2 py-1 rounded-md text-xs font-medium ${getPriorityBadge(
                        request.priority
                      )}`}
                    >
                      {request.priority}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Background Pattern */}
      {/* <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-purple-50/30 rounded-3xl pointer-events-none"></div> */}
    </div>
  );
};

export default RecentPatientList;
