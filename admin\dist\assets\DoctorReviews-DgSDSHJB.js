import{a2 as e,a5 as c,af as y,ah as k,aQ as D,aj as M,bA as A}from"./index-ClX9RVH0.js";const T=[{serialNumber:"1",caseType:"Typhoid",tokenId:"Tk111",patientName:"<PERSON>",date:"2025-02-14",department:"General Medicine",specialist:"Routine checkup",totalPatients:"150",appointment:"50",contactNumber:"**********",address:"kapan,Kathamandu",patientType:"In-Patient",treatment:"General Checkup",doctorName:"<PERSON>",status:"Available",time:"09:00 Am ",month:"Feb 28, 2025",des:" Lorem ipsum dolor sit amet consectetur adipisicing elit. Quo qui consequuntur esse aperiam minima repudiandae nobis officia corporis veritatis suscipit.",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D",rating:"4.0 ⭐"},{serialNumber:"2",caseType:"Typhoid",tokenId:"Tk2",contactNumber:"**********",address:"kapan,Kathamandu",patientType:"In-Patient",patientName:"Jane Smith",date:"2025-02-15",department:"General Medicine",specialist:"Routine checkup",totalPatients:"150",appointment:"50",treatment:"Dental Cleaning",doctorName:"Sarah Johnson",status:"Available",time:"09:00 Am ",month:"Feb 28, 2025",des:" Lorem ipsum dolor sit amet consectetur adipisicing elit. Quo qui consequuntur esse aperiam minima repudiandae nobis officia corporis veritatis suscipit.",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D",rating:"4.0 ⭐"},{serialNumber:"3",caseType:"Typhoid",tokenId:"Tk3",contactNumber:"**********",address:"kapan,Kathamandu",patientType:"In-Patient",patientName:"Michael Brown",date:"2025-02-16",department:"General Medicine",specialist:"Routine checkup",totalPatients:"150",appointment:"50",treatment:"Eye Examination",doctorName:"Emily Davis",status:"Available",time:"09:00 Am ",month:"Feb 28, 2025",des:" Lorem ipsum dolor sit amet consectetur adipisicing elit. Quo qui consequuntur esse aperiam minima repudiandae nobis officia corporis veritatis suscipit.",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D",rating:"4.0 ⭐"},{serialNumber:"4",caseType:"Typhoid",tokenId:"Tk4",patientName:"Alice Williams",contactNumber:"**********",address:"kapan,Kathamandu",patientType:"In-Patient",date:"2025-02-17",department:"General Medicine",specialist:"Routine checkup",totalPatients:"150",appointment:"50",treatment:"Blood Test",doctorName:"David Clark",status:"Available",time:"09:00 Am ",month:"Feb 28, 2025",des:" Lorem ipsum dolor sit amet consectetur adipisicing elit. Quo qui consequuntur esse aperiam minima repudiandae nobis officia corporis veritatis suscipit.",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D",rating:"4.0 ⭐"},{serialNumber:"5",caseType:"Typhoid",tokenId:"Tk5",patientName:"Robert White",contactNumber:"**********",address:"kapan,Kathamandu",patientType:"In-Patient",date:"2025-02-18",department:"General Medicine",specialist:"Routine checkup",totalPatients:"150",appointment:"50",treatment:"X-Ray",doctorName:"Lisa Adams",status:"Unavailable",time:"09:00 Am ",month:"Feb 28, 2025",des:" Lorem ipsum dolor sit amet consectetur adipisicing elit. Quo qui consequuntur esse aperiam minima repudiandae nobis officia corporis veritatis suscipit.",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D",rating:"4.0 ⭐"},{serialNumber:"6",caseType:"Typhoid",tokenId:"Tk6",patientName:"Emily Green",date:"2025-02-19",department:"General Medicine",specialist:"Routine checkup",totalPatients:"150",appointment:"50",contactNumber:"**********",address:"kapan,Kathamandu",patientType:"In-Patient",treatment:"MRI Scan",doctorName:"James Hall",status:"Unavailable",time:"09:00 Am ",month:"Feb 28, 2025",des:" Lorem ipsum dolor sit amet consectetur adipisicing elit. Quo qui consequuntur esse aperiam minima repudiandae nobis officia corporis veritatis suscipit.",image:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D",rating:"4.0 ⭐"}],q=({time:i,des:a,month:t,image:n,patientName:s,handleClick:o})=>e.jsxs("div",{className:"flex justify-center cursor-pointer items-center gap-2",onClick:o,children:[e.jsx("img",{className:"w-14 h-14 object-cover rounded-full",src:n,alt:s}),e.jsxs("div",{className:"mt-3 px-5",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"text-xl font-semibold",children:s}),e.jsxs("div",{className:" flex gap-1 text-gray-400",children:[e.jsx("p",{children:i}),e.jsx("p",{children:t})]})]}),e.jsx("div",{className:"mt-1 text-left font-light",children:a})]})]}),H=({image:i,patientName:a,specialist:t})=>e.jsx("div",{children:e.jsxs("div",{className:"flex text-left gap-2 ",children:[e.jsx("img",{className:"w-14 h-14  object-cover rounded-full",src:i,alt:a}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xl text-left flex font-semibold",children:a}),e.jsx("div",{className:" text-gray-400 ",children:t})]})]})}),R=({setOpen:i,open:a})=>{if(!a)return null;const t=()=>{i(!1)};return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",children:e.jsx("div",{className:"relative w-1/3 max-w-[60vw] max-h-[90vh] p-6 bg-white rounded-lg shadow-lg overflow-y-auto",onClick:n=>n.stopPropagation(),children:e.jsxs("div",{className:"my-6",children:[e.jsx("div",{className:"flex gap-5 my-4 flex-col",children:e.jsx("div",{className:"flex justify-between border h-80 rounded-xl bg-gray-500 pb-2"})}),e.jsxs("div",{className:"border-b pb-5 flex gap-10 my-5",children:[e.jsx("img",{className:"w-14 h-14 object-cover rounded-full",src:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D",alt:""}),e.jsxs("div",{children:[e.jsx("div",{className:"font-bold text-2xl",children:"Vinesh Verma"}),e.jsx("div",{className:"my-3",children:"⭐⭐⭐⭐⭐"}),e.jsx("div",{className:" text-xl text-gray-500",children:'"Lorem ipsum, dolor sit amet consectetur adipisicing elit. Culpa doloremque eveniet deleniti est voluptates quasi dicta quidem aliquid ipsum amet?"'})]})]}),e.jsxs("div",{className:"flex gap-10 flex-row-reverse",children:[e.jsx("button",{onClick:t,className:" border p-3 border-red text-red font-semibold text-lg cursor-pointer rounded-xl transition-all duration-700 hover:bg-red hover:text-white",children:"Delete"}),e.jsx("button",{className:" border p-3 border-green text-green font-semibold text-lg cursor-pointer rounded-xl transition-all duration-700 hover:bg-green hover:text-white",children:"Approved"})]})]})})})},F=()=>{const i=()=>{t(!0)},[a,t]=c.useState(!1),[n,s]=c.useState(!1),o={columns:[{title:"SN",key:"tokenid"},{title:"Reviews",key:"patientName"},{title:"Doctor Name",key:"doctorName"},{title:"Rating",key:"rating"},{title:"Action",key:"action"}],rows:T.map(({tokenId:d,patientName:m,totalPatients:p,department:u,doctorName:x,status:h,image:r,appointment:f,treatment:b,specialist:l,rating:w,time:N,month:g,des:v},j)=>({key:j,tokenid:d,totalPatients:p,patientName:e.jsx(q,{time:N,month:g,image:r,patientName:m,des:v,handleClick:i}),department:u,rating:e.jsx("div",{className:"font-bold",children:w}),doctorName:e.jsx(H,{specialist:l,image:r,patientName:x}),appointment:f,specialist:l,status:e.jsx(k,{status:h}),treatment:b,action:e.jsx(y,{onShow:()=>{t(!0)},onDelete:()=>{s(!0)}})}))};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[e.jsx(D,{headerTitle:"Reviews",onSearch:!0,onToday:!0,onRating:!0}),e.jsx("div",{className:"bg-white rounded-md",children:e.jsx(M,{columns:o.columns,rows:o.rows,loading:!1,color:"bg-white ",textcolor:"text-gray-400",pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})})]}),e.jsx(R,{setOpen:t,open:a}),e.jsx(A,{confirmAction:n,title:"Do you want to delete this review record?",des:"This action cannot be undone.",onClose:()=>s(!1),onConfirm:()=>{s(!1)}})]})};export{F as default};
