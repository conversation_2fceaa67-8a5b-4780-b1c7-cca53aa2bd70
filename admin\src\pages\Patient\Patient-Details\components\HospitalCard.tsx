// import React from "react";

// const HospitalCard = () => {
//   return (
//     <div className="p-4 rounded-xl bg-gradient-to-b from-[#186ff0] to-[#8cd5fe]">
//       <div className="flex items-start justify-start">
//         <img src="/hospicone.png" alt="hospital icon" className="h-10 w-10" />
//         <h1 className="text-white font-bold flex items-center justify-center">
//           Global Hospital
//         </h1>
//       </div>
//     </div>
//   );
// };

// export default HospitalCard;

// import React from "react";

// interface HospitalCardProps {
//   hospitalName: string;
//   membershipType: string;
//   name: string;
//   id: string;
//   validTill: string;
// }

// const HospitalCard: React.FC<HospitalCardProps> = ({
//   hospitalName,
//   membershipType,
//   name,
//   id,
//   validTill,
// }) => {
//   return (
//     <div className="p-4 rounded-xl bg-gradient-to-b from-[#186ff0] to-[#8cd5fe] w-[350px] h-[200px]">
//       <div className="flex items-start justify-start">
//         <img src="/hospicone.png" alt="hospital icon" className="h-10 w-10" />
//         <h1 className="text-white font-bold ml-2 text-xl">{hospitalName}</h1>
//       </div>
//       <div className="mt-4 bg-[#156ae5] text-white px-4 py-2 rounded-lg inline-block">
//         {membershipType}
//       </div>
//       <div className="flex justify-between mt-8 text-white">
//         <div>
//           <p className="text-sm">{name}</p>
//           <p className="text-sm font-medium">{id}</p>
//         </div>
//         <p className="text-sm">Valid till {validTill}</p>
//       </div>
//     </div>
//   );
// };

// export default HospitalCard;

import type React from "react";

interface HospitalCardProps {
  hospitalName: string;
  membershipType: string;
  name: string;
  id: string;
  validTill: string;
}

const HospitalCard: React.FC<HospitalCardProps> = ({
  hospitalName,
  membershipType,
  name,
  id,
  validTill,
}) => {
  return (
    <div className="p-4 rounded-xl bg-gradient-to-b from-[#186ff0] to-[#8cd5fe] w-[350px] h-[220px]">
      <div className="flex items-start justify-start">
        <img src="/hospicone.png" alt="hospital icon" className="h-10 w-10" />
        <h1 className="text-white font-bold ml-2 text-2xl">{hospitalName}</h1>
      </div>
      <div className="mt-4 bg-[#156ae5] text-xl text-white px-4 py-3 rounded-tr-lg rounded-br-lg w-fit -ml-4">
        {membershipType}
      </div>
      <div className="flex justify-between mt-16 ">
        <div className="flex items-center gap-2">
          <p className="text-sm">{name}</p>
          <p className="text-sm font-medium">{id}</p>
        </div>
        <p className="text-sm">Valid till {validTill}</p>
      </div>
    </div>
  );
};

export default HospitalCard;
