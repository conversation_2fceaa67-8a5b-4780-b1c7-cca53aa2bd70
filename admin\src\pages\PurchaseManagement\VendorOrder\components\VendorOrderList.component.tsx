import { useState } from 'react';
import { buildQueryParams } from '../../../../hooks/useBuildQuery';
import { useGetAllInvoice } from '../../../../server-action/api/financialOpsApi';
import { get } from 'lodash';
import dayjs from 'dayjs';
import { Status } from '../../../../components/Status';
import { TableAction } from '../../../../layouts/Table/TableAction';
import { Icon } from '@iconify/react/dist/iconify.js';
import MasterTable from '../../../../layouts/Table/MasterTable';
import { OrderDetails } from './OrderDetails.component';
import { PopupModal } from '../../../../components';
import { useOutsideClick } from '../../../../hooks';

export const OrderList = () => {
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: '',
    status: '',
  });

  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  const ref = useOutsideClick(() => setShowOrderDetails(false));

  const handleViewOrder = (orderId: string) => {
    setSelectedOrderId(orderId);
    setShowOrderDetails(true);
  };

  const handleCloseOrderDetails = () => {
    setSelectedOrderId(null);
    setShowOrderDetails(false);
  };

  const queryParams = buildQueryParams(filters);
  const { data: orderData, isLoading } = useGetAllInvoice({
    ...queryParams,
    category: 'PURCHASE',
  });

  const orders = get(orderData, 'data.invoices', []);

  const columns = [
    { title: 'S.N.', key: 'serialNo' },
    { title: 'Order ID', key: 'orderId' },
    { title: 'Hospital Name', key: 'hospitalName' },
    { title: 'Order Date', key: 'orderDate' },
    { title: 'Total Amount', key: 'totalAmount' },
    { title: 'Status', key: 'status' },
    { title: 'Priority', key: 'priority' },
    { title: 'Action', key: 'action' },
  ];

  const rows = orders.map((order: any, index: number) => ({
    key: order._id || index,
    serialNo: (filters.page - 1) * filters.limit + index + 1,
    orderId: get(order, 'predefinedBillNo', '-'),
    hospitalName: get(
      order,
      'vendor.commonInfo.personalInfo.fullName',
      'Hospital Name'
    ),
    orderDate: dayjs(get(order, 'date', '')).format('MMM DD, YYYY'),
    totalAmount: `Rs. ${get(order, 'totalAmount', 0).toLocaleString()}`,
    status: <Status status={get(order, 'status', 'pending')} />,
    priority: (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          get(order, 'priority', 'medium') === 'high'
            ? 'bg-red-100 text-red-800'
            : get(order, 'priority', 'medium') === 'medium'
            ? 'bg-yellow-100 text-yellow-800'
            : 'bg-green-100 text-green-800'
        }`}
      >
        {get(order, 'priority', 'medium').toUpperCase()}
      </span>
    ),
    action: (
      <TableAction
        onShow={() => handleViewOrder(order._id)}
        onDelete={() => console.log('Delete order:', order._id)}
      />
    ),
  }));

  return (
    <>
      <div className="space-y-4">
        {/* Search and Filter Section */}
        <div className="bg-white p-2 rounded shadow-sm">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex-1  max-w-md">
              <div className="relative">
                <Icon
                  icon="material-symbols:search"
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Search orders..."
                  className="w-full pl-10 pr-4 py-1 border border-gray-300 rounded outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={filters.search}
                  onChange={(e) =>
                    setFilters({ ...filters, search: e.target.value, page: 1 })
                  }
                />
              </div>
            </div>

            <div className="flex gap-3">
              <select
                className="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 outline-none"
                value={filters.status}
                onChange={(e) =>
                  setFilters({ ...filters, status: e.target.value, page: 1 })
                }
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>
        </div>

        {/* Orders Table */}
        <div className="bg-white rounded-lg shadow-sm">
          <MasterTable
            columns={columns}
            rows={rows}
            loading={isLoading}
            pagination={{
              currentPage: filters.page,
              totalPage: get(orderData, 'data.pagination.pages', 1),
              limit: filters.limit,
              onClick: ({ page, limit }: { page?: number; limit?: number }) => {
                if (page) setFilters({ ...filters, page });
                if (limit) setFilters({ ...filters, limit });
              },
            }}
          />
        </div>
      </div>

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrderId && (
        <PopupModal
          ref={ref}
          classname=" w-full max-w-6xl max-h-[80vh] overflow-scroll"
        >
          <OrderDetails
            orderId={selectedOrderId}
            onClose={handleCloseOrderDetails}
          />
        </PopupModal>
      )}
    </>
  );
};
