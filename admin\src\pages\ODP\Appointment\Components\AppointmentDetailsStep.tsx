import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { InputField } from "../../../../components/Input-Field";
import { DropdownField } from "../../../../components/DropDownField";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

// Options for appointment details
const appointmentTypeOptions = [
  { value: "consultation", label: "Consultation" },
  { value: "follow-up", label: "Follow-up" },
  { value: "emergency", label: "Emergency" },
  { value: "routine", label: "Routine Check-up" },
];

const priorityOptions = [
  { value: "normal", label: "Normal" },
  { value: "urgent", label: "Urgent" },
  { value: "emergency", label: "Emergency" },
];

// No validation for now
const appointmentValidationSchema = yup.object().shape({});

interface AppointmentDetailsStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const AppointmentDetailsStep: React.FC<AppointmentDetailsStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const formik = useFormik({
    initialValues: {
      appointmentType: initialData?.appointmentType || "",
      priority: initialData?.priority || "normal",
      reason: initialData?.reason || "",
      symptoms: initialData?.symptoms || "",
      notes: initialData?.notes || "",
      specialInstructions: initialData?.specialInstructions || "",
      duration: initialData?.duration || "",
      followUpRequired: initialData?.followUpRequired || false,
      emergencyContact: initialData?.emergencyContact || "",
    },
    enableReinitialize: true,
    validationSchema: appointmentValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue, getFieldProps } = formik;

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:calendar" className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Appointment Details
            </h2>
            <p className="text-sm text-gray-600">
              Choose date, reason & priority
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
            {/* Appointment Type */}
            <div>
              <DropdownField
                label="Appointment Type"
                options={appointmentTypeOptions}
                firstInput="Select Appointment Type"
                name="appointmentType"
                value={values.appointmentType}
                onChange={(e: any) =>
                  setFieldValue("appointmentType", e.target.value)
                }
              />
            </div>

            {/* Priority */}
            <div>
              <DropdownField
                label="Priority"
                options={priorityOptions}
                name="priority"
                value={values.priority}
                onChange={(e: any) => setFieldValue("priority", e.target.value)}
              />
            </div>

            {/* Duration */}
            <div>
              <InputField
                type="text"
                label="Duration"
                placeholder="30 minutes"
                {...getFieldProps("duration")}
              />
            </div>

            {/* Reason for Visit */}
            <div className="md:col-span-3">
              <InputField
                type="text"
                label="Reason for Visit"
                placeholder="Please describe the reason for your visit"
                {...getFieldProps("reason")}
              />
            </div>

            {/* Current Symptoms */}
            <div className="md:col-span-3">
              <InputField
                type="text"
                label="Current Symptoms"
                placeholder="Please describe your current symptoms (optional)"
                {...getFieldProps("symptoms")}
              />
            </div>

            {/* Additional Notes */}
            <div className="md:col-span-3">
              <InputField
                type="text"
                label="Additional Notes"
                placeholder="Any additional information you'd like to share (optional)"
                {...getFieldProps("notes")}
              />
            </div>

            {/* Special Instructions */}
            <div className="md:col-span-3">
              <InputField
                type="text"
                label="Special Instructions"
                placeholder="Any special instructions for the appointment"
                {...getFieldProps("specialInstructions")}
              />
            </div>

            {/* Emergency Contact */}
            <div className="md:col-span-3">
              <InputField
                type="text"
                label="Emergency Contact"
                placeholder="Emergency contact number (optional)"
                {...getFieldProps("emergencyContact")}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between mt-8">
            <Button
              type="button"
              onClick={onBack}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Icon icon="mdi:arrow-left" className="w-4 h-4" />
              <span>Back</span>
            </Button>

            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            >
              <span>Next</span>
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default AppointmentDetailsStep;
