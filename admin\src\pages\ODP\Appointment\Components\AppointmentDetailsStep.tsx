import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";

import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

// Options for appointment details
const appointmentTypeOptions = [
  { value: "consultation", label: "Consultation" },
  { value: "follow-up", label: "Follow-up" },
  { value: "emergency", label: "Emergency" },
  { value: "routine", label: "Routine Check-up" },
];

const priorityOptions = [
  { value: "normal", label: "Normal" },
  { value: "urgent", label: "Urgent" },
  { value: "emergency", label: "Emergency" },
];

// No validation for now
const appointmentValidationSchema = yup.object().shape({});

interface AppointmentDetailsStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const AppointmentDetailsStep: React.FC<AppointmentDetailsStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const formik = useFormik({
    initialValues: {
      appointmentType: initialData?.appointmentType || "",
      priority: initialData?.priority || "normal",
      reason: initialData?.reason || "",
      symptoms: initialData?.symptoms || "",
      notes: initialData?.notes || "",
      specialInstructions: initialData?.specialInstructions || "",
      duration: initialData?.duration || "",
      followUpRequired: initialData?.followUpRequired || false,
      emergencyContact: initialData?.emergencyContact || "",
    },
    enableReinitialize: true,
    validationSchema: appointmentValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue, getFieldProps } = formik;

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:calendar" className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Appointment Details
            </h2>
            <p className="text-sm text-gray-600">
              Choose date, reason & priority
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Appointment Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Appointment Type *
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={values.appointmentType}
                onChange={(e) =>
                  setFieldValue("appointmentType", e.target.value)
                }
              >
                <option value="">Select appointment type</option>
                {appointmentTypeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Reason for Visit */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason for Visit *
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={4}
                placeholder="Please describe the reason for your visit"
                {...getFieldProps("reason")}
              />
            </div>

            {/* Additional Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional notes
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={4}
                placeholder="Any additional information you would like to share"
                {...getFieldProps("notes")}
              />
            </div>

            {/* Priority Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority Level *
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={values.priority}
                onChange={(e) => setFieldValue("priority", e.target.value)}
              >
                <option value="">Select Priority level</option>
                {priorityOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between mt-8">
            <Button
              type="button"
              onClick={onBack}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Icon icon="mdi:arrow-left" className="w-4 h-4" />
              <span>Back</span>
            </Button>

            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            >
              <span>Next</span>
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default AppointmentDetailsStep;
