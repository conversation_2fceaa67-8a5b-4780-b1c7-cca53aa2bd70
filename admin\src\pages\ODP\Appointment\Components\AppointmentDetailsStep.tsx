import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { DropdownField } from "../../../../components/DropDownField";
import { InputField } from "../../../../components/Input-Field";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

// Static data for appointment details
const appointmentTypeOptions = [
  { value: "consultation", label: "General Consultation" },
  { value: "follow-up", label: "Follow-up Visit" },
  { value: "emergency", label: "Emergency" },
  { value: "routine", label: "Routine Check-up" },
  { value: "specialist", label: "Specialist Consultation" },
  { value: "surgery", label: "Surgery Consultation" },
];

const priorityOptions = [
  { value: "low", label: "Low" },
  { value: "normal", label: "Normal" },
  { value: "high", label: "High" },
  { value: "urgent", label: "Urgent" },
  { value: "emergency", label: "Emergency" },
];

// No validation for now
const appointmentValidationSchema = yup.object().shape({});

interface AppointmentDetailsStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const AppointmentDetailsStep: React.FC<AppointmentDetailsStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const formik = useFormik({
    initialValues: {
      appointmentType: initialData?.appointmentType || "",
      priority: initialData?.priority || "normal",
      reason: initialData?.reason || "",
      symptoms: initialData?.symptoms || "",
      notes: initialData?.notes || "",
      specialInstructions: initialData?.specialInstructions || "",
      duration: initialData?.duration || "",
      followUpRequired: initialData?.followUpRequired || false,
      emergencyContact: initialData?.emergencyContact || "",
    },
    enableReinitialize: true,
    validationSchema: appointmentValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue, getFieldProps } = formik;

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:calendar" className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Appointment Details
            </h2>
            <p className="text-sm text-gray-600">
              Choose date, reason & priority
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5">
            {/* Appointment Type */}
            <div>
              <DropdownField
                required
                label="Appointment Type"
                options={appointmentTypeOptions}
                firstInput="Select appointment type"
                name="appointmentType"
                value={values.appointmentType}
                onChange={(e: any) =>
                  setFieldValue("appointmentType", e.target.value)
                }
              />
            </div>

            {/* Reason for Visit */}
            <div>
              <InputField
                type="text"
                required
                label="Reason for Visit"
                placeholder="Please describe the reason for your visit"
                value={values.reason}
                onChange={(e: any) => setFieldValue("reason", e.target.value)}
              />
            </div>

            {/* Additional Notes */}
            <div>
              <InputField
                type="text"
                label="Additional notes"
                placeholder="Any additional information you would like to share"
                value={values.notes}
                onChange={(e: any) => setFieldValue("notes", e.target.value)}
              />
            </div>

            {/* Priority Level */}
            <div>
              <DropdownField
                required
                label="Priority Level"
                options={priorityOptions}
                firstInput="Select Priority level"
                name="priority"
                value={values.priority}
                onChange={(e: any) => setFieldValue("priority", e.target.value)}
              />
            </div>

            {/* Special Instructions */}
            <div>
              <InputField
                type="text"
                label="Special Instructions"
                placeholder="Any special instructions for the appointment"
                value={values.specialInstructions}
                onChange={(e: any) =>
                  setFieldValue("specialInstructions", e.target.value)
                }
              />
            </div>

            {/* Duration */}
            <div>
              <InputField
                type="text"
                label="Duration"
                placeholder="30 minutes"
                value={values.duration}
                onChange={(e: any) => setFieldValue("duration", e.target.value)}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between mt-8">
            <Button
              type="button"
              onClick={onBack}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Icon icon="mdi:arrow-left" className="w-4 h-4" />
              <span>Back</span>
            </Button>

            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
            >
              <span>Next</span>
              <Icon icon="mdi:arrow-right" className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default AppointmentDetailsStep;
