import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { InputField } from "../../../../components/Input-Field";
import { DropdownField } from "../../../../components/DropDownField";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

// Options for appointment details
const appointmentTypeOptions = [
  { value: "consultation", label: "Consultation" },
  { value: "follow-up", label: "Follow-up" },
  { value: "emergency", label: "Emergency" },
  { value: "routine", label: "Routine Check-up" },
];

const priorityOptions = [
  { value: "normal", label: "Normal" },
  { value: "urgent", label: "Urgent" },
  { value: "emergency", label: "Emergency" },
];

// No validation for now
const appointmentValidationSchema = yup.object().shape({});

interface AppointmentDetailsStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const AppointmentDetailsStep: React.FC<AppointmentDetailsStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const formik = useFormik({
    initialValues: {
      appointmentType: initialData?.appointmentType || "",
      priority: initialData?.priority || "normal",
      reason: initialData?.reason || "",
      symptoms: initialData?.symptoms || "",
      notes: initialData?.notes || "",
      specialInstructions: initialData?.specialInstructions || "",
      duration: initialData?.duration || "",
      followUpRequired: initialData?.followUpRequired || false,
      emergencyContact: initialData?.emergencyContact || "",
    },
    enableReinitialize: true,
    validationSchema: appointmentValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue, getFieldProps } = formik;

  const appointmentTypes = [
    "General Consultation",
    "Follow-up",
    "Emergency",
    "Routine Checkup",
    "Specialist Consultation",
  ];

  const priorityLevels = ["Low", "Normal", "High", "Urgent"];

  const timeSlots = [
    "09:00 AM",
    "09:30 AM",
    "10:00 AM",
    "10:30 AM",
    "11:00 AM",
    "11:30 AM",
    "12:00 PM",
    "12:30 PM",
    "02:00 PM",
    "02:30 PM",
    "03:00 PM",
    "03:30 PM",
    "04:00 PM",
    "04:30 PM",
    "05:00 PM",
  ];

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:calendar" className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Appointment Details
            </h2>
            <p className="text-sm text-gray-600">
              Choose date, reason & priority
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Appointment Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Appointment Date *
            </label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => handleInputChange("date", e.target.value)}
              min={new Date().toISOString().split("T")[0]}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Time Slot */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Time Slot *
            </label>
            <select
              value={formData.timeSlot}
              onChange={(e) => handleInputChange("timeSlot", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select time slot</option>
              {timeSlots.map((slot) => (
                <option key={slot} value={slot}>
                  {slot}
                </option>
              ))}
            </select>
          </div>

          {/* Appointment Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Appointment Type *
            </label>
            <select
              value={formData.appointmentType}
              onChange={(e) =>
                handleInputChange("appointmentType", e.target.value)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select appointment type</option>
              {appointmentTypes.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>

          {/* Priority */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <select
              value={formData.priority}
              onChange={(e) => handleInputChange("priority", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {priorityLevels.map((priority) => (
                <option key={priority} value={priority}>
                  {priority}
                </option>
              ))}
            </select>
          </div>

          {/* Reason for Visit */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reason for Visit *
            </label>
            <input
              type="text"
              value={formData.reason}
              onChange={(e) => handleInputChange("reason", e.target.value)}
              placeholder="Brief description of the reason for your visit"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Symptoms */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Current Symptoms
            </label>
            <textarea
              value={formData.symptoms}
              onChange={(e) => handleInputChange("symptoms", e.target.value)}
              placeholder="Describe your current symptoms (optional)"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Additional Notes */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              placeholder="Any additional information you'd like to share (optional)"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            onClick={onBack}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Icon icon="mdi:arrow-left" className="w-4 h-4" />
            <span>Back</span>
          </Button>

          <Button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
          >
            <span>Next</span>
            <Icon icon="mdi:arrow-right" className="w-4 h-4" />
          </Button>
        </div>
      </form>
    </div>
    </FormikProvider>
  );
};

export default AppointmentDetailsStep;
