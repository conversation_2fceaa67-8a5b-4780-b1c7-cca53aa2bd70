import React from "react";
import { Icon } from "@iconify/react";
import { useNavigate, useParams } from "react-router-dom";
import { FrontendRoutes } from "../../../../../routes";

interface StaffData {
  id: string;
  name: string;
  role: string;
  department?: string;
  specialization: string;
  experience?: string;
  profileImage: string;
  status: string;
  doctorId?: string;
  nurseId?: string;
  designation: string;
}

interface ContactInfo {
  primaryPhone: string;
  secondaryPhone: string;
  email: string;
  currentAddress: string;
  permanentAddress: string;
}

interface StaffHeaderProps {
  staff: StaffData | null;
  contactInfo: ContactInfo | null;
  staffType: "Doctor" | "Nurse" | "Staff";
}

const StaffHeader: React.FC<StaffHeaderProps> = ({
  staff,
  contactInfo,
  staffType,
}) => {
  const navigate = useNavigate();
  const { id } = useParams();

  const handleEditProfile = () => {
    if (staffType === "Doctor") {
      navigate(`${FrontendRoutes.ADDDOCTOR}/${id}`);
    } else if (staffType === "Nurse") {
      navigate(`${FrontendRoutes.ADDNURSE}/${id}`);
    }
  };

  const handleCall = () => {
    if (contactInfo?.primaryPhone && contactInfo.primaryPhone !== "N/A") {
      // Open WhatsApp with the phone number
      const whatsappUrl = `https://wa.me/${contactInfo.primaryPhone.replace(
        /[^0-9]/g,
        ""
      )}`;
      window.open(whatsappUrl, "_blank");
    } else {
      alert("Phone number not available");
    }
  };

  const handleEmail = () => {
    if (contactInfo?.email && contactInfo.email !== "N/A") {
      // Open Gmail compose with the email
      const gmailUrl = `https://mail.google.com/mail/?view=cm&to=${contactInfo.email}`;
      window.open(gmailUrl, "_blank");
    } else {
      alert("Email address not available");
    }
  };

  if (!staff) return null;

  const getStaffId = () => {
    if (staffType === "Doctor") return staff.doctorId;
    if (staffType === "Nurse") return staff.nurseId;
    return staff.id;
  };

  const isAvailable = staff.status === "Available" || staff.status === "Active";

  return (
    <div className="bg-white rounded-lg p-4 shadow-sm border">
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3">
          <div className="relative">
            {staff.profileImage ? (
              <img
                src={staff.profileImage}
                alt={staff.name}
                className="w-16 h-16 rounded-md object-cover"
              />
            ) : (
              <div className="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                <Icon icon="mdi:account" className="text-gray-400 text-2xl" />
              </div>
            )}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h1 className="text-base font-semibold text-gray-900">
                {staff.name}
              </h1>
              {isAvailable && (
                <div className="flex items-center gap-1 bg-green/10 text-green border px-2 py-0.5 rounded text-xs">
                  <div className="w-1.5 h-1.5 bg-green rounded-full"></div>
                  Available
                </div>
              )}
            </div>
            <p className="text-gray-600 text-xs">{staff.specialization}</p>
            <p className="text-gray-500 text-xs">
              {staffType} ID: {getStaffId()}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button
            onClick={handleCall}
            className="p-2 text-gray-600 hover:bg-gray-200 bg-gray-100 rounded"
            title="Call via WhatsApp"
          >
            <Icon icon="material-symbols:call" className="w-4 h-4" />
          </button>
          <button
            onClick={handleEmail}
            className="p-2 text-gray-600 hover:bg-gray-200 bg-gray-100 rounded"
            title="Send Email via Gmail"
          >
            <Icon icon="material-symbols:mail-outline" className="w-4 h-4" />
          </button>
          <button
            onClick={handleEditProfile}
            className="bg-blue text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-blue-700"
          >
            Edit Profile
          </button>
        </div>
      </div>
    </div>
  );
};

export default StaffHeader;
