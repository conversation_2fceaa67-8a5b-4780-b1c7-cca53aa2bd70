import{a5 as d,a1 as m,al as r,a2 as e,a7 as p,am as u,a9 as x,aa as b,ab as l,ac as s,ao as j}from"./index-ClX9RVH0.js";const v=()=>{const[t,a]=d.useState(1),o=[{step:1,title:"Basic Information",isActive:t===1},{step:2,title:"Test Information",isActive:t===2}],i=m({initialValues:{id:"",patientName:"",room:"",bed:"",medicalCondition:"",dietType:"",mealTiming:"",startDate:"",endDate:""},enableReinitialize:!0,onSubmit:c=>{r.success("Form submitted successfully!"),history.back(),console.log(c)}}),{handleSubmit:n}=i;return e.jsxs("div",{children:[e.jsx(p,{listTitle:"Add Test Result",hideHeader:!0}),e.jsxs("div",{className:"relative flex w-full mt-4 gap-6",children:[e.jsx("div",{className:"w-auto h-full",children:e.jsx(u,{steps:o})}),e.jsx("div",{className:"w-full",children:e.jsx(x,{value:i,children:e.jsx(b,{onSubmit:n,children:e.jsxs("div",{className:"flex flex-col w-full gap-5 pb-4",children:[e.jsx("div",{className:"flex flex-col gap-4 p-4 bg-white rounded-sm",onClick:()=>a(1),children:e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(l,{label:"Test ID",options:[{value:"Male",label:"male"},{value:"Female",label:"female"}],name:"id",onFocus:()=>a(1)}),e.jsx(s,{label:"Patient Name",name:"pateintName",onFocus:()=>a(1)})]})}),e.jsx("section",{className:"bg-white p-4",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsx(l,{options:[],label:"Test Type",name:"dietType",onClick:()=>a(2)}),e.jsx(l,{options:[],label:"Doctor Name",name:"dietType",onClick:()=>a(2)}),e.jsx(s,{label:"Tested Date",type:"date",placeholder:"Enter",name:"mealTiming",onClick:()=>a(2)}),e.jsx(s,{label:"Result Date",type:"date",placeholder:"Enter",name:"mealTiming",onClick:()=>a(2)}),e.jsx(s,{label:"Result Summary",type:"text",placeholder:"Enter",name:"mealTiming",onClick:()=>a(2)})]})}),e.jsx(j,{onCancel:()=>history.back(),onSubmit:n})]})})})})]})]})};export{v as AddTestResult};
