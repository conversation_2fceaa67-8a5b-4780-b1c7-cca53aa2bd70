import{a5 as p,a1 as x,a2 as e,a7 as u,a8 as d,a9 as h,aa as b,ac as a,ab as t,b3 as r}from"./index-ClX9RVH0.js";const j={},f=()=>{const[l,i]=p.useState(!1),c=x({initialValues:{},enableReinitialize:!0,validationSchema:j,onSubmit:()=>{console.log("hello")}}),m=[{label:"Male",value:"male"},{label:"Female",value:"female"}],s=[{label:"Paracetamol",value:"paracetamol"},{label:"Cough Syrup",value:"coughSyrup"}],n=[{label:"2 Tabs",value:"2tabs"},{label:"3 Tabs",value:"3tabs"}],o=[{label:"Paracetamol",value:"paracetamol"},{label:"Cough Syrup",value:"coughSyrup"}];return e.jsxs("div",{children:[e.jsx(u,{listTitle:"Add Prescription",hideHeader:!0}),e.jsxs("div",{className:"flex gap-5",children:[e.jsx("div",{className:"h-auto w-[30%]",children:e.jsxs("div",{className:"flex flex-col gap-4 bg-white px-4 py-5",children:[e.jsx(d,{step:1,title:"General Information",isActive:!0}),e.jsx("div",{className:"h-10 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(d,{step:2,title:"Medicine Name",isActive:!1})]})}),e.jsx("div",{className:"w-[70%] h-[85vh] overflow-y-auto",children:e.jsx(h,{value:c,children:e.jsxs(b,{className:"flex flex-col gap-5 ",children:[e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsx(a,{type:"date",label:"Date",placeholder:"Auto Filled",name:"date"}),e.jsx(a,{type:"text",label:"Patient Id",placeholder:"Enter",name:"patientId"}),e.jsx(a,{type:"text",label:"Patient Name",placeholder:"Enter ",name:"patientName"}),e.jsx(a,{type:"text",label:"Doctor Name",placeholder:"doctor",name:"doctorName"}),e.jsx(a,{type:"text",label:"Case",placeholder:"Enter the medical condition",name:"case "}),e.jsx(t,{required:!0,label:"Status",options:m,name:"status"})]}),e.jsxs("div",{className:"bg-white px-5 gap-5 py-5 flex flex-col justify-center",children:[e.jsxs("div",{className:"bg-white px-5 grid grid-cols-3 gap-5 py-5",children:[e.jsx(t,{required:!0,label:"Medicine Name",options:s,name:"medicine"}),e.jsx(t,{required:!0,label:"Dose",options:n,name:"dosage"}),e.jsx(a,{type:"text",label:"Frequence",placeholder:"3 times/day",name:"frequency"}),e.jsx(a,{type:"text",label:"Duration",placeholder:"Enter",name:"duration"}),e.jsx(t,{required:!0,label:"Condition",options:o,name:"condition"}),e.jsx(a,{type:"text",label:"Prescription Note",placeholder:"additional instructions",name:"prescriptionNote"})]}),e.jsx("div",{className:"flex justify-center",children:l?e.jsxs("button",{className:"bg-[#146C71] rounded-md gap-2 py-2 px-3 flex items-center font-semibold text-white",onClick:()=>{i(!1)},children:[e.jsx(r,{icon:"gridicons:add",height:20,width:20,className:"text-white"}),"Remove"]}):e.jsxs("button",{className:"bg-[#146C71] rounded-md gap-2 py-2 px-3 flex items-center font-semibold text-white",onClick:()=>{i(!0)},children:[e.jsx(r,{icon:"gridicons:add",height:20,width:20,className:"text-white"}),"Add Medicine"]})}),l&&e.jsxs("div",{className:"bg-white px-5 grid grid-cols-3 gap-5 py-5",children:[e.jsx(t,{required:!0,label:"Medicine Name",options:s,name:"medicine"}),e.jsx(t,{required:!0,label:"Dose",options:n,name:"dosage"}),e.jsx(a,{type:"text",label:"Frequence",placeholder:"3 times/day",name:"frequency"}),e.jsx(a,{type:"text",label:"Duration",placeholder:"Enter",name:"duration"}),e.jsx(t,{required:!0,label:"Condition",options:o,name:"condition"}),e.jsx(a,{type:"text",label:"Prescription Note",placeholder:"additional instructions",name:"prescriptionNote"})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 mb-5",children:[e.jsx("button",{type:"button",className:"font-medium text-white rounded-md px-5 py-1 bg-[#989898] ",children:"Cancel"}),e.jsx("button",{type:"submit",className:"font-medium text-white rounded-md py-1 px-5 bg-[#146C71] ",children:"Submit"})]})]})})})]})]})};export{f as default};
