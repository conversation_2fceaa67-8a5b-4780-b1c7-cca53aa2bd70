import { use<PERSON><PERSON><PERSON>, <PERSON>ik<PERSON><PERSON><PERSON>, Form } from "formik";
import * as yup from "yup";
import { useCreateUser } from "../../../../server-action/api/auth";
import { FormField } from "../../../LabDashboard/components/FormField";

type Props = {
  EditData?: any;
  token?: string;
  onClose: () => void;
  //   onBack: () => void;
};

const bloodGroupOptions = [
  { value: "A+", label: "A+" },
  { value: "A-", label: "A-" },
  { value: "B+", label: "B+" },
  { value: "B-", label: "B-" },
  { value: "AB+", label: "AB+" },
  { value: "AB-", label: "AB-" },
  { value: "O+", label: "O+" },
  { value: "O-", label: "O-" },
];

const maritalStatusOptions = [
  { value: "single", label: "Single" },
  { value: "married", label: "Married" },
  { value: "divorced", label: "Divorced" },
  { value: "widowed", label: "Widowed" },
  { value: "separated", label: "Separated" },
  { value: "partnered", label: "Partnered" },
];
const newPatientValidationSchema = yup.object().shape({
  patientName: yup.string().required("Full name is required"),
  email: yup.string().email("Invalid email").required("Email is required"),
  number: yup.string().required("Contact number is required"),
  appointmentType: yup.string().required("Appointment type is required"),
  appointmentDate: yup.string().required("Appointment date is required"),
  appointmentTime: yup.string().required("Appointment time is required"),
  status: yup.string().required("Status is required"),
  gender: yup.string().required("Gender is required"),
  bloodGroup: yup.string().required("Blood group is required"),
  dob: yup.string().required("Date of birth is required"),
  currentAddress: yup.string().required("Current address is required"),
  citizenshipNo: yup.string().required("Citizenship number is required"),
  paymentMethod: yup.string().required("Payment method is required"),
  paymentStatus: yup.string().required("Payment status is required"),
});

const NewPatientForm = ({ EditData, token, onClose }: Props) => {
  const createUserAndAppointment = useCreateUser().mutate;

  const formik = useFormik({
    initialValues: {
      patientName: "",
      email: "",
      number: "",
      alternateContactNumber: "",
      appointmentType: "",
      appointmentDate: "",
      appointmentTime: "",
      status: "",
      gender: "",
      bloodGroup: "",
      dob: "",
      maritalStatus: "",
      language: "",
      currentAddress: "",
      permanentAddress: "",
      citizenshipNo: "",
      symptoms: "",
      paymentMethod: "",
      paymentStatus: "",
      bankName: "",
      transactionId: "",
      resellerName: "",
      token: token,
      role: "PATIENT",
      password: "patient@123",
    },
    validationSchema: newPatientValidationSchema,
    onSubmit: async (values) => {
      await createUserAndAppointment({
        role: values.role,
        email: values.email,
        password: values.password,
        isActive: true,
        FCMToken: "",
        commonInfo: {
          generalDescription: "",
          personalInfo: {
            fullName: values.patientName,
            gender: values.gender,
            dob: values.dob,
            language: values.language,
            bloodGroup: values.bloodGroup,
            maritalStatus: values.maritalStatus,
          },
          contactInfo: {
            phone: {
              primaryPhone: values.number,
              secondaryPhone: values.alternateContactNumber,
            },
            address: {
              currentAddress: values.currentAddress,
              permanentAddress: values.permanentAddress,
            },
          },
        },
        identityInformation: {
          primaryIdNo: values.citizenshipNo,
          identityDocuments: [
            {
              documentType: "citizenship",
              documentNumber: "",
              documentImages: [""],
            },
          ],
          profileImage: "",
        },
        appointInfo: {
          date: values.appointmentDate,
          token: values.token || undefined,
          timeSlot: values.appointmentTime,
          doctor: EditData?._id,
          status: values.status,
          department: EditData?.departmentDetails?.department?._id || "",
          firstHirachy: EditData?.departmentDetails?.hirachyFirst?._id || "",
          specialist: EditData?.departmentDetails?.speciality?._id || "",
          remark: values.symptoms,
          paymentMethod: values.paymentMethod,
          transactionNo: values.transactionId || undefined,
          paymentStatus: values.paymentStatus,
          // reseller: values.resellerName !== "OTHER" && values.resellerName !== "none" ? values.resellerName : undefined,
        },
      });
      onClose();
    },
  });

  return (
    <FormikProvider value={formik}>
      <Form onSubmit={formik.handleSubmit} className="space-y-4">
        <div className="max-h-96 overflow-y-scroll">
          <div className="grid grid-cols-3 gap-y-2 gap-x-4">
            <FormField
              name="patientName"
              label="Patient Full Name"
              type="text"
              placeholder="eg: John Doe"
              formik={formik}
              required
            />
            <FormField
              name="email"
              label="Email"
              type="email"
              placeholder="Enter email"
              formik={formik}
              required
            />
            <FormField
              name="number"
              label="Contact Number"
              type="text"
              placeholder="Contact Number"
              formik={formik}
              required
            />
            <FormField
              name="alternateContactNumber"
              label="Alternate Contact Number"
              type="text"
              placeholder="Contact Number"
              formik={formik}
            />

            <FormField
              name="appointmentType"
              label="Appointment Type"
              type="dropdown"
              options={[
                { value: "Online-Booking", label: "Online-Booking" },
                { value: "Walk-in", label: "Walk-in" },
                { value: "Tele-Booking", label: "Tele-Booking" },
              ]}
              placeholder="Select Appointment Type"
              formik={formik}
              required
            />
            <FormField
              name="appointmentDate"
              label="Appointment Date"
              type="date"
              placeholder="Select Date"
              formik={formik}
              required
            />
            <FormField
              name="appointmentTime"
              label="Appointment Time"
              type="dropdown"
              placeholder="Select Time"
              options={[
                { value: "10:00-10:30", label: "10:00-10:30" },
                { value: "10:30-11:00", label: "10:30-11:00" },
                { value: "11:00-11:30", label: "11:00-11:30" },
                { value: "11:30-12:00", label: "11:30-12:00" },
                { value: "12:00-12:30", label: "12:00-12:30" },
              ]}
              formik={formik}
              required
            />
            <FormField
              name="status"
              label="Status"
              type="dropdown"
              placeholder="Status"
              options={[
                { value: "PENDING", label: "Pending" },
                { value: "CONFIRMED", label: "Confirmed" },
                { value: "inprogress", label: "In Progress" },
                { value: "RESCHEDULED", label: "Re-Scheduled" },
                { value: "CANCELLED", label: "Cancelled" },
              ]}
              formik={formik}
              required
            />
            <FormField
              name="gender"
              label="Gender"
              type="dropdown"
              placeholder="Select Gender"
              options={[
                { value: "MALE", label: "Male" },
                { value: "FEMALE", label: "Female" },
                { value: "OTHER", label: "Other" },
              ]}
              formik={formik}
              required
            />
            <FormField
              name="bloodGroup"
              label="Blood Group"
              type="dropdown"
              placeholder="Select Blood Group"
              options={bloodGroupOptions}
              formik={formik}
              required
            />
            <FormField
              name="dob"
              label="Date of Birth"
              type="date"
              formik={formik}
              required
            />
            <FormField
              name="maritalStatus"
              label="Marital Status"
              type="dropdown"
              placeholder="Select Marital Status"
              options={maritalStatusOptions}
              formik={formik}
            />
            <FormField
              name="language"
              label="Language"
              type="text"
              placeholder="Language"
              formik={formik}
            />
            <FormField
              name="currentAddress"
              label="Current Address"
              type="text"
              placeholder="Temporary Address"
              formik={formik}
              required
            />
            <FormField
              name="permanentAddress"
              label="Permanent Address"
              type="text"
              placeholder="Permanent Address"
              formik={formik}
            />
            <FormField
              name="citizenshipNo"
              label="Citizenship Number"
              type="text"
              placeholder="Citizenship Number"
              formik={formik}
              required
            />
            <FormField
              name="symptoms"
              label="Reason for Visit/ Symptoms"
              type="textarea"
              placeholder="Describe your symptoms"
              formik={formik}
            />
          </div>
          <div className="grid grid-cols-4 gap-x-4 gap-y-2 my-2">
            <FormField
              name="paymentMethod"
              label="Payment Method"
              type="dropdown"
              options={[
                { value: "CASH", label: "Cash" },
                { value: "BANK", label: "Bank" },
              ]}
              placeholder="Select"
              formik={formik}
              required
            />
            {formik.values.paymentMethod === "BANK" && (
              <>
                <FormField
                  name="bankName"
                  label="Bank Name"
                  type="dropdown"
                  placeholder="Select"
                  options={[
                    { value: "SBI", label: "SBI" },
                    { value: "HDFC", label: "HDFC" },
                    { value: "ICICI", label: "ICICI" },
                    { value: "AXIS", label: "AXIS" },
                  ]}
                  formik={formik}
                />
                <FormField
                  name="transactionId"
                  label="Transaction ID"
                  type="text"
                  placeholder="Transaction ID"
                  formik={formik}
                />
              </>
            )}
            <FormField
              name="paymentStatus"
              label="Payment Status"
              type="dropdown"
              placeholder="Select"
              options={[
                { value: "PAID", label: "Paid" },
                { value: "PENDING", label: "Pending" },
                { value: "PARTIALLY-PAID", label: "Partially Paid" },
              ]}
              formik={formik}
              required
            />
          </div>

          <div className="grid grid-cols-2 gap-x-4 gap-y-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-1 text-gray-900 border border-gray-300 rounded"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-1 bg-primary text-white rounded"
            >
              Confirm Appointment
            </button>
          </div>
        </div>
      </Form>
    </FormikProvider>
  );
};

export default NewPatientForm;
