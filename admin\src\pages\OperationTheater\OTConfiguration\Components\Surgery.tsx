import { Icon } from "@iconify/react/dist/iconify.js";
import { debounce } from "lodash";
import { useCallback, useState } from "react";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { otConfigColumns } from "./ot.obj";
import OtModal from "./OtModal";
import { useGetAllOperationTreature } from "../../../../server-action/api/operationTreature";

const Surgery = () => {
  const [search, setSearch] = useState({
    searchTerm: "",
    deboundSearchTerm: "",
  });
  const { data } = useGetAllOperationTreature();
  const [modatState, setModalState] = useState({
    state: false,
    edit: false,
    data: null,
  });

  const handleSearch = useCallback(
    debounce(
      (value) => setSearch((prev) => ({ ...prev, deboundSearchTerm: value })),
      500
    ),
    []
  );
  return (
    <div className="space-y-4">
      <div className="flex justify-between bg-white rounded-md p-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search"
            value={search.searchTerm}
            onChange={(e) => {
              setSearch({ ...search, searchTerm: e.target.value });
              handleSearch(e.target.value);
            }}
            className="w-full py-[6px] ps-8 px-4 border border-gray-300 rounded-[6px] text-sm focus:outline-none"
          />
          <Icon
            icon="mdi:magnify"
            className="absolute left-3 top-1/2 size-[16px] transform -translate-y-1/2 text-gray-400"
          />
        </div>
        <button
          onClick={() =>
            setModalState((prev) => ({
              ...prev,
              state: true,
              edit: false,
              data: null,
            }))
          }
          className="py-2 px-3 flex items-center justify-center gap-2 whitespace-nowrap rounded-sm bg-primary hover:bg-light_primary text-white text-sm"
        >
          <Icon icon="lucide:plus" className="size-[14px]" />
          Opeartion Theatres
        </button>
      </div>
      <MasterTable columns={otConfigColumns} loading={false} rows={[]} />
      <OtModal
        onClose={() => setModalState({ state: false, edit: false, data: null })}
        isOpen={modatState.state}
        data={modatState.data}
      />
    </div>
  );
};

export default Surgery;
