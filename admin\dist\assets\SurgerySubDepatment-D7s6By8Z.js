import{bR as H,bV as K,ds as J,aw as z,av as t,aK as P,a5 as d,a2 as e,b3 as f,a1 as F,a9 as G,aa as R,ac as O,a4 as M,bA as V,aH as Q}from"./index-ClX9RVH0.js";import{a as U,c as W,d as X,e as Y,b as Z,f as ee,g as te,h as ae}from"./OTAssignment-BHmGwNc9.js";const L=H(K.OPERATIONTHEATRES,"operation-theatre"),le=L.useCreate,se=L.useGetAll,ie=L.useUpdate,oe=[{title:"S.No",key:"sno"},{title:"Department",key:"department"},{title:"Sub Category",key:"subCategory"},{title:"Action",key:"action"}],de=[{title:"S.No",key:"sno"},{title:"OT Name",key:"otName"},{title:"Floor",key:"floor"},{title:"Department",key:"department"},{title:"Sub-Category",key:"subDepartment"},{title:"Action",key:"action"}],ce=()=>{const{data:i}=J(),{data:r,isSuccess:m}=z({role:"PATIENT"}),{data:n,isSuccess:u}=z({role:"DOCTOR"},{enabled:m}),{data:c,isSuccess:p}=z({role:"NURSE"},{enabled:u}),{data:C}=z({role:"ANESTHESIOLOGIST"},{enabled:p}),{data:y}=U(),{data:S}=se(),g=t.get(n,"data.users",[]).map(a=>({label:t.get(a,"commonInfo.personalInfo.fullName"),value:t.get(a,"_id")})),x=t.get(c,"data.users",[]).map(a=>({label:t.get(a,"commonInfo.personalInfo.fullName"),value:t.get(a,"_id")})),j=t.get(C,"data.users",[]).map(a=>({label:t.get(a,"commonInfo.personalInfo.fullName"),value:t.get(a,"_id")})),N=t.get(r,"data.users",[]).map(a=>({label:t.get(a,"commonInfo.personalInfo.fullName"),value:t.get(a,"_id")})),b=t.get(r,"data.users",[]).map(a=>({label:t.get(a,"patientInfo.patientId"),value:t.get(a,"_id")})),D=t.get(S,"data.ot",[]).map(a=>({label:t.get(a,"name"),value:t.get(a,"_id"),floor:t.get(a,"floor","")})),k=t.get(i,"data.bloodInventory",[]).map(a=>({...a,label:t.get(a,"bloodGroup",""),value:t.get(a,"bloodGroup","")})),o=t.get(y,"data.surgeryDepartments",[]).map(a=>({label:t.get(a,"name"),value:t.get(a,"_id"),subDepartments:t.get(a,"subDepartments",[]).map(v=>({label:t.get(v,"name"),value:t.get(v,"_id"),severity:t.get(v,"severity")}))}));return{patientList:N,patientIdList:b,surgeryDepartment:o,theatres:D,doctorList:g,nurseList:x,anesthesistList:j,bloodInventry:k}};function me(i,r){const m=P();return i.filter(n=>P(n.expiryDate).isAfter(m)&&n.bloodGroup===r).map(n=>({type:n.bloodGroup,unit:n.quantityInUnits}))}const ue=[{step:1,title:"Patient Information"},{step:2,title:"Surgery Details"},{step:3,title:"Medical Staff"},{step:5,title:"Surgical Team"}],pe=[{type:"searchableSelect",label:"Fasting Status",field:"preCheckups.fastingStatus",options:[{label:"Pending",value:"pending"},{label:"Confirmed",value:"confirmed"}]},{type:"searchableSelect",label:"Presurgery Checklist",field:"preCheckups.presurgery",options:[{label:"Pending",value:"pending"},{label:"Confirmed",value:"confirmed"}]},{type:"searchableSelect",label:"Emergency Equipment",field:"preCheckups.emergencyEquipment",options:[{label:"Pending",value:"pending"},{label:"Confirmed",value:"confirmed"}]}],ge=({isOpen:i,onClose:r,children:m,title:n,position:u="right",size:c="md",showOverlay:p=!0,closeOnOverlayClick:C=!0,showCloseButton:y=!0,className:S=""})=>{const[g,x]=d.useState(!1),[j,N]=d.useState(i);if(d.useEffect(()=>{i?(N(!0),setTimeout(()=>x(!0),10)):(x(!1),setTimeout(()=>N(!1),300))},[i]),d.useEffect(()=>{const o=a=>{a.key==="Escape"&&i&&r()};return i&&(document.addEventListener("keydown",o),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",o),document.body.style.overflow="unset"}},[i,r]),!j)return null;const b={sm:{left:"w-64",right:"w-64",top:"h-64",bottom:"h-64"},md:{left:"w-80",right:"w-80",top:"h-80",bottom:"h-80"},lg:{left:"w-96",right:"w-96",top:"h-96",bottom:"h-96"},xl:{left:"w-[32rem]",right:"w-[32rem]",top:"h-[32rem]",bottom:"h-[32rem]"},full:{left:"w-full",right:"w-full",top:"h-full",bottom:"h-full"}},D={left:`left-0 top-0 h-full ${b[c].left} ${g?"translate-x-0":"-translate-x-full"}`,right:`right-0 top-0 h-full ${b[c].right} ${g?"translate-x-0":"translate-x-full"}`,top:`top-0 left-0 w-full ${b[c].top} ${g?"translate-y-0":"-translate-y-full"}`,bottom:`bottom-0 left-0 w-full ${b[c].bottom} ${g?"translate-y-0":"translate-y-full"}`},k=()=>{C&&r()};return e.jsxs(e.Fragment,{children:[p&&e.jsx("div",{className:`fixed inset-0 bg-black transition-opacity duration-300 ease-in-out z-40 ${g?"bg-opacity-50":"bg-opacity-0"}`,onClick:k}),e.jsxs("div",{className:`fixed bg-white shadow-2xl transform transition-transform duration-300 ease-in-out z-50 ${D[u]} ${S}`,style:{backdropFilter:"blur(10px)"},children:[(n||y)&&e.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 bg-white",children:[n&&e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:n}),y&&e.jsx("button",{onClick:r,className:"p-2 hover:bg-gray-100 rounded-full transition-colors duration-200 ml-auto",children:e.jsx(f,{icon:"lucide:x",className:"text-gray-600 size-4"})})]}),e.jsx("div",{className:"flex-1 overflow-y-auto p-4",children:m})]})]})},fe=({onClose:i})=>{const[r,m]=d.useState({searchTerm:"",deboundSearchTerm:""}),[n,u]=d.useState(!1),[c,p]=d.useState({state:!1,id:""}),{data:C,isLoading:y}=U({...r.deboundSearchTerm!==""&&{name:r.deboundSearchTerm}}),S=t.get(C,"data.surgeryDepartments",[]),{mutate:g}=W(),{mutate:x,isPending:j,isSuccess:N}=X(),{mutate:b,isPending:D,isSuccess:k}=Y(),o=j||D,a=N||k,v=F({initialValues:{name:"",_id:""},enableReinitialize:!0,onSubmit:l=>{n?b({_id:l._id,entityData:{name:l.name}}):x({name:l.name})}}),{handleSubmit:h,setFieldValue:I,values:A,resetForm:T}=v;d.useEffect(()=>{a&&(T(),u(!1))},[a,T]);const E=d.useCallback(t.debounce(l=>m($=>({...$,deboundSearchTerm:l})),500),[]),w=l=>{u(!0),I("name",t.get(l,"name","")),I("_id",t.get(l,"_id",""))},_=()=>{n?(u(!1),T()):(T(),i())};return e.jsxs("div",{className:"space-y-6",children:[e.jsx(G,{value:v,children:e.jsxs(R,{className:"space-y-2",onSubmit:h,children:[e.jsx(O,{inputSize:"body-sm-default",inputClassName:"rounded-[6px]",label:n?"Edit Department":"Department",name:"name"}),e.jsxs("div",{className:"flex justify-end items-center gap-4",children:[e.jsx("button",{type:"button",onClick:_,className:"cursor-pointer text-sm border py-1.5 px-4 rounded-md",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:o||!A.name.trim(),className:`text-sm py-1.5 px-4 rounded-md text-white ${o||!A.name.trim()?"bg-primary/70 cursor-not-allowed":"bg-primary hover:bg-light_primary cursor-pointer"}`,children:o?e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(f,{icon:"icon-park-outline:loading-one",className:"size-4 animate-spin"}),n?"Updating...":"Creating..."]}):n?"Update":"Create"})]})]})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(M,{size:"body-sm-default",children:"Department List"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search",value:r.searchTerm,onChange:l=>{m({...r,searchTerm:l.target.value}),E(l.target.value)},className:"w-full py-[6px] ps-8 px-4 border border-gray-300 rounded-[6px] text-sm focus:outline-none"}),e.jsx(f,{icon:"mdi:magnify",className:"absolute left-3 top-1/2 size-[16px] transform -translate-y-1/2 text-gray-400"})]})]}),e.jsx("div",{className:"space-y-2",children:y?e.jsx("div",{className:"flex justify-center py-4",children:e.jsx(f,{icon:"icon-park-outline:loading-one",className:"size-6 animate-spin text-primary"})}):S.length?S.map(l=>e.jsxs("div",{className:"flex gap-2",children:[e.jsx("h4",{className:"rounded-[6px] font-medium border w-full py-1.5 px-2 text-gray-500",children:t.get(l,"name","N/A")}),e.jsxs("div",{className:"flex items-center gap-2 justify-center",children:[e.jsx("button",{onClick:()=>w(l),disabled:o,className:`p-1.5 rounded-sm border border-green ${o?"opacity-50 cursor-not-allowed":"hover:bg-green-50"}`,children:e.jsx(f,{icon:"lucide:edit",className:"size-[14px] min-w-[14px]"})}),e.jsx("button",{onClick:()=>p({state:!0,id:t.get(l,"_id")}),disabled:o,className:`p-1.5 rounded-sm border border-red ${o?"opacity-50 cursor-not-allowed":"hover:bg-red-50"}`,children:e.jsx(f,{icon:"lucide:trash",className:"size-[14px] min-w-[14px]"})})]})]},t.get(l,"_id"))):e.jsx("p",{className:"text-center py-4 text-gray-500",children:"No departments found"})}),e.jsx(V,{confirmAction:c.state,onClose:()=>p({state:!1,id:""}),onConfirm:()=>{g({id:c.id}),p({state:!1,id:""})}})]})]})},be=({onClose:i})=>{const[r,m]=d.useState({searchTerm:"",deboundSearchTerm:""}),[n,u]=d.useState(!1),[c,p]=d.useState({state:!1,id:""}),{data:C}=U({}),y=t.get(C,"data.surgeryDepartments",[]).map(s=>({value:s._id,label:s.name})),{data:S,isLoading:g,refetch:x}=Z({...r.deboundSearchTerm!==""&&{name:r.deboundSearchTerm}}),j=t.get(S,"data.surgerySubDepartments",[]),{mutate:N}=ee(),{mutate:b,isPending:D,isSuccess:k}=te(),{mutate:o,isPending:a,isSuccess:v}=ae(),h=D||a,I=k||v,A=F({initialValues:{name:"",department:"",_id:""},enableReinitialize:!0,onSubmit:s=>{n?o({_id:s._id,entityData:{name:s.name,department:s.department}}):b({name:s.name,department:s.department})}}),{handleSubmit:T,setFieldValue:E,values:w,resetForm:_}=A;d.useEffect(()=>{I&&(_(),u(!1),x())},[I,_,x]);const l=d.useCallback(t.debounce(s=>m(B=>({...B,deboundSearchTerm:s})),500),[]),$=s=>{u(!0),E("name",t.get(s,"name","")),E("department",t.get(s,"department._id",t.get(s,"department",""))),E("_id",t.get(s,"_id",""))},q=()=>{n?(u(!1),_()):(_(),i())};return e.jsxs("div",{className:"space-y-6",children:[e.jsx(G,{value:A,children:e.jsxs(R,{className:"space-y-2",onSubmit:T,children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Department"}),e.jsx(Q,{options:y,onChange:s=>E("department",s),value:w.department,placeholder:"Select Department"})]}),e.jsx(O,{inputSize:"body-sm-default",inputClassName:"rounded-[6px]",label:n?"Edit Sub-Department":"Sub-Department",name:"name"})]}),e.jsxs("div",{className:"flex justify-end items-center gap-4",children:[e.jsx("button",{type:"button",onClick:q,className:"cursor-pointer text-sm border py-1.5 px-4 rounded-md",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:h||!w.name.trim()||!w.department,className:`text-sm py-1.5 px-4 rounded-md text-white ${h||!w.name.trim()||!w.department?"bg-primary/70 cursor-not-allowed":"bg-primary hover:bg-light_primary cursor-pointer"}`,children:h?e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(f,{icon:"icon-park-outline:loading-one",className:"size-4 animate-spin"}),n?"Updating...":"Creating..."]}):n?"Update":"Create"})]})]})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(M,{size:"body-sm-default",children:"Sub-Department List"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search",value:r.searchTerm,onChange:s=>{m({...r,searchTerm:s.target.value}),l(s.target.value)},className:"w-full py-[6px] ps-8 px-4 border border-gray-300 rounded-[6px] text-sm focus:outline-none"}),e.jsx(f,{icon:"mdi:magnify",className:"absolute left-3 top-1/2 size-[16px] transform -translate-y-1/2 text-gray-400"})]})]}),e.jsx("div",{className:"space-y-2",children:g?e.jsx("div",{className:"flex justify-center py-4",children:e.jsx(f,{icon:"icon-park-outline:loading-one",className:"size-6 animate-spin text-primary"})}):j.length?j.map(s=>e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"rounded-[6px] font-medium border w-full py-1.5 px-2 text-gray-500",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{children:t.get(s,"name","N/A")}),e.jsxs("span",{className:"text-xs text-gray-400",children:["Department: ",t.get(s,"department.name","N/A")]})]})}),e.jsxs("div",{className:"flex items-center gap-2 justify-center",children:[e.jsx("button",{onClick:()=>$(s),disabled:h,className:`p-1.5 rounded-sm border border-green ${h?"opacity-50 cursor-not-allowed":"hover:bg-green-50"}`,children:e.jsx(f,{icon:"lucide:edit",className:"size-[14px] min-w-[14px]"})}),e.jsx("button",{onClick:()=>p({state:!0,id:t.get(s,"_id")}),disabled:h,className:`p-1.5 rounded-sm border border-red ${h?"opacity-50 cursor-not-allowed":"hover:bg-red-50"}`,children:e.jsx(f,{icon:"lucide:trash",className:"size-[14px] min-w-[14px]"})})]})]},t.get(s,"_id"))):e.jsx("p",{className:"text-center py-4 text-gray-500",children:"No sub-departments found"})}),e.jsx(V,{confirmAction:c.state,onClose:()=>p({state:!1,id:""}),onConfirm:()=>{N({id:c.id}),p({state:!1,id:""})}})]})]})};export{ge as D,be as S,fe as a,le as b,pe as c,ie as d,se as e,oe as f,me as g,de as o,ue as s,ce as u};
