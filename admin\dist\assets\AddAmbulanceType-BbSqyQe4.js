import{a5 as r,a1 as u,al as p,a2 as e,a7 as d,am as m,a9 as x,aa as b,ac as s,bl as h,ao as j}from"./index-ClX9RVH0.js";import{u as y}from"./ambulanceApi-C42c0mRe.js";const g=()=>{const{mutate:l}=y(),[c,a]=r.useState(1),n=[{step:1,title:"Basic Information",isActive:c===1}],t=u({initialValues:{ambulanceType:"",capacity:"",usage:"",equipments:"",description:""},enableReinitialize:!0,onSubmit:o=>{l(o),p.success("Form submitted successfully!"),history.back()}}),{handleSubmit:i}=t;return e.jsxs("div",{children:[e.jsx(d,{listTitle:"Add Ambulance Type",hideHeader:!0}),e.jsxs("div",{className:"relative flex w-full gap-4",children:[e.jsx("div",{className:"w-auto h-full",children:e.jsx(m,{steps:n})}),e.jsx("div",{className:"w-full",children:e.jsx(x,{value:t,children:e.jsx(b,{onSubmit:i,children:e.jsxs("div",{className:"flex flex-col w-full gap-4 p-4",children:[e.jsxs("div",{className:"flex flex-col gap-4 p-4 w-full bg-white rounded-sm",onClick:()=>a(1),children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(s,{label:"Ambulance Type",type:"text",placeholder:"Basic Life Support",name:"ambulanceType",onFocus:()=>a(1)}),e.jsx(s,{label:"Capacity",type:"text",placeholder:"2-3 patients",name:"capacity",onFocus:()=>a(1)}),e.jsx(s,{label:"Usage",type:"text",placeholder:"Routine Patient Transfer",name:"usage",onFocus:()=>a(1)})]}),e.jsxs("div",{children:[e.jsx(h,{name:"usage"}),e.jsx(s,{label:"Description",type:"text",placeholder:"For non-critical patients",name:"description",onFocus:()=>a(1)})]})]}),e.jsx(j,{onCancel:()=>history.back(),onSubmit:i})]})})})})]})]})};export{g as AddAmbulanceType};
