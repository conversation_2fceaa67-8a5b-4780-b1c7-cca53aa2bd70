import{aV as t,av as r,ca as o,aX as n,aW as i}from"./index-ClX9RVH0.js";const l=[{title:"S.No",key:"sn"},{title:"vednor Id",key:"vendorId"},{title:"Vendor Name",key:"name"},{title:"Contact Person",key:"contactPerson"},{title:"Phone No",key:"phoneNo"},{title:"Email",key:"email"},{title:"Address",key:"address"},{title:"category",key:"category"},{title:"Action",key:"action"}],c=e=>({fullName:r.get(e,"commonInfo.personalInfo.fullName",""),gender:r.get(e,"commonInfo.personalInfo.gender",""),email:r.get(e,"email",""),contactPerson:r.get(e,"vendorInfo.contactPerson",""),primaryPhone:r.get(e,"commonInfo.contactInfo.phone.primaryPhone",""),category:r.get(e,"vendorInfo.category",[]),currentAddress:r.get(e,"commonInfo.contactInfo.address.currentAddress",""),billingType:r.get(e,"vendorInfo.billingCycle","")==="1"?"bill":r.get(e,"vendorInfo.billingCycle","")>"1"?"days":"credit",organizationName:r.get(e,"vendorInfo.organizationName",""),organizationPAN:r.get(e,"vendorInfo.organizationPAN",""),bankName:r.get(e,"financialInfo.accountDetails.bankName",""),branchName:r.get(e,"financialInfo.accountDetails.branchName",""),creditLimit:r.get(e,"vendorInfo.creditLimit",""),billingCycle:r.get(e,"vendorInfo.billingCycle","30"),accountNumber:r.get(e,"financialInfo.accountDetails.accountNumber",""),accountHolderName:r.get(e,"financialInfo.accountDetails.accountHolderName","")}),m=t({fullName:n().required("**Vendor Name is required**"),gender:n().optional(),email:n().email("**Invalid email format**").required("**Email is required**"),contactPerson:n().required("**Contact Person is required**"),primaryPhone:n().required("**Phone Number is required**").min(8,"Minimum 8 characters").max(10,"Maximum 12 characters"),category:i().of(n().required("**Category is required**")),currentAddress:n().required("**Address is required**"),billingType:n().required("**Billing Type is required**"),billingCycle:n().when("billingType",{is:e=>e!=="credit",then:e=>e.required("**Billing Cycle is required**").matches(/^[0-9]+$/,"**Billing Cycle should be all digits**"),otherwise:e=>e.notRequired()}),creditLimit:n().when("billingType",{is:e=>e==="credit",then:e=>e.required("**Credit Limit is required**").matches(/^[0-9]+$/,"**Credit Limit should be all digits**"),otherwise:e=>e.notRequired()})}),d={"Basic Informaton":[{label:"Vendor Name",field:"fullName",type:"text",required:!0},{label:"Gender",field:"gender",type:"searchableSelect",options:[{label:"Male",value:"MALE"},{label:"Female",value:"FEMALE"},{label:"Other",value:"OTHER"}]},{label:"Email",field:"email",type:"email",placeholder:"<EMAIL>",required:!0},{label:"Contact Person",field:"contactPerson",type:"text",required:!0},{label:"Phone Number",field:"primaryPhone",type:"text",placeholder:"9*********",required:!0},{label:"Category",field:"category",type:"multi-select",options:o,required:!0},{label:"Address",field:"currentAddress",type:"text",required:!0}],"Business Information":[{label:"Company Name",field:"organizationName",type:"text"},{label:"PAN",field:"organizationPAN",type:"text"}]},s={"Payment Information":[{label:"Bank Name",field:"bankName",type:"text",required:!1},{label:"Branch Name",field:"branchName",type:"text",required:!1},{label:"Account Number",field:"accountNumber",type:"text",required:!1},{label:"Account Holder Name",field:"accountHolderName",type:"text",required:!1}]},g=[{step:1,title:"Basic Information"},{step:2,title:"Business Information"},{step:3,title:"Payment Information"}],u=e=>({commonInfo:{personalInfo:{fullName:r.get(e,"fullName",""),gender:r.get(e,"gender","")},contactInfo:{phone:{primaryPhone:r.get(e,"primaryPhone","")},address:{currentAddress:r.get(e,"currentAddress","")}}},vendorInfo:{contactPerson:r.get(e,"contactPerson",""),category:r.get(e,"category",[""]),organizationName:r.get(e,"organizationName",""),organizationPAN:r.get(e,"organizationPAN",""),...Number(r.get(e,"billingCycle",0))!==0&&{billingCycle:r.get(e,"billingCycle",0)},...Number(r.get(e,"creditLimit",0))!==0&&{creditLimit:r.get(e,"creditLimit",0)}},email:r.get(e,"email",""),role:"VENDOR",financialInfo:{accountDetails:{bankName:r.get(e,"bankName",""),branchName:r.get(e,"branchName",""),accountNumber:r.get(e,"accountNumber",""),accountHolderName:r.get(e,"accountHolderName","")}}});export{m as a,c as b,g as c,d,u as f,s as p,l as v};
