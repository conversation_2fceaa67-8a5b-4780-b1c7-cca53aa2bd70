import React from "react";
import Button from "../../../../components/Button";

const Header = () => {
  return (
    <div className="bg-white shadow-md rounded-xl p-4 flex flex-col md:flex-row items-center justify-between ">
      <div className=" flex flex-col md:flex-row gap-4">
        <div>
          <img src="/persona.png" alt="" />
        </div>
        <div className="flex flex-col items-start justify-center">
          <h1 className="font-semibold text-lg md:text-xl lg:text-2xl">
            <PERSON><PERSON><PERSON>
          </h1>
          <p className="flex justify-start">Patient ID: 241</p>
        </div>
      </div>

      <div>
        <Button
          variant="primary"
          size="md"
          title="Edit Profile"
          onClick={() => console.log("clicked")}
          className="bg-primary hover:bg-light_primary px-5 py-3  text-white "
        />
      </div>
    </div>
  );
};

export default Header;
