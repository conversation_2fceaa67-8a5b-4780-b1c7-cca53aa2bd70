import{a5 as n,as as $,ce as H,a1 as W,av as i,a2 as e,am as J,a9 as K,aa as Q,cd as l,aH as o,ac as d,b3 as j,aB as X}from"./index-ClX9RVH0.js";import{u as Y,g as k,s as Z,c as _,D as ee,S as se,a as te}from"./SurgerySubDepatment-D7s6By8Z.js";import"./OTAssignment-BHmGwNc9.js";const le=()=>{var C;const[D,b]=n.useState(1),[v,p]=n.useState({open:!1,of:""}),[N,f]=n.useState(""),{data:h}=$({patient:N},{enabled:N!==""}),[S,x]=n.useState(!1),{data:A}=H({},{enabled:S}),[I,G]=n.useState([]),{patientIdList:B,patientList:M,surgeryDepartment:F,theatres:L,doctorList:u,nurseList:c,anesthesistList:y,bloodInventry:m}=Y(),g=W({initialValues:{patient:"",date:"",department:"",subDepartment:"",operationType:"",ot:"",floor:"",time:"",cost:"",surgeon:"",assistantSurgeon:"",anesthesist:"",periOperatingNurse:"",scrubNurses:"",circulatingNurses:"",bloodInStock:!1,bloodGroup:"",bloodUnits:"",ward:"",bed:"",preCheckups:{fastingStatus:"pending",presurgery:"pending",emergencyEquipment:"pending"}},enableReinitialize:!0,onSubmit(s){console.log(s)}}),{setFieldValue:a,values:t}=g;n.useEffect(()=>{if(t.bloodInStock&&t.bloodGroup){const s=k(m,t.bloodGroup);!s||s.length===0||s.length>0&&s[0].unit===0?x(!0):x(!1)}else x(!1)},[t.bloodInStock,t.bloodGroup,m]),n.useEffect(()=>{if(i.get(h,"data.bedAssign",[]).length>0){const s=i.get(h,"data.bedAssign",[])[0],r=i.get(s,"transferInfo",[]);if(r.length>0){const O=r[r.length-1];a("bed",i.get(O,"bedNumber","")),a("ward",i.get(O,"categoryName.categoryName",""))}}},[h,a]),n.useMemo(()=>u.filter(s=>s.value!==t.surgeon&&s.value!==t.assistantSurgeon),[u,t.surgeon,t.assistantSurgeon]);const E=n.useMemo(()=>u.filter(s=>s.value!==t.assistantSurgeon),[u,t.assistantSurgeon]),z=n.useMemo(()=>u.filter(s=>s.value!==t.surgeon),[u,t.surgeon]),P=n.useMemo(()=>y.filter(s=>s.value!==t.anesthesist),[y,t.anesthesist]),T=n.useMemo(()=>c.filter(s=>s.value!==t.scrubNurses&&s.value!==t.circulatingNurses),[c,t.scrubNurses,t.circulatingNurses]),U=n.useMemo(()=>c.filter(s=>s.value!==t.periOperatingNurse&&s.value!==t.circulatingNurses),[c,t.periOperatingNurse,t.circulatingNurses]),q=n.useMemo(()=>c.filter(s=>s.value!==t.periOperatingNurse&&s.value!==t.scrubNurses),[c,t.periOperatingNurse,t.scrubNurses]),w=n.useCallback(()=>{p({open:!1,of:""})},[]),R={department:e.jsx(te,{onClose:w}),subDepartment:e.jsx(se,{onClose:w})},V=Z.map((s,r)=>({...s,isActive:r+1===D}));return e.jsxs("div",{className:"grid grid-cols-1 gap-4 h-full md:grid-cols-4",children:[e.jsx("div",{className:"",children:e.jsx("div",{className:"bg-white rounded-sm",children:e.jsx(J,{steps:V})})}),e.jsx("div",{className:"col-span-3 max-h-[100vh-98px] overflow-auto h-auto",children:e.jsx(K,{value:g,children:e.jsxs(Q,{className:"h-full space-y-4",onSubmit:g.handleSubmit,children:[e.jsxs("div",{onMouseOver:()=>b(1),className:"grid grid-cols-4 gap-4 bg-white rounded-sm p-4",children:[e.jsx(l,{label:"Patient Id",children:e.jsx(o,{onChange:s=>{f(s),a("patient",s)},options:B,value:t.patient})}),e.jsx(l,{label:"Patient Name",children:e.jsx(o,{onChange:s=>{f(s),a("patient",s)},options:M,value:t.patient})}),e.jsx(d,{disabled:!0,value:t.ward,label:"Ward"}),e.jsx(d,{disabled:!0,value:t.bed,label:"Bed"})]}),e.jsxs("div",{onMouseOver:()=>b(2),className:"grid grid-cols-3 gap-4 bg-white rounded-sm p-4",children:[e.jsx(d,{label:"Surgery Date",name:"date"}),e.jsxs("div",{className:"flex  items-end gap-2",children:[e.jsx(l,{label:"Department",children:e.jsx(o,{onChange:(s,r)=>{a("department",s),s!==""&&G(i.get(r,"subDepartments",[]))},options:F,value:t.department})}),e.jsx("button",{onClick:()=>p({open:!0,of:"department"}),className:"size-sm p-2 rounded-full shadow-md drop-shadow-md cursor-pointer",children:e.jsx(j,{icon:"lucide:plus",className:"size-3"})})]}),e.jsxs("div",{className:"flex items-end gap-2",children:[e.jsx(l,{label:"Sub Department",children:e.jsx(o,{onChange:(s,r)=>{a("subDepartment",s),a("severity",i.get(r,"severity",""))},options:I,value:t.subDepartment})}),e.jsx("button",{onClick:()=>p({open:!0,of:"subDepartment"}),className:"size-sm p-2 rounded-full shadow-md drop-shadow-md cursor-pointer",children:e.jsx(j,{icon:"lucide:plus",className:"size-3"})})]}),e.jsx(d,{disabled:!0,type:"text",label:"Operation Type",name:"severity"}),e.jsx(l,{label:"Assign OT",children:e.jsx(o,{onChange:(s,r)=>{a("ot",s),a("floor",i.get(r,"floor",""))},options:L,value:t.ot})}),e.jsx(d,{disabled:!0,type:"text",label:"Floor",name:"floor"}),e.jsx(d,{disabled:!0,type:"text",label:"Surgery Duration",name:"time"})]}),e.jsxs("div",{onMouseOver:()=>b(3),className:"grid grid-cols-3 gap-4 bg-white rounded-sm p-4",children:[e.jsx(l,{label:"Surgeon/Specialist",children:e.jsx(o,{onChange:s=>a("surgeon",s),options:E,value:t.surgeon})}),e.jsx(l,{label:"Assistant Surgeon",children:e.jsx(o,{onChange:s=>a("assistantSurgeon",s),options:z,value:t.assistantSurgeon})}),e.jsx(l,{label:"Anesthesist",children:e.jsx(o,{onChange:s=>a("anesthesist",s),options:P,value:t.anesthesist})}),e.jsx(l,{label:"Peri Operating Nurse",children:e.jsx(o,{onChange:s=>a("periOperatingNurse",s),options:T,value:t.periOperatingNurse})}),e.jsx(l,{label:"Scrub Nurses",children:e.jsx(o,{onChange:s=>a("scrubNurses",s),options:U,value:t.scrubNurses})}),e.jsx(l,{label:"Circulatin Nurses",children:e.jsx(o,{onChange:s=>a("circulatingNurses",s),options:q,value:t.circulatingNurses})})]}),e.jsxs("div",{className:"gap-4 bg-white rounded-sm p-4",children:[e.jsx("h3",{className:"font-medium mb-2",children:"Blood Requirements"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",id:"bloodInStock",className:"mr-2 h-4 w-4",checked:t.bloodInStock,onChange:s=>a("bloodInStock",s.target.checked)}),e.jsx("label",{htmlFor:"bloodInStock",className:"text-sm font-medium",children:"Is Blood in Stock?"})]}),t.bloodInStock&&e.jsxs("div",{className:"grid grid-cols-2",children:[e.jsxs("div",{className:"flex gap-4 w-full",children:[e.jsx(l,{label:"Blood Group",className:"w-full",children:e.jsx(o,{className:"w-full",onChange:s=>a("bloodGroup",s),options:m,value:t.bloodGroup})}),e.jsx(d,{inputClassName:"max-w-20 w-full",label:"Blood Group",type:"number",name:"bloodUnits",min:"1"})]}),e.jsx("div",{className:"flex items-center",children:(C=k(m,t.bloodGroup))==null?void 0:C.map(s=>e.jsxs("div",{className:"flex text-red items-center whitespace-nowrap",children:[e.jsx("span",{children:`${s.type} : `}),e.jsxs("span",{children:[` ${s.unit} units`," "]})]},s.type))})]}),S&&t.bloodInStock&&e.jsxs("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md",children:[e.jsxs("h4",{className:"text-sm font-medium text-yellow-800 mb-2",children:[e.jsx(j,{icon:"mdi:alert-circle-outline",className:"inline mr-1"}),"Blood Alternative Options"]}),e.jsx("div",{className:"grid grid-cols-3 gap-2",children:i.get(A,"data.bloodAlternatives",[]).map((s,r)=>e.jsxs("div",{className:"p-2 bg-white border border-gray-200 rounded text-sm",children:[e.jsx("div",{className:"font-medium",children:s.bloodGroup}),e.jsxs("div",{className:"text-gray-600",children:["Available: ",s.quantityInUnits," units"]})]},r))})]})]}),e.jsx("div",{className:"gap-4 grid grid-cols-3 bg-white rounded-sm p-4",children:e.jsx(X,{...g,formDatails:_})})]})})}),e.jsx(ee,{isOpen:v.open,showCloseButton:!1,onClose:()=>p({open:!1,of:""}),children:R[v.of]})]})};export{le as default};
