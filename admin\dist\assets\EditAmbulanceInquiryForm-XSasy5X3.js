import{ba as Sa,ad as Pa,aw as pa,a5 as p,bo as Ca,aV as _,aX as t,aW as xa,bm as O,a2 as a,bn as Ia,aa as Ra,ac as s,ab as f,bl as ba,a4 as fa,bp as u,bq as $,aY as Ea}from"./index-ClX9RVH0.js";import{a as Ka,b as La,d as _a,e as Oa}from"./ambulanceApi-C42c0mRe.js";const wa=()=>{var X;const{data:g}=Ka(),{data:h}=La(),{id:B}=Sa(),{data:d,isLoading:ga,error:Y}=_a(B),z=Pa(),{data:R}=pa(),{mutate:ha,isPending:W}=Oa(),G=p.useMemo(()=>(R==null?void 0:R.data.users.filter(e=>e.role==="NURSE"))||[],[R]),Aa=p.useMemo(()=>G.map(e=>({value:e._id||"",label:e.commonInfo.personalInfo.fullName||""}))||[],[G]),{data:U}=pa({role:"STAFF"},{enabled:G.length>0}),E=(U==null?void 0:U.data.users)??[],ja=p.useMemo(()=>(E==null?void 0:E.map(e=>({value:e._id||"",label:e.commonInfo.personalInfo.fullName})))||[],[E]),Da=[{value:"PENDING",label:"Pending"},{value:"ASSIGNED",label:"Assigned"},{value:"COMPLETED",label:"Completed"},{value:"CANCELLED",label:"Cancelled"}],ya=p.useMemo(()=>(h==null?void 0:h.data.ambulanceConfiguration.map(e=>({value:e._id||"",label:e.ambulanceType||""})))||[],[h]),{data:w}=Ca(),F=(X=w==null?void 0:w.data)==null?void 0:X.banks,K=F==null?void 0:F.map(e=>({value:e._id??"",label:e.bankName,extra:e.accountNumber})),va=[{label:"Cash",value:u.cash},{label:"Bank",value:u.bank},{label:"cheque",value:u.cheque}],Na=[{label:"Pending",value:$.PENDING},{label:"Paid",value:$.PAID},{label:"Partially-paid",value:$.PARTPAID}],Ma=(e,{resetForm:n})=>{const i={...e};delete i.financialDetails.totalCost,i.financialDetails.paymentMethod!==u.bank&&(delete i.financialDetails.bank,delete i.financialDetails.accountNo),ha({_id:B,entityData:i},{onSuccess:()=>{n(),z("/ambulance-management/ambulance-inquiry")}})},qa=_().shape({patientName:t().required("Patient Name is required"),contactNumber:t().required("Phone No is required"),date:t().required("The request date is required"),inquiryTime:t().required("inquiryTime is required"),assignedAmbulance:t().required("Ambulance is required"),ambulanceType:t(),nurseAssigned:xa().of(t().required("Nurse name is required")).min(1,"At least one nurse must be assigned").required("The assigned nurses are required"),staffAssigned:xa().of(t().required("Staff name is required")).min(1,"At least one staff must be assigned").required("The assigned staffs are required"),pickupLocation:_().shape({address:t().required("Pickup location is required")}),dropLocation:_().shape({address:t().required("Drop location is required")}),notes:t(),status:t().required("Inquiry Status is required"),financialDetails:_().shape({initialReadingKM:O().min(0,"Must be a positive number"),finalReadingKM:O().min(0,"Must be a positive number"),paidAmount:O(),dueAmount:O(),transactionNo:t(),paymentMethod:t(),paymentStatus:t(),bank:t()})}),Ta={patientName:"",contactNumber:"",date:"",inquiryTime:"",assignedAmbulance:"",ambulanceType:"",nurseAssigned:[],staffAssigned:[],pickupLocation:{address:"",coordinates:[]},dropLocation:{address:"",coordinates:[]},notes:"",status:"",financialDetails:{initialReadingKM:0,finalReadingKM:0,totalCost:0,paidAmount:0,dueAmount:0,paymentStatus:"",paymentMethod:"",bank:"",accountNo:""}},ka=p.useMemo(()=>{var i,l,c,L,A,j,D,y,v,N,M,q,T,k,S,P,C;if(!d)return Ta;const e=d;return{patientName:e.patientName||"",contactNumber:e.contactNumber||"",date:e.date?new Date(e.date).toISOString().split("T")[0]:"",inquiryTime:e.inquiryTime||"",assignedAmbulance:((i=e.assignedAmbulance)==null?void 0:i._id)||"",ambulanceType:((l=e.assignedAmbulance)==null?void 0:l.ambulanceType)||"",nurseAssigned:((c=e.nurseAssigned)==null?void 0:c.map(x=>x._id))||[],staffAssigned:((L=e.staffAssigned)==null?void 0:L.map(x=>x._id))||[],pickupLocation:{address:((A=e.pickupLocation)==null?void 0:A.address)||"",coordinates:((j=e.pickupLocation)==null?void 0:j.coordinates)||[]},dropLocation:{address:((D=e.dropLocation)==null?void 0:D.address)||"",coordinates:((y=e.dropLocation)==null?void 0:y.coordinates)||[]},notes:e.notes||"",status:e.status||"",financialDetails:{initialReadingKM:((v=e.financialDetails)==null?void 0:v.initialReadingKM)||0,finalReadingKM:((N=e.financialDetails)==null?void 0:N.finalReadingKM)||0,totalCost:((M=e.financialDetails)==null?void 0:M.totalCost)||0,paidAmount:((q=e.financialDetails)==null?void 0:q.paidAmount)||0,dueAmount:((T=e.financialDetails)==null?void 0:T.dueAmount)||0,paymentStatus:((k=e.financialDetails)==null?void 0:k.paymentStatus)||"",paymentMethod:((S=e.financialDetails)==null?void 0:S.paymentMethod)||"",bank:((P=e.financialDetails)==null?void 0:P.bank)||"",accountNo:((C=e.financialDetails)==null?void 0:C.accountNo)||""}}},[d]);return ga||!h||!g?a.jsx("div",{children:"Loading..."}):Y?a.jsxs("div",{children:["Error: ",Y.message]}):a.jsx("div",{children:a.jsx("div",{className:"bg-white rounded",children:a.jsx(Ia,{initialValues:ka,validationSchema:qa,onSubmit:Ma,enableReinitialize:!0,children:({errors:e,touched:n,values:i,handleChange:l,setFieldValue:c})=>{var A,j,D,y,v,N,M,q,T,k,S,P,C,x,H,J,Q,Z,V,aa,ea,ia,na,ta,sa,la,da,ca,ma,oa;p.useEffect(()=>{const m=i.financialDetails.totalCost||0,I=i.financialDetails.paidAmount||0,o=Math.max(0,m-I);c("financialDetails.dueAmount",o)},[i.financialDetails.totalCost,i.financialDetails.paidAmount,c]);const L=p.useMemo(()=>{const o=((g==null?void 0:g.data.ambulances)||[]).filter(r=>{var ua,ra;return((ua=r.ambulanceType)==null?void 0:ua._id)===i.ambulanceType||r._id===((ra=d==null?void 0:d.assignedAmbulance)==null?void 0:ra._id)}).map(r=>({value:r._id||"",label:r.vehicleNo||""})),b=d==null?void 0:d.assignedAmbulance;return b&&!o.some(r=>r.value===b._id)&&o.push({value:b._id,label:b.vehicleNo||"Unknown"}),o},[g,i.ambulanceType,d]);return a.jsx(Ra,{children:a.jsxs("div",{className:"p-6 relative",children:[a.jsxs("div",{className:"border-2 border-[#e8e6e7] px-4 py-3 rounded-xl mb-3",children:[a.jsx("div",{children:a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsxs("div",{children:[a.jsx(s,{label:"Patient Name",type:"text",name:"patientName",placeholder:"Enter patient Name"}),e.patientName&&n.patientName&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.patientName})]}),a.jsxs("div",{children:[a.jsx(s,{label:"Phone No.",type:"text",name:"contactNumber",placeholder:"Enter phone number"}),e.contactNumber&&n.contactNumber&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.contactNumber})]})]})}),a.jsx("div",{children:a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsxs("div",{children:[a.jsx(s,{label:"Request Date",type:"date",name:"date",placeholder:"2025-01-12"}),e.date&&n.date&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.date})]}),a.jsxs("div",{children:[a.jsx(s,{label:"Inquiry Time",type:"time",name:"inquiryTime",placeholder:"9:00 AM"}),e.inquiryTime&&n.inquiryTime&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.inquiryTime})]})]})}),a.jsx("div",{children:a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsx("div",{children:a.jsx(f,{firstInput:"Select",label:"Ambulance Type",name:"ambulanceType",value:i.ambulanceType,onChange:m=>{l(m),c("assignedAmbulance","")},options:ya})}),a.jsxs("div",{children:[a.jsx(f,{firstInput:"Select",label:"Ambulance",name:"assignedAmbulance",value:i.assignedAmbulance,onChange:l,options:L,disabled:!i.ambulanceType}),e.assignedAmbulance&&n.assignedAmbulance&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.assignedAmbulance})]})]})}),a.jsx("div",{children:a.jsx("div",{className:"grid grid-cols-2 gap-4 my-2",children:a.jsxs("div",{children:[a.jsx(f,{firstInput:"Select",label:"Status",name:"status",value:i.status,onChange:l,options:Da}),e.status&&n.status&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.status})]})})}),a.jsx("div",{children:a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsxs("div",{children:[a.jsx(s,{label:"Pickup Location",type:"text",name:"pickupLocation.address",placeholder:"Enter pickup location"}),((A=e.pickupLocation)==null?void 0:A.address)&&((j=n.pickupLocation)==null?void 0:j.address)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.pickupLocation.address})]}),a.jsxs("div",{children:[a.jsx(s,{label:"Drop Location",type:"text",name:"dropLocation.address",placeholder:"Enter drop location"}),((D=e.dropLocation)==null?void 0:D.address)&&((y=n.dropLocation)==null?void 0:y.address)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.dropLocation.address})]})]})}),a.jsx("div",{children:a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsxs("div",{children:[a.jsx(ba,{label:"Nurse Assigned",name:"nurseAssigned",options:Aa}),e.nurseAssigned&&n.nurseAssigned&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.nurseAssigned})]}),a.jsxs("div",{children:[a.jsx(ba,{label:"Staff Assigned",name:"staffAssigned",options:ja}),e.staffAssigned&&n.staffAssigned&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.staffAssigned})]})]})}),a.jsxs("div",{children:[a.jsx(fa,{text:"Reading in (Km)",className:"text-lg"}),a.jsx("div",{className:"border-b-2 border-gray-200"})]}),a.jsx("div",{children:a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsxs("div",{children:[a.jsx(s,{label:"Trip Start (KM)",type:"number",min:"0",name:"financialDetails.initialReadingKM",placeholder:"Enter the initial reading of ambulance in (Km)"}),((v=e.financialDetails)==null?void 0:v.initialReadingKM)&&((N=n.financialDetails)==null?void 0:N.initialReadingKM)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.initialReadingKM})]}),a.jsxs("div",{children:[a.jsx(s,{label:"Trip End (KM)",type:"number",min:"0",name:"financialDetails.finalReadingKM",placeholder:"Enter the final reading of ambulance in (Km)"}),((M=e.financialDetails)==null?void 0:M.finalReadingKM)&&((q=n.financialDetails)==null?void 0:q.finalReadingKM)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.finalReadingKM})]})]})}),a.jsxs("div",{children:[a.jsx(fa,{text:"Payment Details",className:"text-lg"}),a.jsx("div",{className:"border-b-2 border-gray-200"})]}),a.jsxs("div",{children:[a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsx(f,{firstInput:"Select",label:"Payment Method",name:"financialDetails.paymentMethod",value:i.financialDetails.paymentMethod,onChange:m=>{l(m),m.target.value!==u.bank&&(c("financialDetails.bank",""),c("financialDetails.accountNo",""))},options:va}),((T=e.financialDetails)==null?void 0:T.paymentMethod)&&((k=n.financialDetails)==null?void 0:k.paymentMethod)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.paymentMethod}),a.jsx(f,{firstInput:"Select",label:"Payment Status",name:"financialDetails.paymentStatus",value:i.financialDetails.paymentStatus,onChange:l,options:Na}),((S=e.financialDetails)==null?void 0:S.paymentStatus)&&((P=n.financialDetails)==null?void 0:P.paymentStatus)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.paymentStatus})]}),a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsx(s,{label:"Total Cost",type:"number",min:"0",name:"financialDetails.totalCost",value:i.financialDetails.totalCost,placeholder:"Total Cost",readOnly:!0}),((C=e.financialDetails)==null?void 0:C.totalCost)&&((x=n.financialDetails)==null?void 0:x.totalCost)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.totalCost})]}),i.financialDetails.paymentMethod===u.cash&&a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsxs("div",{children:[a.jsx(s,{label:"Paid Amount",type:"number",min:"0",name:"financialDetails.paidAmount",value:i.financialDetails.paidAmount,placeholder:"Paid Amount",onChange:l}),((H=e.financialDetails)==null?void 0:H.paidAmount)&&((J=n.financialDetails)==null?void 0:J.paidAmount)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.paidAmount})]}),a.jsxs("div",{children:[a.jsx(s,{label:"Due Amount",type:"number",min:"0",name:"financialDetails.dueAmount",value:i.financialDetails.dueAmount,placeholder:"Due Amount",readOnly:!0}),((Q=e.financialDetails)==null?void 0:Q.dueAmount)&&((Z=n.financialDetails)==null?void 0:Z.dueAmount)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.dueAmount})]})]}),i.financialDetails.paymentMethod===u.bank&&a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsxs("div",{children:[a.jsx(f,{label:"Bank Name",name:"financialDetails.bank",value:i.financialDetails.bank,firstInput:"select bank",onChange:m=>{const I=m.target.value;c("financialDetails.bank",I);const o=K==null?void 0:K.find(b=>b.value===I);c("financialDetails.accountNo",(o==null?void 0:o.extra)||"")},options:K}),((V=e.financialDetails)==null?void 0:V.bank)&&((aa=n.financialDetails)==null?void 0:aa.bank)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.bank})]}),a.jsxs("div",{children:[a.jsx(s,{label:"Account Number",type:"text",name:"financialDetails.accountNo",value:i.financialDetails.accountNo,placeholder:"Account Number",readOnly:!0}),((ea=e.financialDetails)==null?void 0:ea.accountNo)&&((ia=n.financialDetails)==null?void 0:ia.accountNo)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.accountNo})]})]}),a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsxs("div",{children:[a.jsx(s,{label:"Paid Amount",type:"number",min:"0",name:"financialDetails.paidAmount",value:i.financialDetails.paidAmount,placeholder:"Paid Amount",onChange:l}),((na=e.financialDetails)==null?void 0:na.paidAmount)&&((ta=n.financialDetails)==null?void 0:ta.paidAmount)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.paidAmount})]}),a.jsxs("div",{children:[a.jsx(s,{label:"Due Amount",type:"number",min:"0",name:"financialDetails.dueAmount",value:i.financialDetails.dueAmount,placeholder:"Due Amount",readOnly:!0}),((sa=e.financialDetails)==null?void 0:sa.dueAmount)&&((la=n.financialDetails)==null?void 0:la.dueAmount)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.dueAmount})]})]})]}),i.financialDetails.paymentMethod===u.cheque&&a.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[a.jsxs("div",{children:[a.jsx(s,{label:"Paid Amount",type:"number",min:"0",name:"financialDetails.paidAmount",value:i.financialDetails.paidAmount,placeholder:"Paid Amount",onChange:l}),((da=e.financialDetails)==null?void 0:da.paidAmount)&&((ca=n.financialDetails)==null?void 0:ca.paidAmount)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.paidAmount})]}),a.jsxs("div",{children:[a.jsx(s,{label:"Due Amount",type:"number",min:"0",name:"financialDetails.dueAmount",value:i.financialDetails.dueAmount,placeholder:"Due Amount",readOnly:!0}),((ma=e.financialDetails)==null?void 0:ma.dueAmount)&&((oa=n.financialDetails)==null?void 0:oa.dueAmount)&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.financialDetails.dueAmount})]})]})]}),a.jsxs("div",{children:[a.jsx(Ea,{label:"Notes",name:"notes"}),e.notes&&n.notes&&a.jsx("div",{className:"text-red text-xs mb-2",children:e.notes})]})]}),a.jsxs("div",{className:"flex justify-end space-x-4",children:[a.jsx("button",{type:"button",onClick:()=>z("/ambulance-management/ambulance-inquiry"),className:"px-4 py-2 bg-gray-300 text-black rounded",children:"Cancel"}),a.jsx("button",{type:"submit",disabled:W,className:"px-4 py-2 bg-primary hover:bg-light_primary text-white rounded",children:W?"Updating...":"Update"})]})]})})}})})})};export{wa as default};
