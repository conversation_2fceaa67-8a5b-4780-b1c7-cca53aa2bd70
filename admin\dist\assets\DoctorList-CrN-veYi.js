import{ad as F,a5 as N,aM as j,bw as a,aw as L,bI as w,a2 as o,af as _,ag as c,ah as $,av as H,aQ as G,aj as M}from"./index-ClX9RVH0.js";const U=()=>{var u,p,D,S;const l=F(),x=[a.DOCTOR,a.MEDICAL_OFFICER,a.CONSULTANT,a.PHYSIOTHERAPIST,a.SURGEON,a.ANESTHESIOLOGIST,a.DENTIST,a.OPTOMETRIST,a.PSYCHOLOGIST],[s,i]=N.useState({limit:5,page:1}),[t,r]=N.useState({selectedDepartment:"",selectedSpecialist:"",search:""}),b=t.search,f=j({multiroles:JSON.stringify(x),search:t.search,"departmentDetails.department":t.selectedDepartment,"departmentDetails.speciality":t.selectedSpecialist,...b?{}:{page:s.page,limit:s.limit}}),{data:n,isLoading:R}=L(f),{mutateAsync:C}=w(),P=()=>{l(`${c.ADDDOCTOR}`)},d={columns:[{title:"Name",key:"doctorName"},{title:"ID",key:"tokenid"},{title:"Department",key:"department"},{title:"Specialist",key:"specialist"},{title:"Available Status",key:"status"},{title:"Action",key:"action"}],rows:(p=(u=n==null?void 0:n.data)==null?void 0:u.users)==null?void 0:p.map((e,v)=>{var g,T,h,O,y,A,I,E;return{key:v,tokenid:`D-${(T=e==null?void 0:e._id)==null?void 0:T.slice(((g=e==null?void 0:e._id)==null?void 0:g.length)-5,e==null?void 0:e._id.length)}`,department:((O=(h=e==null?void 0:e.departmentDetails)==null?void 0:h.hirachyFirst)==null?void 0:O.name)??"-",doctorName:(A=(y=e==null?void 0:e.commonInfo)==null?void 0:y.personalInfo)==null?void 0:A.fullName,specialist:((E=(I=e==null?void 0:e.departmentDetails)==null?void 0:I.speciality)==null?void 0:E.name)??"-",status:o.jsx($,{status:e!=null&&e.isActive?"ACTIVE":"INACTIVE"}),action:o.jsx(_,{onShow:()=>{l(`${c.DOCTORDETAIL}/${e==null?void 0:e._id}`)},onEdit:()=>{l(`${c.ADDDOCTOR}/${e==null?void 0:e._id}`)},onDelete:()=>{C({id:JSON.stringify([e==null?void 0:e._id])})}})}})},k=H.debounce(e=>r({...t,search:e}),500);return o.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[o.jsx(G,{headerTitle:"Doctor List",onSearch:!0,onSpecialist:!0,onDepartment:!0,onFilter:!0,button:!0,buttonText:"Add Doctor",onSearchFunc:e=>k(e),buttonAction:P,onDepartmentSelectFunc:e=>r({...t,selectedDepartment:e}),onSpecialistSelectFunc:e=>r({...t,selectedSpecialist:e})}),o.jsx("div",{className:"bg-white rounded-md",children:o.jsx(M,{columns:d.columns,rows:d.rows,loading:R,pagination:{currentPage:s.page,totalPage:(S=(D=n==null?void 0:n.data)==null?void 0:D.pagination)==null?void 0:S.pages,limit:s.limit,onClick:e=>{e.page&&i({...s,page:e.page}),e.limit&&i({...s,limit:e.limit})}}})})]})};export{U as default};
