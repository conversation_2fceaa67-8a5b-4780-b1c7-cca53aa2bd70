import{a5 as s,aP as I,aM as H,a_ as k,a$ as M,av as t,a2 as a,af as L,aQ as w,aj as F,aR as E}from"./index-ClX9RVH0.js";import{A as O}from"./AddProductListForm-OS2aOkDS.js";import"./ProductMultiSelect-DbxM3Xg6.js";const _=()=>{const[P,Q]=s.useState(""),y=P,[c,h]=s.useState({page:1,limit:10}),[o,i]=s.useState({search:"",selectedDepartment:"",selectedHospInvProductCategory:""}),[T,d]=s.useState("create"),[x,u]=s.useState(null),[S,n]=s.useState(!1),A=I(()=>{n(!1)}),D=()=>{d("create"),u(null),n(!0)},N=e=>{d("edit"),u(e),n(!0)},f=()=>a.jsx(O,{editData:x,onClose:()=>n(!1)}),v=H({search:o.search,department:o.selectedDepartment,category:o.selectedHospInvProductCategory,...y?{}:{page:c.page,limit:c.limit}}),{data:g,isLoading:C}=k(v),{mutate:b}=M(),p=t.get(g,"data.pagination",{}),j=({page:e,limit:l})=>{h(r=>({...r,page:e??1,limit:l??r.limit}))},m={columns:[{title:"S.N",key:"sn"},{title:"Product Name",key:"productName"},{title:"Category",key:"cattegory"},{title:"Department",key:"Department"},{title:"Usages",key:"Usages"},{title:"Action",key:"action"}],rows:t.get(g,"data.products",[]).map((e,l)=>({sn:l+1,productName:t.get(e,"name","N/A"),cattegory:t.get(e,"category.categoryName","N/A"),Department:t.get(e,"department",[]).length>0?t.get(e,"department",[]).map(r=>r.name||"N/A").join(", "):"All",Usages:t.get(e,"description","N/A"),action:a.jsx(L,{onEdit:()=>N(e),onDelete:()=>{b({id:JSON.stringify([t.get(e,"_id","-")])})}})}))};return a.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[a.jsx(w,{headerTitle:"Products List",onSearch:!0,toSearch:"Name, Product Code, Description",onDepartment:!0,onHospInvProductCategory:!0,onSearchFunc:e=>i({...o,search:e}),onHospInvProductCategorySelectFunc:e=>i({...o,selectedHospInvProductCategory:e}),onDepartmentSelectFunc:e=>i({...o,selectedDepartment:e}),onFilter:!0,button:!0,buttonText:"Add product",buttonAction:D}),a.jsx("div",{className:"bg-white rounded-md",children:a.jsx(F,{color:"bg-white",textcolor:"text-gray-400",columns:m.columns,rows:m.rows,loading:C,pagination:{totalPage:p.pages||1,currentPage:p.page||1,limit:c.limit||10,onClick:j}})}),S&&a.jsx(E,{ref:A,children:f()})]})};export{_ as default};
