import { Icon } from "@iconify/react";

interface Reminder {
  icon: string;
  title: string;
  time: string;
  type: string;
}

const Alerts = () => {
  const reminders: <PERSON>minder[] = [
    {
      icon: "fluent:drop-20-filled",
      title: "Blood Collection from bed number 203",
      time: "10:00 AM",
      type: "blood",
    },
    {
      icon: "fluent:stethoscope-20-filled",
      title: "Regular Vitals Checkup for bed number 205",
      time: "2:30 PM",
      type: "vitals",
    },
    {
      icon: "mdi:medication",
      title: "Medication Reminder for bed number 210",
      time: "4:00 PM",
      type: "medication",
    },
    {
      icon: "mdi:water-outline",
      title: "IV Fluid Replacement for bed number 198",
      time: "5:15 PM",
      type: "iv",
    },
    {
      icon: "mdi:food",
      title: "Meal Delivery for bed number 215",
      time: "12:00 PM",
      type: "meal",
    },
    {
      icon: "mdi:bed-clock",
      title: "Patient Rest Evaluation for bed number 220",
      time: "9:30 PM",
      type: "rest",
    },
  ];

  return (
    <div className='w-full bg-[#E0E0E0] rounded-lg shadow-md border border-gray-200'>
      <div className='flex items-center justify-between p-4 mb-4 bg-white rounded-t-lg'>
        <h3 className='text-lg font-semibold text-black'>
          Important Notes/Alerts
        </h3>
        {/* <button className='text-gray-400 hover:text-gray-600'>
          <Icon icon='mdi:dots-horizontal' width='20' height='20' />
        </button> */}
      </div>

      <div className='space-y-3 overflow-y-auto max-h-[280px] pr-2'>
        {reminders.map((reminder, index) => (
          <div
            key={index}
            className='flex items-start gap-3 p-2 transition-colors rounded-md hover:bg-white'
          >
            <div
              className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center ${
                reminder.type === "blood" ? "bg-red-100" : "bg-blue-100"
              }`}
            >
              <Icon
                icon={reminder.icon}
                width='28'
                height='28'
                className={
                  reminder.type === "blood" ? "text-rose-600" : "text-sky-600"
                }
              />
            </div>

            <div className='flex-1 min-w-0'>
              <p className='text-sm font-medium leading-tight text-gray-900'>
                {reminder.title}
              </p>
              <p className='mt-1 text-xs text-gray-500'>{reminder.time}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Alerts;
