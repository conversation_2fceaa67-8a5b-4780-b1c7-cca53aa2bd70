// import React, { useState } from "react";
// import MasterTable from "../../../../../../../layouts/Table/MasterTable";
// import { VitalData } from "../../Consultations/pages/sampleMasterTableData";

// const VitalSigns: React.FC = () => {
//   const [formData, setFormData] = useState({
//     weight: "",
//     bmi: "",
//     temperature: "",
//     systolicBP: "",
//     distolicBP: "",
//     respiratoryRate: "",
//     heartRate: "",
//     urineOutput: "",
//     bloodSugarF: "",
//     bloodSugarR: "",
//     SPO2: "",
//     AVPU: "",
//     trauma: "",
//     mobility: "",
//     oxygenSupplementation: "",
//     intake: "",
//     output: "",
//     vitalTakenDateTime: "",
//     comment: "",
//   });

//   const handleChange = (
//     e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
//   ) => {
//     const { name, value } = e.target;
//     setFormData((prev) => ({ ...prev, [name]: value }));
//   };

//   return (
//     <div className="p-4 space-y-8">
//       {/* Vital Signs Inputs */}
//       <div className="p-4 border-2 rounded-lg">
//         <h2 className="mb-4 font-semibold text-center">Vital Signs</h2>
//         <div className="grid grid-cols-3 gap-4 ">
//           {[
//             { label: "Weight", name: "weight" },
//             { label: "BMI", name: "bmi" },
//             { label: "Temperature", name: "temperature" },
//             { label: "Systolic BP", name: "systolicBP" },
//             { label: "Distolic BP", name: "distolicBP" },
//             { label: "Respiratory Rate", name: "respiratoryRate" },
//             { label: "Heart Rate", name: "heartRate" },
//             { label: "Urine Output", name: "urineOutput" },
//             { label: "Blood Sugar (F)", name: "bloodSugarF" },
//             { label: "Blood Sugar (R)", name: "bloodSugarR" },
//             { label: "SPO2", name: "SPO2" },
//             { label: "AVPU", name: "AVPU" },
//             { label: "Trauma", name: "trauma" },
//             { label: "Mobility", name: "mobility" },
//             { label: "Oxygen Supplement", name: "oxygenSupplementation" },
//             { label: "Intake", name: "intake" },
//             { label: "Output", name: "output" },
//             { label: "Vital Taken Time", name: "vitalTakenDateTime" },
//           ].map((field) => (
//             <div key={field.name} className="flex items-center gap-2">
//               <label className="w-20 text-md">{field.label}</label>
//               <input
//                 type="text"
//                 name={field.name}
//                 value={formData[field.name as keyof typeof formData]}
//                 onChange={handleChange}
//                 className="px-2 py-1 border border-gray-300 rounded grid-1"
//               />
//             </div>
//           ))}
//         </div>

//         <div className="flex items-center gap-2 mt-4">
//           <label className="w-24 text-sm">Notes</label>
//           <textarea
//             name="comment"
//             value={formData.comment}
//             onChange={handleChange}
//             className="flex-1 px-2 py-1 border border-gray-300 rounded"
//             rows={2}
//           ></textarea>
//           <button
//             className="py-2 text-white rounded px-7 bg-[#116aef]"
//             onClick={() => console.log("Vitals Submitted:", formData)}
//           >
//             Submit
//           </button>
//         </div>
//       </div>

//       {/* Vitals History Table */}
//       <div>
//         <h2 className="mb-4 font-semibold text-center">Vitals History</h2>
//         <div className="overflow-x-auto">
//           <MasterTable
//             color="bg-[#b3b3b3]"
//             textcolor="text-[#000000]/100"
//             columns={VitalData.columns}
//             rows={VitalData.rows}
//             loading={false}
//           />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default VitalSigns;
import React from "react";
import { Form, FormikProvider, useFormik } from "formik";
import { get, map } from "lodash";
import { InputField, Text } from "../../../../../../../components";
import { ActionButton } from "../../../../../../../components/ActionButton";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { Status } from "../../../../../../../components/Status";
import { TableAction } from "../../../../../../../layouts/Table/TableAction";
import { useUpdatePatientHistory } from "../../../../../../../server-action/api/patienthistory.api";
import MultiSelectWithChips from "../../../../../../../components/MultiSelectWithChips";
import { useGetUser } from "../../../../../../../server-action/api/user";

interface VitalSignValues {
  nurse?: { _id: string; label: string }[];
  visitNo?: string;
  height?: string;
  weight?: string;
  bmi?: string;
  temperature?: string;
  systolicBP?: string;
  distolicBP?: string;
  respiratoryRate?: string;
  heartRate?: string;
  urineOutput?: string;
  bloodSugarF?: string;
  bloodSugarR?: string;
  SPO2?: string;
  AVPU?: string;
  trauma?: string;
  mobility?: string;
  oxygenSupplementation?: string;
  intake?: string;
  output?: string;
  vitalTakenDateTime?: string;
  comment?: string;
}

interface VitalSignsProps {
  data: {
    _id: string;
    data: {
      users: any[];
    };
  };
}

const VitalSigns: React.FC<VitalSignsProps> = ({ data }) => {
  console.log(data, "data");
  const { mutate } = useUpdatePatientHistory();
  const formik = useFormik<VitalSignValues>({
    initialValues: {
      nurse: [],
      visitNo: "",
      height: "",
      weight: "",
      bmi: "",
      temperature: "",
      systolicBP: "",
      distolicBP: "",
      respiratoryRate: "",
      heartRate: "",
      urineOutput: "",
      bloodSugarF: "",
      bloodSugarR: "",
      SPO2: "",
      AVPU: "",
      trauma: "",
      mobility: "",
      oxygenSupplementation: "",
      intake: "",
      output: "",
      vitalTakenDateTime: "",
      comment: "",
    },
    onSubmit: async (values) => {
      const finalValue = {
        _id: data._id,
        // nurse: values.nurse,
        vitalSigns: [
          {
            ...values,
          },
        ],
      };
      console.log(finalValue, "I am final");
      mutate({
        _id: data._id,
        entityData: finalValue,
      });
    },
  });

  const { handleSubmit, values, setFieldValue } = formik;

  const { data: userData } = useGetUser({ role: "NURSE" });

  const NurseList = map(get(userData, "data.users", []), (user) => ({
    label: get(user, "commonInfo.personalInfo.fullName", ""),
    value: get(user, "_id", ""),
  }));
  console.log(NurseList, "NurseList");
  console.log(data, "hello");

  const tableData = {
    columns: [
      { title: "Visit No.", key: "visitNo" },
      { title: "Time", key: "vitalTakenDateTime" },
      { title: "Heart Rate", key: "heartRate" },
      { title: "Temperature ", key: "temperature" },
      { title: "oxygen", key: "oxygenSupplementation" },
      { title: "Action", key: "action" },
    ],
    rows: [
      {
        visitNo: "001",
        vitalTakenDateTime: "07/04/2025 10:30 AM",
        heartRate: "75 bpm",
        temperature: "98°F",
        oxygenSupplementation: "Room air",
        action: "View",
      },
      {
        visitNo: "002",
        vitalTakenDateTime: "07/04/2025 04:00 PM",
        heartRate: "78 bpm",
        temperature: "98.4°F",
        oxygenSupplementation: "Nasal cannula",
        action: "View",
      },
      {
        visitNo: "003",
        vitalTakenDateTime: "07/05/2025 08:15 AM",
        heartRate: "72 bpm",
        temperature: "97.9°F",
        oxygenSupplementation: "Room air",
        action: <TableAction onShow={() => {}} />,
      },
    ],

    // rows: get(data, "vitalSigns", []).map((item: any, index: number) => ({
    //   key: index,
    //   visitNo: item.visitNo,
    //   vitalTakenDateTime: item.vitalTakenDateTime,
    //   heartRate: item.heartRate,
    //   oxygenSupplementation: item.oxygenSupplementation,
    //   name: get(item, "nurse.commonInfo.personalInfo.fullName", "-"),
    //   action: <TableAction onShow={() => {}} />,
    // })),
  };
  console.log(formik.values, "");

  return (
    <div className="p-2 space-y-0">
      <div className="p-2 border-2 rounded-lg">
        <h2 className="mb-2 font-semibold text-center">Vital Signs</h2>
        <FormikProvider value={formik}>
          <Form autoComplete="off" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 p-2 sm:grid-cols-2 lg:grid-cols-3 gap-x-10 gap-y-4">
              {[
                {
                  label: "Visit No",
                  name: "visitNo",
                  placeholder: "Enter Visitor No.",
                  type: "text",
                },
                {
                  label: "Weight",
                  name: "weight",
                  placeholder: "Enter Weight",
                  type: "text",
                },
                {
                  label: "BMI",
                  name: "bmi",
                  placeholder: "Enter BMI",
                  type: "text",
                },
                {
                  label: "Temperature",
                  name: "temperature",
                  placeholder: "Enter Temperature",
                  type: "text",
                },
                {
                  label: "Systolic BP",
                  name: "systolicBP",
                  placeholder: "Enter Systolic BP",
                  type: "text",
                },
                {
                  label: "Distolic BP",
                  name: "distolicBP",
                  placeholder: "Enter Distolic BP",
                  type: "text",
                },
                {
                  label: "Respiratory Rate",
                  name: "respiratoryRate",
                  placeholder: "Enter Respiratory Rate",
                  type: "text",
                },
                {
                  label: "Heart Rate",
                  name: "heartRate",
                  placeholder: "Enter Heart Rate",
                  type: "text",
                },
                {
                  label: "Urine Output",
                  name: "urineOutput",
                  placeholder: "Enter Urine Output",
                  type: "text",
                },
                {
                  label: "Blood Sugar F",
                  name: "bloodSugarF",
                  placeholder: "Enter Blood Sugar F",
                  type: "text",
                },
                {
                  label: "Blood Sugar R",
                  name: "bloodSugarR",
                  placeholder: "Enter Blood Sugar R",
                  type: "text",
                },
                {
                  label: "SPO2",
                  name: "SPO2",
                  placeholder: "Enter SPO2",
                  type: "text",
                },
                {
                  label: "AVPU",
                  name: "AVPU",
                  placeholder: "Enter AVPU",
                  type: "text",
                },
                {
                  label: "Trauma",
                  name: "trauma",
                  placeholder: "Enter Trauma",
                  type: "text",
                },
                {
                  label: "Mobility",
                  name: "mobility",
                  placeholder: "Enter Mobility",
                  type: "text",
                },
                {
                  label: "Oxygen Supplementation",
                  name: "oxygenSupplementation",
                  placeholder: "Enter Oxygen Supplementation",
                  type: "text",
                },
                {
                  label: "Intake",
                  name: "intake",
                  placeholder: "Enter Intake",
                  type: "text",
                },
                {
                  label: "Output",
                  name: "output",
                  placeholder: "Enter Output",
                  type: "text",
                },
              ].map(({ label, name, placeholder, type }) => (
                <div key={name} className="flex flex-col gap-0">
                  <InputField
                    label={label}
                    name={name}
                    type={type}
                    placeholder={placeholder}
                  />
                </div>
              ))}

              {/* <div className="flex flex-col gap-1">
                <label className="flex gap-1 text-gray-800">
                  <Text
                    variant="grey-500"
                    size="body-sm-sm"
                    className="tracking-wide capitalize"
                  >
                    Nurse Name
                  </Text>
                </label>
                <MultiSelectWithChips
                  field="nurse"
                  options={NurseList}
                  values={Object.values(get(values, "nurse", {}))}
                  setFieldValue={setFieldValue}
                  placeholder="Search and select"
                />
              </div> */}
              <div className="flex flex-col gap-1">
                <InputField
                  label="Date & Time Picker"
                  name="vitalTakenDateTime"
                  type="datetime-local"
                  placeholder="Enter Date & Time"
                />
              </div>

              <div className="flex flex-col gap-1 lg:col-span-2">
                <InputField
                  label="Comment"
                  name="comment"
                  type="text"
                  placeholder="Enter Comment"
                />
              </div>
            </div>

            <div className="flex justify-end mt-2 mb-2">
              <ActionButton hideCancel="hidden" onSubmit={handleSubmit} />
            </div>
          </Form>
        </FormikProvider>

        <div>
          <h2 className="my-2 font-semibold text-center">Vitals History</h2>
          <div className="overflow-x-auto">
            <MasterTable
              columns={tableData.columns}
              rows={tableData.rows}
              loading={!data}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default VitalSigns;
