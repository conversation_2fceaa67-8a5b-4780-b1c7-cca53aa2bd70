import { useFormik } from "formik";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import * as Yup from "yup";
import { Icon } from "@iconify/react/dist/iconify.js";

const SampleTableData = {
  columns: [
    { title: "S.N", key: "serialNumber" },
    { title: "Investigation/Procedure", key: "procedure" },
    { title: "Result", key: "result" },
    { title: "STAT", key: "stat" },
    { title: "OT Required", key: "otRequired" },
  ],
  rows: [
    {
      serialNumber: "1",
      procedure: "LIPID PROFILE",
      result: ".......",
      stat: "No",
      otRequired: "No",
    },
    {
      serialNumber: "2",
      procedure: "LIPID PROFILE",
      result: ".......",
      stat: "No",
      otRequired: "No",
    },
    {
      serialNumber: "3",
      procedure: "LIPID PROFILE",
      result: ".......",
      stat: "No",
      otRequired: "No",
    },
    {
      serialNumber: "4",
      procedure: "LIPID PROFILE",
      result: ".......",
      stat: "No",
      otRequired: "No",
    },
  ],
};

const Investigation = () => {
  const formik = useFormik({
    initialValues: {
      investigation: "",
    },
    validationSchema: Yup.object({
      investigation: Yup.string().required("Investigation Required"),
    }),
    onSubmit: (values) => {
      console.log(values);
    },
  });
  return (
    <div className='flex flex-col w-full gap-4 p-4 mx-auto'>
      <h2 className='mb-4 font-semibold text-center'>
        Investigation / Procedure
      </h2>
      <form
        onSubmit={formik.handleSubmit}
        className='w-full p-6 mx-auto space-y-4 border rounded-md shadow-sm '
      >
        <div className='flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <label className='text-sm font-medium '>
              Investigation/ Procedure
            </label>
            <input
              name='investigation'
              placeholder='Investigation/Procedure'
              className='flex-1 px-3 py-1 border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.investigation}
            />
            {/* Save Button */}
            <div className='flex justify-end'>
              <button
                type='submit'
                className='bg-blue-600 hover:bg-blue-700 text-white bg-[#116aef] px-4 py-1 rounded-md flex items-center gap-1'
              >
                <Icon
                  icon='fa6-solid:floppy-disk'
                  width='18'
                  height='18'
                  color='white'
                />
                Save
              </button>
            </div>
          </div>
          {formik.touched.investigation && formik.errors.investigation && (
            <p className='text-xs text-red-500'>
              {formik.errors.investigation}
            </p>
          )}
        </div>
      </form>
      <MasterTable
        color='bg-[#b3b3b3]'
        textcolor='text-[#000000]/100'
        columns={SampleTableData.columns}
        rows={SampleTableData.rows}
        loading={false}
      />
    </div>
  );
};

export default Investigation;
