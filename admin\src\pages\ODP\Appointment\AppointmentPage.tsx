import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import DatePicker from "../../../components/DatePicker";
import Header from "../../../components/Header";
import { Status } from "../../../components/Status";
import MasterTable from "../../../layouts/Table/MasterTable";
import { TableAction } from "../../../layouts/Table/TableAction";
import { FrontendRoutes } from "../../../routes";
import { CustomTabs } from "../../../components/CustomTab";
import AppointmentCardView from "./Components/ViewAppointment";
import AppointmentSummaryCards, {
  generateAppointmentCardData,
} from "./Components/AppointmentSummaryCards";
import {
  useDeleteAppointmentByQuery,
  useGetAllAppointments,
} from "../../../server-action/api/appointmentApi";
import { userRole } from "../../../constant/constant";
import { useGetUser } from "../../../server-action/api/user";
import { get } from "lodash";
import dayjs from "dayjs";
import TransferPatient from "../../IPD/TransferPatient";
import ExportActions from "../../../components/ExportActions";
import { useExportUtils } from "../../../utils/export.utils";
import RenderPrintableData from "../../../components/RenderPrinatableData";

export interface ISampleData {
  patientName: string;
  date: string;
  treatment: string;
  doctorName: string;
  consultationFee: string;
  status: string;
  remark: string;
  phoneNumber: string;
  dob: string;
  gender: string;
  bloodGroup: string;
  address: string;
}

export const AppointmentPage = () => {
  const navigate = useNavigate();
  const [transferHospital, setTransferHospital] = useState({
    state: false,
    patientId: "",
  });
  const [date, setDate] = useState("");
  const [tabValue, setTabValue] = useState("All");
  const { contentRef, exportToPDF, exportToExcel, handlePrint } =
    useExportUtils();
  // Get today's date in YYYY-MM-DD format
  const getTodayDate = () => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  };
  const [openView, setOpenView] = useState(false);
  const [selectedAppointment, setSelectedAppointment] =
    useState<ISampleData | null>(null);
  const [searchText, setSearchText] = useState<string>("");
  const [pagination, setPagination] = useState<{ page: number; limit: number }>(
    {
      page: 1,
      limit: 20,
    }
  );
  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return d.toISOString().split("T")[0];
  };

  const { data: userDoctorDetails } = useGetUser(
    { role: userRole.DOCTOR },
    {
      enabled: openView === true,
      refetchOnMount: true,
      refetchOnWindowFocus: true,
    }
  ) as any;

  const getTabCondition = () => {
    switch (tabValue.toLowerCase()) {
      case "new":
        return "PRESENT";
      case "today":
        return "PRESENT";
      case "history":
        return "PAST";
      case "upcoming":
        return date ? undefined : "FUTURE";
      default:
        return undefined;
    }
  };

  // Handle tab change and set appropriate date
  const handleTabChange = (tab: string) => {
    // Only reset date if we're actually changing tabs
    if (tabValue !== tab) {
      setTabValue(tab);

      switch (tab.toLowerCase()) {
        case "today":
          setDate(getTodayDate());
          break;
        case "upcoming":
        case "history":
        case "all":
          setDate("");
          break;
        default:
          setDate("");
          break;
      }
    }
  };

  // Get date picker constraints based on selected tab
  const getDatePickerProps = () => {
    const today = getTodayDate();
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowDate = tomorrow.toISOString().split("T")[0];

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayDate = yesterday.toISOString().split("T")[0];

    switch (tabValue.toLowerCase()) {
      case "today":
        return {
          disabled: true,
          value: today,
        };
      case "upcoming":
        return {
          disabled: false,
          min: tomorrowDate, // Tomorrow onwards only
        };
      case "history":
        return {
          disabled: false,
          max: yesterdayDate, // Yesterday and previous dates only
        };
      default:
        return {
          disabled: false,
        };
    }
  };

  const apiParams = {
    page: pagination.page,
    limit: pagination.limit,
    search: searchText || undefined,
    date: date ? formatDate(date) : undefined,
    aCondition: getTabCondition(),
  };

  console.log("API Params:", {
    tabValue,
    selectedDate: date,
    formattedDate: date ? formatDate(date) : undefined,
    aCondition: getTabCondition(),
  });

  const { data: appointmentData, isLoading } = useGetAllAppointments(
    apiParams
  ) as any;

  const appointments = get(appointmentData, "data.appointments", []);
  const totalPages = get(appointmentData, "data.pagination.pages", 1);

  const { mutateAsync: deleteAppointment } = useDeleteAppointmentByQuery();

  const calculateAge = (dob: string): string => {
    const birthDate = new Date(dob);
    const ageDifMs = Date.now() - birthDate.getTime();
    const ageDate = new Date(ageDifMs);
    return Math.abs(ageDate.getUTCFullYear() - 1970).toString();
  };

  const mapToSampleData = (appointment: any): ISampleData => {
    const patient = get(appointment, "user.commonInfo", {});
    const doctor = get(appointment, "doctor", {});
    const doctorDetails = get(userDoctorDetails, "data.users", []).find(
      (user: any) => user._id === doctor._id
    );
    const consultationFee = get(
      doctorDetails,
      "professionalDetails.consultationFee",
      "N/A"
    );

    return {
      patientName: get(patient, "personalInfo.fullName", "N/A"),
      date: dayjs(get(appointment, "date", "")).format("MMM-DD-YYYY"),
      treatment: get(appointment, "treatment", "N/A"),
      doctorName: get(doctor, "commonInfo.personalInfo.fullName", "N/A"),
      consultationFee: consultationFee,
      status: get(appointment, "status", "N/A"),
      remark: get(appointment, "remark", "N/A"),
      phoneNumber: get(patient, "contactInfo.phone.primaryPhone", "N/A"),
      dob: get(patient, "personalInfo.dob")
        ? calculateAge(get(patient, "personalInfo.dob"))
        : "N/A",
      gender: get(patient, "personalInfo.gender", "N/A"),
      bloodGroup: get(patient, "personalInfo.bloodGroup", "N/A"),
      address: get(patient, "contactInfo.address.currentAddress", "N/A"),
    };
  };

  const tableData = {
    columns: [
      { title: "Patient Id", key: "patientId" },
      { title: "Patient Name", key: "patientName" },
      { title: "Appointment Date", key: "date" },
      { title: "Doctor Name", key: "doctorName" },
      { title: "Status", key: "status" },
      { title: "Action", key: "action" },
    ],
    rows: appointments.map((appointment: any, index: number) => ({
      key: index,
      patientId: get(appointment, "user.patientInfo.patientId", "N/A"),
      patientName: get(
        appointment,
        "user.commonInfo.personalInfo.fullName",
        "N/A"
      ),
      date: dayjs(get(appointment, "date")).format("MMMM-DD-YYYY") || "N/A",
      doctorName: get(
        appointment,
        "doctor.commonInfo.personalInfo.fullName",
        "N/A"
      ),
      status: <Status status={appointment.status} />,
      action: (
        <TableAction
          onEdit={() => {
            navigate(`/appointment/add-appointment/${appointment._id}`);
          }}
          onMore={() => {}}
          onMoreList={[
            {
              title: "Transfer Hospital",
              onClick: () =>
                setTransferHospital({
                  state: true,
                  patientId: appointment._id,
                }),
              index: 4,
            },
          ]}
          onDelete={async () => {
            await deleteAppointment({
              id: JSON.stringify([appointment._id]),
            });
          }}
          onShow={() => {
            setSelectedAppointment(mapToSampleData(appointment));
            setOpenView(true);
          }}
        />
      ),
    })),
  };

  useEffect(() => {
    // Reset to first page when tab, search, or date changes
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, [tabValue, searchText, date]);
  console.log("sldkfsdlfk");

  return (
    <>
      <Header
        title="Appointment"
        onSearch={(value: string) => {
          setSearchText(value);
        }}
        onAddClick={() => {
          navigate(FrontendRoutes.ADDAPPOINTMENT);
        }}
        listTitle="Appointment List"
        FilterSection={() => {
          const dateProps = getDatePickerProps();
          return (
            <div className="flex gap-4">
              <div>
                <DatePicker
                  date={date}
                  setDate={setDate}
                  disabled={dateProps.disabled}
                  min={dateProps.min}
                  max={dateProps.max}
                />
              </div>
            </div>
          );
        }}
      >
        <ExportActions
          className="col-span-2"
          onExportExcel={() => exportToExcel(tableData.rows, "report.xlsx")}
          onExportPDF={() => exportToPDF(contentRef.current!, "report.pdf")}
          onPrint={handlePrint}
        />
      </Header>

      {/* Summary Cards */}
      <div className="mt-0.5 mb-0.5 ">
        <AppointmentSummaryCards
          appointmentsData={appointmentData}
          isLoading={isLoading}
          tabValue={tabValue}
          selectedDate={date}
        />
      </div>

      <CustomTabs
        tabs={["All", "Today", "UpComing", "History"]}
        defaultTab={tabValue}
        onTabChange={handleTabChange}
      />
      <MasterTable
        columns={tableData.columns}
        rows={tableData.rows}
        loading={isLoading}
        pagination={{
          currentPage: pagination.page,
          totalPage: totalPages,
          limit: pagination.limit,
          onClick: (params: { page?: number; limit?: number }) => {
            setPagination((prev) => ({
              page: params.page ?? prev.page,
              limit: params.limit ?? prev.limit,
            }));
          },
        }}
      />

      <RenderPrintableData
        ref={contentRef}
        tableData={tableData}
        cardData={generateAppointmentCardData(appointmentData, tabValue, date)}
      />
      {selectedAppointment && (
        <AppointmentCardView
          data={selectedAppointment}
          setOpen={setOpenView}
          open={openView}
        />
      )}
      <TransferPatient
        isOpen={transferHospital.state}
        onClose={() => {
          setTransferHospital({ ...transferHospital, state: false });
        }}
        patientId={transferHospital.patientId}
      />
    </>
  );
};
