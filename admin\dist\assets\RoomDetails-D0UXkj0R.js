import{ad as R,a5 as o,bz as F,aM as P,ay as T,a2 as e,af as m,ag as f,ah as I,a7 as O,ab as w,aj as U}from"./index-ClX9RVH0.js";const Q=()=>{var S,h,x;const v=R(),[M,G]=o.useState(1),[i,j]=o.useState(""),[c,k]=o.useState("All"),[u,B]=o.useState("All"),[l,p]=o.useState({limit:5,page:1}),{mutateAsync:E}=F(),L=P({search:i,...i?{}:{page:l.page,limit:l.limit}}),{data:t}=T(L),g=((S=t==null?void 0:t.data)==null?void 0:S.rooms)||[],b=g==null?void 0:g.filter(a=>{var s,n,N,D;const d=(s=a==null?void 0:a.roomNo)==null?void 0:s.toString().toLowerCase().includes(i.toLowerCase()),y=c==="All"||(c==="Available"?(n=a==null?void 0:a.beds)==null?void 0:n.some(A=>A.status==="AVAILABLE"):!((N=a==null?void 0:a.beds)!=null&&N.some(A=>A.status==="AVAILABLE"))),r=u==="All"||(((D=a==null?void 0:a.categoryName)==null?void 0:D.categoryName)||"").toLowerCase().includes(u.toLowerCase());return d&&y&&r}),C={columns:[{title:"Room No",key:"roomNo"},{title:"Ward Type",key:"wardType"},{title:"Floor",key:"floor"},{title:"Bed Capacity",key:"bedCapacity"},{title:"Available Beds",key:"availableBeds"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:b==null?void 0:b.map((a,d)=>{var r,s;const y=(r=a==null?void 0:a.beds)==null?void 0:r.some(n=>n.status==="AVAILABLE");return{key:d,roomNo:a.roomNo||"-",wardType:((s=a.categoryName)==null?void 0:s.categoryName)||"-",floor:a.floor??"-",bedCapacity:a.bedCapacity??"-",availableBeds:a.availableBed??"-",status:e.jsx(I,{status:y?"Available":"Unavailable"}),action:e.jsx(m,{onEdit:()=>{v(`${f.BEDADDROOMS}/${a._id}`)},onDelete:()=>{E({id:JSON.stringify([a==null?void 0:a._id])})}})}})};return e.jsxs("div",{className:"space-y-4",children:[e.jsx(O,{title:"Room",onSearch:a=>{j(a)},onAddClick:()=>{v(f.BEDADDROOMS)},listTitle:"Rooms Detail",FilterSection:()=>e.jsxs("div",{className:"flex min-w-96 gap-4",children:[e.jsx(w,{label:"",value:u,options:[{value:"All",label:"All Categories"},{value:"General",label:"General"},{value:"ICU",label:"ICU"},{value:"Emergency",label:"Emergency"}],onChange:a=>{B(a.target.value)}}),e.jsx(w,{label:"",value:c,options:[{value:"All",label:"All Status"},{value:"Available",label:"Available"},{value:"Unavailable",label:"Unavailable"}],onChange:a=>{k(a.target.value)}})]})}),e.jsx(U,{columns:C.columns,rows:C.rows,loading:!1,pagination:{currentPage:l.page,totalPage:(x=(h=t==null?void 0:t.data)==null?void 0:h.pagination)==null?void 0:x.pages,limit:l.limit,onClick:a=>{a.page&&p({...l,page:a.page}),a.limit&&p({...l,limit:a.limit})}}})]})};export{Q as default};
