import { useState } from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { StatusBadge } from "../../../../../../../components/StatusBadge";
const ChronicConditionsData = {
  columns: [
    { title: "S.N", key: "sn" },
    { title: "Condition", key: "condition" },
    { title: "Diagnosis Date", key: "diagnosisDate" },
    { title: "Status", key: "status" },
    { title: "Treatment", key: "treatment" },
    { title: "Doctor/Dept", key: "doctorDept" },
    { title: "Remarks", key: "remarks" },
  ],
  rows: [
    {
      sn: 1,
      condition: "Diabetes Mellitus",
      diagnosisDate: "12/12/24",
      status: <StatusBadge statusText='Active' />,
      treatment: "Metformin 500mg",
      doctorDept: "Endocrinology",
      remarks: "Blood sugar under control",
    },
    {
      sn: 2,
      condition: "Hypertension",
      diagnosisDate: "10/11/23",
      status: <StatusBadge statusText='Inactive' />,
      treatment: "Amlodipine 5mg",
      doctorDept: "Cardiology",
      remarks: "BP normal in last checkup",
    },
    {
      sn: 3,
      condition: "Asthma",
      diagnosisDate: "05/06/22",
      status: <StatusBadge statusText='Critical' />,
      treatment: "Inhaler prescribed",
      doctorDept: "Pulmonology",
      remarks: "Frequent attacks during winter",
    },
    {
      sn: 4,
      condition: "Chronic Kidney Disease",
      diagnosisDate: "18/03/21",
      status: <StatusBadge statusText='Resolved' />,
      treatment: "Dialysis done",
      doctorDept: "Nephrology",
      remarks: "No further treatment needed",
    },
    {
      sn: 5,
      condition: "Thyroid Disorder",
      diagnosisDate: "22/09/20",
      status: <StatusBadge statusText='Inactive' />,
      treatment: "Levothyroxine",
      doctorDept: "Endocrinology",
      remarks: "Dosage reduced, stable",
    },
  ],
};

const ChronicConditions = () => {
  const [statusFilter, setStatusFilter] = useState("");
  const [dateFilter, setDateFilter] = useState("");
  return (
    <div className='w-full p-4 mx-auto'>
      <h2 className='mb-4 text-lg font-semibold text-center'>
        Chronic Conditions
      </h2>

      {/* Filters */}
      <div className='flex justify-end gap-2 mb-2'>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className='w-full max-w-[100px] border border-gray-300 p-1 rounded focus:outline-none'
        >
          <option value=''>Status</option>
          <option value='Active'>Active</option>
          <option value='Inactive'>Inactive</option>
          <option value='Critical'>Critical</option>
          <option value='Resolved'>Resolved</option>
        </select>

        <input
          type='date'
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className='w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none'
        />
      </div>

      {/* Medication Table */}
      <div className='overflow-x-auto'>
        <MasterTable
          columns={ChronicConditionsData.columns}
          rows={ChronicConditionsData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default ChronicConditions;
