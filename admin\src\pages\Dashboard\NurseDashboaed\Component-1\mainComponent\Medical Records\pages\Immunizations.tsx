import { useState } from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { ImmunizationData } from "../../Consultations/pages/sampleMasterTableData";
import { Icon } from "@iconify/react/dist/iconify.js";

const Immunizations = () => {
  const [dateFilter, setDateFilter] = useState("");

  return (
    <div className='w-full p-4 mx-auto'>
      <h2 className='mb-4 text-lg font-semibold text-center'>Immunizations</h2>

      {/* Search and Date Filter */}
      <div className='flex items-center justify-end gap-2 mb-2'>
        <div className='relative'>
          <input
            type='text'
            placeholder='Search immunization'
            className='px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none'
          />

          <Icon
            icon='majesticons:search-line'
            width={20}
            height={20}
            className='absolute text-base text-gray-500 -translate-y-1/2 right-2 top-1/2'
            onClick={() => {
              console.log("Search");
            }}
          />
        </div>

        <input
          type='date'
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className='w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none'
        />
      </div>

      {/* Immunizations Table */}
      <div className='overflow-x-auto'>
        <MasterTable
          columns={ImmunizationData.columns}
          rows={ImmunizationData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default Immunizations;
