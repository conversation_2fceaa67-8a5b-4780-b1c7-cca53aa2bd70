import{a1 as p,aV as g,aX as c,a2 as e,a9 as x,aa as f,c7 as u,ao as y,a5 as n,aP as b,a7 as P,aj as j,aR as h,a4 as k}from"./index-ClX9RVH0.js";const N=({editData:r,onClose:t})=>{const l=[{type:"select",field:"productCategory",label:"Product Category",required:!0}],i=[{type:"text",field:"productName",label:"Product Name",required:!0},{type:"text",field:"unit",label:"Unit",required:!0},{type:"text",field:"usages",label:"Usages"}],a=p({initialValues:{productCategory:(r==null?void 0:r.category)??"",productName:(r==null?void 0:r.name)??"",unit:(r==null?void 0:r.unit)??""},validationSchema:g({categoryName:c().required("Ward name is required"),department:c().required("Department is required"),headOfWard:c().required("Head of ward is required")}),onSubmit:async m=>{console.log(m)}}),{handleSubmit:s,errors:o,touched:d}=a;return e.jsx("div",{className:"p-4",children:e.jsx(x,{value:a,children:e.jsxs(f,{onSubmit:s,className:"flex flex-col gap-5 w-full ",children:[e.jsx("section",{className:"grid grid-cols-2  place-items-center w-full   gap-5",children:e.jsx(u,{formDatails:l,getFieldProps:a.getFieldProps,errors:o,touched:d})}),e.jsx("section",{className:"grid grid-cols-2  place-items-center w-full border rounded-sm p-5  gap-2",children:e.jsx(u,{formDatails:i,getFieldProps:a.getFieldProps,errors:o,touched:d})}),e.jsx("div",{className:"col-span-3",children:e.jsx(y,{onCancel:()=>{a.resetForm(),t==null||t()},submitLabel:"Save",submitlabelButton:!0,onSubmit:s})})]})})})},F=()=>{const[r,t]=n.useState(!1),[l,i]=n.useState(),a=b(()=>t(!1)),s={columns:[{title:"Product ID",key:"productId"},{title:"Product Name",key:"productName"},{title:"Category",key:"category"},{title:"Available Stock",key:"availableStock"},{title:"Unit",key:"unit"},{title:"Supplier Name",key:"supplier"}],rows:[]};return e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsx(P,{listTitle:"Product List",onSearch:o=>{console.log(o)},title:"Product",onAddClick:()=>{i(void 0),t(!0)}}),e.jsx(j,{columns:s.columns,rows:s.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}}),r&&e.jsxs(h,{ref:a,classname:"p-5",children:[e.jsx(k,{as:"h3",size:"body-lg-lg",variant:"primary-blue",className:"mt-6 px-2 text-primary",children:l?"Edit Product":"Add Product"}),e.jsx(N,{editData:l,onClose:()=>t(!1)})]})]})};export{F as CanteenProductList};
