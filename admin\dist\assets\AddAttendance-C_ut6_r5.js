import{ba as de,a5 as i,at as ie,br as pe,a1 as me,aV as ce,aX as d,aU as oe,bs as be,aw as ve,a2 as t,a7 as De,am as he,a9 as xe,aa as Se,ab as m,ac as f,ao as je}from"./index-ClX9RVH0.js";import{b as fe,c as ye,d as Ce}from"./attendance.api-CVfqyNC8.js";const Ee=()=>{var w,T,V,E,I,_,g,k,G,A,H,L;const{id:c}=de(),[B,R]=i.useState(1),{data:s}=fe(c||""),{data:n}=ie((s==null?void 0:s.user)||""),U=(T=(w=n==null?void 0:n.departmentDetails)==null?void 0:w.hirachyFirst)==null?void 0:T._id,$=(E=(V=n==null?void 0:n.departmentDetails)==null?void 0:V.department)==null?void 0:E._id,z=(_=(I=n==null?void 0:n.departmentDetails)==null?void 0:I.speciality)==null?void 0:_._id,Q=n==null?void 0:n._id,X=s==null?void 0:s.status,J=ye(),K=Ce(),{data:b}=pe(),v=(g=b==null?void 0:b.data)==null?void 0:g.departmentCategory,M=v==null?void 0:v.map(e=>({label:e==null?void 0:e.name,value:e==null?void 0:e._id})),[D,y]=i.useState(""),[O,o]=i.useState(""),[C,W]=i.useState(""),[Fe,F]=i.useState(""),[Ne,N]=i.useState(""),a=me({initialValues:{department:(s==null?void 0:s.department)||U||"",subDepartment:(s==null?void 0:s.subDepartment)||$||"",departmentSubDepartment:z||"",user:(s==null?void 0:s.user)||Q||"",date:(s==null?void 0:s.date)||"",entryTime:(s==null?void 0:s.entryTime)||"",exitTime:(s==null?void 0:s.exitTime)||"",status:X||""},enableReinitialize:!0,validationSchema:ce().shape({department:d().required("Department is required"),subDepartment:d().required("Sub Department is required"),departmentSubDepartment:d().required("Department sub department is required"),user:d().required("User is required"),date:d().required("Date is required"),entryTime:d(),exitTime:d(),status:d().required(" Status is required")}),onSubmit:async e=>{c?await K.mutateAsync({_id:c,entityData:e}):await J.mutateAsync(e),history.back()}});i.useEffect(()=>{a.values.department&&y(a.values.department),a.values.subDepartment&&o(a.values.subDepartment),a.values.user&&F(a.values.user),a.values.date&&N(a.values.date),a.values.departmentSubDepartment&&W(a.values.departmentSubDepartment)},[a.values.department,a.values.subDepartment,a.values.user,a.values.date,a.values.departmentSubDepartment,a.values.status]);const{data:h}=oe({upperHirachy:D}),Y=((G=(k=h==null?void 0:h.data)==null?void 0:k.departments)==null?void 0:G.map(e=>({label:e==null?void 0:e.name,value:e==null?void 0:e._id})))||[],{data:x}=be({upperHirachy:O}),Z=((H=(A=x==null?void 0:x.data)==null?void 0:A.departmentSubCat)==null?void 0:H.map(e=>({label:e==null?void 0:e.name,value:e==null?void 0:e._id})))||[];let S={};C?S={"departmentDetails.speciality":C}:D&&(S={"departmentDetails.hirachyFirst":D});const{data:j}=ve(S),ee=(((L=j==null?void 0:j.data)==null?void 0:L.users)||[]).map(e=>{var r,P;return{label:(P=(r=e==null?void 0:e.commonInfo)==null?void 0:r.personalInfo)==null?void 0:P.fullName,value:e==null?void 0:e._id}}),te=e=>{const r=e.target.value;y(r),a.setFieldValue("department",r),a.setFieldValue("subDepartment",""),a.setFieldValue("setDepartmentSubDepartment",""),a.setFieldValue("user",""),a.setFieldValue("status","")},ae=e=>{const r=e.target.value;o(r),a.setFieldValue("subDepartment",r),a.setFieldValue("departmentSubDepartment","")},se=e=>{const r=e.target.value;o(r),a.setFieldValue("departmentSubDepartment",r),a.setFieldValue("user","")},re=e=>{const r=e.target.value;o(r),a.setFieldValue("status",r)},ne=e=>{const r=e.target.value;F(r),a.setFieldValue("user",r)},le=e=>{const r=e.target.value;N(r),a.setFieldValue("date",r)},{handleSubmit:q,errors:l,touched:p,values:u}=a,ue=[{label:"Present",value:"PRESENT"},{label:"Absent",value:"ABSENT"}].map(e=>({label:e==null?void 0:e.label,value:e==null?void 0:e.value}));return t.jsxs("div",{children:[t.jsx(De,{listTitle:c?"Edit Attendance":"Add Attendance",hideHeader:!0}),t.jsxs("div",{className:"relative flex w-full gap-6",children:[t.jsx("div",{className:"w-auto h-full",children:t.jsx(he,{steps:[{step:1,title:"Basic Information",isActive:B===1}]})}),t.jsx("div",{className:"w-full",children:t.jsx(xe,{value:a,children:t.jsx(Se,{onSubmit:q,children:t.jsxs("div",{className:"flex flex-col w-full gap-5 pb-4",children:[t.jsx("div",{className:"flex flex-col gap-4 p-4 bg-white rounded-sm",onClick:()=>R(1),children:t.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[t.jsxs("div",{children:[t.jsx(m,{label:"Department",required:!0,options:M||[],firstInput:"Select Department",name:"department",value:u.department,onChange:te}),l.department&&p.department&&t.jsx("div",{className:"text-red text-sm my-2",children:l.department})]}),t.jsxs("div",{children:[t.jsx(m,{label:"Sub Department",options:Y,firstInput:"Select Sub Department",name:"subDepartment",value:u.subDepartment,onChange:ae}),l.subDepartment&&p.subDepartment&&t.jsx("div",{className:"text-red text-sm my-2",children:l.subDepartment})]}),t.jsxs("div",{children:[t.jsx(m,{label:"Department Subcategory",options:Z,firstInput:"Select Department Sub Department",name:"departmentSubDepartment",value:u.departmentSubDepartment,onChange:se}),l.departmentSubDepartment&&p.departmentSubDepartment&&t.jsx("div",{className:"text-red text-sm my-2",children:l.departmentSubDepartment})]}),t.jsxs("div",{children:[t.jsx(m,{required:!0,label:"Name",options:ee,firstInput:"Select User Name",name:"user",value:u.user,onChange:ne}),l.user&&p.user&&t.jsx("div",{className:"text-red text-sm my-2",children:l.user})]}),t.jsxs("div",{children:[t.jsx(f,{required:!0,label:"Date",type:"date",name:"date",value:u.date,onChange:le}),l.date&&p.date&&t.jsx("div",{className:"text-red text-sm my-2",children:l.date})]}),t.jsx("div",{children:t.jsx(f,{label:"Entry Time",type:"time",name:"entryTime",value:u.entryTime,onChange:a.handleChange})}),t.jsx("div",{children:t.jsx(f,{label:"Exit Time",type:"time",name:"exitTime",value:u.exitTime,onChange:a.handleChange})}),t.jsxs("div",{children:[t.jsx(m,{required:!0,label:"Status",options:ue,firstInput:"Select Status",name:"status",value:u.status,onChange:re}),l.status&&p.status&&t.jsx("div",{className:"text-red text-sm my-2",children:l.status})]})]})}),t.jsx(je,{onCancel:()=>history.back(),onSubmit:q})]})})})})]})]})};export{Ee as AddAttendance};
