import { useState } from "react";
import Medicalsilders from "../Consultations/component/Medicalsilders";
import VitalSigns from "./pages/VitalSigns";
import MedicalAdministration from "./pages/MedicalAdministration";
import Allergies from "./pages/Allergies";
import Immunizations from "./pages/Immunizations";
import ChronicConditions from "./pages/ChronicConditions";
import SurgicalHistory from "./pages/SurgicalHistory";
import FamilyHistory from "./pages/FamilyHistory";
import SocialHistory from "./pages/SocialHistory";
import VisitHistory from "./pages/VisitHistory";
import { ClientHistory } from "../../../../../../server-action/api/patienthistory.api";

const medicalRecordTab = [
  "Vital signs",
  "Medical Administration",
  "Allergies",
  "Immunizations",
  "Chronic Conditions",
  "Surgical History",
  "Family History",
  "Social History",
  "Visit History",
];
interface proptype {
  data: any;
}
const MedicalRecordSideBar: React.FC<proptype> = ({ data }) => {
  const [activeSection, setActiveSection] = useState("Vital signs");

  const renderContent = () => {
    switch (activeSection) {
      case "Vital signs":
        return (
          <div>
            <VitalSigns data={data} />
          </div>
        );
      case "Medical Administration":
        return (
          <div>
            <MedicalAdministration data={data} />
          </div>
        );
      case "Allergies":
        return (
          <div>
            <Allergies data={data} />
          </div>
        );

      case "Immunizations":
        return (
          <div>
            <Immunizations />
          </div>
        );
      case "Chronic Conditions":
        return (
          <div>
            <ChronicConditions />
          </div>
        );

      case "Surgical History":
        return (
          <div>
            <SurgicalHistory />
          </div>
        );
      case "Family History":
        return (
          <div>
            <FamilyHistory />
          </div>
        );
      case "Social History":
        return (
          <div>
            <SocialHistory />
          </div>
        );
      case "Visit History":
        return (
          <div>
            <VisitHistory />
          </div>
        );
    }
  };

  return (
    <div className="flex h-screen bg-[#F8F8F8]">
      <Medicalsilders
        tabs={medicalRecordTab}
        defaultTab="Vital signs"
        onTabChange={setActiveSection}
      />
      <div className="flex-1 overflow-auto">{renderContent()}</div>
    </div>
  );
};

export default MedicalRecordSideBar;
