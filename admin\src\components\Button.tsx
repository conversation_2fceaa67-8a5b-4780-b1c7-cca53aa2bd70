interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline";
  title?: string;
  className?: string;
  size?: "xs" | "sm" | "md" | "lg";
  onClick?: () => void;
  children?: React.ReactNode;
}

const Button = ({
  variant = "primary",
  title,
  className,
  onClick,
  size = "md",
  children,
  ...props
}: ButtonProps) => {
  const sizes = {
    xs: "px-3 py-1.5 text-sm",
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base ",
    lg: "px-6 py-3 text-lg",
  };

  const variants = {
    primary: "bg-primary text-white hover:bg-light_primary",
    secondary: "bg-gray-200 text-gray-700 hover:bg-gray-300",
    outline: "border border-primary text-primary hover:bg-primary/10",
  };

  return (
    <button
      className={`
              ${variants[variant]}
              ${sizes[size]}
              ${className || ""}
              rounded-md transition inline-flex items-center justify-center
            `}
      onClick={onClick}
      {...props}
    >
      {children || title || "Add"}
    </button>
  );
};

export default Button;
