import Header from "../../../components/Header";
import HeadNurseCard from "./components/HeadNurseCard";

import HeadNurseTestRequestTable from "./components/HeadNurseTestRequestTable";

import ShiftCalendar from "./components/ShiftCalendar";
import NurseList from "./components/NurseList";
// import RecentPatientList from "./components/RecentPatientList";

const HeadNurseDashboard = () => {
  return (
    <div>
      <Header hideHeader={true} listTitle="Head Nurse Dashboard" />

      <section className="grid-cols-1 lg:grid-cols-6 gap-4">
        <HeadNurseCard />
      </section>

      <div className="flex flex-wrap w-full max-w-full min-w-0 gap-2 overflow-hidden">
        <section className="flex-1 min-w-0 space-y-2">
          <NurseList />
          <HeadNurseTestRequestTable />
        </section>

        <section className="w-full lg:w-[35%] min-w-0 space-y-2 ">
          <ShiftCalendar />
        </section>
      </div>
    </div>
  );
};

export default HeadNurseDashboard;
