import{a1 as u,al as x,a2 as e,a7 as b,am as y,a9 as h,aa as g,an as n,ac as a,ab as i,bf as d,ao as v}from"./index-ClX9RVH0.js";function f(){const p=[{step:1,title:"Symptoms",isActive:!0},{step:2,title:"Medical History",isActive:!1},{step:3,title:"Test Request",isActive:!1},{step:4,title:"Prescription",isActive:!1},{step:5,title:"Refers",isActive:!1},{step:6,title:"Advices",isActive:!1}],o=u({initialValues:{referForEmergency1:"No",referForEmergency2:"No",referForSurgery:"No",symptoms:[{symptom:"",details:""}],appointments:[{department:"",testType:"",requestedDate:"",priority:""}],prescription:[{medicineName:"",dose:"",frequence:"",duration:"",condition:"",prescriptionNote:""}]},enableReinitialize:!0,onSubmit:s=>{x.success("Form submitted successfully!"),history.back(),console.log(s)}}),{handleSubmit:c,values:t}=o;return e.jsxs("div",{children:[e.jsx(b,{listTitle:"General Physician",hideHeader:!0}),e.jsxs("div",{className:"flex w-full gap-10",children:[e.jsx("div",{className:"h-auto w-auto",children:e.jsx(y,{steps:p})}),e.jsx(h,{value:o,children:e.jsx(g,{onSubmit:c,children:e.jsxs("div",{className:"flex flex-col gap-8 w-full pb-4",children:[e.jsx("div",{className:"bg-white p-4 rounded-sm",children:e.jsx(n,{name:"symptoms",children:({push:s,remove:r})=>e.jsx("div",{children:t.symptoms.map((m,l)=>e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-5",children:[e.jsx(a,{label:"Symptom",name:`symptoms.${l}.symptom`,type:"text",placeholder:"Enter symptom"}),e.jsx(a,{label:"Details",name:`symptoms.${l}.detais`,type:"text",placeholder:"Details"})]}),e.jsxs("div",{className:"flex justify-center mt-4 gap-4",children:[t.symptoms.length>1&&e.jsxs("button",{type:"button",onClick:()=>r(l),className:"flex items-center px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:[e.jsx("span",{className:"mr-2",children:"−"})," Remove"]}),l===t.symptoms.length-1&&e.jsxs("button",{type:"button",onClick:()=>s({symptom:"",details:""}),className:"flex items-center px-4 py-2 bg-primary text-white rounded-md  transition-colors",children:[e.jsx("span",{className:"mr-2",children:"+"})," Add Symptoms"]})]})]},l))})})}),e.jsx("div",{className:"bg-white p-4 rounded-sm",children:e.jsxs("div",{className:"grid grid-cols-3 gap-5",children:[e.jsx(a,{label:"Known Allergies",type:"text",placeholder:"Enter",name:"Known Allergies"}),e.jsx(a,{label:"Chronic Diseases",type:"text",placeholder:"Enter",name:"allergies"}),e.jsx(a,{label:"Past Surgeries",type:"text",placeholder:"Enter",name:"pastSurgeries"}),e.jsx(a,{label:"Current Medications",type:"text",placeholder:"Enter",name:"currentMedications"})]})}),e.jsx("div",{className:"bg-white p-4 rounded-sm",children:e.jsx(n,{name:"appointments",children:({push:s,remove:r})=>e.jsx("div",{children:t.appointments.map((m,l)=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-3 gap-5",children:[e.jsx(i,{required:!0,label:"Department",options:[{value:"Cardiology",label:"Cardiology"},{value:"Orthopedics",label:"Orthopedics"},{value:"Neurology",label:"Neurology"},{value:"Gastroenterology",label:"Gastroenterology"},{value:"Urology",label:"Urology"},{value:"Pediatrics",label:"Pediatrics"},{value:"Dermatology",label:"Dermatology"},{value:"Endocrinology",label:"Endocrinology"},{value:"Immunology",label:"Immunology"}],name:`appointment.${l}.department`}),e.jsx(i,{required:!0,label:"Test Type",options:[{value:"Blood Test",label:"Blood Test"},{value:"Urinalysis",label:"Urinalysis"},{value:"X-Ray",label:"X-Ray"},{value:"ECG",label:"ECG"},{value:"MRI",label:"MRI"}],name:`appointment.${l}.testType`}),e.jsx(a,{label:"Requested Date",type:"date",placeholder:"Enter",name:`appointment.${l}.requestedDate`}),e.jsx(i,{required:!0,label:"Priority",options:[{value:"High",label:"High"},{value:"Medium",label:"Medium"},{value:"Low",label:"Low"}],name:`appointment.${l}.priority`})]}),e.jsxs("div",{className:"flex justify-center mt-4 gap-4",children:[t.appointments.length>1&&e.jsxs("button",{type:"button",onClick:()=>r(l),className:"flex items-center px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:[e.jsx("span",{className:"mr-2",children:"−"})," Remove"]}),l===t.appointments.length-1&&e.jsxs("button",{type:"button",onClick:()=>s({department:"",testType:"",requestedDate:"",priority:""}),className:"flex items-center px-4 py-2 bg-primary text-white rounded-md  transition-colors",children:[e.jsx("span",{className:"mr-2",children:"+"})," Add Test Type"]})]})]}))})})}),e.jsx("div",{className:"bg-white p-4 rounded-sm",children:e.jsx(n,{name:"prescription",children:({push:s,remove:r})=>e.jsx("div",{children:t.prescription.map((m,l)=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-3 gap-5",children:[e.jsx(i,{required:!0,label:"Medicine name",options:[{value:"Paracetamol",label:"Paracetamol"},{value:"Aspirin",label:"Aspirin"},{value:"Ibuprofen",label:"Ibuprofen"},{value:"Acetaminophen",label:"Acetaminophen"},{value:"Tylenol",label:"Tylenol"}],name:`prescription${l}.medicineName`}),e.jsx(i,{required:!0,label:"Dose",options:[{value:"1 tablet",label:"1 tablet"},{value:"2 tablets",label:"2 tablets"},{value:"3 tablets",label:"3 tablets"},{value:"1 capsule",label:"1 capsule"},{value:"2 capsules",label:"2 capsules"}],name:`prescription${l}.dose`}),e.jsx(a,{label:"Frequence",type:"text",placeholder:"Enter",name:`prescription${l}.frequence`}),e.jsx(a,{label:"Duration",type:"text",placeholder:"Enter",name:`prescription${l}.duration`}),e.jsx(i,{required:!0,label:"Condition",options:[{value:"Before meals",label:"Before meals"},{value:"After meals",label:"After meals"},{value:"Before bed",label:"Before bed"},{value:"After bed",label:"After bed"},{value:"Everyday",label:"Everyday"}],name:`prescription${l}.condition`}),e.jsx(a,{label:"Prescription Note",type:"text",placeholder:"Enter",name:`prescription${l}.prescriptionNote`})]}),e.jsxs("div",{className:"flex justify-center mt-4 gap-4",children:[t.prescription.length>1&&e.jsxs("button",{type:"button",onClick:()=>r(l),className:"flex items-center px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors",children:[e.jsx("span",{className:"mr-2",children:"−"})," Remove"]}),l===t.prescription.length-1&&e.jsxs("button",{type:"button",onClick:()=>s({medicineName:"",dose:"",frequence:"",duration:"",condition:"",prescriptionNote:""}),className:"flex items-center px-4 py-2 bg-primary text-white rounded-md  transition-colors",children:[e.jsx("span",{className:"mr-2",children:"+"})," Add Medicine"]})]})]}))})})}),e.jsxs("div",{className:"bg-white p-4 rounded-sm flex flex-col gap-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-5",children:[e.jsx(a,{label:"Refer to Department",type:"text",placeholder:"Enter",name:"Known Allergies"}),e.jsx(i,{required:!0,label:"Doctor Name",options:[{value:"Dr. John Doe",label:"Dr. John Doe"},{value:"Dr. Jane Doe",label:"Dr. Jane Doe"},{value:"Dr. Alex Doe",label:"Dr. Alex Doe"},{value:"Dr. Sarah Doe",label:"Dr. Sarah Doe"}],name:"refersDoctor"})]}),e.jsx("div",{className:"grid grid-cols-3 gap-5",children:[{label:"Refer for Emergency",name:"referForEmergency1"},{label:"Refer for Emergency",name:"referForEmergency2"},{label:"Refer for Surgery",name:"referForSurgery"}].map((s,r)=>e.jsxs("div",{className:"flex flex-col items-start",children:[e.jsx("label",{className:"font-medium",children:s.label}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsxs("label",{className:"flex items-center gap-1",children:[e.jsx(d,{type:"radio",name:s.name,value:"Yes",className:"form-checkbox accent-primary h-5 w-5 text-red-600"}),"Yes"]}),e.jsxs("label",{className:"flex items-center gap-1",children:[e.jsx(d,{type:"radio",name:s.name,value:"No",className:"form-checkbox accent-primary h-5 w-5 text-red-600 dark:checked:bg-purple-400"}),"No"]})]})]},r))})]}),e.jsxs("div",{className:"bg-white p-4 rounded-sm flex flex-col gap-4",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-5",children:[e.jsx(a,{label:"Lifestyle Recommendations",type:"text",placeholder:"Enter",name:"lifestyleRecommendations"}),e.jsx(a,{label:"Dietary Advice",type:"text",placeholder:"Enter",name:"dietaryAdvice"}),e.jsx(a,{label:"Exercise Suggestions",type:"text",placeholder:"Enter",name:"exerciseSuggestions"}),e.jsx(a,{label:"Precautionary Measures",type:"text",placeholder:"Enter",name:"precautionaryMeasures"})]}),e.jsx("div",{className:"w-full",children:e.jsx(a,{label:"Other Advise",type:"textarea",placeholder:"Enter",name:"otherAdvise"})})]}),e.jsx("div",{className:"bg-white p-4 rounded-sm flex flex-col gap-4",children:e.jsxs("div",{className:"grid grid-cols-2 gap-5",children:[e.jsx(a,{label:"Recommended Follow-Up Date",type:"date",placeholder:"Enter",name:"recommendedFollowUpDate"}),e.jsx(a,{label:"Reason for Revisit",type:"text",placeholder:"Enter",name:"reasonForRevisit"})]})}),e.jsx(v,{onCancel:()=>history.back(),onSubmit:c})]})})})]})]})}export{f as AddPhysicsian};
