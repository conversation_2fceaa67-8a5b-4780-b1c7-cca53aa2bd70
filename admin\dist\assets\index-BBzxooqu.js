import{cB as p,aK as l,av as c,a2 as e,b3 as o,ah as w}from"./index-ClX9RVH0.js";const Y=()=>{const{data:h,isLoading:g,error:f}=p({role:"DOCTOR",date:l().format("YYYY-MM-DD")}),d=c.get(h,"data.shiftassigned",[]),j=l().format("MMMM DD, YYYY"),N=l().format("dddd");if(g)return e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(f)return e.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(o,{icon:"mdi:alert-circle",className:"text-red-500 mr-2"}),e.jsx("span",{className:"text-red-700",children:"Error loading doctor availability data"})]})});const m=s=>{var i,x;const t=(i=s.shiftAssignment)==null?void 0:i.find(n=>n.date===l().format("YYYY-MM-DD"));if(!t||!((x=t.shifts)!=null&&x.length))return{status:"Unavailable",color:"red"};const a=l();return t.shifts.some(n=>{const b=l(`${t.date} ${n.startTime}`),y=l(`${t.date} ${n.endTime}`);return a.isAfter(b)&&a.isBefore(y)})?{status:"Available",color:"green"}:{status:"Off Duty",color:"yellow"}},u=s=>{var a,r;const t=(a=s.shiftAssignment)==null?void 0:a.find(i=>i.date===l().format("YYYY-MM-DD"));return!t||!((r=t.shifts)!=null&&r.length)?"No shift assigned":t.shifts.map(i=>`${i.startTime} - ${i.endTime}`).join(", ")},v=s=>{var a,r,i,x;const t=(a=s.shiftAssignment)==null?void 0:a.find(n=>n.date===l().format("YYYY-MM-DD"));return((x=(i=(r=t==null?void 0:t.shifts)==null?void 0:r[0])==null?void 0:i.shift)==null?void 0:x.shiftType)||"Not assigned"};return e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsx("div",{className:"bg-white rounded-xl p-6 shadow-sm border mb-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"Doctor Availability"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Today's shift details and availability status"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-semibold text-gray-900",children:j}),e.jsx("div",{className:"text-sm text-gray-500",children:N})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[e.jsx("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-green-100 rounded-lg",children:e.jsx(o,{icon:"mdi:doctor",className:"text-green-600 text-2xl"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:d.filter(s=>m(s).status==="Available").length}),e.jsx("div",{className:"text-sm text-gray-500",children:"Available"})]})]})}),e.jsx("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-yellow-100 rounded-lg",children:e.jsx(o,{icon:"mdi:clock-outline",className:"text-yellow-600 text-2xl"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:d.filter(s=>m(s).status==="Off Duty").length}),e.jsx("div",{className:"text-sm text-gray-500",children:"Off Duty"})]})]})}),e.jsx("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-red-100 rounded-lg",children:e.jsx(o,{icon:"mdi:account-off",className:"text-red-600 text-2xl"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:d.filter(s=>m(s).status==="Unavailable").length}),e.jsx("div",{className:"text-sm text-gray-500",children:"Unavailable"})]})]})}),e.jsx("div",{className:"bg-white rounded-xl p-6 shadow-sm border",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"p-3 bg-blue-100 rounded-lg",children:e.jsx(o,{icon:"mdi:account-group",className:"text-blue-600 text-2xl"})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:d.length}),e.jsx("div",{className:"text-sm text-gray-500",children:"Total Doctors"})]})]})})]}),e.jsxs("div",{className:"bg-white rounded-xl shadow-sm border",children:[e.jsx("div",{className:"p-6 border-b border-gray-200",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Today's Doctor Schedule"})}),e.jsx("div",{className:"p-6",children:d.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(o,{icon:"mdi:doctor",className:"text-gray-400 text-6xl mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No doctors scheduled"}),e.jsx("p",{className:"text-gray-500",children:"No doctor shifts are assigned for today."})]}):e.jsx("div",{className:"space-y-4",children:d.map((s,t)=>{const a=m(s),r=u(s),i=v(s);return e.jsxs("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:c.get(s,"user.identityInfo.profileImage")||"/no-user.png",alt:"Doctor",className:"w-12 h-12 rounded-full object-cover"}),e.jsx("div",{className:`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${a.color==="green"?"bg-green-500":a.color==="yellow"?"bg-yellow-500":"bg-red-500"}`})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:c.get(s,"user.commonInfo.personalInfo.fullName","Unknown Doctor")}),e.jsx("p",{className:"text-sm text-gray-500",children:c.get(s,"user.professionalInfo.specialization","General Medicine")}),e.jsxs("p",{className:"text-xs text-gray-400",children:["ID: ",c.get(s,"user.employmentDetails.employeeId","N/A")]})]})]}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:i}),e.jsx("div",{className:"text-xs text-gray-500",children:"Shift Type"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:r}),e.jsx("div",{className:"text-xs text-gray-500",children:"Timing"})]}),e.jsx("div",{className:"text-center",children:e.jsx(w,{status:a.status})})]})]},c.get(s,"_id",t))})})})]})]})};export{Y as DoctorAvailability};
