import MasterTable from '../../../../layouts/Table/MasterTable';

export const VendorProducts = () => {
  const tableData = {
    columns: [
      { title: 'S.No', key: 'sn' },
      { title: 'Product Name', key: 'productName' },
      { title: 'Category', key: 'category' },
      { title: 'Description', key: 'description' },
      { title: 'Unit', key: 'unit' },
      { title: 'Action', key: 'action' },
    ],
  };
  return (
    <div>
      <MasterTable columns={tableData.columns} rows={[]} loading={false} />
    </div>
  );
};
