import{a5 as n,aP as k,ae as C,a2 as e,af as f,ah as v,aV as w,aX as r,aQ as N,aj as A,bA as D,aR as S,bn as T,aa as B,a4 as F,ac as l}from"./index-ClX9RVH0.js";const R=()=>{const[d,o]=n.useState(!1),[c,a]=n.useState(!1),m=k(()=>{a(!1)}),i={columns:[{title:"S.N.",key:"tokenid"},{title:"Bed Category ",key:"patientName"},{title:"Description",key:"doctorName"},{title:"Total Beds",key:"tokenid"},{title:"Occupied",key:"tokenid"},{title:"Available",key:"tokenid"},{title:"Action",key:"action"}],rows:C.map(({tokenId:t,patientName:s,doctorName:y,status:h,treatment:g},j)=>({key:j,tokenid:t,patientName:s,doctorName:y,status:e.jsx(v,{status:h}),treatment:g,action:e.jsx(f,{onEdit:()=>{a(!0)},onDelete:()=>{o(!0)}})}))},u=()=>{a(!0)},x=w().shape({bedCategory:r().required("Bed Category is required"),description:r().required("Description is required")}),p=t=>{console.log("Form Data Submitted:",t),a(!1)},b=()=>e.jsx(T,{initialValues:{bedCategory:"",bedNo:"",description:""},validationSchema:x,onSubmit:p,children:({errors:t,touched:s})=>e.jsx(B,{children:e.jsxs("div",{className:" py-6 mt-2 px-10 relative",children:[e.jsx(F,{as:"h3",text:"Add Bed Category",size:"body-lg-lg",variant:"primary-blue",className:"mb-4 text-primary"}),e.jsx("div",{className:"absolute cursor-pointer hover:text-red top-0 right-2",onClick:()=>a(!1),children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",children:e.jsx("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeWidth:2,d:"m8 8l4 4m0 0l4 4m-4-4l4-4m-4 4l-4 4"})})}),e.jsxs("div",{children:[e.jsx(l,{label:"Bed Category",type:"text",name:"bedCategory",placeholder:"Auto",className:"mb-4 outline-none border p-3 rounded-md"}),t.bedCategory&&s.bedCategory&&e.jsx("div",{className:"text-red text-sm mb-2",children:t.bedCategory})]}),e.jsxs("div",{children:[e.jsx(l,{label:"Description",type:"text",name:"description",placeholder:"Auto",className:"mb-4 outline-none border p-3 rounded-md"}),t.description&&s.description&&e.jsx("div",{className:"text-red text-sm mb-2",children:t.description})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx("button",{type:"button",className:"px-4 py-2 bg-gray-300 text-black rounded",onClick:()=>a(!1),children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary text-white rounded",children:"Create"})]})]})})});return e.jsxs(e.Fragment,{children:[e.jsx(N,{headerTitle:"Bed Category",onSearch:!0,onFilter:!0,buttonText:"Add Category",button:!0,buttonAction:u}),e.jsx(A,{columns:i.columns,rows:i.rows,loading:!1,color:"bg-white ",textcolor:"text-gray-400",pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}}),e.jsx(D,{confirmAction:d,title:"Do you want to delete this token record?",des:"This action cannot be undone.",onClose:()=>o(!1),onConfirm:()=>{o(!1)}}),c&&e.jsx(S,{ref:m,children:b()})]})};export{R as default};
