import{a5 as g,ad as p,ae as x,a2 as e,af as h,ag as y,ah as b,ai as k,aj as D}from"./index-ClX9RVH0.js";import{C as f,D as j}from"./Svg-BMTGOzwv.js";import{D as C}from"./DepartmentHeader-Aj6XBXn4.js";const N=()=>{const[i,s]=g.useState("Patient"),n=p(),a={columns:[{title:"Patient Id",key:"tokenid"},{title:"Patient Name",key:"patientName"},{title:"Age",key:"date"},{title:"Diagnosis",key:"treatment"},{title:"Vital Signs",key:"date"},{title:"Bed No.",key:"date"},{title:"Addmission Date",key:"date"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:x.map(({tokenId:t,patientName:r,date:l,doctorName:c,status:d,treatment:m},u)=>({key:u,tokenid:t,patientName:r,date:l,doctorName:c,status:e.jsx(b,{status:d}),treatment:m,action:e.jsx(h,{onEdit:()=>{},onMore:()=>{},onMoreList:[{title:"Transfer",onClick:()=>{n(y.ADDAPPOINTMENT)},index:1},{title:"Treatment",onClick:()=>console.log("Treatment selected"),index:2},{title:"Surgery",onClick:()=>console.log("Surgery selected"),index:3},{title:"Discharge",onClick:()=>console.log("Discharge selected"),index:4}]})}))},o=t=>{console.log(t,"onSearch")};return e.jsxs("div",{children:[e.jsx(C,{title:"Day Care Unit",doctorName:"Dr. Shishir Thapa",services:["Pre-Surgery Checkup","Vital Monitoring","Medication Support","Plain Management","Doctor Consultation","Observation & Care"],totalPatients:200,underObservation:50,bedAvailability:50,dischargedPatients:10,doctorImage:e.jsx(j,{}),headerTitle:"Inpatient Department",icon:e.jsx(f,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pt-2 pb-1 pr-4 mt-5",children:[e.jsx(k,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:i,onTabChange:t=>s(t)}),e.jsx("div",{className:"flex flex-row gap-10",children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name, id",onChange:t=>o(t.target.value),className:"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(D,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{N as DayCareUnit};
