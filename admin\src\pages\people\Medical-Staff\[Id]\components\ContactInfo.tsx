import React from "react";

interface ContactInfoProps {
  contactInfo: {
    phoneNumber?: string;
    primaryPhone?: string;
    secondaryPhone?: string;
    email?: string;
    address?: string;
    currentAddress?: string;
    permanentAddress?: string;
    emergencyContact?: string;
  } | null;
}

const ContactInfo: React.FC<ContactInfoProps> = ({ contactInfo }) => {
  if (!contactInfo) {
    return (
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden w-full">
        <div className="bg-blue-50 px-4 py-3 border-b">
          <h3 className="text-base font-semibold text-blue">Contact Info</h3>
        </div>
        <div className="p-4">
          <p className="text-gray-500 text-sm">
            No contact information available
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border overflow-hidden w-full">
      {/* Contact Info Header */}
      <div className="bg-blue-50 px-4 py-3 border-b">
        <h3 className="text-base font-semibold text-blue">Contact Info</h3>
      </div>
      <div className="p-4">
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">
              Phone Number
            </span>
            <span className="text-sm text-gray-600">
              {contactInfo.phoneNumber || contactInfo.primaryPhone || "N/A"}
            </span>
          </div>
          {contactInfo.secondaryPhone && (
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-900">
                Secondary Phone
              </span>
              <span className="text-sm text-gray-600">
                {contactInfo.secondaryPhone}
              </span>
            </div>
          )}
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">Email</span>
            <span className="text-sm text-gray-600">
              {contactInfo.email || "N/A"}
            </span>
          </div>
          {(contactInfo.address || contactInfo.currentAddress) && (
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-900">
                Current Address
              </span>
              <span className="text-sm text-gray-600">
                {contactInfo.address || contactInfo.currentAddress}
              </span>
            </div>
          )}
          {contactInfo.permanentAddress && (
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-900">
                Permanent Address
              </span>
              <span className="text-sm text-gray-600">
                {contactInfo.permanentAddress}
              </span>
            </div>
          )}
          {contactInfo.emergencyContact && (
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-900">
                Emergency Contact
              </span>
              <span className="text-sm text-gray-600">
                {contactInfo.emergencyContact}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContactInfo;
