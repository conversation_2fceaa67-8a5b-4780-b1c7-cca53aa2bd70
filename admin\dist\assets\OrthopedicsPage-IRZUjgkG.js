import{a5 as p,ae as u,a2 as e,ah as h,ai as x,aj as g}from"./index-ClX9RVH0.js";import{d as b,D as j}from"./Svg-BMTGOzwv.js";import{D as y}from"./DepartmentHeader-Aj6XBXn4.js";const v=()=>{const[s,o]=p.useState("Patient"),a={columns:[{title:"Paitent Id",key:"tokenid"},{title:"Patient Name",key:"patientName"},{title:"Date Assigned",key:"date"},{title:"Contact Number",key:"treatment"},{title:"Appointment Date",key:"doctorName"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:u.map(({tokenId:t,patientName:n,date:r,doctorName:l,status:c,treatment:d},m)=>({key:m,tokenid:t,patientName:n,date:r,doctorName:l,status:e.jsx(h,{status:c}),treatment:d,action:e.jsx("button",{className:"px-4 py-2 text-white rounded bg-primary",children:"Checkup"})}))},i=t=>{console.log(t,"onSearch")};return e.jsxs(e.Fragment,{children:[e.jsx(y,{headerTitle:"ODP",title:"Orthopedics",doctorName:"Dr. John Smith",services:["Diagnostic","Interventional","Electrophysiology","Cardiac Rehabilitation","Preventive Cardiology"],patientServed:100,doctorImage:e.jsx(j,{}),followUpPatient:110,newPatient:20,revenueGenerated:4900,icon:e.jsx(b,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pb-2 mt-5",children:[e.jsx(x,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:s,onTabChange:t=>o(t)}),e.jsx("div",{children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name id",onChange:t=>i(t.target.value),className:"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(g,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{v as OrthopedicsDepartmentPage};
