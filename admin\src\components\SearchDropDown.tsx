import React, { useState, useRef, useEffect } from "react";
import { FieldProps } from "formik";

interface Option {
  value: string;
  label: string;
  [key: string]: any;
}

interface SearchableDropdownProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  field: string;
  label: string;
  options: Option[];
  disableInput?: boolean;
  // value?: string;
  isEditMode?: boolean;
  getFieldProps: (field: string) => FieldProps["field"];
  onValueChange?: (field: string, value: string, extra?: any) => void;
  // required?: boolean;
}

export const SearchableDropdown: React.FC<SearchableDropdownProps> = ({
  field,
  label,
  options,
  placeholder,
  getFieldProps,
  onValueChange,
  value,
  disableInput,
  required = false,
  ...other
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredOptions, setFilteredOptions] = useState<Option[]>(options);
  const [selectedOption, setSelectedOption] = useState<Option | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Find and set the selected option based on the value prop when options change or in edit mode
  useEffect(() => {
    if (value && options?.length > 0) {
      const option = options.find((opt) => opt.value === value);
      if (option) {
        setSelectedOption(option);
        setSearchTerm(option.label);
      }
    }
  }, [value, options]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Filter options based on search term
  useEffect(() => {
    const filtered = options?.filter((option) =>
      option?.label?.toLowerCase()?.includes(searchTerm.toLowerCase())
    );
    setFilteredOptions(filtered || []);
  }, [searchTerm, options]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setSearchTerm(inputValue);

    // Clear the selected option when user types something different
    if (selectedOption && inputValue !== selectedOption.label) {
      setSelectedOption(null);
      // Clear the field value in formik if needed
      if (onValueChange) {
        onValueChange(field, "");
      }
    }

    setIsOpen(true);
  };

  const handleSelectOption = (option: Option) => {
    setSelectedOption(option);
    setSearchTerm(option.label);
    if (onValueChange) {
      onValueChange(field, option.value, option);
    }
    setIsOpen(false);
  };

  const handleDropdownToggle = () => {
    if (!isOpen) {
      // When opening dropdown, clear search term to show all options
      setSearchTerm("");
    }
    setIsOpen(!isOpen);
  };

  const handleInputClick = () => {
    // When clicking on input, clear search term to show all options
    setSearchTerm("");
    setIsOpen(true);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="text-gray-700 block text-sm mb-1">
        {label}
        {required && (
          <span className="text-red text-opacity-75 text-xs relative bottom-1  ml-1 ">
            *
          </span>
        )}
      </div>
      <div className="relative">
        <input
          {...getFieldProps(field)}
          type="text"
          className="w-full text-black placeholder-gray-400 text-1sm px-2 py-[7.5px] font-normal border rounded-md border-gray-300 focus:outline-none"
          placeholder={placeholder || "Search or select..."}
          value={searchTerm}
          onChange={handleInputChange}
          disabled={disableInput}
          onClick={handleInputClick}
          {...other}
        />
        <button
          type="button"
          className="absolute right-2 top-4 text-gray-400"
          onClick={handleDropdownToggle}
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d={isOpen ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"}
            />
          </svg>
        </button>
      </div>
      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {filteredOptions?.length > 0 ? (
            filteredOptions?.map((option) => (
              <div
                key={option.value}
                className={`px-4 py-3 hover:bg-gray-100 cursor-pointer text-sm ${
                  selectedOption?.value === option.value ? "bg-blue-50" : ""
                }`}
                onClick={() => handleSelectOption(option)}
              >
                {option.label}
              </div>
            ))
          ) : (
            <div className="px-4 py-2 text-gray-500 text-sm">
              No results found
            </div>
          )}
        </div>
      )}
    </div>
  );
};
