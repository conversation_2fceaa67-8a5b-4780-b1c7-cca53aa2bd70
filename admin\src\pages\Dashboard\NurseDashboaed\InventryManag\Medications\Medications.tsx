import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../../../layouts/Table/MasterTable";

const Medications = () => {
  const tableData = {
    columns: [
      { title: "Item Name", key: "itemName" },
      { title: "Unit", key: "unit" },
      { title: "Quantity", key: "quantity" },
      { title: "Minimum Stock", key: "minimumStock" },
      { title: "Expiration Date", key: "expirationDate" },
      { title: "Batch", key: "batch" },
      { title: "Supplier", key: "supplier" },
      { title: "Order", key: "orderQuantity" },
    ],
    rows: [
      {
        itemName: "Paracetamol",
        unit: "Strips",
        quantity: 25,
        minimumStock: 50,
        expirationDate: "Nov 20, 2024",
        batch: "PCM-2024-005",
        supplier: "MediPharma Ltd.",
        orderQuantity: 30,
      },
      {
        itemName: "IV Antibiotic",
        unit: "Vials",
        quantity: 18,
        minimumStock: 30,
        expirationDate: "Oct 5, 2024",
        batch: "ABX-2024-011",
        supplier: "InjectaCare Pvt. Ltd.",
        orderQuantity: 20,
      },
      {
        itemName: "Insulin",
        unit: "Vials",
        quantity: 12,
        minimumStock: 25,
        expirationDate: "Dec 12, 2024",
        batch: "INS-2024-002",
        supplier: "GlucoMed Inc.",
        orderQuantity: 20,
      },
      {
        itemName: "Salbutamol Inhaler",
        unit: "Units",
        quantity: 8,
        minimumStock: 15,
        expirationDate: "Jan 30, 2025",
        batch: "INH-2025-007",
        supplier: "AirBreathe Corp.",
        orderQuantity: 10,
      },
      {
        itemName: "Normal Saline",
        unit: "Bottles",
        quantity: 20,
        minimumStock: 40,
        expirationDate: "Sep 22, 2024",
        batch: "NS-2024-008",
        supplier: "IVFlow Solutions",
        orderQuantity: 25,
      },
      {
        itemName: "Chloramphenicol Eye",
        unit: "Bottles",
        quantity: 10,
        minimumStock: 20,
        expirationDate: "Oct 15, 2024",
        batch: "ED-2024-003",
        supplier: "OcuPharma",
        orderQuantity: 15,
      },
      {
        itemName: "Metformin",
        unit: "Strips",
        quantity: 22,
        minimumStock: 40,
        expirationDate: "Nov 28, 2024",
        batch: "MET-2024-006",
        supplier: "DiaMed Ltd.",
        orderQuantity: 20,
      },
      {
        itemName: "Amoxicillin",
        unit: "Capsules",
        quantity: 30,
        minimumStock: 50,
        expirationDate: "Dec 5, 2024",
        batch: "AMX-2024-009",
        supplier: "BioAntibiotics Inc.",
        orderQuantity: 25,
      },
      {
        itemName: "Hydrocortisone",
        unit: "Tubes",
        quantity: 14,
        minimumStock: 20,
        expirationDate: "Aug 18, 2025",
        batch: "HC-2025-010",
        supplier: "DermaWell Pvt. Ltd.",
        orderQuantity: 10,
      },
      {
        itemName: "ORS Sachets",
        unit: "Packs",
        quantity: 40,
        minimumStock: 60,
        expirationDate: "Sep 15, 2025",
        batch: "ORS-2025-001",
        supplier: "Rehydrate Co.",
        orderQuantity: 30,
      },
    ],
  };

  const statCards = [
    {
      title: "Total Items",
      count: 120,
      subtitle: "Active inventory items",
      icon: "pajamas:work-item-epic",
      iconBg: "text-gray-9000",
      textColor: "text-gray-900",
      bgColor: "bg-[#9DF2DB]",
    },
    {
      title: "Low Stock",
      count: 32,
      subtitle: "Items below minimum",
      icon: "tdesign:error-triangle",
      iconBg: "text-rose-600",
      textColor: "text-rose-600",
      bgColor: "bg-[#FAECEC]",
    },
    {
      title: "Expiring Soon",
      count: 10,
      subtitle: "within 10 days",
      icon: "lets-icons:date-fill",
      iconBg: "text-amber-500",
      textColor: "text-amber-500",
      bgColor: "bg-[#CEF9F6]",
    },
    {
      title: "Today's Reorders",
      count: 40,
      subtitle: "Active inventory items",
      icon: "solar:restart-circle-bold",
      iconBg: "text-lime-600",
      textColor: "text-lime-600",
      bgColor: "bg-[#D8F7DE]",
    },
  ];

  return (
    <div className='p-2 mt-4'>
      {/* Header */}

      {/* Metrics Cards */}
      <div className='grid grid-cols-4 gap-4 mb-6'>
        {statCards.map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgColor} rounded-lg shadow-sm border border-gray-200 p-4`}
          >
            <div className='flex items-center justify-between gap-2'>
              <div className='text-sm font-semibold text-gray-800'>
                {stat.title}
              </div>
              <div>
                <Icon
                  icon={stat.icon}
                  width='24'
                  height='24'
                  className={`${stat.iconBg}`}
                />
              </div>
            </div>
            <div className={`text-2xl font-bold mt-1 ${stat.textColor}`}>
              {stat.count}
            </div>
            <div className='text-xs text-gray-500'>{stat.subtitle}</div>
          </div>
        ))}
      </div>

      <div className='bg-white p-6'>
        <div className='flex items-center justify-between mb-4'>
          <h2 className='text-lg font-semibold text-gray-900'>
            Stock Inventory
          </h2>
          <div className='flex items-center gap-3'>
            <div className='relative'>
              <svg
                className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
                />
              </svg>
              <input
                placeholder='Search items'
                className='pl-10 w-64 border border-gray-300 rounded-md px-3 py-2'
              />
            </div>
            <button className='flex items-center gap-2 bg-transparent border border-gray-300 rounded-md px-4 py-2 hover:bg-gray-50'>
              Sort
              <svg
                className='w-4 h-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M19 9l-7 7-7-7'
                />
              </svg>
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Add New Item
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Reorder Selected
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Download Report
            </button>
          </div>
        </div>
        <div>
          <MasterTable
            columns={tableData.columns}
            rows={tableData.rows}
            loading={false}
          />
        </div>
      </div>
    </div>
  );
};

export default Medications;
