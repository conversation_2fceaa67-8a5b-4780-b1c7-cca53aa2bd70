import{ad as $,a5 as P,aM as C,bw as j,bI as Q,aw as L,av as q,a2 as t,af as J,ag as r,ah as M,aQ as O,aj as V}from"./index-ClX9RVH0.js";const H=()=>{var u,p,h,i;const o=$(),[e,c]=P.useState({limit:5,page:1}),v=[j.NURSE,j.DIETICIAN],[n,l]=P.useState({selectedDepartment:"",selectedSpecialist:"",search:""}),R=n.search,T=C({multiroles:JSON.stringify(v),search:n.search,"departmentDetails.department":n.selectedDepartment,...R?{}:{page:e.page,limit:e.limit}}),{mutateAsync:w}=Q(),{data:s}=L(T),F=()=>{o(`/${r.ADDNURSE}`)},U=q.debounce(a=>l({...n,search:a}),500),d={columns:[{title:"Name",key:"doctorName"},{title:"ID",key:"tokenid"},{title:"Department",key:"department"},{title:"Phone Number",key:"contactNumber"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:(p=(u=s==null?void 0:s.data)==null?void 0:u.users)==null?void 0:p.map((a,_)=>{var g,N,f,y,D,S,b,I,x,A,E,k;return{key:_,tokenid:`N-${(N=a==null?void 0:a._id)==null?void 0:N.slice(((g=a==null?void 0:a._id)==null?void 0:g.length)-5,a==null?void 0:a._id.length)}`,contactNumber:((D=(y=(f=a==null?void 0:a.commonInfo)==null?void 0:f.contactInfo)==null?void 0:y.phone)==null?void 0:D.primaryPhone)??((I=(b=(S=a==null?void 0:a.commonInfo)==null?void 0:S.contactInfo)==null?void 0:b.phone)==null?void 0:I.secondaryPhone),department:(A=(x=a==null?void 0:a.departmentDetails)==null?void 0:x.hirachyFirst)==null?void 0:A.name,doctorName:(k=(E=a==null?void 0:a.commonInfo)==null?void 0:E.personalInfo)==null?void 0:k.fullName,shift:a==null?void 0:a.shift,status:t.jsx(M,{status:a!=null&&a.isActive?"ACTIVE":"INACTIVE"}),action:t.jsx(J,{onShow:()=>{o(`/${r.NURSEDETAIL}/${a==null?void 0:a._id}`)},onEdit:()=>{o(`/${r.ADDNURSE}/${a==null?void 0:a._id}`)},onDelete:()=>{w({id:JSON.stringify([a==null?void 0:a._id])})}})}})};return t.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[t.jsx(O,{headerTitle:"Nurse List",onSearch:!0,onDepartment:!0,onFilter:!0,button:!0,buttonText:"Add Nurse",buttonAction:F,onSearchFunc:a=>U(a),onDepartmentSelectFunc:a=>l({...n,selectedDepartment:a})}),t.jsx("div",{className:"bg-white rounded-md",children:t.jsx(V,{columns:d.columns,rows:d.rows,loading:!1,pagination:{currentPage:e.page,totalPage:(i=(h=s==null?void 0:s.data)==null?void 0:h.pagination)==null?void 0:i.pages,limit:e.limit,onClick:a=>{a.page&&c({...e,page:a.page}),a.limit&&c({...e,limit:a.limit})}}})})]})};export{H as default};
