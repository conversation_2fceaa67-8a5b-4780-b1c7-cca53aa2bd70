import { useState } from "react";
import HorizontalCalender from "./HorizontalCalendar";

type EventType = "shift" | "off" | "training" | "meeting";

interface NurseEvent {
  date: Date;
  time: string;
  event: string;
  type: EventType;
}

type NurseShiftData = {
  [nurseName: string]: NurseEvent[];
};

const ShiftCalendar = () => {
  const today = new Date();
  const [selectedMonth, setSelectedMonth] = useState<number>(today.getMonth());
  const [selectedYear, setSelectedYear] = useState<number>(today.getFullYear());
  const [selectedDate, setSelectedDate] = useState<Date>(today);
  const [selectedNurse, setSelectedNurse] = useState<string>("Sita");

  const getDayOfMonth = (date: Date): number => {
    return date.getDate();
  };

  const nurseShiftData: NurseShiftData = {
    Sita: [
      {
        date: new Date(2025, 6, 1),
        time: "06:00 AM - 02:00 PM",
        event: "Morning Shift - ICU",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 2),
        time: "02:00 PM - 10:00 PM",
        event: "Evening Shift - Emergency",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 3),
        time: "10:00 PM - 06:00 AM",
        event: "Night Shift - General Ward",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 4),
        time: "All Day",
        event: "Day Off",
        type: "off",
      },
      {
        date: new Date(2025, 6, 5),
        time: "06:00 AM - 02:00 PM",
        event: "Morning Shift - Pediatrics",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 6),
        time: "09:00 AM - 11:00 AM",
        event: "Training Session - CPR",
        type: "training",
      },
      {
        date: new Date(2025, 6, 7),
        time: "02:00 PM - 10:00 PM",
        event: "Evening Shift - Surgery",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 8),
        time: "10:00 PM - 06:00 AM",
        event: "Night Shift - ICU",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 9),
        time: "All Day",
        event: "Day Off",
        type: "off",
      },
      {
        date: new Date(2025, 6, 10),
        time: "06:00 AM - 02:00 PM",
        event: "Morning Shift - Emergency",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 15),
        time: "02:00 PM - 04:00 PM",
        event: "Staff Meeting",
        type: "meeting",
      },
      {
        date: new Date(2025, 6, 20),
        time: "10:00 PM - 06:00 AM",
        event: "Night Shift - General Ward",
        type: "shift",
      },
    ],
    Geeta: [
      {
        date: new Date(2025, 6, 1),
        time: "02:00 PM - 10:00 PM",
        event: "Evening Shift - Maternity",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 2),
        time: "10:00 PM - 06:00 AM",
        event: "Night Shift - ICU",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 3),
        time: "All Day",
        event: "Day Off",
        type: "off",
      },
      {
        date: new Date(2025, 6, 4),
        time: "06:00 AM - 02:00 PM",
        event: "Morning Shift - Emergency",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 5),
        time: "02:00 PM - 10:00 PM",
        event: "Evening Shift - Surgery",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 6),
        time: "10:00 PM - 06:00 AM",
        event: "Night Shift - General Ward",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 7),
        time: "All Day",
        event: "Day Off",
        type: "off",
      },
      {
        date: new Date(2025, 6, 8),
        time: "06:00 AM - 02:00 PM",
        event: "Morning Shift - Pediatrics",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 9),
        time: "01:00 PM - 03:00 PM",
        event: "Training Session - Patient Care",
        type: "training",
      },
      {
        date: new Date(2025, 6, 10),
        time: "02:00 PM - 10:00 PM",
        event: "Evening Shift - ICU",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 12),
        time: "10:00 PM - 06:00 AM",
        event: "Night Shift - Emergency",
        type: "shift",
      },
      {
        date: new Date(2025, 6, 15),
        time: "02:00 PM - 04:00 PM",
        event: "Staff Meeting",
        type: "meeting",
      },
      {
        date: new Date(2025, 6, 18),
        time: "06:00 AM - 02:00 PM",
        event: "Morning Shift - Maternity",
        type: "shift",
      },
    ],
  };

  const getEventColor = (type: EventType): string => {
    switch (type) {
      case "shift":
        return "text-blue-600";
      case "off":
        return "text-gray-500";
      case "training":
        return "text-green-600";
      case "meeting":
        return "text-purple-600";
      default:
        return "text-gray-600";
    }
  };

  const getEventIcon = (type: EventType): string => {
    switch (type) {
      case "shift":
        return "";
      case "off":
        return "";
      case "training":
        return "";
      case "meeting":
        return "";
      default:
        return "";
    }
  };

  const filteredEvents = nurseShiftData[selectedNurse]?.filter(
    (event) =>
      getDayOfMonth(event.date) === getDayOfMonth(selectedDate) &&
      event.date.getMonth() === selectedDate.getMonth() &&
      event.date.getFullYear() === selectedDate.getFullYear()
  );

  return (
    <div className="w-full bg-white shadow-sm rounded-xl px-4 py-1.5 border border-purple-100 transition-all duration-300">
      <main className="flex flex-wrap gap-2 rounded-2xl rounded-bl-none rounded-br-none py-1.5">
        <h3 className="text-xl font-bold text-gray-800">
          Nurse Shift Calendar
        </h3>
        <div className="flex gap-1 text-xs">
          <select
            name="nurseName"
            id="nurseName"
            value={selectedNurse}
            onChange={(e) => setSelectedNurse(e.target.value)}
            className="px-2 py-0.5 outline-none rounded-lg text-black border border-gray-300"
          >
            <option value="Sita">Sita</option>
            <option value="Geeta">Geeta</option>
          </select>

          <select
            value={selectedMonth}
            onChange={(e) => {
              const newMonth = Number(e.target.value);
              setSelectedMonth(newMonth);
              setSelectedDate(
                (prevDate) =>
                  new Date(prevDate.getFullYear(), newMonth, prevDate.getDate())
              );
            }}
            className="px-2 py-0.5 outline-none rounded-lg text-black border border-gray-300"
          >
            {[
              "January",
              "February",
              "March",
              "April",
              "May",
              "June",
              "July",
              "August",
              "September",
              "October",
              "November",
              "December",
            ].map((month, index) => (
              <option key={month} value={index}>
                {month}
              </option>
            ))}
          </select>

          <select
            value={selectedYear}
            onChange={(e) => {
              const newYear = Number(e.target.value);
              setSelectedYear(newYear);
              setSelectedDate(
                (prevDate) =>
                  new Date(newYear, prevDate.getMonth(), prevDate.getDate())
              );
            }}
            className=" px-3 py-1 outline-none rounded-lg text-black border border-gray-300"
          >
            {[...Array(10)].map((_, i) => {
              const year = new Date().getFullYear() + i;
              return (
                <option key={year} value={year}>
                  {year}
                </option>
              );
            })}
          </select>
        </div>
      </main>

      <HorizontalCalender
        selectedMonth={selectedMonth}
        selectedYear={selectedYear}
        onDateSelect={setSelectedDate}
      />

      <div className=" h-[110px] overflow-y-scroll mb-3">
        {selectedDate ? (
          <>
            <h1 className="text-sm text-gray-500 mb-2">
              {selectedDate.toLocaleDateString("en-US", {
                month: "long",
                day: "numeric",
                year: "numeric",
              })}{" "}
              - {selectedNurse}
            </h1>
            <ul className="">
              {(filteredEvents || []).length > 0 ? (
                filteredEvents.map((event, idx) => (
                  <li
                    key={idx}
                    className="flex justify-between items-center border-b pb-2 hover:bg-gray-50  rounded"
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-sm">
                        {getEventIcon(event.type)}
                      </span>
                      <h1 className="text-xs font-medium">{event.time}</h1>
                    </div>
                    <p
                      className={`text-xs font-medium ${getEventColor(
                        event.type
                      )}`}
                    >
                      {event.event}
                    </p>
                  </li>
                ))
              ) : (
                <p className="text-center text-gray-500 text-sm">
                  No shifts or events scheduled for this date.
                </p>
              )}
            </ul>
          </>
        ) : (
          <p className="text-center text-gray-500">
            Select a date to view shifts and events.
          </p>
        )}
      </div>
    </div>
  );
};

export default ShiftCalendar;
