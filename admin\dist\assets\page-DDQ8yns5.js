import{a2 as t,af as i,ad as o,aQ as n,aj as u}from"./index-ClX9RVH0.js";const l=[{requestId:"TRQ-001",name:"<PERSON>",testType:"Blood Test",reqDate:"2023-05-20",priority:"High",doctorName:"<PERSON><PERSON> <PERSON>",status:"in progress"},{requestId:"TRQ-002",name:"<PERSON>",testType:"Liver Functino Test",reqDate:"2023-05-20",priority:"Medium",doctorName:"Dr. <PERSON>",status:"Pending"}],d=()=>{const a={columns:[{key:"id",title:"S.N."},{key:"requestId",title:"Request ID"},{key:"name",title:"Patient Name"},{key:"testType",title:"Test Type"},{key:"reqDate",title:"Date Req."},{key:"priority",title:"Priority"},{key:"doctor<PERSON><PERSON>",title:"Doctor Name"},{key:"status",title:"Status"},{key:"action",title:"Action"}],rows:l.map((e,r)=>({id:r+1,requestId:e.requestId,name:e.name,testType:e.testType,reqDate:e.reqDate,priority:e.priority,doctorName:e.doctorName,status:e.status,action:t.jsx(i,{onEdit:()=>{},onDelete:()=>{}})}))},s=o();return t.jsxs("div",{children:[t.jsx(n,{headerTitle:"Test List",onSearch:!0,onDatePicker:!0,onFilter:!0,button:!0,buttonText:"Add Test Request",buttonAction:()=>s("/test-results/add-test-request")}),t.jsx("section",{className:"mt-4",children:t.jsx(u,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})})]})};export{d as TestRequestPage};
