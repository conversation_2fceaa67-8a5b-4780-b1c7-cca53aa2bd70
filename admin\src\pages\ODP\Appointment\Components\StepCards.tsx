import React from "react";
import { Icon } from "@iconify/react";
import { StepData } from "./OPDAppointmentForm";

interface Step {
  id: number;
  title: string;
  subtitle: string;
  icon: string;
  color: string;
}

interface StepCardsProps {
  steps: Step[];
  currentStep: number;
  stepData: StepData;
  onEdit: (step: number) => void;
  isStepCompleted: (step: number) => boolean;
}

const StepCards: React.FC<StepCardsProps> = ({
  steps,
  currentStep,
  stepData,
  onEdit,
  isStepCompleted,
}) => {
  const getCardBorderColor = (step: Step) => {
    const colorMap = {
      teal: "border-teal-500",
      red: "border-red-500",
      blue: "border-blue-500",
      purple: "border-purple-500",
    };
    return colorMap[step.color as keyof typeof colorMap] || "border-teal-500";
  };

  const getIconBgColor = (step: Step) => {
    const colorMap = {
      teal: "bg-teal-50 text-teal-600",
      red: "bg-red-50 text-red-600",
      blue: "bg-blue-50 text-blue-600",
      purple: "bg-purple-50 text-purple-600",
    };
    return (
      colorMap[step.color as keyof typeof colorMap] ||
      "bg-teal-50 text-teal-600"
    );
  };

  const renderStepData = (step: Step) => {
    switch (step.id) {
      case 1: // Patient Information
        const patientData = stepData.patientInfo;
        if (!patientData) return null;

        return (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Patient ID:</span>
              <span className="ml-2 font-medium">
                {patientData.patientId || "Auto Generated"}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Name:</span>
              <span className="ml-2 font-medium">{patientData.fullName}</span>
            </div>
            <div>
              <span className="text-gray-600">Blood Group:</span>
              <span className="ml-2 font-medium">{patientData.bloodGroup}</span>
            </div>
            <div>
              <span className="text-gray-600">DOB:</span>
              <span className="ml-2 font-medium">
                {patientData.dateOfBirth}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Phone:</span>
              <span className="ml-2 font-medium">
                {patientData.phoneNumber}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Email:</span>
              <span className="ml-2 font-medium">
                {patientData.emailAddress}
              </span>
            </div>
          </div>
        );

      case 2:
        const doctorData = stepData.doctorInfo;
        if (!doctorData) return null;

        return (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Department:</span>
              <span className="ml-2 font-medium">{doctorData.department}</span>
            </div>
            <div>
              <span className="text-gray-600">Doctor:</span>
              <span className="ml-2 font-medium">{doctorData.doctorName}</span>
            </div>
            <div>
              <span className="text-gray-600">Available Status:</span>
              <span className="ml-2 font-medium">
                {doctorData.availableStatus}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Appointment Date:</span>
              <span className="ml-2 font-medium">
                {doctorData.appointmentDate}
              </span>
            </div>
          </div>
        );

      case 3: // Appointment
        const appointmentData = stepData.appointmentInfo;
        if (!appointmentData) return null;

        return (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Date:</span>
              <span className="ml-2 font-medium">{appointmentData.date}</span>
            </div>
            <div>
              <span className="text-gray-600">Time:</span>
              <span className="ml-2 font-medium">
                {appointmentData.timeSlot}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Type:</span>
              <span className="ml-2 font-medium">
                {appointmentData.appointmentType}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Priority:</span>
              <span className="ml-2 font-medium">
                {appointmentData.priority}
              </span>
            </div>
          </div>
        );

      case 4: // Payment
        const paymentData = stepData.paymentInfo;
        if (!paymentData) return null;

        return (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Payment Method:</span>
              <span className="ml-2 font-medium">
                {paymentData.paymentMethod}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Amount:</span>
              <span className="ml-2 font-medium">Rs. {paymentData.amount}</span>
            </div>
            <div>
              <span className="text-gray-600">Status:</span>
              <span className="ml-2 font-medium">
                {paymentData.paymentStatus}
              </span>
            </div>
            {paymentData.transactionId && (
              <div>
                <span className="text-gray-600">Transaction ID:</span>
                <span className="ml-2 font-medium">
                  {paymentData.transactionId}
                </span>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      {steps.map((step) => {
        const isCompleted = isStepCompleted(step.id);
        const isActive = step.id === currentStep;
        const stepData = renderStepData(step);

        return (
          <div
            key={step.id}
            className={`bg-white rounded-lg border-2 transition-all duration-300 ${
              isCompleted
                ? `${getCardBorderColor(step)} shadow-md`
                : isActive
                ? "border-gray-300 shadow-sm"
                : "border-gray-200"
            }`}
          >
            {/* Card Header */}
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center ${getIconBgColor(
                      step
                    )}`}
                  >
                    <Icon icon={step.icon} className="w-4 h-4" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-800">
                      {step.title}
                    </h3>
                    <p className="text-xs text-gray-500">{step.subtitle}</p>
                  </div>
                </div>

                {isCompleted && (
                  <button
                    onClick={() => onEdit(step.id)}
                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    title="Edit"
                  >
                    <Icon icon="mdi:pencil" className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>

            {/* Card Content */}
            <div className="p-4">
              {isCompleted && stepData ? (
                stepData
              ) : (
                <div className="text-center py-8">
                  <Icon
                    icon="mdi:dots-horizontal"
                    className="w-8 h-8 text-gray-300 mx-auto"
                  />
                  <p className="text-xs text-gray-400 mt-2">
                    {isActive ? "In Progress" : "Pending"}
                  </p>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default StepCards;
