import React from "react";
import { Icon } from "@iconify/react";
import { StepData } from "./OPDAppointmentForm";

interface Step {
  id: number;
  title: string;
  subtitle?: string;
  icon: string;
  color: string;
}

interface StepCardsProps {
  steps: Step[];
  currentStep: number;
  stepData: StepData;
  onEdit: (step: number) => void;
  isStepCompleted: (step: number) => boolean;
}

const StepCards: React.FC<StepCardsProps> = ({
  steps,
  currentStep,
  stepData,
  onEdit,
  isStepCompleted,
}) => {
  const getCardBorderColor = (step: Step) => {
    const colorMap = {
      teal: "border-teal-500",
      red: "border-red",
      blue: "border-blue",
      purple: "border-purple-200",
    };
    return colorMap[step.color as keyof typeof colorMap] || "border-teal-500";
  };

  const getIconBgColor = (step: Step) => {
    const colorMap = {
      teal: "text-teal-600",
      red: "text-red-600",
      blue: "text-blue",
      purple: "bg-purple-50 text-purple-600",
    };
    return (
      colorMap[step.color as keyof typeof colorMap] || "bg-red text-teal-600"
    );
  };

  const renderStepData = (step: Step) => {
    switch (step.id) {
      case 1: {
        const patientData = stepData.patientInfo;
        console.log(patientData, "paitentdata");
        if (!patientData) return null;
        return (
          <div className="grid grid-cols-2 gap-1 text-xs">
            <div>
              <span className="text-black font-medium">Name:</span>
              <span className="ml-2 text-gray-700 ">
                {patientData.fullName}
              </span>
            </div>
            <div>
              <span className="text-black font-medium">Blood Group:</span>
              <span className="ml-2 text-gray-700">
                {patientData.bloodGroup}
              </span>
            </div>
            <div>
              <span className="text-black font-medium">DOB:</span>
              <span className="ml-2 text-gray-700">{patientData.dob}</span>
            </div>
            <div>
              <span className="text-black font-medium">Phone:</span>
              <span className="ml-2 text-gray-700">
                {patientData.contactNumber}
              </span>
            </div>
            <div>
              <span className="text-black font-medium">Email:</span>
              <span className="ml-2 text-gray-700">{patientData.email}</span>
            </div>
            <div>
              <span className="text-black font-medium">Gender:</span>
              <span className="ml-2 text-gray-700">{patientData.gender}</span>
            </div>
            <div>
              <span className="text-black font-medium">Marital Status:</span>
              <span className="ml-2 text-gray-700">
                {patientData.maritalStatus}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Language:</span>
              <span className="ml-2 text-gray-700">{patientData.language}</span>
            </div>
            <div>
              <span className="text-black font-medium">Current Address:</span>
              <span className="ml-2 text-gray-700">
                {patientData.currentAddress}
              </span>
            </div>
            <div>
              <span className="text-black font-medium">Permanent Address:</span>
              <span className="ml-2 text-gray-700">
                {patientData.permanentAddress}
              </span>
            </div>
          </div>
        );
      }
      case 2: {
        const doctorData = stepData.doctorInfo;
        if (!doctorData) return null;
        return (
          <div className="grid grid-cols-1 gap-1 text-sm">
            <div>
              <span className="text-gray-600">Department:</span>
              <span className="ml-2 font-medium">
                {doctorData.departmentName || doctorData.department}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Doctor:</span>
              <span className="ml-2 font-medium">
                {doctorData.doctorName || doctorData.doctor}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Available Time Slot:</span>
              <span className="ml-2 font-medium">
                {doctorData.availableTimeSlot}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Appointment Date:</span>
              <span className="ml-2 font-medium">
                {doctorData.appointmentDate}
              </span>
            </div>
          </div>
        );
      }
      case 3: {
        const appointmentData = stepData.appointmentInfo;
        if (!appointmentData) return null;
        return (
          <div className="grid grid-cols-1 gap-1 text-sm">
            <div>
              <span className="text-gray-600">Date:</span>
              <span className="ml-2 font-medium">{appointmentData.date}</span>
            </div>
            <div>
              <span className="text-gray-600">Time:</span>
              <span className="ml-2 font-medium">
                {appointmentData.timeSlot}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Type:</span>
              <span className="ml-2 font-medium">
                {appointmentData.appointmentType}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Priority:</span>
              <span className="ml-2 font-medium">
                {appointmentData.priority}
              </span>
            </div>
          </div>
        );
      }
      case 4: {
        const paymentData = stepData.paymentInfo;
        if (!paymentData) return null;
        return (
          <div className="grid grid-cols-1 gap-1 text-sm ">
            <div>
              <span className="text-gray-600">Payment Method:</span>
              <span className="ml-2 font-medium">
                {paymentData.paymentMethod}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Amount:</span>
              <span className="ml-2 font-medium">Rs. {paymentData.amount}</span>
            </div>
            <div>
              <span className="text-gray-600">Status:</span>
              <span className="ml-2 font-medium">
                {paymentData.paymentStatus}
              </span>
            </div>
            {paymentData.transactionId && (
              <div>
                <span className="text-gray-600">Transaction ID:</span>
                <span className="ml-2 font-medium">
                  {paymentData.transactionId}
                </span>
              </div>
            )}
          </div>
        );
      }
      default:
        return null;
    }
  };

  const renderCard = (step: Step) => {
    const isCompleted = isStepCompleted(step.id);
    const isActive = step.id === currentStep;
    const stepDataContent = renderStepData(step);

    return (
      <div
        className={`h-full flex flex-col bg-white rounded-lg border-2 transition-all duration-300 
        ${
          isCompleted
            ? `${getCardBorderColor(step)} shadow-md`
            : isActive
            ? "border-gray-300 shadow-sm"
            : "border-gray-200"
        }
      `}
      >
        {/* Card Header */}
        <div className="p-1 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${getIconBgColor(
                  step
                )}`}
              >
                <Icon icon={step.icon} className="w-4 h-4" />
              </div>
              <h3 className="text-sm font-medium text-gray-900">
                {step.title}
              </h3>
            </div>
            {isCompleted && (
              <button
                onClick={() => onEdit(step.id)}
                className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                title="Edit"
              >
                <Icon icon="mdi:pencil" className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Card Body (takes remaining space) */}
        <div className="p-4 flex-1">
          {isCompleted && stepDataContent ? (
            stepDataContent
          ) : (
            <div className="text-center py-8">
              <Icon
                icon="mdi:dots-horizontal"
                className="w-8 h-8 text-gray-300 mx-auto"
              />
              <p className="text-xs text-gray-400 mt-2">
                {isActive ? "In Progress" : "Pending"}
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex gap-2">
      {/* Card 1 - 1/3 of the row */}
      <div className="w-full lg:w-1/3">{renderCard(steps[0])}</div>

      {/* Cards 2-4 - 2/3 of the row, split into 3 equal parts */}
      <div className="w-full lg:w-2/3 flex gap-2">
        {steps.slice(1).map((step) => (
          <div key={step.id} className="w-1/3">
            {renderCard(step)}
          </div>
        ))}
      </div>
    </div>
  );
};

export default StepCards;
