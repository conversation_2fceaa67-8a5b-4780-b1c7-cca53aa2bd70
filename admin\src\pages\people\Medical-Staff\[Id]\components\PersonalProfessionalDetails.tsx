import React from "react";

interface PersonalDetails {
  fullName: string;
  gender?: string;
  dateOfBirth?: string;
  age?: string | number;
  maritalStatus?: string;
  bloodGroup: string;
  religion?: string;
  language?: string;
}

interface ProfessionalDetails {
  doctorId?: string;
  nurseId?: string;
  department: string;
  specialization?: string;
  designation: string;
  licenseNumber: string;
  joiningDate: string;
  academicDegree?: string;
  qualification?: string;
  institution?: string;
  fieldOfStudy?: string;
  shiftTiming?: string;
  yearsofExperience?: string;
  employmentType?: string;
}

interface PersonalProfessionalDetailsProps {
  personalDetails: PersonalDetails | null;
  professionalDetails: ProfessionalDetails | null;
  staffType?: "Doctor" | "Nurse" | "Staff";
}

const PersonalProfessionalDetails: React.FC<
  PersonalProfessionalDetailsProps
> = ({ personalDetails, professionalDetails, staffType = "Doctor" }) => {
  if (!personalDetails || !professionalDetails) return null;

  const getStaffId = () => {
    if (staffType === "Doctor") return professionalDetails.doctorId;
    if (staffType === "Nurse") return professionalDetails.nurseId;
    return "N/A";
  };

  const getAcademicInfo = () => {
    if (staffType === "Doctor") {
      return professionalDetails.academicDegree || "N/A";
    }
    return professionalDetails.qualification || "N/A";
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
      {/* General Info Section */}
      <div className="px-4 py-3 border-b">
        <h3 className="text-base font-semibold text-blue">General Info</h3>
      </div>
      <div className="p-4">
        <div className="grid grid-cols-2 gap-x-4 gap-y-3">
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">Age</span>
            <span className="text-sm text-gray-600">{personalDetails.age}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">
              {staffType === "Doctor" ? "Academic Degree" : "Qualification"}
            </span>
            <span className="text-sm text-gray-600">{getAcademicInfo()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">Gender</span>
            <span className="text-sm text-gray-600">
              {personalDetails.gender}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">
              Designation
            </span>
            <span className="text-sm text-gray-600">
              {professionalDetails.designation}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">
              Date of Birth
            </span>
            <span className="text-sm text-gray-600">
              {personalDetails.dateOfBirth}
            </span>
          </div>

          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">
              Marital Status
            </span>
            <span className="text-sm text-gray-600">
              {personalDetails.maritalStatus}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">
              Department
            </span>
            <span className="text-sm text-gray-600">
              {professionalDetails.department}
            </span>
          </div>
          {/* <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">
              Blood Group
            </span>
            <span className="text-sm text-gray-600">
              {personalDetails.bloodGroup}
            </span>
          </div> */}
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">
              Experience
            </span>
            <span className="text-sm text-gray-600">
              {professionalDetails.yearsofExperience} years
            </span>
          </div>
          {/* <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">Religion</span>
            <span className="text-sm text-gray-600">
              {personalDetails.religion}
            </span>
          </div> */}
          {/* <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">
              License Number
            </span>
            <span className="text-sm text-gray-600">
              {professionalDetails.licenseNumber}
            </span>
          </div> */}
          {/* <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">Language</span>
            <span className="text-sm text-gray-600">
              {personalDetails.language}
            </span>
          </div> */}
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-900">
              Joining Date
            </span>
            <span className="text-sm text-gray-600">
              {professionalDetails.joiningDate}
            </span>
          </div>
          {/* {staffType === "Nurse" && professionalDetails.shiftTiming && (
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-900">
                Shift Timing
              </span>
              <span className="text-sm text-gray-600">
                {professionalDetails.shiftTiming}
              </span>
            </div>
          )} */}
          {staffType === "Nurse" && professionalDetails.employmentType && (
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-900">
                Employment Type
              </span>
              <span className="text-sm text-gray-600">
                {professionalDetails.employmentType}
              </span>
            </div>
          )}
          {/* {professionalDetails.institution && (
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-900">
                Institution
              </span>
              <span className="text-sm text-gray-600">
                {professionalDetails.institution}
              </span>
            </div>
          )} */}
          {professionalDetails.fieldOfStudy && (
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-900">
                Field of Study
              </span>
              <span className="text-sm text-gray-600">
                {professionalDetails.fieldOfStudy}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PersonalProfessionalDetails;
