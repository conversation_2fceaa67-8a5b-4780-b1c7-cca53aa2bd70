import{a2 as t,b3 as d}from"./index-ClX9RVH0.js";const P=(s,e=100)=>{var o;const r=(o=s==null?void 0:s.replace)==null?void 0:o.call(s,/<[^>]+>/g,"");return(r==null?void 0:r.length)>e?(r==null?void 0:r.slice(0,e))+"...":r},E=(s,e="/placeholder.svg")=>Array.isArray(s)?(s==null?void 0:s.length)>0?s[0]:e:s||e,A=({item:s,config:e,onEdit:r,onDelete:o,onView:n,className:g=""})=>{var p,b,j,w,v,N,S;const m=e==null?void 0:e.getTitle(s),F=e==null?void 0:e.getDescription(s),D=P(F,e==null?void 0:e.maxDescriptionLength),C=E((p=e==null?void 0:e.getImage)==null?void 0:p.call(e,s)),x=(b=e==null?void 0:e.getDate)==null?void 0:b.call(e,s),l=(j=e==null?void 0:e.getStatus)==null?void 0:j.call(e,s),u=((w=e==null?void 0:e.getMetadata)==null?void 0:w.call(e,s))||[],c=((N=(v=e==null?void 0:e.getKeyPoints)==null?void 0:v.call(e,s))==null?void 0:N.slice(0,e.maxKeyPoints||3))||[],y=x?(S=new Date(x))==null?void 0:S.toLocaleDateString("en-US",{month:"short",day:"numeric"}):"",a=(()=>{switch(e==null?void 0:e.cardStyle){case"campaign":return{container:"bg-white group rounded-xl shadow-md border hover:shadow-lg transition-shadow duration-300 max-w-md mx-auto",title:"text-xl font-bold text-gray-900 group-hover:text-[#3F87BE] transition-colors duration-200",titleFont:{fontFamily:"Georgia, serif"},dateStyle:"absolute top-3 right-3 bg-[#16273B] group-hover:bg-[#3F87BE] border-2 border-white rounded-lg text-white px-3 py-1 text-center font-semibold text-sm transition-colors duration-200"};case"blog":return{container:"rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow",title:"text-lg font-semibold text-gray-900 line-clamp-2 flex-1 mr-2",titleFont:{},dateStyle:""};case"job":return{container:"bg-white rounded-xl shadow-md border hover:shadow-lg transition-shadow duration-300",title:"text-xl font-bold text-gray-900 hover:text-blue-600 transition-colors duration-200",titleFont:{},dateStyle:"absolute top-3 right-3 bg-blue-600 text-white px-3 py-1 rounded-lg text-sm font-medium"};default:return{container:"bg-white rounded-lg shadow-md border hover:shadow-lg transition-shadow duration-300",title:"text-lg font-semibold text-gray-900",titleFont:{},dateStyle:""}}})();return t.jsxs("div",{className:`${a.container} ${g}`,children:[t.jsxs("div",{className:"relative",children:[t.jsx("img",{src:C,alt:m,className:"w-full h-48 object-cover rounded-t-xl",width:400,height:192,loading:"lazy"}),x&&(e.cardStyle==="campaign"||e.cardStyle==="job")&&t.jsx("div",{className:a.dateStyle,style:a.titleFont,children:y})]}),t.jsxs("div",{className:"p-5 space-y-4",children:[e.cardStyle==="blog"?t.jsx("div",{className:"flex items-start justify-between mb-2",children:t.jsx("h3",{className:a.title,style:a.titleFont,children:m})}):t.jsx("h3",{className:a.title,style:a.titleFont,children:m}),e.cardStyle==="blog"&&t.jsxs("div",{className:"flex items-center justify-between gap-2 text-sm text-gray-600 mb-3",children:[x&&t.jsxs("span",{children:["Published: ",y]}),l&&t.jsx("span",{className:`px-3 py-2 rounded-full text-xs font-medium ${l.color}`,children:l.label})]}),u.length>0&&t.jsx("div",{className:"flex flex-wrap gap-4 text-gray-600 text-sm",children:u.map((i,h)=>t.jsxs("div",{className:"flex place-items-center  gap-1 font-medium",children:[t.jsx(d,{icon:i.icon,fontSize:16}),t.jsx("p",{className:"text-[16px]",children:i.value})]},h))}),c.length>0&&t.jsxs("div",{className:"text-sm text-gray-700",children:[t.jsxs("div",{className:"flex items-center gap-1 font-semibold text-gray-800 mb-2",children:[t.jsx(d,{icon:"mdi:format-list-bulleted",className:"w-5 h-5"}),"Key Points"]}),t.jsx("ul",{className:"list-disc list-inside space-y-1 text-gray-600",children:c.map((i,h)=>t.jsx("li",{className:"line-clamp-1",children:i},h))})]}),t.jsx("p",{className:"text-base text-gray-700 line-clamp-2 leading-relaxed",children:D}),l&&e.cardStyle!=="blog"&&t.jsx("div",{className:"flex justify-start",children:t.jsx("span",{className:`px-3 py-1 rounded-full text-xs font-medium ${l.color}`,children:l.label})})]}),t.jsxs("div",{className:"px-5 pb-5 flex items-center justify-end gap-3 border-t border-gray-200 pt-4",children:[n&&t.jsxs("button",{onClick:()=>n==null?void 0:n(s),className:"flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200",children:[t.jsx(d,{icon:"hugeicons:view",className:"w-4 h-4"}),"View"]}),t.jsxs("button",{onClick:()=>r(s),className:"flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors duration-200",children:[t.jsx(d,{icon:"lucide:edit",className:"w-4 h-4"}),"Edit"]}),t.jsxs("button",{onClick:()=>o(s),className:"flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-red rounded-lg hover:bg-rose-300 transition-colors duration-200",children:[t.jsx(d,{icon:"mdi:delete",className:"w-4 h-4"}),"Delete"]})]})]})};export{A as G};
