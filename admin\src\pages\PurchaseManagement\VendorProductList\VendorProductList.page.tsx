import { useState, useCallback, useEffect } from "react";
import { useAuth, useOutsideClick } from "../../../hooks";
import { INVENTROYENUM } from "../../../constant/constant";
import Button from "../../../components/Button";
import MasterTable from "../../../layouts/Table/MasterTable";
import { PopupModal } from "../../../components";
import { TableAction } from "../../../layouts/Table/TableAction";
import { buildQueryParams } from "../../../hooks/useBuildQuery";
import { get, values } from "lodash";

// API imports for different departments
import { useGetPharmacyProduct } from "../../../server-action/api/pharmacy-product.api";
import { useGetAllHospInvProduct } from "../../../server-action/api/inventoryApi";
import { useGetAllEquipment } from "../../../server-action/api/equipmentsApi";
import { useGetAllCanteenProduct } from "../../../server-action/api/canteen-product.api";
import { CustomTabs } from "../../../components/CustomTab";
import { ExtraCustomTabs } from "../../../components/CustomTabWithExtraField";
import { ProductCategory } from "../../../constant/constant";

// Import add product forms for each department
import { AddPharmacyProduct } from "../../Pharmacy/inventory/components/AddPharmacyProduct.component";
import AddLabProduct from "../../LabDashboard/LabProductList/components/AddProduct";
import AddRadiologyProduct from "../../Radiology/RadiologyInventory/ProductList/AddProduct";
import AddHospitalProduct from "../../InventoryManagement/ProductList/components/AddProductListForm";

// Product Details Component
const ProductDetails = ({
  product,
  onClose,
}: {
  product: any;
  onClose: () => void;
}) => {
  const productDetails = [
    {
      title: "Product Name",
      value: product?.name || product?.productName || "N/A",
    },
    {
      title: "Category",
      value: product?.category?.categoryName || product?.category || "N/A",
    },
    { title: "Description", value: product?.description || "N/A" },
    { title: "Unit", value: product?.unit || "N/A" },
    { title: "Department", value: product?.department || "N/A" },
  ];

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">Product Details</h2>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          ✕
        </button>
      </div>

      <div className="space-y-3">
        {productDetails.map((item, index) => (
          <div key={index} className="flex justify-between border-b pb-2">
            <span className="font-medium text-gray-600">{item.title}:</span>
            <span className="text-gray-800">{item.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export const VendorProductList = () => {
  const { data: authData } = useAuth();
  const userRole = authData?.user?.role;

  const [vendorProps, setVendorProps] = useState({
    currentTab: "all",
    availableTab: [] as any,
  });

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
  });

  // Tab state for department-specific categories
  const [departmentTabValue, setDepartmentTabValue] = useState<string>("");

  // Tab configurations for each department
  const getPharmacyTabs = () => [
    { label: "All", value: "all" },
    { label: "Medicine", value: ProductCategory.DRUG },
    { label: "Device", value: ProductCategory.DEVICES },
    { label: "Skin Product", value: ProductCategory.BEAUTIESSKINCARE },
    { label: "Medical Supplies", value: ProductCategory.MEDICALSUPPLIES },
    { label: "Lab Test Equipment", value: ProductCategory.LABTEST },
    { label: "Other", value: ProductCategory.OTHER },
  ];

  const getLabTabs = () => [
    "All",
    "Equipment",
    "Chemical",
    "Consumable",
    "Glassware",
    "Plasticware",
    "Safety Supplies",
    "Instruments",
    "Kits",
    "Others",
  ];

  const getRadiologyTabs = () => [
    "All",
    "Equipment",
    "Chemical",
    "Consumable",
    "Glassware",
    "Plasticware",
    "Safety Supplies",
    "Instruments",
    "Kits",
    "Others",
  ];

  const getHospitalTabs = () => [
    "All",
    "Medical Equipment",
    "Supplies",
    "Furniture",
    "Others",
  ];

  const modalRef = useOutsideClick(() => setIsModalOpen(false));
  const addModalRef = useOutsideClick(() => setIsAddModalOpen(false));

  const departmentOptions = [
    { label: "Department", value: "department" },
    { label: "Laboratory", value: `${INVENTROYENUM.LABORATORY}` },
    { label: "Hospital", value: `${INVENTROYENUM.HOSPITAL}` },
    { label: "Pharmacy", value: `${INVENTROYENUM.PHARMACY}` },
    { label: "Radiology", value: "RADIOLOGY" },
  ];

  // Reset department tab when main department changes
  useEffect(() => {
    if (vendorProps.currentTab === INVENTROYENUM.PHARMACY) {
      setDepartmentTabValue(getPharmacyTabs()[0]?.value || "");
    } else if (vendorProps.currentTab === INVENTROYENUM.LABORATORY) {
      setDepartmentTabValue(getLabTabs()[0] || "");
    } else if (vendorProps.currentTab === "RADIOLOGY") {
      setDepartmentTabValue(getRadiologyTabs()[0] || "");
    } else if (vendorProps.currentTab === INVENTROYENUM.HOSPITAL) {
      setDepartmentTabValue(getHospitalTabs()[0] || "");
    } else {
      setDepartmentTabValue("");
    }
    setPagination({ page: 1, limit: 10 }); // Reset pagination
  }, [vendorProps.currentTab]);

  // Build query parameters with tab filtering
  const queryParams = buildQueryParams({
    search: searchTerm,
    page: pagination.page,
    limit: pagination.limit,
    ...(vendorProps.currentTab === INVENTROYENUM.PHARMACY &&
    departmentTabValue &&
    departmentTabValue !== "All"
      ? { category: departmentTabValue }
      : {}),
    ...(vendorProps.currentTab === INVENTROYENUM.LABORATORY &&
    departmentTabValue &&
    departmentTabValue !== "All"
      ? { category: departmentTabValue }
      : {}),
    ...(vendorProps.currentTab === "RADIOLOGY" &&
    departmentTabValue &&
    departmentTabValue !== "All"
      ? { category: departmentTabValue }
      : {}),
    ...(vendorProps.currentTab === INVENTROYENUM.HOSPITAL &&
    departmentTabValue &&
    departmentTabValue !== "All"
      ? { category: departmentTabValue }
      : {}),
  });

  // API calls for different departments
  const { data: pharmacyData, isLoading: pharmacyLoading } =
    useGetPharmacyProduct(
      vendorProps.currentTab === INVENTROYENUM.PHARMACY
        ? queryParams
        : undefined,
      { enabled: vendorProps.currentTab === INVENTROYENUM.PHARMACY }
    );

  const { data: hospitalData, isLoading: hospitalLoading } =
    useGetAllHospInvProduct(
      vendorProps.currentTab === INVENTROYENUM.HOSPITAL
        ? queryParams
        : undefined,
      { enabled: vendorProps.currentTab === INVENTROYENUM.HOSPITAL }
    );

  const { data: labData, isLoading: labLoading } = useGetAllEquipment(
    vendorProps.currentTab === INVENTROYENUM.LABORATORY
      ? { ...queryParams, upperHirachy: "6811cb6808e7f503fbf1c40a" }
      : undefined,
    { enabled: vendorProps.currentTab === INVENTROYENUM.LABORATORY }
  );

  const { data: radiologyData, isLoading: radiologyLoading } =
    useGetAllEquipment(
      vendorProps.currentTab === "RADIOLOGY"
        ? { ...queryParams, upperHirachy: "6823163b7abfaa19e7374883" }
        : undefined,
      { enabled: vendorProps.currentTab === "RADIOLOGY" }
    );

  // Handle view product details
  const handleViewProduct = useCallback((product: any) => {
    setSelectedProduct(product);
    setIsModalOpen(true);
  }, []);

  // Generate table data based on current department
  const getTableData = () => {
    let data: any[] = [];
    let loading = false;
    let paginationData: any = {};

    switch (vendorProps.currentTab) {
      case INVENTROYENUM.PHARMACY:
        data = get(pharmacyData, "data.medicalProducts", []);
        loading = pharmacyLoading;
        paginationData = get(pharmacyData, "data.pagination", {});
        break;
      case INVENTROYENUM.HOSPITAL:
        data = get(hospitalData, "data.products", []);
        loading = hospitalLoading;
        paginationData = get(hospitalData, "data.pagination", {});
        break;
      case INVENTROYENUM.LABORATORY:
        data = get(labData, "data.equipments", []);
        loading = labLoading;
        paginationData = get(labData, "data.pagination", {});
        break;
      case "RADIOLOGY":
        data = get(radiologyData, "data.equipments", []);
        loading = radiologyLoading;
        paginationData = get(radiologyData, "data.pagination", {});
        break;
      default:
        data = [];
        loading = false;
        paginationData = {};
    }

    const columns = [
      { title: "S.No", key: "sn" },
      { title: "Product Name", key: "productName" },
      { title: "Category", key: "category" },
      { title: "Description", key: "description" },
      { title: "Unit", key: "unit" },
      { title: "Action", key: "action" },
    ];

    const rows = data.map((product: any, index: number) => ({
      sn: index + 1,
      productName: product?.name || product?.productName || "N/A",
      category: product?.category?.categoryName || product?.category || "N/A",
      description: product?.description || "N/A",
      unit: product?.unit || "N/A",
      action: <TableAction onShow={() => handleViewProduct(product)} />,
    }));

    return { columns, rows, loading, paginationData };
  };

  const { columns, rows, loading, paginationData } = getTableData();

  return (
    <div>
      <section className="flex justify-between p-4 bg-white shadow-sm rounded-md mb-2">
        <select
          className="w-full px-2 py-[6px] text-sm rounded-sm md:w-64 border outline-none"
          value={vendorProps.currentTab || ""}
          onChange={(e) =>
            setVendorProps({
              currentTab: e.target.value,
              availableTab: [],
            })
          }
        >
          {departmentOptions.map((dashboard) => (
            <option key={dashboard.value} value={dashboard.value}>
              {dashboard.label}
            </option>
          ))}
        </select>

        <div className="flex place-items-center gap-4">
          <input
            type="text"
            className="w-full px-2 py-[6px] text-sm rounded-sm md:w-64 border outline-none"
            placeholder="Search by Name, Item Code"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {vendorProps.currentTab !== "all" && (
            <Button
              title="Add Product"
              onClick={() => setIsAddModalOpen(true)}
            />
          )}
        </div>
      </section>

      {/* Product Table */}
      {vendorProps.currentTab !== "all" && (
        <div className="bg-white rounded-md">
          {/* Department-specific tabs */}
          {vendorProps.currentTab === INVENTROYENUM.PHARMACY && (
            <div className="px-5 pt-4">
              <ExtraCustomTabs
                tabs={getPharmacyTabs()}
                defaultTab={departmentTabValue || getPharmacyTabs()[0]?.value}
                onTabChange={(tab) => {
                  setDepartmentTabValue(tab);
                  setPagination({ ...pagination, page: 1 }); // Reset to first page
                }}
              />
            </div>
          )}

          {vendorProps.currentTab === INVENTROYENUM.LABORATORY && (
            <div className="px-5 pt-4">
              <CustomTabs
                tabs={getLabTabs()}
                defaultTab={departmentTabValue || getLabTabs()[0]}
                onTabChange={(tab) => {
                  setDepartmentTabValue(tab);
                  setPagination({ ...pagination, page: 1 }); // Reset to first page
                }}
              />
            </div>
          )}

          {vendorProps.currentTab === "RADIOLOGY" && (
            <div className="px-5 pt-4">
              <CustomTabs
                tabs={getRadiologyTabs()}
                defaultTab={departmentTabValue || getRadiologyTabs()[0]}
                onTabChange={(tab) => {
                  setDepartmentTabValue(tab);
                  setPagination({ ...pagination, page: 1 }); // Reset to first page
                }}
              />
            </div>
          )}

          {vendorProps.currentTab === INVENTROYENUM.HOSPITAL && (
            <div className="px-5 pt-4">
              <CustomTabs
                tabs={getHospitalTabs()}
                defaultTab={departmentTabValue || getHospitalTabs()[0]}
                onTabChange={(tab) => {
                  setDepartmentTabValue(tab);
                  setPagination({ ...pagination, page: 1 }); // Reset to first page
                }}
              />
            </div>
          )}

          <MasterTable
            columns={columns}
            rows={rows}
            loading={loading}
            pagination={{
              currentPage: pagination.page,
              totalPage: paginationData?.pages || 1,
              limit: pagination.limit,
              onClick: (params: { page?: number; limit?: number }) => {
                if (params.page) {
                  setPagination({ ...pagination, page: params.page });
                }
                if (params.limit) {
                  setPagination({ ...pagination, limit: params.limit });
                }
              },
            }}
          />
        </div>
      )}

      {/* Product Details Modal */}
      {isModalOpen && selectedProduct && (
        <PopupModal ref={modalRef} classname="max-w-2xl w-full">
          <ProductDetails
            product={selectedProduct}
            onClose={() => setIsModalOpen(false)}
          />
        </PopupModal>
      )}

      {/* Add Product Modals - Different approach for each department */}

      {/* Pharmacy and Hospital use PopupModal wrapper */}
      {isAddModalOpen &&
        (vendorProps.currentTab === INVENTROYENUM.PHARMACY ||
          vendorProps.currentTab === INVENTROYENUM.HOSPITAL) && (
          <PopupModal ref={addModalRef} classname="p-4 max-w-4xl w-full">
            <h3 className="text-[#4188f2] mb-4 text-lg font-medium">
              Add Product -{" "}
              {vendorProps.currentTab.charAt(0).toUpperCase() +
                vendorProps.currentTab.slice(1)}
            </h3>

            {vendorProps.currentTab === INVENTROYENUM.PHARMACY && (
              <AddPharmacyProduct
                onClose={() => setIsAddModalOpen(false)}
                showRate
              />
            )}

            {vendorProps.currentTab === INVENTROYENUM.HOSPITAL && (
              <AddHospitalProduct onClose={() => setIsAddModalOpen(false)} />
            )}
          </PopupModal>
        )}

      {/* Laboratory and Radiology render directly (they have built-in HeadingPopup) */}
      {isAddModalOpen &&
        vendorProps.currentTab === INVENTROYENUM.LABORATORY && (
          <AddLabProduct
            onClose={() => setIsAddModalOpen(false)}
            heading="Add Product"
          />
        )}

      {isAddModalOpen && vendorProps.currentTab === "RADIOLOGY" && (
        <AddRadiologyProduct onClose={() => setIsAddModalOpen(false)} />
      )}
    </div>
  );
};
