import{ba as G,a5 as s,bg as T,bh as V,av as r,a1 as X,a2 as t,a7 as Z,bi as v,a9 as z,aa as J,ac as o,ao as K}from"./index-ClX9RVH0.js";function W(){var p,h,m,u,c,l,g,d,x,b,f,y,I,k,j,C,w,R,H,S;const{id:E}=G(),[Q,F]=s.useState(!1),[D,O]=s.useState({temperature:"",weight:"",bp:"",height:"",heartRate:""}),B=T(),{data:e}=V(E??"");s.useEffect(()=>{O({temperature:r.get(e,"preCheckups.temperature"),weight:r.get(e,"preCheckups.weight"),bp:r.get(e,"preCheckups.bloodPressure"),height:r.get(e,"preCheckups.height"),heartRate:r.get(e,"preCheckups.heartRate")})},[e]);const i=X({initialValues:D,enableReinitialize:!0,onSubmit:async a=>{var A;F(!0);const N={date:new Date().toISOString().split("T")[0],user:(A=e==null?void 0:e.user)==null?void 0:A._id,preCheckups:{temperature:a.temperature,bloodPressure:a.bp,weight:a.weight,height:a.height,heartRate:a.heartRate},status:"PRE-CHECKED"};try{await B.mutateAsync({_id:e._id,entityData:N}),history.back()}catch(_){console.error("Error submitting form:",_)}}}),{handleSubmit:n}=i;return t.jsxs(t.Fragment,{children:[t.jsx(Z,{hideHeader:!0,listTitle:"General Precheckup"}),t.jsx(v,{name:((m=(h=(p=e==null?void 0:e.user)==null?void 0:p.commonInfo)==null?void 0:h.personalInfo)==null?void 0:m.fullName)??"N/A",patientId:((c=(u=e==null?void 0:e.user)==null?void 0:u.patientInfo)==null?void 0:c.patientId)??"",gender:((d=(g=(l=e==null?void 0:e.user)==null?void 0:l.commonInfo)==null?void 0:g.personalInfo)==null?void 0:d.gender)??"",location:((y=(f=(b=(x=e==null?void 0:e.user)==null?void 0:x.commonInfo)==null?void 0:b.contactInfo)==null?void 0:f.address)==null?void 0:y.currentAddress)??"",phone:((C=(j=(k=(I=e==null?void 0:e.user)==null?void 0:I.commonInfo)==null?void 0:k.contactInfo)==null?void 0:j.phone)==null?void 0:C.primaryPhone)??"",status:e==null?void 0:e.status,image:(R=(w=e==null?void 0:e.user)==null?void 0:w.identityInformation)!=null&&R.profileImage?(S=(H=e.user)==null?void 0:H.identityInformation)==null?void 0:S.profileImage:"selehttps://media.istockphoto.com/id/**********/photo/health-care-and-medical.jpg?s=612x612&w=0&k=20&c=SOVZXQByOX1k81ugrrn5DAIIl7yJAa0m2apxPbOZjAg="}),t.jsx(z,{value:i,children:t.jsx(J,{onSubmit:n,children:t.jsxs("div",{className:"flex flex-col gap-5",children:[t.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-5 bg-white py-8 px-5",children:[t.jsx(o,{type:"text",label:"Temperature",placeholder:"98c",name:"temperature"}),t.jsx(o,{type:"text",label:"BP",placeholder:"mmHg",name:"bp"}),t.jsx(o,{type:"text",label:"Weight",placeholder:"50",name:"weight"}),t.jsx(o,{type:"text",label:"Height",placeholder:"5ft",name:"height"}),t.jsx(o,{type:"text",label:"Heart Rate",placeholder:"80bpm",name:"heartRate"})]}),t.jsx(K,{onCancel:()=>{history.back()},onSubmit:n})]})})})]})}export{W as AddGeneralPreCheckupForm};
