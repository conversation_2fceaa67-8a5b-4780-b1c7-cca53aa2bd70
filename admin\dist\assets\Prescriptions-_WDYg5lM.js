import{ad as x,ae as m,a2 as e,b3 as a,ag as h,ah as j,a7 as g,cf as y,aj as k}from"./index-ClX9RVH0.js";const N=()=>{const s=x(),t={columns:[{title:"S.N.",key:"serialNumber"},{title:"Date ",key:"date"},{title:"Patient ",key:"patientName"},{title:"Doctor",key:"doctorName"},{title:"Case",key:"caseType"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:m.map(({tokenId:i,patientName:n,date:o,caseType:r,doctorName:l,status:c,treatment:d,serialNumber:p},u)=>({key:u,tokenid:i,patientName:n,serialNumber:p,caseType:r,date:o,doctorName:l,status:e.jsx(j,{status:c}),treatment:d,action:e.jsxs("button",{className:" py-2 px-4 rounded flex items-center justify-self-center gap-2 text-[#0D0D0D] ",onClick:()=>s(h.ENT),children:[e.jsx("span",{children:e.jsx(a,{icon:"simple-line-icons:note",width:20,height:20})}),e.jsx("span",{className:"text-[#D6DCE1] ",children:"| |"}),e.jsx("span",{children:e.jsx(a,{icon:"ph:trash-light",width:20,height:20})})]})}))};return e.jsxs("div",{children:[e.jsx(g,{listTitle:"Prescription List",hideHeader:!0}),e.jsx("div",{className:"my-5",children:e.jsx(y,{button:"Add Prescription",date:!0,navigateTo:"../add-prescription"})}),e.jsx(k,{columns:t.columns,rows:t.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})};export{N as default};
