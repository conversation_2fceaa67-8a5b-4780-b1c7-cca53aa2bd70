import{a2 as s,b3 as V,aY as fe,al as B,a5 as x,aP as ve,ct as Ne,aM as re,aE as je,bg as Ie,c6 as we,dz as Ae,dA as Te,bN as pe,bM as xe,bo as Ce,c7 as ue,aR as ie,cU as Me,dB as Fe,bO as Se,av as Ee,a9 as ge,c0 as U,a4 as Re,c4 as De,dC as Pe,a1 as Ve,aw as he,bw as be,di as Le,bK as He,aa as qe,ab as _e}from"./index-ClX9RVH0.js";import{f as Oe}from"./Utils-BMpLpMWy.js";const Be=({onClose:e,formik:h})=>{const{values:l}=h,b=()=>{try{if(!l.selectedMedicine||l.selectedMedicine.length===0){B.error("No medicines selected to hold transaction");return}const f=new Date().getHours(),T=new Date().getMinutes(),n={id:Date.now().toString(),timestamp:new Date().toISOString(),customerType:l.customerType,patientId:l.patientId,patientName:l.patientName||l.patient,tel:l.tel,address:l.address,selectedMedicine:l.selectedMedicine,totalAmount:l.totalAmount,subTotal:l.subTotal,tax:l.tax,discount:l.discount,notes:l.notes,time:`${f}:${T}`,reseller:l.reseller,patientAge:l.patientAge,patientGender:l.patientGender},p=localStorage.getItem("heldTransactions");let v=[];if(p)try{v=JSON.parse(p),Array.isArray(v)||(v=[])}catch(H){console.error("Error parsing existing transactions:",H),v=[]}v.push(n),localStorage.setItem("heldTransactions",JSON.stringify(v)),h.resetForm(),B.success("Transaction held successfully"),e()}catch(f){console.error("Error holding transaction:",f),B.error("Failed to hold transaction")}};return s.jsxs("div",{className:"flex flex-col gap-3 ",children:[s.jsxs("section",{className:"flex place-items-center justify-between",children:[s.jsx("p",{className:"text-base font-medium",children:"Hold Transaction"}),s.jsx(V,{icon:"material-symbols:close-rounded",onClick:e})]}),s.jsxs("section",{className:"flex flex-col gap-3",children:[s.jsxs("div",{className:"flex flex-col gap-1",children:[s.jsxs("p",{className:"text-[#47515C] text-sm",children:["Customer: ",h.values.customerType]}),s.jsxs("p",{className:"text-[#47515C] text-sm",children:["Item: ",h.values.selectedMedicine.length," | Total: Rs."," ",h.values.totalAmount]})]}),s.jsxs("div",{className:"flex flex-col gap-1",children:[s.jsx("label",{className:"text-gray-800 text-sm",children:"Notes: (Optional)"}),s.jsx(fe,{label:"",name:"notes",className:"border border-gray-300 rounded-md p-3 outline-none transition-all duration-200 resize-none focus:border-blue-500"})]})]}),s.jsxs("section",{className:"flex justify-between place-items-center gap-3",children:[s.jsx("button",{className:"border border-[#47515C] border-opacity-[.4] text-black p-2 rounded w-full",onClick:e,children:"Cancel"}),s.jsx("button",{className:"w-full bg-[#FF2323] text-white p-2 rounded",onClick:b,children:"Hold Transactions"})]})]})},$e=({formik:e,onClose:h})=>{const[l,b]=x.useState([]);x.useEffect(()=>{(()=>{try{const p=localStorage.getItem("heldTransactions");if(p){const v=JSON.parse(p);Array.isArray(v)&&b(v)}}catch(p){console.error("Error fetching held transactions:",p),B.error("Failed to load held transactions")}})()},[]);const f=n=>{try{console.log(n,"transaction"),e.setValues({...e.values,customerType:n.customerType,patientId:n.patientId||"",patientName:n.patientName||"",patient:n.patientName||"",tel:n.tel||"",address:n.address||"",selectedMedicine:n.selectedMedicine||[],totalAmount:n.totalAmount||"",subTotal:n.subTotal||"",tax:n.tax||"",discount:n.discount||"",reseller:n.reseller||"",patientAge:n.patientAge||"",patientGender:n.patientGender||""});const p=l.filter(v=>v.id!==n.id);localStorage.setItem("heldTransactions",JSON.stringify(p)),b(p),B.success("Transaction resumed successfully"),h()}catch(p){console.error("Error resuming transaction:",p),B.error("Failed to resume transaction")}},T=n=>{try{const p=l.filter(v=>v.id!==n);localStorage.setItem("heldTransactions",JSON.stringify(p)),b(p),B.success("Transaction deleted successfully")}catch(p){console.error("Error deleting transaction:",p),B.error("Failed to delete transaction")}};return s.jsxs("div",{className:"flex flex-col gap-3 max-h-96 overflow-y-auto",children:[s.jsxs("section",{className:"flex place-items-center justify-between",children:[s.jsxs("p",{className:"text-base font-medium",children:["Held Transactions (",l.length,")"]}),s.jsx(V,{icon:"material-symbols:close-rounded",onClick:h,className:"cursor-pointer"})]}),l.length===0?s.jsx("div",{className:"text-center py-8 text-gray-500",children:s.jsx("p",{children:"No held transactions found"})}):s.jsx("div",{className:"flex flex-col gap-3",children:l.map(n=>{var p,v;return s.jsxs("section",{className:"border border-[#47515C] border-opacity-[.4] flex flex-col gap-2 rounded p-2",children:[s.jsxs("div",{className:"flex place-items-center justify-between",children:[s.jsxs("section",{className:"flex place-items-center gap-3",children:[s.jsx("p",{className:"font-medium capitalize",children:(p=ye.find(H=>H.value===n.customerType))==null?void 0:p.label}),s.jsx("p",{className:"text-sm",children:n.patientName||n.patient}),n.tel&&s.jsxs("p",{className:"text-xs text-gray-600",children:["(",n.tel,")"]})]}),s.jsxs("section",{className:"flex place-items-center gap-2",children:[s.jsx("button",{className:"bg-[#319CFF] px-2 py-1 text-[12px] text-white rounded hover:bg-blue-600 transition-colors",onClick:()=>f(n),children:"Resume"}),s.jsx("button",{className:"bg-[#FF0000] px-2 py-1 text-white text-[12px] rounded hover:bg-red-600 transition-colors",onClick:()=>T(n.id),children:"Delete"})]})]}),s.jsxs("div",{className:"flex flex-col gap-1",children:[s.jsxs("p",{className:"text-sm text-[#47515C]",children:["Items: ",((v=n.selectedMedicine)==null?void 0:v.length)||0," | Total: Rs. ",n.totalAmount||"0"]}),s.jsxs("p",{className:"text-sm text-[#47515C]",children:["Held: ",Oe(n.timestamp)," - ",n.time]}),n.notes&&s.jsxs("p",{className:"text-xs text-gray-600 italic",children:["Note: ",n.notes]})]})]},n.id)})})]})},Ge=({formik:e})=>{var K,Y,J;const h=()=>`Bill-${Fe().slice(0,8)}`,[l,b]=x.useState({holdModal:!1,heldModal:!1}),f=x.useRef(null),T=x.useRef(null),[n,p]=x.useState(0),v=JSON.parse(localStorage.getItem("heldTransactions")||"[]"),H=ve(()=>b({heldModal:!1,holdModal:!1})),[le,D]=x.useState(),Z=x.useRef(null),k=Ne.useReactToPrint({contentRef:Z,pageStyle:`
        @page {
          size: A4;
          margin: 0.5in;
        }
        @media print {
          body {
            -webkit-print-color-adjust: exact;
          }
        }
      `}),ee=x.useCallback(t=>{var R,C,I;const r=(((R=t==null?void 0:t.item)==null?void 0:R.mrpRate)||0)*((t==null?void 0:t.quantity)||0),i=((C=t==null?void 0:t.item)==null?void 0:C.discount)||0,g=((I=t==null?void 0:t.item)==null?void 0:I.vat)||0;let o;const P=r*i/100;o=r-P;const j=o*(1+g/100);return Math.max(0,j)},[]),se=()=>{var t;return((t=e.values.selectedMedicine)==null?void 0:t.reduce((r,i)=>r+ee(i),0))||0},m=t=>{var r,i;return(r=t==null?void 0:t.item)!=null&&r.batchId?(i=t==null?void 0:t.item)==null?void 0:i.availableStock:0},c=(t,r,i)=>{const g=m(t);return r>g?(B.error(`Only ${g} units available in stock!`),!1):!0},N=se(),w=N*e.values.tax/100,L=N*e.values.discount/100,E=N-L+w,q=E-e.values.cashRecieved;x.useEffect(()=>{e.setFieldValue("totalAmount",E),e.setFieldValue("subTotal",E)},[e.values.selectedMedicine,e.values.discount]),x.useEffect(()=>{const t=()=>{if(f.current&&T.current){const i=f.current.offsetHeight,g=T.current.offsetTop,j=i-g-400-24;p(Math.max(200,j))}},r=setTimeout(t,100);return window.addEventListener("resize",t),()=>{clearTimeout(r),window.removeEventListener("resize",t)}},[]);const y=[{type:"number",field:"tax",minValidation:!0,placeholder:"tax (%)"},{type:"number",field:"discount",placeholder:"Discount (%)",minValidation:!0},{type:"number",field:"cashRecieved",placeholder:"0.00",minValidation:!0}],$=(t,r,i)=>{const g=Number(r==null?void 0:r.quantity);let o=0;if(g===0){e.setFieldValue(`selectedMedicine.${i}.quantity`,"");return}t==="plus"?o=g+1:o=g-1,!(isNaN(o)||o<=0)&&c(r,o)&&e.setFieldValue(`selectedMedicine.${i}.quantity`,o)},u=re({user:e.values.patientId,isActive:"1"}),{data:a}=je(u),{mutateAsync:A}=Ie(),{mutateAsync:F}=we(),{mutateAsync:S}=Ae(),z=async()=>{var R,C,I,M,ae;const t=h();D(d=>({...d,invoiceNo:t})),setTimeout(()=>{k()},100);const r=e.values.cashRecieved==0?"PENDING":e.values.cashRecieved==e.values.totalAmount?"PAID":"PARTIALLY-PAID",i=(R=e.values.selectedMedicine)==null?void 0:R.map(d=>{var X,de,ne,ce,te,oe;const O=((X=d==null?void 0:d.item)==null?void 0:X.mrpRate)*(d==null?void 0:d.quantity);return(de=d==null?void 0:d.item)==null||de.discount,{pProduct:(ne=d==null?void 0:d.item)==null?void 0:ne.productId,pBatch:(ce=d==null?void 0:d.item)==null?void 0:ce.batchId,quantity:(d==null?void 0:d.quantity)??1,discount:((te=d==null?void 0:d.item)==null?void 0:te.discount)??0,tax:((oe=d==null?void 0:d.item)==null?void 0:oe.vat)??0,totalAmount:O}}),g=i.reduce((d,O)=>d+(O==null?void 0:O.totalAmount),0),o={date:new Date().toISOString().split("T")[0],inventoryFor:xe.PHARMACY,category:pe.SALE,discount:Number(e.values.discount),paidAmount:Number(e.values.paidAmount),totalAmount:Number(g),remarks:e.values.remarks,paymentStatus:r,subTotal:Number(e.values.subTotal),paymentMethod:e.values.paymentMethod,dueAmount:Number(e.values.totalAmount-e.values.paidAmount),productList:i,invoiceNo:t||h(),isActive:e.values.patientId?!0:r!=="PAID"};if(e.values.supervisor&&(o.supervisor=e.values.supervisor),e.values.customerType!=="new"&&(o.billingAgainst=e.values.patientId),e.values.customerType==="new"){const d={name:e.values.patient,contact:e.values.phoneNo};o.walkInCustomer=d,e.values.reseller&&(o.reseller=e.values.reseller)}e.values.paymentMethod==="BANK"&&(o.bank=e.values.bank),e.values.currentInvoiceId?(await S({_id:e.values.currentInvoiceId,entityData:o}),e.resetForm()):(await F(o),e.resetForm());const P=e.values.selectedMedicine,j=(I=(C=a==null?void 0:a.data)==null?void 0:C.patientHistory[0])==null?void 0:I.prescriptionDetails[0];if(j&&(j!=null&&j.prescriptionList)){let d=!1;P.forEach(O=>{var ne,ce;const X=((ne=O.item)==null?void 0:ne._id)||((ce=O.item)==null?void 0:ce.productId);j.prescriptionList.find(te=>{var oe;return((oe=te==null?void 0:te.prescribedMedicine)==null?void 0:oe._id)===X})&&(d=!0)}),d&&(j.status="COMPLETED")}e.values.customerType!=="new"&&e.values.patientId&&await A({_id:(ae=(M=a==null?void 0:a.data)==null?void 0:M.patientHistory[0])==null?void 0:ae._id,entityData:{prescriptionDetails:[j]}}),e.resetForm()};x.useEffect(()=>{var g;const t=e.values.cashRecieved==0?"PENDING":e.values.cashRecieved==e.values.totalAmount?"PAID":"PARTIALLY-PAID",r=(g=e.values.selectedMedicine)==null?void 0:g.map(o=>{var P,j,R,C,I;return{pProduct:{name:(P=o==null?void 0:o.item)==null?void 0:P.product,productCategory:(j=o==null?void 0:o.item)==null?void 0:j.productCategory,strength:(R=o==null?void 0:o.item)==null?void 0:R.strength},pBatch:{batchNo:(C=o==null?void 0:o.item)==null?void 0:C.batch,expiryDate:(I=o==null?void 0:o.item)==null?void 0:I.expiryDate},quantity:o==null?void 0:o.quantity,discount:L,totalAmount:Te(o)}}),i={date:new Date().toISOString().split("T")[0],inventoryFor:xe.PHARMACY,category:pe.SALE,discount:Number(e.values.discount),paidAmount:Number(e.values.paidAmount),totalAmount:Number(e.values.totalAmount),subTotal:Number(e.values.subTotal),remarks:e.values.remarks,paymentStatus:t,age:e.values.patientAge,paymentMethod:e.values.paymentMethod,payableAmount:e.values.totalAmount,dueAmount:Number(e.values.totalAmount-e.values.cashRecieved),supervisor:e.values.supervisor,productList:r,tax:Number(e.values.taxValue),isActive:e.values.patientId?!0:t!=="PAID"};if(e.values.customerType==="new"){const o={patientInfo:{patientId:e.values.patientRegisteredId},commonInfo:{personalInfo:{fullName:e.values.patient,gender:"N/A"},contactInfo:{phone:{primaryPhone:e.values.tel},address:{currentAddress:e.values.address}}}};i.billingAgainst=o}if(e.values.customerType!=="new"){const o={commonInfo:{personalInfo:{fullName:e.values.patientName,gender:e.values.patientGender},contactInfo:{phone:{primaryPhone:e.values.tel},address:{currentAddress:e.values.address}}}};i.billingAgainst=o}e.values.paymentMethod==="BANK"&&(i.bank=e.values.bank),console.log(i,"form"),D(i)},[e.values.paymentMethod,e.values.paidAmount,e.values.totalAmount]);const{data:_}=Ce(),G=(K=_==null?void 0:_.data)==null?void 0:K.banks,W=G==null?void 0:G.map(t=>({value:t._id??"",label:t.bankName,extra:t.accountNumber})),Q=[{type:"multi-search",field:"bank",label:"Bank Name",required:!0,options:W},{type:"text",field:"accountNo",label:"Account Number",required:!0}];return s.jsxs("div",{ref:f,className:"flex flex-col h-full bg-white px-3 py-2 gap-3 shadow rounded",children:[s.jsxs("section",{className:"flex justify-between place-items-center",children:[s.jsx("p",{className:"text-base font-medium",children:"Cart item"}),s.jsxs("div",{className:"flex place-items-center gap-3",children:[s.jsxs("section",{className:"flex gap-1 place-items-center border border-[#EEF8FF] py-1 px-[6px] rounded cursor-pointer",onClick:()=>b({...l,heldModal:!0}),children:[s.jsx(V,{icon:"clarity:clipboard-line"}),s.jsxs("p",{children:["Held (",v==null?void 0:v.length,")"]})]}),s.jsxs("section",{className:"flex gap-1 place-items-center border border-[#FFECD5] py-1 px-[6px] rounded cursor-pointer",onClick:()=>b({...l,holdModal:!0}),children:[s.jsx(V,{icon:"bi:pause-btn-fill",className:"text-primary"}),s.jsx("p",{children:"Hold"})]}),s.jsx("button",{onClick:()=>e.setFieldValue("selectedMedicine",[]),children:"Clear All"})]})]}),s.jsx("section",{ref:T,className:"flex flex-col overflow-y-auto gap-2",style:{maxHeight:n>0?`${n}px`:"250px"},children:(J=(Y=e==null?void 0:e.values)==null?void 0:Y.selectedMedicine)==null?void 0:J.map((t,r)=>{var i,g,o,P,j,R;return s.jsxs("div",{className:"flex justify-between place-items-center rounded border border-[#47515C] border-opacity-[.2] hover:border-opacity-[.5] cursor-pointer py-2 px-3",children:[s.jsxs("div",{className:"flex flex-col ",children:[s.jsxs("p",{className:"font-semibold text-[12px] flex ",children:[(i=t.item)==null?void 0:i.product," ",(g=t.item)==null?void 0:g.strength]}),s.jsxs("p",{className:"text-[#47515C] text-[12px] font-medium",children:["Batch No: ",(o=t==null?void 0:t.item)==null?void 0:o.batch]}),s.jsxs("p",{className:"text-[#47515C] text-[12px] font-medium",children:["Exp. Date: ",(P=t.item)==null?void 0:P.expiryDate]})]}),s.jsxs("div",{className:"flex gap-6 place-items-center",children:[s.jsxs("p",{className:"text-sm",children:["Rs. ",(j=t.item)==null?void 0:j.mrpRate]}),s.jsxs("div",{className:"flex place-items-center gap-1",children:[s.jsx(V,{icon:"ic:baseline-remove",onClick:()=>$("minus",t,r)}),s.jsx("input",{type:"text",className:"border rounded-full w-[28px] text-center shadow-md ",min:1,max:m(t),value:(t==null?void 0:t.quantity)===0?"":t.quantity,onChange:C=>{const I=C.target.value;if(I===""){e.setFieldValue(`selectedMedicine.${r}.quantity`,"");return}const M=Number.parseInt(I);isNaN(M)||M<=0||(c(t,M)?e.setFieldValue(`selectedMedicine.${r}.quantity`,M):C.target.value=t.quantity.toString())},onBlur:C=>{const I=C.target.value;if(I===""||I==="0"){e.setFieldValue(`selectedMedicine.${r}.quantity`,1);return}const M=Number.parseInt(I);isNaN(M)||M<=0?e.setFieldValue(`selectedMedicine.${r}.quantity`,1):c(t,M)||e.setFieldValue(`selectedMedicine.${r}.quantity`,Math.min(t.quantity,m(t)))}}),s.jsx(V,{icon:"ic:baseline-add",onClick:()=>$("plus",t,r)})]}),s.jsxs("p",{className:"text-sm",children:["Rs. ",((R=t.item)==null?void 0:R.mrpRate)*t.quantity]}),s.jsx(V,{icon:"ic:baseline-delete",className:"text-red",onClick:()=>{const C=e.values.selectedMedicine;e.setFieldValue("selectedMedicine",C.filter(I=>{var M,ae;return((M=I.item)==null?void 0:M._id)!==((ae=t==null?void 0:t.item)==null?void 0:ae._id)}))}})]})]},r)})}),s.jsx("hr",{className:"bg-red"}),s.jsx("section",{className:"flex flex-col",children:s.jsx("p",{className:"text-base font-medium",children:"Payment methods"})}),s.jsxs("section",{className:"flex place-items-center justify-between gap-3",children:[s.jsxs("button",{className:`rounded ${e.values.paymentMethod==="CASH"?"bg-primary text-white":"bg-[#F9FAFB] text-[#374151]"} w-full flex place-items-center gap-2  justify-center p-2`,onClick:()=>e.setFieldValue("paymentMethod","CASH"),children:[s.jsx(V,{icon:"mingcute:cash-line",className:"text-green"}),s.jsx("p",{children:"Cash"})]}),s.jsxs("button",{className:`rounded ${e.values.paymentMethod==="BANK"?"bg-primary text-white":"bg-[#F9FAFB] text-[#374151]"}  border border-[#47515C] border-opacity-[.2] flex w-full p-2 place-items-center gap-2  justify-center`,onClick:()=>e.setFieldValue("paymentMethod","BANK"),children:[s.jsx(V,{icon:"circum:bank"}),s.jsx("p",{children:"Bank"})]})]}),e.values.paymentMethod==="BANK"&&s.jsx("section",{className:"grid grid-cols-2 mt-2 gap-4 transition-all duration-300 ease-in-out opacity-100 translate-y-0 animate-in slide-in-from-top-1 fade-in",id:"bank-details",children:s.jsx(ue,{formDatails:Q,getFieldProps:e.getFieldProps,errors:e.errors,touched:e.touched,onValueChange:(t,r)=>{if(t==="bank"){const i=W.find(g=>g.value===r);e.setFieldValue("bank",r),i&&e.setFieldValue("accountNo",i.extra)}}})}),s.jsxs("section",{className:"flex flex-col gap-4 bg-[#F9FAFB] p-3",children:[s.jsxs("div",{className:"grid grid-cols-4  ",children:[s.jsx("p",{className:"text-base font-medium flex place-items-center ",children:"Cash Received:"}),s.jsx("section",{className:"grid grid-cols-3 col-span-3  gap-1",children:s.jsx(ue,{formDatails:y,getFieldProps:e.getFieldProps})})]}),s.jsx("hr",{className:"bg-red"}),s.jsxs("section",{className:"flex place-items-center justify-between",children:[s.jsx("p",{children:"Change:"}),s.jsxs("p",{children:["Rs. ",Math.abs(Number(q.toFixed(2)))," ",q&&q<0?"To Return":""]})]})]}),s.jsxs("section",{className:"flex place-items-center justify-between bg-[#D9EFFF] p-3 rounded",children:[s.jsx("p",{children:"Total:"}),s.jsxs("p",{children:["Rs. ",E.toFixed(2)]})]}),s.jsx("button",{className:"bg-primary text-white p-3 rounded",onClick:z,children:"Confirm Purchase"}),l.holdModal&&s.jsx(ie,{ref:H,classname:"p-6 w-[400px]",children:s.jsx(Be,{onClose:()=>b({...l,holdModal:!1}),formik:e})}),l.heldModal&&s.jsx(ie,{ref:H,classname:"p-6 w-[450px]",children:s.jsx($e,{onClose:()=>b({...l,heldModal:!1}),formik:e})}),s.jsx("section",{className:"hidden",children:s.jsx(Me,{ref:Z,invoiceData:le})})]})},Ue=({item:e,onClick:h})=>{var l,b,f,T,n;return s.jsxs("div",{className:"flex flex-col shadow-md p-3 rounded-md   gap-3 cursor-pointer",onClick:()=>h==null?void 0:h(e),children:[s.jsxs("section",{className:"flex w-full justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-semibold",children:(l=e==null?void 0:e.product)==null?void 0:l.name}),s.jsx("p",{children:(f=(b=e==null?void 0:e.product)==null?void 0:b.drug)==null?void 0:f.strength})]}),((T=e==null?void 0:e.product)==null?void 0:T.prescriptionRequired)&&s.jsx("p",{className:"bg-[#FFF1F1] rounded text-red px-2 h-fit",children:"Rx"})]}),s.jsxs("section",{className:"flex flex-col gap-2",children:[s.jsx("p",{children:(n=e==null?void 0:e.product)==null?void 0:n.name}),s.jsxs("div",{className:"flex flex-col",children:[s.jsxs("p",{className:"text-[#47515C] text-[12px] font-medium",children:["Batch No: ",e==null?void 0:e.batchNo]}),s.jsxs("p",{className:"text-[#47515C] text-[12px] font-medium",children:["Exp. Date: ",e==null?void 0:e.expiryDate]})]})]}),s.jsxs("section",{className:"flex justify-between place-items-center",children:[s.jsxs("p",{className:"text-primaryBlue",children:["Rs. ",e==null?void 0:e.mrpRate]}),s.jsxs("p",{className:"text-[#47515C]",children:["Stock: ",e==null?void 0:e.availableStock]})]})]})},me=({formik:e})=>{var u;const h=x.useRef(null),l=x.useRef(null),[b,f]=x.useState(!1),T=ve(()=>f(!1)),n=x.useRef(null),[p,v]=x.useState(0),[H,le]=x.useState(0),[D,Z]=x.useState(!1),[k,ee]=x.useState(!1),[se,m]=x.useState(""),c=re({search:se,limit:1e6,productCategory:e.values.productCategory}),{data:N}=Se(c),w=(u=N==null?void 0:N.data)==null?void 0:u.medicalProductsInventory,L=[{label:"All",value:"all"},{label:"Medicine",value:U.DRUG},{label:"Medical Device",value:U.DEVICES},{label:"Skin Product",value:U.BEAUTIESSKINCARE},{label:"Medical Supplies",value:U.MEDICALSUPPLIES},{label:"Lab Equipment",value:U.LABTEST},{label:"Other",value:U.OTHER}],E=()=>{if(n.current){const{scrollLeft:a,scrollWidth:A,clientWidth:F}=n.current;Z(a>0),ee(a+F+2<A)}},q=()=>{n.current&&n.current.scrollBy({left:-200,behavior:"smooth"})},y=()=>{n.current&&n.current.scrollBy({left:200,behavior:"smooth"})};x.useEffect(()=>{const a=()=>{if(h.current){const S=h.current.offsetWidth-24;v(S)}};return a(),window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)},[]);const $=Ee.debounce(a=>m(a),600);return x.useEffect(()=>{const a=n.current;if(a)return setTimeout(E,100),a.addEventListener("scroll",E),()=>{a.removeEventListener("scroll",E)}},[]),x.useEffect(()=>{const a=()=>{if(h.current&&l.current){const F=window.innerHeight,S=l.current.offsetTop,_=F-S-100;le(Math.max(300,_))}},A=setTimeout(a,100);return window.addEventListener("resize",a),()=>{clearTimeout(A),window.removeEventListener("resize",a)}},[]),s.jsxs("div",{ref:h,className:"flex flex-col  bg-white px-3 py-2   gap-3 shadow rounded ",children:[s.jsxs("section",{className:"flex justify-between place-items-center gap-4 ",children:[s.jsxs("div",{className:"w-[80%] flex place-items-center gap-2  shadow-md  outline-none rounded",children:[s.jsx(V,{icon:"ic:baseline-search",className:"px-1 text-3xl text-[#47515C]"}),s.jsx(ge,{value:e,children:s.jsx("input",{className:" outline-none p-2",placeholder:"Search",value:e.values.search,onChange:a=>{e.setFieldValue("search",a.target.value),$(a.target.value)}})})]}),s.jsxs("button",{className:"bg-[#319CFF]  flex gap-1 place-items-center text-white rounded p-2",onClick:()=>f(!0),children:[s.jsx(V,{icon:"ic:round-add",className:"text-white text-sm"}),s.jsx("p",{children:"Products"})]})]}),s.jsxs("section",{className:"relative",children:[s.jsx("div",{ref:n,className:"flex shadow-md rounded place-items-center overflow-x-auto w-full gap-6 p-4 scrollbar-hide",style:{maxWidth:p>0?`${p}px`:"100%"},children:L.map(a=>s.jsx("button",{className:`text-sm whitespace-nowrap flex-shrink-0 font-mediumtext-[#47515C] transition-colors ${a.value===e.values.productCategory?"border-b-2 border-[#1652B7]":""}`,onClick:()=>e.setFieldValue("productCategory",a.value),children:a.label},a.value))}),D&&s.jsx("button",{onClick:q,className:"absolute left-0 top-1/2 transform -translate-y-1/2 bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg hover:bg-blue-600 transition-colors z-10",children:s.jsx(V,{icon:"ic:round-chevron-left",className:"text-lg"})}),k&&s.jsx("button",{onClick:y,className:"absolute right-0 top-1/2 transform -translate-y-1/2 bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg hover:bg-blue-600 transition-colors z-10",children:s.jsx(V,{icon:"ic:round-chevron-right",className:"text-lg"})})]}),s.jsx("section",{ref:l,className:"grid grid-cols-3 gap-3 overflow-y-auto",id:"medicineList",style:{maxHeight:H>0?`${H}px`:"400px"},children:w==null?void 0:w.map(a=>s.jsx(Ue,{item:a,onClick:()=>{var z,_,G,W,Q,K,Y,J,t,r,i,g,o,P,j,R,C,I,M;const A=(z=e==null?void 0:e.values)==null?void 0:z.selectedMedicine,F=((_=a==null?void 0:a.product)==null?void 0:_.productCategory)===U.DRUG&&((W=(G=a==null?void 0:a.product)==null?void 0:G.drug)==null?void 0:W.form),S=((Q=a==null?void 0:a.product)==null?void 0:Q.productCategory)===U.DRUG?(Y=(K=a==null?void 0:a.product)==null?void 0:K.drug)==null?void 0:Y.strength:((J=a==null?void 0:a.product)==null?void 0:J.productCategory)===U.BEAUTIESSKINCARE?(r=(t=a==null?void 0:a.product)==null?void 0:t.beautySkinCare)==null?void 0:r.volume:"-";if((a==null?void 0:a.availableStock)<=0)return B.info("No stock available");if((A==null?void 0:A.length)>0){if(A.some(d=>{var O,X;return((O=d.item)==null?void 0:O._id)===((X=a.product)==null?void 0:X._id)}))return B.info("Selected medicine already exists, try by removing it");e.setFieldValue("selectedMedicine",[...A,{quantity:1,item:{batchId:(i=a==null?void 0:a.product)==null?void 0:i._id,product:(g=a==null?void 0:a.product)==null?void 0:g.name,productCategory:(o=a==null?void 0:a.product)==null?void 0:o.productCategory,expiryDate:a==null?void 0:a.expiryDate,strength:S,availableStock:a==null?void 0:a.availableStock,batch:a==null?void 0:a.batchNo,productId:(P=a==null?void 0:a.product)==null?void 0:P._id,_id:(j=a==null?void 0:a.product)==null?void 0:j._id,fromInvoice:!1,mrpRate:a==null?void 0:a.mrpRate,medicineForm:F}}])}e.setFieldValue("selectedMedicine",[...A,{quantity:1,item:{product:(R=a==null?void 0:a.product)==null?void 0:R.name,batch:a==null?void 0:a.batchNo,productId:(C=a==null?void 0:a.product)==null?void 0:C._id,expiryDate:a==null?void 0:a.expiryDate,availableStock:a==null?void 0:a.availableStock,productCategory:(I=a==null?void 0:a.product)==null?void 0:I.productCategory,strength:S,_id:(M=a==null?void 0:a.product)==null?void 0:M._id,batchId:a==null?void 0:a._id,fromInvoice:!1,mrpRate:a==null?void 0:a.mrpRate,medicineForm:F}}])}},a==null?void 0:a._id))}),b&&s.jsxs(ie,{ref:T,classname:"p-4 max-w-[850px] w-full",children:[s.jsx(Re,{as:"h3",size:"body-md-default",className:" text-[#4188f2] mb-2",children:"Add Product"}),s.jsx(De,{onClose:()=>f(!1),navigate:!0})]})]})},ze=e=>{if(!e||e.length===0)return null;const h=e.filter(b=>b.availableStock>0&&b.expiryDate&&new Date(b.expiryDate)>new Date);return h.length===0?null:h.reduce((b,f)=>{const T=new Date(b.expiryDate);return new Date(f.expiryDate)<T?f:b})},ye=[{value:"new",label:"Walk in Customer"},{value:"IPD",label:"In-patients"},{value:"OPD",label:"Out-patients"},{value:"EMERGENCY",label:"Emergency"}],Ye=()=>{var k,ee,se,m;const{toggleSidebar:e,miniSidebar:h}=Pe();x.useEffect(()=>{h&&e()},[]);const l=Ve({initialValues:{customerType:"new",productCategory:"all",paymentMethod:"CASH",search:"",tel:"",address:"",patient:"",reseller:"",patientId:"",patientName:"",patientRegisteredId:"",patientAge:"",patientGender:"",supervisor:"",selectedMedicine:[],totalAmount:"",subTotal:"",paidAmount:"",remarks:"",taxValue:"",invoiceNo:"",bank:"",pan:"",accountNo:"",currentInvoiceId:"",notes:"",tax:"",discount:"",total:"",due:"",cashRecieved:""},onSubmit:c=>{}}),b=l.values.customerType==="new",f=re({role:be.PATIENT,...b?{}:{"commonInfo.ipdOpd":l.values.customerType}}),{data:T}=he(f),{data:n}=he({role:be.RESELLER},{enabled:l.values.customerType==="new"}),p=(ee=(k=n==null?void 0:n.data)==null?void 0:k.users)==null?void 0:ee.map(c=>{var N,w;return{value:c._id,label:`${(w=(N=c==null?void 0:c.commonInfo)==null?void 0:N.personalInfo)==null?void 0:w.fullName}`}}),v=(m=(se=T==null?void 0:T.data)==null?void 0:se.users)==null?void 0:m.map(c=>{var N,w,L,E,q,y,$,u,a,A,F,S,z,_,G,W,Q,K,Y,J;return{value:c._id,label:`${(N=c==null?void 0:c.patientInfo)==null?void 0:N.patientId} - ${(L=(w=c==null?void 0:c.commonInfo)==null?void 0:w.personalInfo)==null?void 0:L.fullName} `,name:(q=(E=c==null?void 0:c.commonInfo)==null?void 0:E.personalInfo)==null?void 0:q.fullName,contact:($=(y=c==null?void 0:c.commonInfo)==null?void 0:y.contactInfo)==null?void 0:$.phone,address:((A=(a=(u=c==null?void 0:c.commonInfo)==null?void 0:u.contactInfo)==null?void 0:a.address)==null?void 0:A.currentAddress)||((z=(S=(F=c==null?void 0:c.commonInfo)==null?void 0:F.contactInfo)==null?void 0:S.address)==null?void 0:z.permanentAddress),gender:(G=(_=c==null?void 0:c.commonInfo)==null?void 0:_.personalInfo)==null?void 0:G.gender,phone:(K=(Q=(W=c==null?void 0:c.commonInfo)==null?void 0:W.contactInfo)==null?void 0:Q.phone)==null?void 0:K.primaryPhone,age:Le(((J=(Y=c==null?void 0:c.commonInfo)==null?void 0:Y.personalInfo)==null?void 0:J.dob)??"")}}),H=l.values.patientId,le=re({...H?{billingAgainst:l.values.patientId,inventoryFor:"PHARMACY",category:"SALE",paymentStatus:"PENDING"}:{}}),{data:D}=He(le),Z=[{type:"multi-search",field:"patientId",label:"Patient ID",required:!0,isVisible:l.values.customerType!=="new",options:v},{type:"text",field:"patientName",label:"Patient Name",required:!0,isVisible:l.values.customerType!=="new"},{type:"text",field:"patient",label:"Patient Name",required:!0,isVisible:l.values.customerType==="new"},{type:"text",field:"doctor",label:"Prescribed By",isVisible:l.values.customerType!=="new"},{type:"text",field:"nmc",label:"NMC",isVisible:l.values.customerType!=="new"},{type:"text",field:"tel",label:"Telephone",required:!0},{type:"text",field:"pan",label:"PAN"},{type:"text",field:"address",label:"Address",required:!0},{type:"multi-search",field:"reseller",label:"Referral",isVisible:l.values.customerType==="new",options:p}];return x.useEffect(()=>{var c,N,w,L,E,q;if(l.values.patientId&&D&&((N=(c=D==null?void 0:D.data)==null?void 0:c.invoices)==null?void 0:N.length)>0){const y=(L=(w=D==null?void 0:D.data)==null?void 0:w.invoices)==null?void 0:L[0];if(l.setFieldValue("currentInvoiceId",y==null?void 0:y._id),y!=null&&y.supervisor&&l.setFieldValue("supervisor",y==null?void 0:y.supervisor),((E=y==null?void 0:y.productList)==null?void 0:E.length)>0){const $=(q=y==null?void 0:y.productList)==null?void 0:q.map(u=>{var A,F,S;const a=ze((u==null?void 0:u.batches)??[]);return{quantity:(u==null?void 0:u.quantity)??1,item:{expiryDate:a==null?void 0:a.expiryDate,availableStock:a==null?void 0:a.availableStock,fromInvoice:!0,mrpRate:a==null?void 0:a.mrpRate,batchId:a==null?void 0:a._id,batch:a==null?void 0:a.batchNo,product:((A=u==null?void 0:u.pProduct)==null?void 0:A.name)??"",productId:((F=u==null?void 0:u.pProduct)==null?void 0:F._id)??"",_id:(S=u==null?void 0:u.pProduct)==null?void 0:S._id,doses:u==null?void 0:u.doses,frequency:u==null?void 0:u.frequency,duration:u==null?void 0:u.duration}}});l.setFieldValue("selectedMedicine",$)}}},[D,l.values.patientId]),s.jsx("div",{className:"flex flex-col mt-2",children:s.jsx(ge,{value:l,children:s.jsxs(qe,{className:"grid grid-cols-2 gap-4",children:[s.jsx("section",{className:"col-span-2 shadow rounded flex bg-white px-2 pt-1 pb-1",children:s.jsx(_e,{options:ye,value:l.values.customerType,firstInput:"Select customer",onChange:c=>l.setFieldValue("customerType",c.target.value)})}),s.jsx("section",{className:"col-span-2 bg-white px-3 py-2 grid grid-cols-4 gap-4 shadow rounded",children:s.jsx(ue,{formDatails:Z,getFieldProps:l.getFieldProps,errors:l.errors,touched:l.touched,onValueChange:(c,N)=>{if(c==="patientId"){const w=v.find(L=>L.value===N);l.setFieldValue("patientId",N),w&&(l.setFieldValue("patientName",w.name),l.setFieldValue("tel",w.phone),l.setFieldValue("address",w.address))}c==="reseller"&&l.setFieldValue("reseller",N)}})}),s.jsxs("section",{className:"grid grid-cols-2 gap-3 col-span-2",children:[s.jsx(Ge,{formik:l}),s.jsx(me,{formik:l})]})]})})})};export{Ye as PharmacyPosPage,ye as customerOptions};
