import React from "react";

interface BankDetailsProps {
  bankDetails: {
    accountHolderName?: string;
    bankName?: string;
    accountNumber?: string;
    mobileLinkedToBank?: string;
    taxId?: string;
    panNumber?: string;
    basicSalary?: string;
    tdsApplicable?: boolean;
  } | null;
}

const BankDetails: React.FC<BankDetailsProps> = ({ bankDetails }) => {
  if (!bankDetails) {
    return (
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="bg-blue-50 px-4 py-3 border-b">
          <h3 className="text-base font-semibold text-blue">Bank Details</h3>
        </div>
        <div className="p-4">
          <p className="text-gray-500 text-sm">No bank details available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
      {/* Header */}
      <div className="bg-blue-50 px-4 py-3 border-b">
        <h3 className="text-base font-semibold text-blue">Bank Details</h3>
      </div>

      <div className="p-4">
        <div className="space-y-3">
          {bankDetails.accountHolderName && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">
                Account Holder Name
              </span>
              <span className="text-sm text-gray-600">
                {bankDetails.accountHolderName}
              </span>
            </div>
          )}

          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900">Bank Name</span>
            <span className="text-sm text-gray-600">
              {bankDetails.bankName || "N/A"}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-900">
              Account Number
            </span>
            <span className="text-sm text-gray-600">
              {bankDetails.accountNumber || "N/A"}
            </span>
          </div>

          {bankDetails.panNumber && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">
                PAN Number
              </span>
              <span className="text-sm text-gray-600">
                {bankDetails.panNumber}
              </span>
            </div>
          )}

          {bankDetails.basicSalary && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">
                Basic Salary
              </span>
              <span className="text-sm text-gray-600">
                {bankDetails.basicSalary}
              </span>
            </div>
          )}

          {bankDetails.tdsApplicable !== undefined && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">
                TDS Applicable
              </span>
              <span className="text-sm text-gray-600">
                {bankDetails.tdsApplicable ? "Yes" : "No"}
              </span>
            </div>
          )}

          {bankDetails.mobileLinkedToBank && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">
                Mobile (Linked to Bank)
              </span>
              <span className="text-sm text-gray-600">
                {bankDetails.mobileLinkedToBank}
              </span>
            </div>
          )}

          {bankDetails.taxId && (
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">
                Tax ID (PAN)
              </span>
              <span className="text-sm text-gray-600">{bankDetails.taxId}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BankDetails;
