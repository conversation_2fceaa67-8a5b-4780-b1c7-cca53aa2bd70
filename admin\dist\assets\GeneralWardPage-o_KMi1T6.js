import{a5 as n,aD as M,ax as W,ad as O,aE as B,av as a,a2 as t,aj as V,aF as Y,aG as _,ai as U,aH as $,aI as z,aJ as J,aK as K,af as q,aL as f,ag as p,ah as Q}from"./index-ClX9RVH0.js";import{C as X}from"./Svg-BMTGOzwv.js";import{T as Z}from"./TransferPatient-qcC_AtDM.js";const ne=()=>{const[w,A]=n.useState(""),[g,h]=n.useState({state:!1,patientId:""}),T=n.useRef(null),{contentRef:E,exportToPDF:I,exportToExcel:N,handlePrint:P}=M(),[r,S]=n.useState("PATIENT"),[b,R]=n.useState(""),[l,G]=n.useState(""),{data:y}=W(),[i,D]=n.useState({page:1,limit:10}),c=O(),{data:u,isLoading:k}=B({page:i.page,limit:i.limit,...b.trim()&&{search:b.trim()},...l&&l!=="all"&&{gender:l.toUpperCase()},ipdOpd:"IPD",isActive:1}),d=a.get(u,"data.patientHistory",[]),m=a.get(u,"data.pagination",{}),j=({page:s,limit:o})=>{D(e=>({page:s??(o?1:e.page),limit:o??e.limit}))};n.useEffect(()=>{j({page:1})},[r]);const F={"Total Patients":{value:a.get(u,"data.pagination.total",0),icon:"mdi:account-group",iconColor:"#3B82F6"},Wards:{value:a.get(y,"data.wardCategory.length",0),icon:"mdi:hospital-building",iconColor:"#10B981"}};console.log(d,"usersss");const C={rows:(()=>{if(!d||!d.length)return[];const s=e=>t.jsx(q,{onShow:()=>c(`${p.PATIENTDETAIL}/${e}`),onMore:()=>{},onMoreList:[{title:"Transfer Bed",onClick:()=>{f(e),c(p.TRANSFER_GENERALWARD)},index:1},{title:"Transfer Hospital",onClick:()=>h({state:!0,patientId:e}),index:4},{title:"Treatment",onClick:()=>{f(e),c(p.TREATMENT_GENERALWARD)},index:2},{title:"Surgery",onClick:()=>{f(e),c(p.SURGERY_GENERALWARD)},index:3}]}),o=e=>t.jsx(Q,{status:e});switch(r){case"PATIENT":return d.map((e,v)=>({key:v.toString(),sno:(i.page-1)*i.limit+v+1,date:a.get(e,"date","-"),patinetId:a.get(e,"user.patientInfo.patientId","-"),patientName:a.get(e,"user.commonInfo.personalInfo.fullName","-"),gender:a.get(e,"user.commonInfo.personalInfo.gender","-"),contactNo:a.get(e,"user.commonInfo.contactInfo.phone.primaryPhone","-"),admission:K(a.get(e,"date","")).format("MMM-DD-YYYY"),status:a.get(e,"isActive")?o("ACTIVE"):o("DISCHARGE"),action:s(a.get(e,"user._id",""))}));default:return[]}})()},H={PATIENT:t.jsx(V,{ref:E,loading:k,columns:Y.PATIENT,rows:C.rows,pagination:{totalPage:m.pages||1,currentPage:m.page||1,limit:m.limit||10,onClick:j}})},x=n.useCallback(a.debounce(s=>{R(s)},500),[]);n.useEffect(()=>()=>{x.cancel()},[x]);const L=s=>{G(s)};return t.jsxs("div",{className:"space-y-1",children:[t.jsx(_,{title:"",services:["General Checkup","Cardiology","Root Canal","Scaling & Cleaning","Braces & Aligners","Pediatrics","Orthopedics"],icon:t.jsx(X,{}),statsObject:F}),t.jsxs("div",{className:"bg-white rounded-small",children:[t.jsxs("div",{className:"flex items-center justify-between px-4 pt-0.5",children:[t.jsx(U,{tabs:["PATIENT"],defaultTab:r,onTabChange:s=>S(s)}),t.jsxs("div",{className:"flex items-center flex-row gap-4",children:[t.jsxs("div",{className:"relative flex items-center w-full sm:w-[14rem] md:w-[16rem] lg:w-[16rem]",children:[t.jsx("input",{type:"text",placeholder:"Search name or id",value:w,onChange:s=>{A(s.target.value),x(s.target.value)},className:"py-[6px] pl-10 pr-4 border rounded-sm focus:outline-none focus:ring-2 focus:ring-blue-500"}),t.jsx("svg",{className:"absolute left-3 w-4 h-4 text-gray-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]}),t.jsx("div",{className:"w-32",children:t.jsx($,{onChange:L,value:l,placeholder:"Gender",options:[{value:"all",label:"All"},{value:"male",label:"Male"},{value:"female",label:"Female"},{value:"others",label:"Others"}]})}),t.jsx(z,{onExportExcel:()=>N(C.rows,"report.xlsx"),onExportPDF:()=>I(E.current,"report.pdf",T),onPrint:P})]})]}),t.jsx(J,{ref:T}),H[r],t.jsx(Z,{isOpen:g.state,onClose:()=>{h({...g,state:!1})},patientId:g.patientId})]})]})};export{ne as GeneralWardPage};
