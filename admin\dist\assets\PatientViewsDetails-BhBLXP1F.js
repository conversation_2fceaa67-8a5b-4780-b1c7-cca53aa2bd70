import{ba as F,bh as $,bj as p,a2 as e,b3 as o,bk as Y,av as d,aK as E}from"./index-ClX9RVH0.js";const H=()=>{const{id:x}=F(),{data:l}=$(x??""),[g,b]=p.useState("overview"),[u,h]=p.useState(!1),f=s=>{h(!0),setTimeout(()=>{b(s.toLowerCase()),h(!1)},150)},c=(s,t,a)=>!t||t==="-"?null:e.jsx("div",{className:"group hover:bg-gray-50 p-3 rounded-lg transition-all duration-200",children:e.jsxs("div",{className:"flex items-start gap-3",children:[a&&e.jsx(o,{icon:a,className:"w-4 h-4 text-indigo-500 mt-0.5 group-hover:scale-110 transition-transform"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("span",{className:"text-gray-500 text-sm font-medium",children:s}),e.jsx("div",{className:"text-gray-900 font-semibold mt-1",children:t})]})]})}),n=(s,t,a,i="from-white to-gray-50")=>e.jsxs("div",{className:`bg-gradient-to-br ${i} rounded-md shadow-lg border border-gray-100 p-4 mb-6 hover:shadow-xl transition-shadow duration-300`,children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"p-2 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl",children:e.jsx(o,{icon:t,className:"w-6 h-6 text-indigo-600"})}),e.jsx("h3",{className:"text-lg font-bold text-gray-800",children:s})]}),a]}),r=(s,t,a="-")=>{const i=d.get(s,t);return i==null?a:Array.isArray(i)?i.length>0?i:a:typeof i=="object"&&!(i instanceof Date)&&!Array.isArray(i)?Object.keys(i).length>0?i:a:i},v=()=>{const s=r(l,"user.commonInfo.personalInfo.fullName","N/A"),t=r(l,"user.patientInfo.patientId","N/A"),a=r(l,"user.commonInfo.personalInfo.gender","N/A"),i=r(l,"user.commonInfo.personalInfo.dob",null),m=i?C(i):"N/A",P=r(l,"user.commonInfo.contactInfo.phone.primaryPhone","N/A"),k=r(l,"user.commonInfo.contactInfo.address.currentAddress","N/A"),S=r(l,"user.commonInfo.personalInfo.profilePicture","/no-user.png"),M=r(l,"status","N/A"),T=r(l,"department.name","N/A");return e.jsxs("div",{className:"relative overflow-hidden bg-white rounded-md shadow-lg border border-gray-200 mb-4",children:[e.jsx("div",{className:"absolute inset-0 bg-blue-50/30"}),e.jsx("div",{className:"relative p-4",children:e.jsxs("div",{className:"flex flex-col lg:flex-row items-center gap-8",children:[e.jsxs("div",{className:"relative group",children:[e.jsx("div",{className:"absolute -inset-2 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full blur opacity-50 group-hover:opacity-75 transition duration-300"}),e.jsx("div",{className:"relative w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg",children:e.jsx("img",{src:S,alt:s,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300",onError:R=>{R.target.src="/no-user.png"}})})]}),e.jsxs("div",{className:"flex-1 text-center lg:text-left rounded-md space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-center lg:justify-start gap-3",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-800",children:s}),e.jsx("span",{className:"px-4 py-1 bg-green-100 text-green-700 rounded-full text-sm font-semibold",children:M})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"bg-blue-50 rounded-md p-4 border border-blue-100",children:[e.jsxs("div",{className:"flex items-center gap-2 text-blue-600 text-sm mb-1",children:[e.jsx(o,{icon:"mdi:id-card",className:"w-4 h-4"}),"Patient ID"]}),e.jsx("div",{className:"text-gray-800 font-semibold",children:t})]}),e.jsxs("div",{className:"bg-purple-50 rounded-md p-4 border border-purple-100",children:[e.jsxs("div",{className:"flex items-center gap-2 text-purple-600 text-sm mb-1",children:[e.jsx(o,{icon:"mdi:human-male-female",className:"w-4 h-4"}),"Age & Gender"]}),e.jsxs("div",{className:"text-gray-800 font-semibold",children:[m," years, ",a]})]}),e.jsxs("div",{className:"bg-indigo-50 rounded-md p-4 border border-indigo-100",children:[e.jsxs("div",{className:"flex items-center gap-2 text-indigo-600 text-sm mb-1",children:[e.jsx(o,{icon:"mdi:hospital-building",className:"w-4 h-4"}),"Department"]}),e.jsx("div",{className:"text-gray-800 font-semibold",children:T})]}),e.jsxs("div",{className:"bg-teal-50 rounded-md p-4 border border-teal-100",children:[e.jsxs("div",{className:"flex items-center gap-2 text-teal-600 text-sm mb-1",children:[e.jsx(o,{icon:"mdi:phone",className:"w-4 h-4"}),"Phone"]}),e.jsx("div",{className:"text-gray-800 font-semibold",children:P})]}),e.jsxs("div",{className:"bg-cyan-50 rounded-md p-4 border border-cyan-100 md:col-span-2",children:[e.jsxs("div",{className:"flex items-center gap-2 text-cyan-600 text-sm mb-1",children:[e.jsx(o,{icon:"mdi:map-marker",className:"w-4 h-4"}),"Address"]}),e.jsx("div",{className:"text-gray-800 font-semibold",children:k})]})]})]})]})})]})},N=()=>{const s=r(l,"preCheckups",null);if(!s)return null;const t=[{label:"Temperature",value:r(s,"temperature","N/A"),icon:"mdi:thermometer",color:"bg-blue-50",borderColor:"border-blue-100",textColor:"text-blue-600"},{label:"Blood Pressure",value:r(s,"bloodPressure","N/A"),icon:"mdi:heart-pulse",color:"bg-red-50",borderColor:"border-red-100",textColor:"text-red-600"},{label:"Heart Rate",value:r(s,"heartRate","N/A"),icon:"mdi:heart",color:"bg-green-50",borderColor:"border-green-100",textColor:"text-green-600"},{label:"Weight",value:r(s,"weight","N/A"),icon:"mdi:weight",color:"bg-yellow-50",borderColor:"border-yellow-100",textColor:"text-yellow-600"},{label:"Height",value:r(s,"height","N/A"),icon:"mdi:human-male-height",color:"bg-purple-50",borderColor:"border-purple-100",textColor:"text-purple-600"}];return n("Vital Signs","lucide:activity",e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4",children:t.map((a,i)=>e.jsx("div",{className:`group relative overflow-hidden ${a.color} rounded-md p-4 border ${a.borderColor} hover:shadow-md transition-all duration-300`,children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"flex items-center justify-between mb-2",children:e.jsx(o,{icon:a.icon,className:`w-6 h-6 ${a.textColor} group-hover:scale-110 transition-transform`})}),e.jsx("div",{className:"text-xs text-gray-500 mb-1",children:a.label}),e.jsx("div",{className:"text-lg font-semibold text-gray-800",children:a.value})]})},i))}))},j=()=>{const s=r(l,"symptoms",[]);return!Array.isArray(s)||s.length===0?n("Current Symptoms","mdi:stethoscope",e.jsx("div",{className:"text-sm italic text-gray-500",children:"No symptoms available."}),"from-orange-50 to-red-50"):n("Current Symptoms","mdi:stethoscope",e.jsx("div",{className:"space-y-4",children:s.map((t,a)=>e.jsxs("div",{className:"group relative overflow-hidden bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-xl p-4 hover:shadow-lg transition-all duration-300",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-orange-400/5 to-red-400/5 group-hover:from-orange-400/10 group-hover:to-red-400/10 transition-all"}),e.jsxs("div",{className:"relative flex items-start gap-4",children:[e.jsx("div",{className:"p-2 bg-orange-100 rounded-lg group-hover:bg-orange-200 transition-colors",children:e.jsx(o,{icon:"mdi:alert-circle",className:"w-5 h-5 text-orange-600"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-bold text-gray-800 mb-2",children:r(t,"symptom")}),e.jsxs("div",{className:"flex flex-wrap gap-4 text-sm text-gray-600",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(o,{icon:"mdi:calendar",className:"w-4 h-4"}),"Since: ",r(t,"startDate")]}),r(t,"additionalInfo","")&&e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(o,{icon:"mdi:information",className:"w-4 h-4"}),r(t,"additionalInfo")]})]})]})]})]},a))}),"from-orange-50 to-red-50")},y=()=>{const s=r(l,"allergies",[]);return!Array.isArray(s)||s.length===0?n("Allergies & Reactions","mdi:allergy",e.jsx("div",{className:"text-sm italic text-gray-500",children:"No known allergies."}),"from-red-50 to-pink-50"):n("Allergies & Reactions","mdi:allergy",e.jsx("div",{className:"flex flex-wrap gap-3",children:s.map((t,a)=>e.jsx("div",{className:"group px-4 py-2 bg-gradient-to-r from-red-100 to-pink-100 border-2 border-red-200 text-red-800 rounded-full text-sm font-semibold hover:from-red-200 hover:to-pink-200 hover:scale-105 transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(o,{icon:"mdi:alert",className:"w-4 h-4 group-hover:scale-110 transition-transform"}),r(t,"allergy")]})},a))}),"from-red-50 to-pink-50")},w=()=>{const s=r(l,"chronicDisease",[]);return!Array.isArray(s)||s.length===0?n("Chronic Conditions","mdi:hospital-box",e.jsx("div",{className:"text-sm italic text-gray-500",children:"No chronic conditions reported."}),"from-blue-50 to-indigo-50"):n("Chronic Conditions","mdi:hospital-box",e.jsx("div",{className:"space-y-4",children:s.map((t,a)=>e.jsx("div",{className:"group bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4 hover:shadow-lg transition-all duration-300",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors",children:e.jsx(o,{icon:"mdi:medical-bag",className:"w-5 h-5 text-blue-600"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-bold text-blue-800 mb-2",children:r(t,"chronicDisease")}),e.jsxs("div",{className:"flex flex-wrap gap-4 text-sm text-blue-600",children:[e.jsxs("span",{children:["Since: ",r(t,"startDate")]}),r(t,"additionalInfo","")&&e.jsxs("span",{children:["Note: ",r(t,"additionalInfo")]})]})]})]})},a))}),"from-blue-50 to-indigo-50")},A=()=>{const s=r(l,"prescriptionDetails",[]);return!Array.isArray(s)||s.length===0?n("Active Prescriptions","mdi:pill",e.jsx("div",{className:"text-sm italic text-gray-500",children:"No active prescriptions."}),"from-green-50 to-emerald-50"):n("Active Prescriptions","mdi:pill",e.jsx("div",{className:"space-y-6",children:s.map((t,a)=>e.jsxs("div",{className:"bg-white rounded-md border border-gray-200 overflow-hidden shadow-sm hover:shadow-lg transition-shadow",children:[e.jsxs("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-green-100 flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(o,{icon:"mdi:calendar-check",className:"w-5 h-5 text-green-600"}),e.jsxs("span",{className:"font-semibold text-green-800",children:["Prescribed:"," ",E(r(t,"date","-")).format("MMM DD, YYYY")]})]}),e.jsx("span",{className:"px-3 py-1 bg-green-200 text-green-800 rounded-full text-sm font-medium",children:r(t,"status","Active")})]}),e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-200",children:[e.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-700",children:"Medicine"}),e.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-700",children:"Dosage"}),e.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-700",children:"Frequency"}),e.jsx("th",{className:"text-left py-3 px-4 font-semibold text-gray-700",children:"Duration"})]})}),e.jsx("tbody",{children:r(t,"prescriptionList",[]).map((i,m)=>e.jsxs("tr",{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors",children:[e.jsx("td",{className:"py-4 px-4 font-medium text-gray-900",children:r(i,"prescribedMedicine.name",r(i,"notFromDatabase","-"))}),e.jsx("td",{className:"py-4 px-4 text-gray-700",children:r(i,"doses")}),e.jsx("td",{className:"py-4 px-4 text-gray-700",children:r(i,"frequency")}),e.jsx("td",{className:"py-4 px-4 text-gray-700",children:r(i,"duration")})]},m))})]})})})]},a))}),"from-green-50 to-emerald-50")},C=s=>{const t=new Date(s),a=Date.now()-t.getTime(),i=new Date(a);return Math.abs(i.getUTCFullYear()-1970).toString()},I=()=>{const s=[{id:"overview",label:"Overview",icon:"mdi:view-dashboard"},{id:"prescriptions",label:"Prescriptions",icon:"mdi:pill"},{id:"tests",label:"Tests",icon:"mdi:test-tube"},{id:"services",label:"Services",icon:"mdi:medical-bag"}];return e.jsx("div",{className:"flex flex-wrap gap-2 p-2 bg-gray-100 rounded-md mb-4",children:s.map(t=>e.jsxs("button",{onClick:()=>f(t.id),className:`flex items-center gap-2 px-4 text-sm py-2 rounded-md font-semibold transition-all duration-300 ${g===t.id?"bg-white text-indigo-600 shadow-lg scale-105":"text-gray-600 hover:text-gray-800 hover:bg-white/50"}`,children:[e.jsx(o,{icon:t.icon,className:"w-5 h-5"}),t.label]},t.id))})},D=()=>{const s=(()=>{switch(g){case"overview":return e.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"xl:col-span-3 space-y-4",children:[N(),j(),w()]}),e.jsxs("div",{className:"space-y-4",children:[y(),e.jsxs("div",{className:"bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 shadow-lg",children:[e.jsxs("h3",{className:"font-bold text-gray-800 mb-4 flex items-center gap-2",children:[e.jsx(o,{icon:"mdi:information",className:"w-5 h-5 text-indigo-600"}),"Additional Info"]}),e.jsxs("div",{className:"space-y-3",children:[c("Current Medication",d.get(l,"currentMedication","-"),"mdi:pill"),c("Follow-up Date",d.get(l,"followUpDate","-"),"mdi:calendar"),c("Reason for Revisit",d.get(l,"reasonForRevisit","-"),"mdi:clipboard-text")]})]})]})]});case"prescriptions":return e.jsx("div",{children:A()});case"tests":return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"text-center py-8",children:e.jsx(Y,{patientId:x})})});case"services":return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(o,{icon:"mdi:medical-bag",className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-500",children:"Medical services and consultations will appear here"})]})});default:return null}})();return e.jsx("div",{className:`transition-all duration-300 ${u?"opacity-0 transform translate-y-4":"opacity-100 transform translate-y-0"}`,children:s})};return e.jsx("div",{className:"h-full",children:e.jsxs("div",{className:"",children:[v(),e.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-md shadow-2xl border border-white/50 p-4",children:[I(),D()]})]})})};export{H as default};
