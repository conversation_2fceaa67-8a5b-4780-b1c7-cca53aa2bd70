import{b0 as p,av as l,b1 as b,a1 as g,a5 as j,a2 as e,b2 as h,b3 as S,a9 as y,aa as v,aB as I,ao as B}from"./index-ClX9RVH0.js";const F=[{type:"date",field:"transferInformations.date",label:"Date"},{type:"text",field:"transferInformations.transferedHospital",label:"Transfered Hospital"}],P=({patientId:i,isOpen:o,onClose:s})=>{const n=localStorage.getItem("user"),c=n?JSON.parse(n).id:"",{data:d,isLoading:m}=p(i),f=l.get(d,"allBills",[]).some(t=>l.get(t,"paymentStatus")!=="PAID"),{mutate:u,isPending:x,isSuccess:r}=b(),a=g({initialValues:{transferInformations:{transferedBy:c,transferedHospital:"",date:""}},enableReinitialize:!0,onSubmit:t=>{u({entityData:t,_id:i})}});return j.useEffect(()=>{r&&s()},[r]),o?e.jsx(h,{onClose:s,classname:"max-w-lg w-full p-8",children:m?e.jsx("div",{className:"p-4 text-center",children:e.jsx(S,{icon:"icon-park-outline:loading-one",className:"size-8"})}):f?e.jsx("div",{className:"p-4 text-center",children:e.jsx("p",{className:"text-gray-700",children:"Please clear any pending bills before transferring the patient."})}):e.jsx(y,{value:a,children:e.jsxs(v,{onSubmit:a.handleSubmit,children:[e.jsx("div",{className:"grid grid-cols-2 gap-5",children:e.jsx(I,{formDatails:F,...a})}),e.jsx(B,{isPending:x,onSubmit:a.submitForm,onCancel:s})]})})}):e.jsx(e.Fragment,{})};export{P as T};
