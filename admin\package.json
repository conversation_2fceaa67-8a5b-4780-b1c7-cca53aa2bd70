{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --force", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@daypilot/daypilot-lite-react": "^4.1.0", "@gsap/react": "^2.1.2", "@tailwindcss/postcss": "^4.0.6", "@tanstack/react-query": "^5.66.5", "@tanstack/react-query-devtools": "^5.66.5", "@tanstack/react-store": "^0.7.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/crypto-js": "^4.2.2", "apexcharts": "^4.7.0", "axios": "^1.7.9", "classnames": "^2.5.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "firebase": "^11.8.1", "formik": "^2.4.6", "graphql-request": "^7.1.2", "html2pdf.js": "0.9.0", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-big-calendar": "^1.18.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-quill": "^2.0.0", "react-router-dom": "^7.1.5", "react-select": "^5.10.1", "react-to-print": "^3.1.0", "react-toastify": "^11.0.3", "tailwind-merge": "^3.0.1", "uuid": "^11.1.0", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.19.0", "@iconify/react": "^5.2.0", "@types/leaflet": "^1.9.17", "@types/lodash": "^4.17.16", "@types/node": "^22.13.8", "@types/react": "^19.0.8", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "husky": "^9.1.7", "postcss": "^8.5.2", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}