import { Icon } from "@iconify/react/dist/iconify.js";
import { get } from "lodash";
import { useCallback, useEffect, useState } from "react";
import Drawer from "../../../../components/Drawer";
import SearchableSelect from "../../../../components/SearchableSelect";
import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import {
  useGetAllSurgeryDepartment,
  useGetAllSurgerySubDepartment,
} from "../../../../server-action/api/OTAssignment";
import { Label } from "../../../PurchaseManagement/PurchaseOrder/components/NewPurchaseOrder";
import { surgerConfigColumns } from "./ot.obj";
import SurgeryDepartment from "./SurgeryDepartment";
import SurgerySubDepatment from "./SurgerySubDepatment";

const Ot = () => {
  const [drawer, setDrawer] = useState({
    open: false,
    of: "",
  });
  const [selectedDepartment, setSelectedDepartment] = useState("");
  const [selectedSubDepartment, setSelectedSubDepartment] = useState("");
  const [tableData, setTableData] = useState<any[]>([]);

  // Fetch departments
  const {
    data: departmentsData,
    isLoading: isLoadingDepartments,
    isSuccess,
  } = useGetAllSurgeryDepartment();
  let { data: surgeryDepartments } = useGetAllSurgeryDepartment(
    { ...(selectedDepartment !== "" && { name: selectedDepartment }) },
    {
      enabled: isSuccess,
    }
  );
  surgeryDepartments = get(surgeryDepartments, "data.surgeryDepartments", []);

  const departments = get(departmentsData, "data.surgeryDepartments", []);

  const departmentOptions = [
    { value: "", label: "All" },
    ...departments.map((dept: any) => ({
      value: dept._id,
      label: dept.name,
    })),
  ];

  const { data: subDepartmentsData, isLoading: isLoadingSubDepartments } =
    useGetAllSurgerySubDepartment({
      ...(selectedDepartment ? { department: selectedDepartment } : {}),
    });
  const subDepartments = get(
    subDepartmentsData,
    "data.surgerySubDepartments",
    []
  );
  const subDepartmentOptions = subDepartments.map((subDept: any) => ({
    value: subDept._id,
    label: subDept.name,
  }));

  // Handle drawer close
  const handleClose = useCallback(() => {
    setDrawer({ open: false, of: "" });
  }, []);

  // Handle department change
  const handleDepartmentChange = (value: string) => {
    setSelectedDepartment(value);
    setSelectedSubDepartment(""); // Reset sub-department when department changes
  };

  // Handle sub-department change
  const handleSubDepartmentChange = (value: string) => {
    setSelectedSubDepartment(value);
  };

  // Prepare table data
  useEffect(() => {
    if (departments.length > 0) {
      const rows = surgeryDepartments.map((dept: any, index: number) => {
        // Find all sub-departments for this department
        const relatedSubDepts = subDepartments.filter(
          (subDept: any) => get(subDept, "department._id", "") === dept._id
        );

        // If no sub-departments, show just the department
        if (relatedSubDepts.length === 0) {
          return {
            key: `${dept._id}-${index}`,
            sno: index + 1,
            department: dept.name,
            subCategory: "-",
            action: (
              <TableAction
                onDelete={() => {
                  /* Handle delete */
                }}
              />
            ),
          };
        }

        // Otherwise, create a row for each sub-department
        return relatedSubDepts.map((subDept: any, subIndex: number) => ({
          key: `${dept._id}-${subDept._id}-${subIndex}`,
          sno: index + subIndex + 1,
          department: dept.name,
          subCategory: subDept.name,
          action: (
            <TableAction
              onDelete={() => {
                /* Handle delete */
              }}
            />
          ),
        }));
      });

      // Flatten the array of arrays
      setTableData(rows.flat());
    }
  }, [departments, subDepartments]);

  // Component mapping for drawer
  const components: Record<string, React.ReactNode> = {
    department: <SurgeryDepartment onClose={handleClose} />,
    subDepartment: <SurgerySubDepatment onClose={handleClose} />,
  };

  return (
    <div>
      <div className="grid grid-cols-3 bg-white rounded-md p-4 gap-8">
        <div className="flex items-center gap-2">
          <Label className="flex items-center gap-4" label="Department">
            <SearchableSelect
              options={departmentOptions}
              onChange={handleDepartmentChange}
              value={selectedDepartment}
              placeholder="Select Department"
            />
          </Label>
          <button
            onClick={() => setDrawer({ open: true, of: "department" })}
            className="size-sm p-2 rounded-full shadow-md drop-shadow-md cursor-pointer"
          >
            <Icon icon="lucide:plus" className="size-3" />
          </button>
        </div>
        <div className="flex items-center gap-2">
          <Label className="flex items-center gap-4" label="Sub Department">
            <SearchableSelect
              options={subDepartmentOptions}
              onChange={handleSubDepartmentChange}
              value={selectedSubDepartment}
              placeholder="Select Sub Department"
            />
          </Label>
          <button
            onClick={() => setDrawer({ open: true, of: "subDepartment" })}
            className="size-sm p-2 rounded-full shadow-md drop-shadow-md cursor-pointer"
            // disabled={!selectedDepartment}
          >
            <Icon icon="lucide:plus" className="size-3" />
          </button>
        </div>
      </div>
      <MasterTable
        columns={surgerConfigColumns}
        rows={tableData}
        loading={isLoadingDepartments || isLoadingSubDepartments}
      />
      <Drawer
        isOpen={drawer.open}
        showCloseButton={false}
        onClose={() => setDrawer({ open: false, of: "" })}
      >
        {drawer.of === "subDepartment" && selectedDepartment ? (
          <SurgerySubDepatment onClose={handleClose} />
        ) : (
          components[drawer.of]
        )}
      </Drawer>
    </div>
  );
};

export default Ot;
