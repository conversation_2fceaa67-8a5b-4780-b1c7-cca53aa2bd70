// import React from "react";

// interface GeneralInfoCardProps {
//   title: string;
//   leftItems: { label: string; value: string }[];
//   rightItems?: { label: string; value: string }[];
// }

// const GeneralInfoCard: React.FC<GeneralInfoCardProps> = ({
//   title,
//   leftItems,
//   rightItems,
// }) => {
//   return (
//     <div className="bg-white p-4 rounded-lg shadow-md">
//       <h2 className="text-[#156ae5] font-semibold text-lg mb-2">{title}</h2>
//       <div className="flex space-x-16 w-full">
//         <div className="grid grid-cols-1 gap-2  ">
//           {leftItems.map((item, index) => (
//             <div
//               key={index}
//               className="flex items-center justify-between gap-4"
//             >
//               <span className="font-medium">{item.label}</span>
//               <span className="ml-2">{item.value}</span>
//             </div>
//           ))}
//         </div>
//         {rightItems && (
//           <div className="grid grid-cols-1 gap-2 ">
//             {rightItems.map((item, index) => (
//               <div
//                 key={index}
//                 className="flex items-center justify-between gap-4"
//               >
//                 <span className="font-medium">{item.label}</span>
//                 <span className="ml-2">{item.value}</span>
//               </div>
//             ))}
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default GeneralInfoCard;

// Second

// import React from "react";

// interface GeneralInfoCardProps {
//   title: string;
//   leftItems: { label: string; value: string }[];
//   rightItems?: { label: string; value: string }[];
// }

// const GeneralInfoCard: React.FC<GeneralInfoCardProps> = ({
//   title,
//   leftItems,
//   rightItems,
// }) => {
//   return (
//     <div className="bg-white p-4 rounded-lg shadow-md w-full">
//       <h2 className="text-[#156ae5] font-semibold text-lg mb-2">{title}</h2>
//       <div className="flex flex-col md:flex-row space-x-0 md:space-x-16">
//         <div className="grid grid-cols-1 gap-2 w-full md:w-auto">
//           {leftItems.map((item, index) => (
//             <div
//               key={index}
//               className="flex items-center justify-between gap-4"
//             >
//               <span className="font-medium">{item.label}</span>
//               <span className="ml-2">{item.value}</span>
//             </div>
//           ))}
//         </div>
//         {rightItems && (
//           <div className="grid grid-cols-1 gap-2 w-full md:w-auto mt-2 md:mt-0">
//             {rightItems.map((item, index) => (
//               <div
//                 key={index}
//                 className="flex items-center justify-between gap-4"
//               >
//                 <span className="font-medium">{item.label}</span>
//                 <span className="ml-2">{item.value}</span>
//               </div>
//             ))}
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default GeneralInfoCard;

// Third

// import React from "react";

// interface GeneralInfoCardProps {
//   title: string;
//   leftItems: { label: string; value: string }[];
//   rightItems?: { label: string; value: string }[];
//   variant?: "single" | "double";
// }

// const GeneralInfoCard: React.FC<GeneralInfoCardProps> = ({
//   title,
//   leftItems,
//   rightItems,
//   variant = "single",
// }) => {
//   return (
//     <div className="bg-white p-4 rounded-lg shadow-md w-full">
//       <h2 className="text-[#156ae5] font-semibold text-lg mb-2">{title}</h2>
//       {variant === "single" ? (
//         <div className="grid grid-cols-1 gap-2">
//           {leftItems.map((item, index) => (
//             <div
//               key={index}
//               className="flex items-center justify-between gap-4"
//             >
//               <span className="font-medium">{item.label}</span>
//               <span className="ml-2">{item.value}</span>
//             </div>
//           ))}
//         </div>
//       ) : (
//         <div className="flex flex-col md:flex-row space-x-0 md:space-x-16">
//           <div className="grid grid-cols-1 gap-2 w-full md:w-auto">
//             {leftItems.map((item, index) => (
//               <div
//                 key={index}
//                 className="flex items-center justify-between gap-4"
//               >
//                 <span className="font-medium">{item.label}</span>
//                 <span className="ml-2">{item.value}</span>
//               </div>
//             ))}
//           </div>
//           <div className="grid grid-cols-1 gap-2 w-full md:w-auto mt-2 md:mt-0">
//             {rightItems?.map((item, index) => (
//               <div
//                 key={index}
//                 className="flex items-center justify-between gap-4"
//               >
//                 <span className="font-medium">{item.label}</span>
//                 <span className="ml-2">{item.value}</span>
//               </div>
//             ))}
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default GeneralInfoCard;

// Fourth

// import React from "react";

// interface GeneralInfoCardProps {
//   title: string;
//   leftItems: { label: string; value: string }[];
//   rightItems?: { label: string; value: string }[];
//   variant?: "single" | "double";
// }

// const GeneralInfoCard: React.FC<GeneralInfoCardProps> = ({
//   title,
//   leftItems,
//   rightItems,
//   variant = "single",
// }) => {
//   return (
//     <div className="bg-white p-4 rounded-lg shadow-md w-full min-h-[320px] flex flex-col">
//       <h2 className="text-[#156ae5] font-semibold text-lg mb-2">{title}</h2>
//       <div className="flex-1">
//         {variant === "single" ? (
//           <div className="grid grid-cols-1 gap-2 h-full">
//             {leftItems.map((item, index) => (
//               <div
//                 key={index}
//                 className="flex items-center justify-between gap-4"
//               >
//                 <span className="font-medium">{item.label}</span>
//                 <span className="ml-2">{item.value}</span>
//               </div>
//             ))}
//           </div>
//         ) : (
//           <div className="flex flex-col md:flex-row h-full">
//             <div className="grid grid-cols-1 gap-2 w-full md:w-1/2 pr-2">
//               {leftItems.map((item, index) => (
//                 <div
//                   key={index}
//                   className="flex items-center justify-between gap-4"
//                 >
//                   <span className="font-medium">{item.label}</span>
//                   <span className="ml-2 text-nowrap">{item.value}</span>
//                 </div>
//               ))}
//             </div>
//             <div className="grid grid-cols-1 gap-2 w-full md:w-1/2 pl-2">
//               {rightItems?.map((item, index) => (
//                 <div
//                   key={index}
//                   className="flex items-center justify-between gap-4"
//                 >
//                   <span className="font-medium">{item.label}</span>
//                   <span className="ml-2 text-nowrap">{item.value}</span>
//                 </div>
//               ))}
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default GeneralInfoCard;

// import React from "react";

// interface GeneralInfoCardProps {
//   title: string;
//   leftItems: { label: string; value: string }[];
//   rightItems?: { label: string; value: string }[];
//   variant?: "single" | "double";
// }

// const GeneralInfoCard: React.FC<GeneralInfoCardProps> = ({
//   title,
//   leftItems,
//   rightItems,
//   variant = "single",
// }) => {
//   return (
//     <div className="bg-white p-4 rounded-lg shadow-md w-full min-h-[320px] flex flex-col">
//       <h2 className="text-[#156ae5] font-semibold text-lg mb-2">{title}</h2>
//       <div className="flex-1">
//         {variant === "single" ? (
//           <div className="grid grid-cols-1 gap-2 h-full">
//             {leftItems.map((item, index) => (
//               <div
//                 key={index}
//                 className="flex items-center justify-between gap-2"
//               >
//                 <span className="font-medium">{item.label}</span>
//                 <span className="ml-2 break-words">{item.value}</span>
//               </div>
//             ))}
//           </div>
//         ) : (
//           <div className="flex flex-col md:flex-row h-full">
//             <div className="grid grid-cols-1 gap-2 w-full md:w-1/2 pr-2">
//               {leftItems.map((item, index) => (
//                 <div key={index} className="flex items-center gap-2">
//                   <span className="font-medium">{item.label}</span>
//                   <span className="ml-2 break-words">{item.value}</span>
//                 </div>
//               ))}
//             </div>
//             <div className="grid grid-cols-1 gap-2 w-full md:w-1/2 pl-2">
//               {rightItems?.map((item, index) => (
//                 <div
//                   key={index}
//                   className="flex items-center justify-between gap-2"
//                 >
//                   <span className="font-medium">{item.label}</span>
//                   <span className="ml-2 break-words">{item.value}</span>
//                 </div>
//               ))}
//             </div>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default GeneralInfoCard;

import type React from "react";

interface GeneralInfoCardProps {
  title: string;
  leftItems: { label: string; value: string }[];
  rightItems?: { label: string; value: string }[];
  variant?: "single" | "double";
}

const GeneralInfoCard: React.FC<GeneralInfoCardProps> = ({
  title,
  leftItems,
  rightItems,
  variant = "single",
}) => {
  return (
    <div className="bg-white p-3 rounded-lg shadow-md w-full flex flex-col">
      <h2 className="text-[#156ae5] font-semibold text-base mb-3">{title}</h2>
      <div className="flex-1">
        {variant === "single" ? (
          <div className="grid grid-cols-1 gap-1">
            {leftItems.map((item, index) => (
              <div
                key={index}
                className="flex items-center justify-between gap-2 py-1"
              >
                <span className="font-medium text-sm text-gray-700 whitespace-nowrap">
                  {item.label}
                </span>
                <span className="text-sm text-gray-900 text-right">
                  {item.value}
                </span>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex gap-6">
            <div className="grid grid-cols-1 gap-1 flex-1">
              {leftItems.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between gap-3 py-1"
                >
                  <span className="font-medium text-sm text-gray-700 whitespace-nowrap">
                    {item.label}
                  </span>
                  <span className="text-sm text-gray-900">{item.value}</span>
                </div>
              ))}
            </div>
            <div className="grid grid-cols-1 gap-1 flex-1">
              {rightItems?.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between gap-3 py-1"
                >
                  <span className="font-medium text-sm text-gray-700 whitespace-nowrap">
                    {item.label}
                  </span>
                  <span className="text-sm text-gray-900 whitespace-nowrap">
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GeneralInfoCard;
