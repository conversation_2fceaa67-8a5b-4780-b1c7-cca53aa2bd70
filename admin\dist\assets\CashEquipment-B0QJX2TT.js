import{av as j,a2 as e,b3 as a,ch as v,ad as w,a5 as u,ci as D,cj as k,af as A,a7 as C,ck as E,cl as S,cm as T,aj as L,cn as I}from"./index-ClX9RVH0.js";const i=(l,c,t="N/A")=>j.get(l,c,t),q=({isOpen:l,onClose:c,data:t})=>{const o=s=>new Date(s).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),d=s=>new Date(s).toLocaleString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"});if(!l)return null;const m=i(t,"donationFor",""),x=i(t,"productList",[]);return e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"Donation Details"}),e.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Record Information"})]}),e.jsx("button",{onClick:c,className:"p-2 hover:bg-white hover:bg-opacity-80 rounded-lg transition-colors duration-200",children:e.jsx(a,{icon:"mdi:close",className:"w-6 h-6 text-gray-600"})})]}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-3 flex items-center",children:[e.jsx(a,{icon:"mdi:receipt-text",className:"w-5 h-5 mr-2 text-blue-600"}),"Donation Information"]}),e.jsxs("div",{className:"space-y-2",children:[[["Donation No","donationNo"],["Category","donationCategory"],["For","donationFor"],["Type","category"]].map(([s,r])=>e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("span",{className:"text-gray-600",children:[s,":"]}),e.jsx("span",{className:"font-medium text-gray-800",children:i(t,r)})]},r)),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Date:"}),e.jsxs("span",{className:"font-medium text-gray-800 flex items-center",children:[e.jsx(a,{icon:"mdi:calendar",className:"w-4 h-4 mr-1 text-blue-600"}),o(i(t,"date",""))]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Status:"}),e.jsxs("span",{className:`px-2 py-1 rounded-full text-xs font-medium flex items-center ${t.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:[e.jsx(a,{icon:t.isActive?"mdi:check-circle":"mdi:close-circle",className:"w-3 h-3 mr-1"}),t.isActive?"Active":"Inactive"]})]})]})]}),e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-3 flex items-center",children:[e.jsx(a,{icon:"mdi:account-group",className:"w-5 h-5 mr-2 text-blue-600"}),"Donor Information"]}),e.jsx("div",{className:"space-y-2",children:[["Name","donorDetails.name"],["Organization","donorDetails.organization"],["Email","donorDetails.email"],["Contact","donorDetails.contact"],["Address","donorDetails.address"]].map(([s,r])=>e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("span",{className:"text-gray-600 flex items-center",children:[s==="Email"&&e.jsx(a,{icon:"mdi:email",className:"w-4 h-4 mr-1"}),s==="Contact"&&e.jsx(a,{icon:"mdi:phone",className:"w-4 h-4 mr-1"}),s==="Address"&&e.jsx(a,{icon:"mdi:map-marker",className:"w-4 h-4 mr-1"}),s!=="Email"&&s!=="Contact"&&s!=="Address"&&null,s,":"]}),e.jsx("span",{className:"font-medium text-gray-800 text-right",children:i(t,r)})]},r))})]})]}),e.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-3 flex items-center",children:[e.jsx(a,{icon:"mdi:package-variant",className:"w-5 h-5 mr-2 text-green-600"}),"Product List (",x.length," items)"]}),e.jsx("div",{className:"space-y-3",children:x.map(s=>e.jsx("div",{className:"bg-white p-3 rounded-lg border border-green-200",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[e.jsxs("div",{children:[e.jsxs("span",{className:"text-gray-600 text-sm flex items-center",children:[e.jsx(a,{icon:"mdi:identifier",className:"w-4 h-4 mr-1"}),"Product :"]}),e.jsx("p",{className:"font-medium text-gray-800 text-sm break-all",children:i(s,`${v[m]}.name`,"Unknown Product")})]}),e.jsxs("div",{children:[e.jsxs("span",{className:"text-gray-600 text-sm flex items-center",children:[e.jsx(a,{icon:"mdi:calendar-manufacturing",className:"w-4 h-4 mr-1"}),"Manufactured Date:"]}),e.jsx("p",{className:"font-medium text-gray-800",children:o(i(s,"manufacturedDate",""))})]}),e.jsxs("div",{children:[e.jsxs("span",{className:"text-gray-600 text-sm flex items-center",children:[e.jsx(a,{icon:"mdi:counter",className:"w-4 h-4 mr-1"}),"Quantity:"]}),e.jsx("p",{className:"font-medium text-gray-800",children:i(s,"quantity",0).toLocaleString()})]})]})},i(s,"_id","")))})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-3 flex items-center",children:[e.jsx(a,{icon:"mdi:information",className:"w-5 h-5 mr-2 text-gray-600"}),"Record Information"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsxs("span",{className:"text-gray-600 text-sm flex items-center",children:[e.jsx(a,{icon:"mdi:clock-plus",className:"w-4 h-4 mr-1"}),"Created At:"]}),e.jsx("p",{className:"font-medium text-gray-800",children:d(i(t,"createdAt",""))})]}),e.jsxs("div",{children:[e.jsxs("span",{className:"text-gray-600 text-sm flex items-center",children:[e.jsx(a,{icon:"mdi:clock-edit",className:"w-4 h-4 mr-1"}),"Updated At:"]}),e.jsx("p",{className:"font-medium text-gray-800",children:d(i(t,"updatedAt",""))})]})]})]}),i(t,"remarks","")&&e.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[e.jsxs("h3",{className:"text-lg font-semibold text-gray-800 mb-2 flex items-center",children:[e.jsx(a,{icon:"mdi:note-text",className:"w-5 h-5 mr-2 text-yellow-600"}),"Remarks"]}),e.jsx("p",{className:"text-gray-700",children:i(t,"remarks","No remarks provided")})]})]}),e.jsx("div",{className:"flex justify-end p-6 border-t border-gray-200 bg-gray-50",children:e.jsx("button",{onClick:c,className:"bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center",children:"Close"})})]})})},P=()=>{const l=w(),[c,t]=u.useState({page:1,limit:10}),[o,d]=u.useState({state:!1,data:{}}),[m,x]=u.useState("FINANCE"),{mutate:s}=D(),{data:r,isLoading:N}=k({page:c.page,limit:c.limit,donationCategory:m}),h=j.get(r,"data.pagination",{}),f=({page:n,limit:g})=>{t(p=>({page:n??(g?1:p.page),limit:g??p.limit}))},y=j.get(r,"data.donations",[]).map(n=>({...n,no:n.productList.length,name:n.donorDetails.name,action:e.jsx(A,{onEdit:()=>l(`/donor/cash-equipment/edit-${n._id}`),onDelete:()=>s({id:JSON.stringify([n._id])}),...m==="EQUIPMENT"?{onShow:()=>d({state:!0,data:n})}:{}})})),b=I[m];return e.jsxs("section",{className:"",children:[e.jsx(C,{listTitle:"Cash Equipment",hideHeader:!0}),e.jsxs("div",{className:"bg-white p-4 rounded-sm",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(E,{defaultTab:"FINANCE",onTabChange:n=>{x(n),t(g=>({...g,page:1}))},tabsList:S}),e.jsx(T,{to:"/donor/cash-equipment/add",className:"px-4 py-2 text-white transition rounded-md bg-primary text-nowrap hover:bg-teal-700",children:"Add Donation"})]}),e.jsx(L,{className:"px-0 py-4",rows:y,columns:b,pagination:{totalPage:h.pages||1,currentPage:h.page||1,limit:h.limit||10,onClick:f},loading:N}),e.jsx(q,{onClose:()=>d({state:!1,data:{}}),isOpen:o.state,data:o.data})]})]})};export{P as default};
