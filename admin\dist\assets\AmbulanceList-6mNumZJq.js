import{a2 as e,a5 as s,aP as V1,aM as h1,aw as z,av as c,aV as J,aX as h,bv as M1,bm as A,af as v1,ah as H1,aQ as j1,aj as b1,aR as y1,bn as g1,aa as f1,a4 as K,ac as M,ab as N}from"./index-ClX9RVH0.js";import{j as Z1,k as L1,l as N1,a as D1,b as w1}from"./ambulanceApi-C42c0mRe.js";const S1=()=>e.jsxs("svg",{width:"31",height:"30",viewBox:"0 0 31 30",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{opacity:"0.21","fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.709961 15V22C0.709961 26.4183 4.29168 30 8.70996 30H15.71H22.71C27.1282 30 30.71 26.4183 30.71 22V15V8C30.71 3.58172 27.1282 0 22.71 0H15.71H8.70996C4.29168 0 0.709961 3.58172 0.709961 8V15Z",fill:"#15B7ED"}),e.jsx("path",{opacity:"0.46",d:"M13.0827 18.1231C13.0817 18.1219 13.0806 18.1208 13.0795 18.1197C13.0686 18.1087 13.0543 18.102 13.039 18.1005C13.0379 18.1004 13.0368 18.1003 13.0357 18.1002C13.0346 18.1003 13.0335 18.1004 13.0324 18.1005C13.0171 18.102 13.0028 18.1087 12.9919 18.1197C12.9908 18.1208 12.9897 18.1219 12.9887 18.1231C12.9782 18.1352 12.9724 18.1507 12.9724 18.1668C12.9724 18.1829 12.9782 18.1984 12.9887 18.2105C12.9897 18.2117 12.9908 18.2128 12.9919 18.2139L13.0324 18.1735L12.9919 18.2139C13.0028 18.2249 13.0171 18.2316 13.0324 18.2331C13.0335 18.2332 13.0346 18.2333 13.0357 18.2334C13.0368 18.2333 13.0379 18.2332 13.039 18.2331C13.0543 18.2316 13.0686 18.2249 13.0795 18.2139C13.0806 18.2128 13.0817 18.2117 13.0827 18.2105C13.0932 18.1984 13.099 18.1829 13.099 18.1668C13.099 18.1507 13.0932 18.1352 13.0827 18.1231ZM13.0827 18.1231L13.039 18.1668L13.0827 18.1231ZM18.3857 15.4935L18.4262 15.453C18.4273 15.4541 18.4283 15.4553 18.4294 15.4564C18.4341 15.4618 18.4379 15.468 18.4406 15.4746C18.444 15.4827 18.4457 15.4914 18.4457 15.5001C18.4457 15.5162 18.4399 15.5317 18.4294 15.5438C18.4284 15.545 18.4273 15.5462 18.4262 15.5473C18.4152 15.5582 18.4009 15.5649 18.3857 15.5665C18.3846 15.5666 18.3835 15.5667 18.3824 15.5667C18.3812 15.5667 18.3801 15.5666 18.379 15.5665C18.3704 15.5656 18.3622 15.5631 18.3546 15.5591C18.3487 15.556 18.3433 15.552 18.3385 15.5473C18.3374 15.5462 18.3364 15.545 18.3353 15.5438M18.3857 15.4935L18.4262 15.453C18.42 15.4468 18.4126 15.4419 18.4045 15.4385C18.3985 15.436 18.3922 15.4344 18.3857 15.4338C18.3846 15.4337 18.3835 15.4336 18.3824 15.4335C18.3812 15.4336 18.3801 15.4337 18.379 15.4338C18.3638 15.4353 18.3495 15.4421 18.3385 15.453C18.3374 15.4541 18.3364 15.4553 18.3353 15.4564C18.3249 15.4685 18.319 15.484 18.319 15.5001C18.319 15.5081 18.3205 15.5159 18.3232 15.5233C18.3259 15.5307 18.33 15.5377 18.3353 15.5438M18.3857 15.4935L18.3824 15.4968M18.3857 15.4935L18.3824 15.4968M18.3353 15.5438L18.379 15.5001M18.3353 15.5438L18.379 15.5001M18.3824 15.4968L18.379 15.5001M18.3824 15.4968L18.379 15.5001M18.379 15.5001L18.3743 15.5115M18.379 15.5001L18.381 15.4954L18.3743 15.5115M18.379 15.5001L18.3743 15.5115M18.379 15.5001L18.3743 15.5115M13.099 15.5001C13.099 15.5162 13.0932 15.5317 13.0827 15.5438C13.0817 15.545 13.0806 15.5462 13.0795 15.5473C13.0686 15.5582 13.0543 15.5649 13.039 15.5665C13.0379 15.5666 13.0368 15.5667 13.0357 15.5667C13.0346 15.5667 13.0335 15.5666 13.0324 15.5665C13.0238 15.5656 13.0155 15.5631 13.0079 15.5591C13.0021 15.556 12.9967 15.552 12.9919 15.5473L13.0276 15.5115L12.9919 15.5473C12.9908 15.5462 12.9897 15.545 12.9887 15.5438C12.9834 15.5377 12.9793 15.5307 12.9765 15.5233C12.9738 15.5159 12.9724 15.5081 12.9724 15.5001C12.9724 15.484 12.9782 15.4685 12.9887 15.4564C12.9897 15.4553 12.9908 15.4541 12.9919 15.453C13.0028 15.4421 13.0171 15.4353 13.0324 15.4338C13.0335 15.4337 13.0346 15.4336 13.0357 15.4335C13.0368 15.4336 13.0379 15.4337 13.039 15.4338C13.0455 15.4344 13.0518 15.436 13.0579 15.4385C13.066 15.4419 13.0733 15.4468 13.0795 15.453L13.039 15.4935L13.0795 15.453C13.0806 15.4541 13.0817 15.4553 13.0827 15.4564C13.0874 15.4618 13.0912 15.468 13.0939 15.4746C13.0973 15.4827 13.099 15.4914 13.099 15.5001ZM15.7724 15.5001C15.7724 15.5162 15.7665 15.5317 15.756 15.5438C15.755 15.545 15.7539 15.5462 15.7528 15.5473L15.7124 15.5068L15.7528 15.5473C15.7419 15.5582 15.7276 15.5649 15.7124 15.5665C15.7113 15.5666 15.7101 15.5667 15.709 15.5667C15.7079 15.5667 15.7068 15.5666 15.7057 15.5665C15.6971 15.5656 15.6888 15.5631 15.6813 15.5591C15.6754 15.556 15.67 15.552 15.6652 15.5473C15.6641 15.5462 15.663 15.545 15.662 15.5438C15.6567 15.5377 15.6526 15.5307 15.6498 15.5233C15.6471 15.5159 15.6457 15.5081 15.6457 15.5001C15.6457 15.484 15.6515 15.4685 15.662 15.4564C15.663 15.4553 15.6641 15.4541 15.6652 15.453C15.6761 15.4421 15.6905 15.4353 15.7057 15.4338C15.7068 15.4337 15.7079 15.4336 15.709 15.4335C15.7101 15.4336 15.7112 15.4337 15.7124 15.4338C15.7188 15.4344 15.7252 15.436 15.7312 15.4385C15.7393 15.4419 15.7466 15.4468 15.7528 15.453L15.7124 15.4935L15.7528 15.453C15.7539 15.4541 15.755 15.4553 15.756 15.4564C15.7607 15.4618 15.7645 15.468 15.7673 15.4746C15.7706 15.4827 15.7724 15.4914 15.7724 15.5001ZM15.7124 18.1735L15.7528 18.2139C15.7419 18.2249 15.7276 18.2316 15.7124 18.2331C15.7113 18.2332 15.7101 18.2333 15.709 18.2334C15.7079 18.2333 15.7068 18.2332 15.7057 18.2331C15.6905 18.2316 15.6761 18.2249 15.6652 18.2139C15.6641 18.2128 15.663 18.2117 15.662 18.2105C15.6515 18.1984 15.6457 18.1829 15.6457 18.1668C15.6457 18.1507 15.6515 18.1352 15.662 18.1231C15.663 18.1219 15.6641 18.1208 15.6652 18.1197C15.6761 18.1087 15.6905 18.102 15.7057 18.1005C15.7068 18.1004 15.7079 18.1003 15.709 18.1002C15.7101 18.1003 15.7113 18.1004 15.7124 18.1005C15.7276 18.102 15.7419 18.1087 15.7528 18.1197C15.7539 18.1208 15.755 18.1219 15.756 18.1231C15.7665 18.1352 15.7724 18.1507 15.7724 18.1668C15.7724 18.1829 15.7665 18.1984 15.756 18.2105C15.755 18.2117 15.7539 18.2128 15.7528 18.2139L15.7124 18.1735ZM18.4262 18.2139C18.4152 18.2249 18.4009 18.2316 18.3857 18.2331C18.3846 18.2332 18.3835 18.2333 18.3824 18.2334C18.3812 18.2333 18.3801 18.2332 18.379 18.2331C18.3638 18.2316 18.3495 18.2249 18.3385 18.2139C18.3374 18.2128 18.3364 18.2117 18.3353 18.2105C18.3249 18.1984 18.319 18.1829 18.319 18.1668C18.319 18.1507 18.3249 18.1352 18.3353 18.1231C18.3364 18.1219 18.3374 18.1208 18.3385 18.1197C18.3495 18.1087 18.3638 18.102 18.379 18.1005C18.3801 18.1004 18.3812 18.1003 18.3824 18.1002C18.3835 18.1003 18.3846 18.1004 18.3857 18.1005C18.4009 18.102 18.4152 18.1087 18.4262 18.1197C18.4273 18.1208 18.4284 18.1219 18.4294 18.1231C18.4399 18.1352 18.4457 18.1507 18.4457 18.1668C18.4457 18.1829 18.4399 18.1984 18.4294 18.2105C18.4284 18.2117 18.4273 18.2128 18.4262 18.2139Z",fill:"#15B7ED",stroke:"#15B7ED","stroke-width":"1.2"}),e.jsx("mask",{id:"path-3-inside-1_2059_97787",fill:"white",children:e.jsx("path",{d:"M13.5426 8.83301C13.5426 8.7004 13.49 8.57322 13.3962 8.47945C13.3024 8.38569 13.1753 8.33301 13.0426 8.33301C12.91 8.33301 12.7829 8.38569 12.6891 8.47945C12.5953 8.57322 12.5426 8.7004 12.5426 8.83301V9.49967H11.376C10.9782 9.49967 10.5966 9.65771 10.3153 9.93901C10.034 10.2203 9.87598 10.6018 9.87598 10.9997V19.6663C9.87598 20.0642 10.034 20.4457 10.3153 20.727C10.5966 21.0083 10.9782 21.1663 11.376 21.1663H20.0426C20.4405 21.1663 20.822 21.0083 21.1033 20.727C21.3846 20.4457 21.5426 20.0642 21.5426 19.6663V10.9997C21.5426 10.6018 21.3846 10.2203 21.1033 9.93901C20.822 9.65771 20.4405 9.49967 20.0426 9.49967H18.876V8.83301C18.876 8.7004 18.8233 8.57322 18.7295 8.47945C18.6358 8.38569 18.5086 8.33301 18.376 8.33301C18.2434 8.33301 18.1162 8.38569 18.0224 8.47945C17.9287 8.57322 17.876 8.7004 17.876 8.83301V9.49967H13.5426V8.83301ZM20.5426 12.4997H10.876V10.9997C10.876 10.8671 10.9287 10.7399 11.0224 10.6461C11.1162 10.5524 11.2434 10.4997 11.376 10.4997H20.0426C20.1753 10.4997 20.3024 10.5524 20.3962 10.6461C20.49 10.7399 20.5426 10.8671 20.5426 10.9997V12.4997ZM10.876 13.4997H20.5426V19.6663C20.5426 19.799 20.49 19.9261 20.3962 20.0199C20.3024 20.1137 20.1753 20.1663 20.0426 20.1663H11.376C11.2434 20.1663 11.1162 20.1137 11.0224 20.0199C10.9287 19.9261 10.876 19.799 10.876 19.6663V13.4997Z"})}),e.jsx("path",{d:"M13.5426 8.83301C13.5426 8.7004 13.49 8.57322 13.3962 8.47945C13.3024 8.38569 13.1753 8.33301 13.0426 8.33301C12.91 8.33301 12.7829 8.38569 12.6891 8.47945C12.5953 8.57322 12.5426 8.7004 12.5426 8.83301V9.49967H11.376C10.9782 9.49967 10.5966 9.65771 10.3153 9.93901C10.034 10.2203 9.87598 10.6018 9.87598 10.9997V19.6663C9.87598 20.0642 10.034 20.4457 10.3153 20.727C10.5966 21.0083 10.9782 21.1663 11.376 21.1663H20.0426C20.4405 21.1663 20.822 21.0083 21.1033 20.727C21.3846 20.4457 21.5426 20.0642 21.5426 19.6663V10.9997C21.5426 10.6018 21.3846 10.2203 21.1033 9.93901C20.822 9.65771 20.4405 9.49967 20.0426 9.49967H18.876V8.83301C18.876 8.7004 18.8233 8.57322 18.7295 8.47945C18.6358 8.38569 18.5086 8.33301 18.376 8.33301C18.2434 8.33301 18.1162 8.38569 18.0224 8.47945C17.9287 8.57322 17.876 8.7004 17.876 8.83301V9.49967H13.5426V8.83301ZM20.5426 12.4997H10.876V10.9997C10.876 10.8671 10.9287 10.7399 11.0224 10.6461C11.1162 10.5524 11.2434 10.4997 11.376 10.4997H20.0426C20.1753 10.4997 20.3024 10.5524 20.3962 10.6461C20.49 10.7399 20.5426 10.8671 20.5426 10.9997V12.4997ZM10.876 13.4997H20.5426V19.6663C20.5426 19.799 20.49 19.9261 20.3962 20.0199C20.3024 20.1137 20.1753 20.1663 20.0426 20.1663H11.376C11.2434 20.1663 11.1162 20.1137 11.0224 20.0199C10.9287 19.9261 10.876 19.799 10.876 19.6663V13.4997Z",fill:"#15B7ED"}),e.jsx("path",{d:"M12.5426 8.83301H11.3426H12.5426ZM12.5426 9.49967V10.6997H13.7426V9.49967H12.5426ZM11.376 9.49967V8.29967V9.49967ZM9.87598 10.9997H8.67598H9.87598ZM9.87598 19.6663H11.076H9.87598ZM18.876 9.49967H17.676V10.6997H18.876V9.49967ZM17.876 9.49967V10.6997H19.076V9.49967H17.876ZM13.5426 9.49967H12.3426V10.6997H13.5426V9.49967ZM20.5426 12.4997V13.6997H21.7426V12.4997H20.5426ZM10.876 12.4997H9.67598V13.6997H10.876V12.4997ZM11.376 10.4997V9.29967V10.4997ZM20.0426 10.4997V9.29967V10.4997ZM10.876 13.4997V12.2997H9.67598V13.4997H10.876ZM20.5426 13.4997H21.7426V12.2997H20.5426V13.4997ZM10.876 19.6663H9.67598H10.876ZM14.7426 8.83301C14.7426 8.38214 14.5635 7.94974 14.2447 7.63093L12.5477 9.32798C12.4164 9.19671 12.3426 9.01866 12.3426 8.83301H14.7426ZM14.2447 7.63093C13.9259 7.31211 13.4935 7.13301 13.0426 7.13301V9.53301C12.857 9.53301 12.6789 9.45926 12.5477 9.32798L14.2447 7.63093ZM13.0426 7.13301C12.5918 7.13301 12.1594 7.31211 11.8406 7.63093L13.5376 9.32798C13.4063 9.45926 13.2283 9.53301 13.0426 9.53301V7.13301ZM11.8406 7.63093C11.5217 7.94974 11.3426 8.38214 11.3426 8.83301H13.7426C13.7426 9.01866 13.6689 9.19671 13.5376 9.32798L11.8406 7.63093ZM11.3426 8.83301V9.49967H13.7426V8.83301H11.3426ZM12.5426 8.29967H11.376V10.6997H12.5426V8.29967ZM11.376 8.29967C10.6599 8.29967 9.97314 8.58414 9.46679 9.09049L11.1638 10.7875C11.2201 10.7313 11.2964 10.6997 11.376 10.6997V8.29967ZM9.46679 9.09049C8.96044 9.59683 8.67598 10.2836 8.67598 10.9997H11.076C11.076 10.9201 11.1076 10.8438 11.1638 10.7875L9.46679 9.09049ZM8.67598 10.9997V19.6663H11.076V10.9997H8.67598ZM8.67598 19.6663C8.67598 20.3824 8.96044 21.0692 9.46679 21.5755L11.1638 19.8785C11.1076 19.8222 11.076 19.7459 11.076 19.6663H8.67598ZM9.46679 21.5755C9.97314 22.0819 10.6599 22.3663 11.376 22.3663V19.9663C11.2964 19.9663 11.2201 19.9347 11.1638 19.8785L9.46679 21.5755ZM11.376 22.3663H20.0426V19.9663H11.376V22.3663ZM20.0426 22.3663C20.7587 22.3663 21.4455 22.0819 21.9518 21.5755L20.2548 19.8785C20.1985 19.9347 20.1222 19.9663 20.0426 19.9663V22.3663ZM21.9518 21.5755C22.4582 21.0692 22.7426 20.3824 22.7426 19.6663H20.3426C20.3426 19.7459 20.311 19.8222 20.2548 19.8785L21.9518 21.5755ZM22.7426 19.6663V10.9997H20.3426V19.6663H22.7426ZM22.7426 10.9997C22.7426 10.2836 22.4582 9.59683 21.9518 9.09049L20.2548 10.7875C20.311 10.8438 20.3426 10.9201 20.3426 10.9997H22.7426ZM21.9518 9.09049C21.4455 8.58414 20.7587 8.29967 20.0426 8.29967V10.6997C20.1222 10.6997 20.1985 10.7313 20.2548 10.7875L21.9518 9.09049ZM20.0426 8.29967H18.876V10.6997H20.0426V8.29967ZM20.076 9.49967V8.83301H17.676V9.49967H20.076ZM20.076 8.83301C20.076 8.38214 19.8969 7.94974 19.5781 7.63093L17.881 9.32798C17.7497 9.19671 17.676 9.01866 17.676 8.83301H20.076ZM19.5781 7.63093C19.2592 7.31211 18.8268 7.13301 18.376 7.13301V9.53301C18.1903 9.53301 18.0123 9.45926 17.881 9.32798L19.5781 7.63093ZM18.376 7.13301C17.9251 7.13301 17.4927 7.31211 17.1739 7.63093L18.871 9.32798C18.7397 9.45926 18.5616 9.53301 18.376 9.53301V7.13301ZM17.1739 7.63093C16.8551 7.94974 16.676 8.38214 16.676 8.83301H19.076C19.076 9.01866 19.0022 9.19671 18.871 9.32798L17.1739 7.63093ZM16.676 8.83301V9.49967H19.076V8.83301H16.676ZM17.876 8.29967H13.5426V10.6997H17.876V8.29967ZM14.7426 9.49967V8.83301H12.3426V9.49967H14.7426ZM20.5426 11.2997H10.876V13.6997H20.5426V11.2997ZM12.076 12.4997V10.9997H9.67598V12.4997H12.076ZM12.076 10.9997C12.076 11.1853 12.0022 11.3634 11.871 11.4946L10.1739 9.79759C9.85508 10.1164 9.67598 10.5488 9.67598 10.9997H12.076ZM11.871 11.4946C11.7397 11.6259 11.5616 11.6997 11.376 11.6997V9.29967C10.9251 9.29967 10.4927 9.47878 10.1739 9.79759L11.871 11.4946ZM11.376 11.6997H20.0426V9.29967H11.376V11.6997ZM20.0426 11.6997C19.857 11.6997 19.6789 11.6259 19.5477 11.4946L21.2447 9.79759C20.9259 9.47878 20.4935 9.29967 20.0426 9.29967V11.6997ZM19.5477 11.4946C19.4164 11.3634 19.3426 11.1853 19.3426 10.9997H21.7426C21.7426 10.5488 21.5635 10.1164 21.2447 9.79759L19.5477 11.4946ZM19.3426 10.9997V12.4997H21.7426V10.9997H19.3426ZM10.876 14.6997H20.5426V12.2997H10.876V14.6997ZM19.3426 13.4997V19.6663H21.7426V13.4997H19.3426ZM19.3426 19.6663C19.3426 19.4807 19.4164 19.3026 19.5477 19.1714L21.2447 20.8684C21.5635 20.5496 21.7426 20.1172 21.7426 19.6663H19.3426ZM19.5477 19.1714C19.6789 19.0401 19.857 18.9663 20.0426 18.9663V21.3663C20.4935 21.3663 20.9259 21.1872 21.2447 20.8684L19.5477 19.1714ZM20.0426 18.9663H11.376V21.3663H20.0426V18.9663ZM11.376 18.9663C11.5616 18.9663 11.7397 19.0401 11.871 19.1714L10.1739 20.8684C10.4927 21.1872 10.9251 21.3663 11.376 21.3663V18.9663ZM11.871 19.1714C12.0022 19.3026 12.076 19.4807 12.076 19.6663H9.67598C9.67598 20.1172 9.85508 20.5496 10.1739 20.8684L11.871 19.1714ZM12.076 19.6663V13.4997H9.67598V19.6663H12.076Z",fill:"#15B7ED",mask:"url(#path-3-inside-1_2059_97787)"})]}),k1=()=>e.jsxs("svg",{width:"31",height:"30",viewBox:"0 0 31 30",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{opacity:"0.21","fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.209961 15V24C0.209961 27.3137 2.89625 30 6.20996 30H15.21H24.21C27.5237 30 30.21 27.3137 30.21 24V15V6C30.21 2.68629 27.5237 0 24.21 0H15.21H6.20996C2.89625 0 0.209961 2.68629 0.209961 6V15Z",fill:"#8280FF"}),e.jsxs("g",{opacity:"0.46",children:[e.jsx("path",{d:"M9.87695 13.7061V15.9887C9.87695 17.2241 9.87695 17.8421 10.0243 18.3461C10.3756 19.5461 11.3143 20.4901 12.5143 20.9101C12.9143 21.0274 13.3116 21.1521 14.077 21.2647C14.5236 21.3285 14.9781 21.3136 15.4196 21.2207C15.6216 21.1794 15.7863 21.1441 15.925 21.1141C16.2516 21.0434 16.5783 20.9587 16.8783 20.8114C17.217 20.6461 17.4803 20.4741 17.8616 20.1894C18.0896 20.0194 18.2983 19.8121 18.715 19.3974L20.8796 17.2441C20.9844 17.14 21.0676 17.0161 21.1244 16.8798C21.1812 16.7434 21.2104 16.5971 21.2104 16.4494C21.2104 16.3017 21.1812 16.1554 21.1244 16.019C21.0676 15.8826 20.9844 15.7588 20.8796 15.6547C20.6673 15.4438 20.3802 15.3255 20.081 15.3255C19.7817 15.3255 19.4946 15.4438 19.2823 15.6547L17.7823 17.1474V13.7061",fill:"#8280FF"}),e.jsx("path",{d:"M9.87695 13.7061V15.9887C9.87695 17.2241 9.87695 17.8421 10.0243 18.3461C10.3756 19.5461 11.3143 20.4901 12.5143 20.9101C12.9143 21.0274 13.3116 21.1521 14.077 21.2647C14.5236 21.3285 14.9781 21.3136 15.4196 21.2207C15.6216 21.1794 15.7863 21.1441 15.925 21.1141C16.2516 21.0434 16.5783 20.9587 16.8783 20.8114C17.217 20.6461 17.4803 20.4741 17.8616 20.1894C18.0896 20.0194 18.2983 19.8121 18.715 19.3974L20.8796 17.2441C20.9844 17.14 21.0676 17.0161 21.1244 16.8798C21.1812 16.7434 21.2104 16.5971 21.2104 16.4494C21.2104 16.3017 21.1812 16.1554 21.1244 16.019C21.0676 15.8826 20.9844 15.7588 20.8796 15.6547C20.6673 15.4438 20.3802 15.3255 20.081 15.3255C19.7817 15.3255 19.4946 15.4438 19.2823 15.6547L17.7823 17.1474V13.7061",stroke:"#8280FF","stroke-width":"1.2","stroke-linecap":"round","stroke-linejoin":"round"})]}),e.jsx("path",{d:"M15.8056 12.2351V10.9671C15.8056 10.4237 16.2483 9.98372 16.7943 9.98372C17.3396 9.98372 17.7823 10.4237 17.7823 10.9671V13.9164M13.8296 13.7037V9.63372C13.8296 9.09039 14.2723 8.65039 14.8176 8.65039C15.3636 8.65039 15.8056 9.09039 15.8056 9.63372V13.7064M11.853 11.8804V13.7064V10.3037C11.8596 10.046 11.9667 9.80117 12.1513 9.62128C12.3359 9.4414 12.5835 9.34073 12.8413 9.34073C13.0991 9.34073 13.3466 9.4414 13.5312 9.62128C13.7159 9.80117 13.8229 10.046 13.8296 10.3037V13.7064M11.853 12.9031V12.3004C11.853 11.7571 11.411 11.3171 10.865 11.3171C10.3196 11.3171 9.87695 11.7571 9.87695 12.3004V14.1257",fill:"#8280FF"}),e.jsx("path",{d:"M15.8056 12.2351V10.9671C15.8056 10.4237 16.2483 9.98372 16.7943 9.98372C17.3396 9.98372 17.7823 10.4237 17.7823 10.9671V13.9164M13.8296 13.7037V9.63372C13.8296 9.09039 14.2723 8.65039 14.8176 8.65039C15.3636 8.65039 15.8056 9.09039 15.8056 9.63372V13.7064M11.853 11.8804V13.7064V10.3037C11.8596 10.046 11.9667 9.80117 12.1513 9.62128C12.3359 9.4414 12.5835 9.34073 12.8413 9.34073C13.0991 9.34073 13.3466 9.4414 13.5312 9.62128C13.7159 9.80117 13.8229 10.046 13.8296 10.3037V13.7064M11.853 12.9031V12.3004C11.853 11.7571 11.411 11.3171 10.865 11.3171C10.3196 11.3171 9.87695 11.7571 9.87695 12.3004V14.1257",stroke:"#8280FF","stroke-width":"1.2","stroke-linecap":"round","stroke-linejoin":"round"})]}),A1=()=>e.jsxs("svg",{width:"31",height:"30",viewBox:"0 0 31 30",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{opacity:"0.21","fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.709961 15V24C0.709961 27.3137 3.39625 30 6.70996 30H15.71H24.71C28.0237 30 30.71 27.3137 30.71 24V15V6C30.71 2.68629 28.0237 0 24.71 0H15.71H6.70996C3.39625 0 0.709961 2.68629 0.709961 6V15Z",fill:"#FF5555"}),e.jsx("path",{opacity:"0.46",d:"M17.0258 9.20459L17.1691 9.60033H17.59H20.3766C20.5319 9.60033 20.6767 9.64978 20.7959 9.73366H20.3766H16.7342C16.7831 9.60952 16.81 9.47456 16.81 9.33366C16.81 8.92837 16.5875 8.57222 16.2587 8.38141C16.6111 8.53521 16.8912 8.8328 17.0258 9.20459ZM20.9766 10.3337V9.91444C21.0605 10.0336 21.11 10.1784 21.11 10.3337V19.667C21.11 19.8223 21.0605 19.967 20.9766 20.0862V19.667V10.3337ZM20.3766 20.267H20.7959C20.6767 20.3509 20.5319 20.4003 20.3766 20.4003H11.0433C10.9856 20.4003 10.9341 20.3967 10.8834 20.3896C10.7891 20.3681 10.6985 20.3258 10.6181 20.267H11.0433H20.3766ZM10.4433 19.667V20.0858C10.4118 20.0412 10.388 19.9982 10.3705 19.9562C10.3313 19.8623 10.31 19.7605 10.31 19.667V10.3337C10.31 10.2336 10.3313 10.1415 10.3669 10.0596L10.3669 10.0596L10.37 10.0523C10.3898 10.005 10.4144 9.96007 10.4433 9.91817V10.3337V19.667ZM10.6258 9.73366C10.708 9.67567 10.8013 9.63493 10.9001 9.61414L10.9002 9.61425L10.9115 9.61163C10.9401 9.60504 10.9785 9.60033 11.0433 9.60033H13.83H14.2508L14.3941 9.20459C14.5287 8.8328 14.8088 8.53521 15.1613 8.38141C14.8325 8.57222 14.61 8.92837 14.61 9.33366C14.61 9.47456 14.6369 9.60952 14.6858 9.73366H11.0433H10.6258ZM15.7766 17.6003V17.7337H15.6433V17.6003H15.7766ZM15.7766 12.267V15.067H15.6433V12.267H15.7766Z",fill:"#FF0000",stroke:"#FF0000","stroke-width":"1.2"})]}),R1=()=>e.jsxs("svg",{width:"31",height:"30",viewBox:"0 0 31 30",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{opacity:"0.3","fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.209961 15V24C0.209961 27.3137 2.89625 30 6.20996 30H15.21H24.21C27.5237 30 30.21 27.3137 30.21 24V15V6C30.21 2.68629 27.5237 0 24.21 0H15.21H6.20996C2.89625 0 0.209961 2.68629 0.209961 6V15Z",fill:"#FFC342"}),e.jsx("path",{opacity:"0.78","fill-rule":"evenodd","clip-rule":"evenodd",d:"M14.5168 12.012C14.5314 11.8223 14.6896 11.6758 14.8799 11.6758C15.0669 11.6758 15.2235 11.8175 15.2421 12.0036L15.5427 15.0091L17.6983 16.2409C17.8082 16.3037 17.876 16.4206 17.876 16.5472C17.876 16.7798 17.6548 16.9488 17.4304 16.8876L14.608 16.1178C14.3766 16.0547 14.2227 15.8362 14.241 15.5971L14.5168 12.012Z",fill:"#FFB866"}),e.jsx("path",{opacity:"0.901274","fill-rule":"evenodd","clip-rule":"evenodd",d:"M11.8903 7.87521C11.6262 7.56046 11.1162 7.68093 11.0209 8.0806L10.3974 10.6947C10.3197 11.0204 10.5778 11.3289 10.912 11.3099L13.6012 11.1575C14.0121 11.1343 14.2205 10.6522 13.9559 10.3369L13.3752 9.64491C13.9578 9.44577 14.5756 9.34175 15.21 9.34175C18.3396 9.34175 20.8766 11.8788 20.8766 15.0084C20.8766 18.138 18.3396 20.6751 15.21 20.6751C12.0803 20.6751 9.54329 18.138 9.54329 15.0084C9.54329 14.483 9.61444 13.9685 9.75317 13.4739L8.46938 13.1138C8.30036 13.7164 8.20996 14.3518 8.20996 15.0084C8.20996 18.8744 11.344 22.0084 15.21 22.0084C19.076 22.0084 22.21 18.8744 22.21 15.0084C22.21 11.1424 19.076 8.00842 15.21 8.00842C14.2373 8.00842 13.311 8.20679 12.4693 8.56528L11.8903 7.87521Z",fill:"#FFB866"})]}),F1=({firstCardTitle:o,firstCardValue:v,firstCardDescription:x,secondCardTitle:p,secondCardValue:u,secondCardDescription:H,thirdCardTitle:a,thirdCardValue:j,thirdCardDescription:R,fourthCardTitle:w,fourthCardValue:m,fourthCardDescription:b})=>e.jsx("div",{className:"bg-white rounded-md",children:e.jsxs("div",{className:"grid grid-cols-4 gap-5 p-5",children:[o&&e.jsx(D,{title:o,value:v,icon:e.jsx(S1,{}),lastUpdated:x}),p&&e.jsx(D,{title:p,value:u,icon:e.jsx(k1,{}),lastUpdated:H}),a&&e.jsx(D,{title:a,value:j,icon:e.jsx(A1,{}),lastUpdated:R}),w&&e.jsx(D,{title:w,value:m,icon:e.jsx(R1,{}),lastUpdated:b})]})}),D=({title:o,value:v,icon:x,lastUpdated:p})=>e.jsx("div",{className:"p-4 bg-[#f6f8f9] rounded-md shadow-sm",children:e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("p",{className:"mt-1 text-sm font-medium text-gray-600",children:o}),e.jsx("p",{className:"text-xl font-semibold",children:v}),e.jsx("p",{className:"mt-1 text-[13px] text-gray-600",children:p})]}),e.jsx("span",{className:"text-2xl",children:x})]})}),E1=()=>{var E,B;const[o,v]=s.useState(""),[x,p]=s.useState(""),[u,H]=s.useState("create"),[a,j]=s.useState(null),[R,w]=s.useState(!1),[m,b]=s.useState({page:1,limit:10}),[W,d]=s.useState(!1),X=V1(()=>{d(!1)}),{mutate:e1}=Z1(),[y,F]=s.useState({search:"",selectedAvailabilityStatus:""}),a1=x,t1=h1({search:y.search,availabilityStatus:y.selectedAvailabilityStatus,...a1?{}:{page:m.page,limit:m.limit}}),{mutate:i1}=L1(),{mutate:s1}=N1(),{data:r,isLoading:l1}=D1(t1),{data:g}=w1(),l=(E=r==null?void 0:r.data)==null?void 0:E.statusCounts,{data:S}=z({role:"DRIVER"}),V=(S==null?void 0:S.data.users)??[],{data:k}=z({role:"VENDOR"},{enabled:V.length>0}),f=(k==null?void 0:k.data.users)??[],n1=s.useMemo(()=>f==null?void 0:f.map(t=>({value:t._id||"",label:t.commonInfo.personalInfo.fullName})),[f]),C1=s.useMemo(()=>V==null?void 0:V.map(t=>({value:t._id||"",label:t.commonInfo.personalInfo.fullName})),[V]),d1=[{label:"Available",value:"AVAILABLE"},{label:"Maintenance",value:"MAINTAINANCE"},{label:"On Duty",value:"ON-DUTY"}],r1=s.useMemo(()=>(g==null?void 0:g.data.ambulanceConfiguration.map(t=>({value:t._id||"",label:t.ambulanceType||""})))||[],[g]),I=c.get(r,"data.pagination",{}),c1=()=>{H("create"),j(null),d(!0)},o1=t=>{H("edit"),j(t),d(!0)},x1=({page:t,limit:n})=>{b(i=>({...i,page:t??1,limit:n??i.limit}))},p1=J().shape({vehicleNo:h().required("Ambulance No. is required"),ambulanceType:h().required("Ambulance Type is required"),driver:h().required("The assigned driver is required"),lastMaintainanceDate:M1().required("Last maintenance date is required"),vendor:h(),availabilityStatus:h().required("Status is required"),financialDetails:J().shape({kmRate:A(),perDayRate:A(),nightRate:A()})}),u1=t=>{u==="edit"&&a?i1({_id:a._id,entityData:t},{onSuccess:()=>{d(!1)}}):s1(t,{onSuccess:()=>{d(!1)}})},m1=()=>{var t,n;return e.jsx(g1,{initialValues:{vehicleNo:(a==null?void 0:a.vehicleNo)||"",ambulanceType:((t=a==null?void 0:a.ambulanceType)==null?void 0:t._id)||"",driver:((n=a==null?void 0:a.driver)==null?void 0:n._id)||"",lastMaintainanceDate:(a==null?void 0:a.lastMaintainanceDate)||"",availabilityStatus:(a==null?void 0:a.availabilityStatus)||"AVAILABLE",vendor:(a==null?void 0:a.vendor)||"",financialDetails:{kmRate:(a==null?void 0:a.financialDetails.kmRate)||0,perDayRate:(a==null?void 0:a.financialDetails.perDayRate)||0,nightRate:(a==null?void 0:a.financialDetails.nightRate)||0}},validationSchema:p1,onSubmit:u1,children:({errors:i,touched:C,values:Z,handleChange:L})=>{var P,O,q,_,U,Q,$,G,Y;return e.jsx(f1,{children:e.jsxs("div",{className:"p-4 relative ",children:[e.jsx(K,{as:"h3",text:u==="edit"?"Update Ambulance":"Add New Ambulance",size:"body-lg-lg",variant:"primary-blue",className:"mb-4 text-primary flex justify-center items-center"}),e.jsxs("div",{className:"border-2 border-[#e8e6e7] px-4 py-3 rounded-xl mb-3",children:[e.jsx("div",{className:"absolute cursor-pointer hover:text-red  top-5 right-10",onClick:()=>d(!1),children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",children:e.jsx("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeWidth:2,d:"m8 8l4 4m0 0l4 4m-4-4l4-4m-4 4l-4 4"})})}),e.jsx("div",{children:e.jsxs("div",{className:" grid grid-cols-3 gap-4 my-2",children:[e.jsxs("div",{children:[e.jsx(M,{label:"Ambulance No",type:"text",name:"vehicleNo",placeholder:"Enter"}),i.vehicleNo&&C.vehicleNo&&e.jsx("div",{className:"text-red text-xs mb-2",children:i.vehicleNo})]}),e.jsxs("div",{children:[e.jsx(N,{firstInput:"Select",label:"Ambulance Type",name:"ambulanceType",value:Z.ambulanceType,onChange:L,inputClassname:"",options:r1}),i.ambulanceType&&C.ambulanceType&&e.jsx("div",{className:"text-red text-xs mb-2",children:i.ambulanceType})]}),e.jsxs("div",{children:[e.jsx(N,{firstInput:"Select",label:"Status",name:"availabilityStatus",value:Z.availabilityStatus,onChange:L,options:d1}),i.availabilityStatus&&C.availabilityStatus&&e.jsx("div",{className:"text-red text-xs mb-2",children:i.availabilityStatus})]})]})}),e.jsx("div",{children:e.jsxs("div",{className:" grid grid-cols-3 gap-4 my-2",children:[e.jsxs("div",{children:[e.jsx(N,{firstInput:"Select",label:"Driver Assigned",name:"driver",value:Z.driver,onChange:L,options:C1}),i.driver&&C.driver&&e.jsx("div",{className:"text-red text-xs mb-2",children:i.driver})]}),e.jsxs("div",{children:[e.jsx(M,{label:"Last Maintainenance",type:"date",name:"lastMaintainanceDate",placeholder:"Enter date"}),i.lastMaintainanceDate&&C.lastMaintainanceDate&&e.jsx("div",{className:"text-red text-xs mb-2",children:i.lastMaintainanceDate})]}),e.jsxs("div",{children:[e.jsx(N,{firstInput:"Select",label:"Vendor",name:"vendor",value:Z.vendor,onChange:L,options:n1}),i.vendor&&C.vendor&&e.jsx("div",{className:"text-red text-xs mb-2",children:i.vendor})]})]})}),e.jsxs("div",{children:[e.jsx(K,{text:"Ambulance Cost Details",className:"text-base"}),e.jsx("div",{className:"border-b-2 border-gray-200"})]}),e.jsx("div",{children:e.jsxs("div",{className:" grid grid-cols-3 gap-4 my-2",children:[e.jsxs("div",{children:[e.jsx(M,{label:"Price Per KM (NRs)",type:"number",name:"financialDetails.kmRate",min:"0",placeholder:"500"}),((P=i.financialDetails)==null?void 0:P.kmRate)&&((O=C.financialDetails)==null?void 0:O.kmRate)&&e.jsx("div",{className:"text-red text-xs mb-2",children:(q=i.financialDetails)==null?void 0:q.kmRate})]}),e.jsxs("div",{children:[e.jsx(M,{label:"Price Per Day (NRs)",type:"number",min:"0",name:"financialDetails.perDayRate",placeholder:"500"}),((_=i.financialDetails)==null?void 0:_.perDayRate)&&((U=C.financialDetails)==null?void 0:U.perDayRate)&&e.jsx("div",{className:"text-red text-xs mb-2",children:(Q=i.financialDetails)==null?void 0:Q.perDayRate})]}),e.jsxs("div",{children:[e.jsx(M,{label:"Price Per Night Charge (NRs)",type:"number",name:"financialDetails.nightRate",placeholder:"500"}),(($=i.financialDetails)==null?void 0:$.nightRate)&&((G=C.financialDetails)==null?void 0:G.nightRate)&&e.jsx("div",{className:"text-red text-xs mb-2",children:(Y=i.financialDetails)==null?void 0:Y.nightRate})]})]})})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx("button",{type:"button",className:"px-4 py-2 bg-gray-300 text-black rounded",onClick:()=>d(!1),children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary text-white rounded",children:u==="edit"?"Update":"Create"})]})]})})}})},T={columns:[{title:"S.N.",key:"ambId"},{title:"AMB no.",key:"vehicleNo"},{title:"Type",key:"ambulanceType"},{title:"Driver",key:"driverName"},{title:"Last Maintenance",key:"lastMaintenanceDate"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:((B=r==null?void 0:r.data.ambulances)==null?void 0:B.map((t,n)=>({key:n,ambId:n+1,vehicleNo:c.get(t,"vehicleNo","N/A"),ambulanceType:c.get(t,"ambulanceType.ambulanceType","N/A"),driverName:c.get(t,"driver.commonInfo.personalInfo.fullName","N/A"),lastMaintenanceDate:c.get(t,"lastMaintainanceDate",""),status:e.jsx(H1,{status:c.get(t,"availabilityStatus","")}),action:e.jsx(v1,{onEdit:()=>{o1(t)},onDelete:()=>{e1({id:JSON.stringify([c.get(t,"_id","-")])})}})})))??[]};return e.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[e.jsx(j1,{headerTitle:"Ambulance List",onSearch:!0,toSearch:"Search by vehicle no",onSearchFunc:t=>{F({...y,search:t}),b(n=>({...n,page:1}))},onAvailabilityStatus:!0,onAvailabilityStatusSelectFunc:t=>F({...y,selectedAvailabilityStatus:t}),onFilter:!0,button:!0,buttonText:"Add Ambulance",buttonAction:c1}),e.jsx(F1,{firstCardTitle:"Available",firstCardValue:l==null?void 0:l.AVAILABLE,secondCardTitle:"On Duty",secondCardValue:l==null?void 0:l["ON-DUTY"],thirdCardTitle:"Under Maintenance",thirdCardValue:l==null?void 0:l.MAINTAINANCE}),e.jsx("div",{className:"bg-white rounded-md",children:e.jsx(b1,{color:"bg-white",textcolor:"text-gray-400",columns:T.columns,rows:T.rows,loading:l1,pagination:{totalPage:I.pages||1,currentPage:I.page||1,limit:m.limit||10,onClick:x1}})}),W&&e.jsx(y1,{ref:X,children:m1()})]})};export{E1 as AmbulanceList};
