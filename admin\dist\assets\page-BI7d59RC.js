import{a2 as t,af as i,ad as l,aQ as n,aj as r}from"./index-ClX9RVH0.js";const u=[{testId:"TRQ-001",name:"<PERSON>",testType:"Blood Test",testedDate:"2023-05-20",resultDate:"2023-05-20",result:"Positive",doctorName:"<PERSON><PERSON> <PERSON>",status:"in progress"},{testId:"TRQ-002",name:"<PERSON>",testType:"Liver Functino Test",testedDate:"2023-05-20",resultDate:"2023-05-20",result:"Negative",doctorName:"<PERSON><PERSON>",status:"Pending"}],d=()=>{const e={columns:[{key:"id",title:"S.N."},{key:"testId",title:"Test ID"},{key:"name",title:"Patient Name"},{key:"testType",title:"Test Type"},{key:"testedDate",title:"Date Tested"},{key:"resultDate",title:"Result Date"},{key:"result",title:"Result"},{key:"doctor<PERSON><PERSON>",title:"Doctor Name"},{key:"status",title:"Status"},{key:"action",title:"Action"}],rows:u.map((o,s)=>({key:s,...o,id:s+1,action:t.jsx(i,{onEdit:()=>{},onDelete:()=>{}})}))},a=l();return t.jsxs("div",{children:[t.jsx(n,{headerTitle:"Test Results",onSearch:!0,onDatePicker:!0,onFilter:!0,button:!0,buttonText:"Add Test Result",buttonAction:()=>{a("/test-results/add-test-result")}}),t.jsx("section",{className:"mt-4",children:t.jsx(r,{columns:e.columns,rows:e.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})})]})};export{d as TestResultPage};
