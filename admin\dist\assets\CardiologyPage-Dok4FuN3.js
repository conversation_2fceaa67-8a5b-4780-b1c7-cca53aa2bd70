import{a5 as p,ad as g,ae as x,a2 as e,ag as h,ah as y,ai as b,aj as j}from"./index-ClX9RVH0.js";import{C as f,D as v}from"./Svg-BMTGOzwv.js";import{D as C}from"./DepartmentHeader-Aj6XBXn4.js";const P=()=>{const[o,s]=p.useState("Patient"),n=g(),a={columns:[{title:"Paitent Id",key:"tokenid"},{title:"Patient Name",key:"patientName"},{title:"Date Assigned",key:"date"},{title:"Contact Number",key:"treatment"},{title:"Appointment Date",key:"doctorName"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:x.map(({tokenId:t,patientName:r,date:l,doctorName:c,status:d,treatment:m},u)=>({key:u,tokenid:t,patientName:r,date:l,doctorName:c,status:e.jsx(y,{status:d}),treatment:m,action:e.jsx("button",{onClick:()=>n(h.CARDILOGY),className:"bg-primary py-2 px-4 text-white rounded",children:"Checkup"})}))},i=t=>{console.log(t,"onSearch")};return e.jsxs(e.Fragment,{children:[e.jsx(C,{title:"Cardiology",doctorName:"Dr. John Smith",services:["Diagnostic","Interventional","Electrophysiology","Cardiac Rehabilitation","Preventive Cardiology"],patientServed:100,doctorImage:e.jsx(v,{}),followUpPatient:110,newPatient:20,revenueGenerated:4900,headerTitle:"Department",icon:e.jsx(f,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"mt-5 pb-2 flex justify-between items-center",children:[e.jsx(b,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:o,onTabChange:t=>s(t)}),e.jsx("div",{children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name id",onChange:t=>i(t.target.value),className:"pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(j,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{P as CardiologyDepartmentPage};
