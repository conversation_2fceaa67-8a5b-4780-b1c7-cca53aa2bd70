import { IRoutesConfig } from "../Interface/global.interface";
import { lazy } from "react";
import { FrontendRoutes } from "./FrontendRoutes";
import DashboardIndex from "../pages/Dashboard/DashboardIndex";
// Lazy load all components
const EmergencyRoomPage = lazy(
  () => import("../pages/Emergency/EmergencyRoom/EmergencyRoomPage")
);
const Add_Events = lazy(() => import("../pages/Events/Add_Events"));
const Events = lazy(() => import("../pages/Events/Events"));
const DayCareUnit = lazy(() =>
  import("../pages/IPD/DayCareUnit/DayCareUnit").then((module) => ({
    default: module.DayCareUnit,
  }))
);
const DischargeGeneralWard = lazy(() =>
  import("../pages/IPD/GeneralWard/COMPONENTS/DischargeGeneralWard").then(
    (module) => ({ default: module.DischargeGeneralWard })
  )
);
const SurgeryGeneralWard = lazy(() =>
  import("../pages/IPD/GeneralWard/COMPONENTS/SurgeryGeneralWard").then(
    (module) => ({ default: module.SurgeryGeneralWard })
  )
);
const TransferGeneralWard = lazy(() =>
  import("../pages/IPD/GeneralWard/COMPONENTS/TransferGeneralWard").then(
    (module) => ({ default: module.TransferGeneralWard })
  )
);
const TreatmentGeneralWard = lazy(() =>
  import("../pages/IPD/GeneralWard/COMPONENTS/TreatmentGeneralWard").then(
    (module) => ({ default: module.TreatmentGeneralWard })
  )
);
const GeneralWardPage = lazy(() =>
  import("../pages/IPD/GeneralWard/GeneralWardPage").then((module) => ({
    default: module.GeneralWardPage,
  }))
);
const GyneObservationWard = lazy(() =>
  import("../pages/IPD/GyneObservationWard/GyneObservation").then((module) => ({
    default: module.GyneObservationWard,
  }))
);
const HighDependencyUnit = lazy(() =>
  import("../pages/IPD/HighDependencyUnit/HighDependency").then((module) => ({
    default: module.HighDependencyUnit,
  }))
);
const ICU = lazy(() =>
  import("../pages/IPD/ICU/ICU").then((module) => ({ default: module.ICU }))
);
const CashEquipment = lazy(
  () => import("../pages/Donor/CashEquipment/CashEquipment")
);
const NICU = lazy(() =>
  import("../pages/IPD/NICU/NICU").then((module) => ({ default: module.NICU }))
);
const PostOperativeWard = lazy(() =>
  import("../pages/IPD/PostOperativeWard/PostOperativeWard").then((module) => ({
    default: module.PostOperativeWard,
  }))
);

const Voucher = lazy(() => import("../pages/Expenses/Voucher/VoucherIndex"));
// Inventory Management
const CategoryList = lazy(
  () => import("../pages/InventoryManagement/CategoryList/CategoryList")
);
const AddCategory = lazy(
  () =>
    import("../pages/InventoryManagement/CategoryList/components/AddCategory")
);
const InventoryPage = lazy(
  () => import("../pages/InventoryManagement/Inventory/InventoryPage")
);
const SubCategory = lazy(
  () => import("../pages/InventoryManagement/SubCategoryList/SubCategory")
);
const AddSubCategory = lazy(
  () =>
    import(
      "../pages/InventoryManagement/SubCategoryList/components/AddSubCategories"
    )
);
const ProductList = lazy(
  () => import("../pages/InventoryManagement/ProductList/ProductList")
);
const ShiftOverView = lazy(
  () => import("../pages/Dashboard/NurseDashboaed/ShiftOverview/ShiftOverView")
);

const BalanceSheet = lazy(
  () => import("../pages/Accounting/BalanceSheet/BalanceSheet")
);
const Receiver = lazy(() => import("../pages/Donor/Receiever/Receiver"));
const PatientDetailIPD = lazy(
  () => import("../pages/Dashboard/NurseDashboaed/Component-1/PatientDetail")
);

// Core pages that might be needed immediately (keep eager loading for critical paths)
import { NotFoundPage } from "../pages/NotFoundPage";
import { LoginPage, RadiologyInventory } from "../pages";
import { Unauthorized } from "../pages/unauthorized/Unauthorized.page";
import AddGeneralCheckupForm from "../pages/ODP/GeneralCheckup/Components/AddGeneralCheckupForm";
import RadiologyPurchase from "../pages/Radiology/Purchase/RadiologyPurchase";
import RadiologyTestRequest from "../pages/Radiology/TestRequest/RadiologyTestRequest";
import RadiologyEditReport from "../pages/Radiology/TestRequest/components/RadiologyEditReport";
import IpdPatientForm from "../pages/IPD/GeneralWard/COMPONENTS/IpdPatientForm";
import TestListConfig from "../pages/Radiology/TestConfig/ServiceType/TestListConfig";
import ServiceType from "../pages/Radiology/TestConfig/ServiceType/ServiceType";
import RadiologyDueList from "../pages/Radiology/BillingAndConfig/DueLlist/RadiologyDueList";
import { CertificatePage } from "../pages/Certificate/Certificate.page";
import { AddCertificate } from "../pages/Certificate/AddCertificate";
import AddAllDepartment from "../pages/Settings/DepartmentConfig/AddAllDepartment";
import RoleManagementForm from "../pages/Settings/RoleManagement/Components/RoleManagementForm";
import { WardConfigPage } from "../pages/Settings/ward-config/page";
import UserManagement from "../pages/Settings/UserManagement/UserManagement";
import { AddUser } from "../pages/Settings/UserManagement/AddUser/AddUser";
import { AddNotification } from "../pages/Settings/NotificationSetup/AddNotification/AddNotification";
import Report from "../pages/Settings/Report/Report";
import PayrollList from "../pages/Payroll/PayrollList";
import PayrollConfig from "../pages/Payroll/PayrollConfig";
import ExpensesList from "../pages/Expenses/ExpensesList";
import { AddExpense } from "../pages/Expenses/components/AddExpense";
import ExpenseCategory from "../pages/Expenses/ExpenseCategory";
import { Bank } from "../pages/Bank/bank.page";
import { TransactionDetails } from "../pages/Bank/transaction-details.page";
import AccountTokenList from "../pages/TokenManagement/Account/AccountTokenList";
import AppointmentTokenList from "../pages/TokenManagement/Appointment/AppointmentTokenList";
import TokenConfig from "../pages/TokenManagement/Appointment/TokenConfig";
import BloodDonorList from "../pages/Donor/BloodDonorList/BloodDonorList";
import AddDonor from "../pages/Donor/BloodDonorList/components/AddDonor";
import OrganDonorList from "../pages/Donor/OrganDonorList/OrganDonorList";
import BloodBank from "../pages/Donor/BloodBank/BloodBank";
import TypeService from "../pages/Settings/CommissionMgt/Reseller/Type & Service/TypeService";
import AddType from "../pages/Settings/CommissionMgt/Reseller/Type & Service/Component/AddType";
import AddServicecom from "../pages/Settings/CommissionMgt/Reseller/Type & Service/Component/AddService";
import CommissionList from "../pages/Settings/CommissionMgt/CommissionConfig/CommissionList/CommissionList";
import AddCommission from "../pages/Settings/CommissionMgt/CommissionConfig/AddCommission/AddCommission";
import LabPurchaseList from "../pages/LabDashboard/LabPurchase/LabPurchase";
import LabTestResult from "../pages/LabDashboard/LabTestResult/LabTestResult";
import TestRequestList from "../pages/LabDashboard/TestRequestList/TestRequestList";
import EditReport from "../pages/LabDashboard/TestRequestList/components/EditReport";
import SampleCollection from "../pages/LabDashboard/SampleCollection/SampleCollection";
import LabInventory from "../pages/LabDashboard/LabInventory/LabInventory";
import LabProductList from "../pages/LabDashboard/LabProductList/LabProductList";
import LabDepartment from "../pages/LabDashboard/LabDepartment/LabDepartment";
import LabSubDepartment from "../pages/LabDashboard/LabDepartment/LabSubDepartment";
import InvoiceList from "../pages/LabDashboard/LabBilling/InvoiceList";
import TestTypeConfig from "../pages/LabDashboard/LabTestConfig/TestTypeConfig";
// import { default as NotificationsPage } from '../pages/Settings/NotificationSetup/NotificationSetUp';
import DoctorDetailsPage from "../pages/people/Medical-Staff/[Id]/DoctorDetails/DoctorDetailsPage";

// import { } from '../pages/PurchaseManagement/ProductList/ProductList.page';
import ViewVendor from "../pages/PurchaseManagement/VendorList/components/ViewVendor";
import PermissionIndex from "../pages/Settings/RoleManagement/RoleManagementIndex";
import NotificationsPage from "../pages/Settings/NotificationSetup/NotificationSetUp";
import ViewDetailsReseller from "../pages/Settings/CommissionMgt/CommissionConfig/CommissionList/DetailCommission/ViewDetailsReseller";
import { PharmacyDashboard } from "../pages/Dashboard/pharmacyDashboard/pharmacyDashboard";
import BlogPageIndex from "../website/pages/blog/BlogPageIndex";
import GalleryPageIndex from "../website/pages/gallery/GalleryPageIndex";
import ContactPageIndex from "../website/pages/contact/ContactPageIndex";
import DynamicEquipmentForm from "../pages/Donor/CashEquipment/components/DynamicEquipmentForm";
import LabDashboard from "../pages/Dashboard/LabDashboard/LabDashboard";
import CampaignPage from "../website/pages/compaign/CompaignPage";
import ProfileIndex from "../pages/Profile/components/ProfileIndex";
import Availability from "../pages/OperationTheater/surgery availability/Availability";
import AvailabilityDetail from "../pages/OperationTheater/surgery availability/Component/AvailabilityDetail";
import EditPatientForm from "../pages/IPD/GeneralWard/COMPONENTS/EditPatientForm";
import AddLabTestRequest from "../pages/LabDashboard/TestRequestList/components/AddLabTestRequest";
import AddRadiologyTestRequest from "../pages/Radiology/TestRequest/components/AddRadiologyTestRequest";
import TrainingIndex from "../website/pages/training/TrainingIndex";
import InventoryDetails from "../pages/Pharmacy/inventory/components/InventoryDetails";
import AccountDashboard from "../pages/Dashboard/AccountDashboard/AccountDashboard";
import GeneralLedgerBalance from "../pages/Accounting/GeneralLedgerBalance/GeneralLedgerBalance";
import SalesReturn from "../pages/Accounting/SalesReturn/SalesReturn";
import SalesVoucher from "../pages/Accounting/SalesVoucher/SalesVoucher";
import TransactionsList from "../pages/Accounting/TransactionList/TransactionsList";
import RadiologyDepartment from "../pages/Radiology/TestConfig/Department/RadiologyDepartment";
import CustomForm from "../pages/Donor/Receiever/customform/CustomForm";
import NurseDashboard from "../pages/Dashboard/NurseDashboaed/NurseDashboard";
import Doctors from "../pages/ODP/Doctors/Doctors";
import InPatientList from "../pages/Patient/PatientList/InPatientList";
import InventryManag from "../pages/Dashboard/NurseDashboaed/InventryManag/inventryManag";
import PatientDetailsA from "../pages/Patient/Patient-Details/PatientDetailsA";

// ODP Department Pages
const AppointmentPage = lazy(() =>
  import("../pages/ODP").then((module) => ({ default: module.AppointmentPage }))
);

const VendorBillingPage = lazy(() =>
  import("../pages/PurchaseManagement/VendorBilling/VendorBilling.page").then(
    (module) => ({
      default: module.VendorBillingPage,
    })
  )
);

const VendorProductList = lazy(() =>
  import(
    "../pages/PurchaseManagement/VendorProductList/VendorProductList.page"
  ).then((module) => ({
    default: module.VendorProductList,
  }))
);

const VendorOrderPage = lazy(() =>
  import("../pages/PurchaseManagement/VendorOrder/VendorOrder.page").then(
    (module) => ({
      default: module.VendorOrderPage,
    })
  )
);

const RadioReport = lazy(() =>
  import("../pages/Radiology").then((module) => ({
    default: module.RadiologyReport,
  }))
);
const AddAppointment = lazy(() =>
  import("../pages/ODP/Appointment/Components/AddAppointmentPage").then(
    (module) => ({ default: module.AddAppointment })
  )
);
const OPDAppointmentPage = lazy(() =>
  import("../pages/ODP/Appointment/OPDAppointmentPage").then((module) => ({
    default: module.default,
  }))
);
const CardiologyDepartmentPage = lazy(() =>
  import("../pages/ODP/Cardiology/CardiologyPage").then((module) => ({
    default: module.CardiologyDepartmentPage,
  }))
);
const AddCardiology = lazy(() =>
  import("../pages/ODP/Cardiology/Components/AddCardiologyPage").then(
    (module) => ({ default: module.AddCardiology })
  )
);
const AddDentalCarePage = lazy(() =>
  import("../pages/ODP/DentalCare/Components/AddDentalCarePage").then(
    (module) => ({ default: module.AddDentalCarePage })
  )
);
const DentalCareDepartmentPage = lazy(() =>
  import("../pages/ODP/DentalCare/DentalCarePage").then((module) => ({
    default: module.DentalCareDepartmentPage,
  }))
);
const AddDermatology = lazy(() =>
  import("../pages/ODP/Dermatology/Components/AddDermotologyPage").then(
    (module) => ({ default: module.AddDermatology })
  )
);
const DermatologyDepartmentPage = lazy(() =>
  import("../pages/ODP/Dermatology/DermatologyPage").then((module) => ({
    default: module.DermatologyDepartmentPage,
  }))
);
const AddENT = lazy(() =>
  import("../pages/ODP/ENT/Components/AddENTPage").then((module) => ({
    default: module.AddENT,
  }))
);
const ENTDepartmentPage = lazy(() =>
  import("../pages/ODP/ENT/EntDepartmentPage").then((module) => ({
    default: module.ENTDepartmentPage,
  }))
);
const AddGeneralPreCheckupForm = lazy(() =>
  import(
    "../pages/ODP/GeneralCheckup/Components/AddGeneralPreCheckupForm"
  ).then((module) => ({ default: module.AddGeneralPreCheckupForm }))
);
const GeneralCheckupPage = lazy(() =>
  import("../pages/ODP/GeneralCheckup/GeneralCheckupPage").then((module) => ({
    default: module.GeneralCheckupPage,
  }))
);
const AddPhysicsian = lazy(() =>
  import("../pages/ODP/GeneralPhysics/Components/AddGeneralPhysicsPage").then(
    (module) => ({ default: module.AddPhysicsian })
  )
);
const GeneralPhysicianPage = lazy(() =>
  import("../pages/ODP/GeneralPhysics/GeneralPhysicianPage").then((module) => ({
    default: module.GeneralPhysicianPage,
  }))
);
const AddGynecologyPage = lazy(() =>
  import("../pages/ODP/Gynecology/Components/AddGynecologyPage").then(
    (module) => ({ default: module.AddGynecologyPage })
  )
);
const GynecologyDepartmentPage = lazy(() =>
  import("../pages/ODP/Gynecology/GynecologyPage").then((module) => ({
    default: module.GynecologyDepartmentPage,
  }))
);
const AddNeuroLogy = lazy(() =>
  import("../pages/ODP/Neurology/Components/AddNeuroLogyPage").then(
    (module) => ({ default: module.AddNeuroLogy })
  )
);
const NeurologyDepartmentPage = lazy(() =>
  import("../pages/ODP/Neurology/NeurologyPage").then((module) => ({
    default: module.NeurologyDepartmentPage,
  }))
);
const OrthomologyDepartmentPage = lazy(() =>
  import("../pages/ODP/Orthomology/OrthomologyPage").then((module) => ({
    default: module.OrthomologyDepartmentPage,
  }))
);
const AddOrthopedics = lazy(() =>
  import("../pages/ODP/Orthopedics/Components/AddOrthopedicsPage").then(
    (module) => ({ default: module.AddOrthopedics })
  )
);
const OrthopedicsDepartmentPage = lazy(() =>
  import("../pages/ODP/Orthopedics/OrthopedicsPage").then((module) => ({
    default: module.OrthopedicsDepartmentPage,
  }))
);
const PediatricDepartmentPage = lazy(() =>
  import("../pages/ODP/Pediatrics/PediatricsPage").then((module) => ({
    default: module.PediatricDepartmentPage,
  }))
);

// Purchase Management
const PurchaseListPage = lazy(
  () => import("../pages/PurchaseManagement/PurchaseList/PurchaseList")
);
const AddNewPurchase = lazy(
  () =>
    import("../pages/PurchaseManagement/PurchaseList/components/AddNewPurchase")
);
const PurchaseOrderPage = lazy(
  () => import("../pages/PurchaseManagement/PurchaseOrder/PurchaseOrder")
);
const EditOrders = lazy(
  () => import("../pages/PurchaseManagement/PurchaseOrder/components/EditOrder")
);
const NewPurchaseOrder = lazy(
  () =>
    import(
      "../pages/PurchaseManagement/PurchaseOrder/components/NewPurchaseOrder"
    )
);
const PurchaseReturn = lazy(
  () => import("../pages/PurchaseManagement/PurchaseReturn/PurchaseReturn")
);
const AddPurchase = lazy(
  () =>
    import("../pages/PurchaseManagement/PurchaseReturn/components/AddPurchase")
);
const VendorListPage = lazy(
  () => import("../pages/PurchaseManagement/VendorList/VendorListPage")
);
const AddVendor = lazy(
  () => import("../pages/PurchaseManagement/VendorList/components/AddVendor")
);
const EditVendor = lazy(
  () => import("../pages/PurchaseManagement/VendorList/components/EditVendor")
);

// Auth Pages

const ForgotPage = lazy(() =>
  import("../pages/Auth/ForgotPage").then((module) => ({
    default: module.ForgotPage,
  }))
);
const VerificationPage = lazy(() =>
  import("../pages/Auth/VerificationPage").then((module) => ({
    default: module.VerificationPage,
  }))
);

const OperationForm = lazy(
  () => import("../pages/OperationTheater/OTConfiguration/Operation")
);

// Financial Operations
const CreateInvoicePage = lazy(
  () => import("../pages/FinancialOps/Invoice/CreateInvoice")
);
const InvoicePage = lazy(
  () => import("../pages/FinancialOps/Invoice/InvoicePage")
);
// const CreateReceiptPage = lazy(
//   () => import('../pages/FinancialOps/Payment/CreateReceiptPage')
// );
const PaymentPage = lazy(
  () => import("../pages/FinancialOps/Payment/PaymentPage")
);

// Pricing Configuration
const Category = lazy(
  () => import("../pages/PricingConfig/CategoryList/Category")
);
const Addpricingcategory = lazy(
  () =>
    import("../pages/PricingConfig/CategoryList/components/Addpricingcategory")
);
const AddService = lazy(
  () => import("../pages/PricingConfig/ServiceItem/components/AddService")
);
const ServiceItemList = lazy(
  () => import("../pages/PricingConfig/ServiceItem/ServiceItemList")
);

// Settings
const DepartmentConfigPage = lazy(
  () => import("../pages/Settings/DepartmentConfig/DepartmentConfigPage")
);

// Ambulance Management
const AmbulanceList = lazy(() =>
  import("../pages/Ambulance/AmbulanceList/AmbulanceList").then((module) => ({
    default: module.AmbulanceList,
  }))
);
const AmbulanceType = lazy(() =>
  import("../pages/Ambulance/AmbulanceType/AmbulanceType").then((module) => ({
    default: module.AmbulanceType,
  }))
);
const AddAmbulanceType = lazy(() =>
  import("../pages/Ambulance/AddAmbulanceType/AddAmbulanceType").then(
    (module) => ({ default: module.AddAmbulanceType })
  )
);
const AmbulanceInquiry = lazy(() =>
  import("../pages/Ambulance/AmbulanceInquiry/AmbulanceInquiry").then(
    (module) => ({ default: module.AmbulanceInquiry })
  )
);
const AddAmbulanceInquiryForm = lazy(
  () =>
    import(
      "../pages/Ambulance/AmbulanceInquiry/components/AddAmbulanceInquiryForm"
    )
);
const EditAmbulanceInquiryForm = lazy(
  () =>
    import(
      "../pages/Ambulance/AmbulanceInquiry/components/EditAmbulanceInquiryForm"
    )
);
const PriceConfig = lazy(
  () => import("../pages/Ambulance/PriceConfig/PriceConfig")
);

// Attendance Management
const AttendanceList = lazy(() =>
  import("../pages/Attendance/AttendanceList").then((module) => ({
    default: module.AttendanceList,
  }))
);
const AddAttendance = lazy(() =>
  import("../pages/Attendance/Components/AddAttendance").then((module) => ({
    default: module.AddAttendance,
  }))
);
const ViewAttendance = lazy(() =>
  import("../pages/Attendance/Components/ViewAttendance").then((module) => ({
    default: module.ViewAttendance,
  }))
);

const RadiologyProductList = lazy(() =>
  import("../pages/Radiology").then((module) => ({
    default: module.RadiologyProductList,
  }))
);

const RadiologyInvoiceList = lazy(() =>
  import("../pages/Radiology").then((module) => ({
    default: module.RadiologyInvoice,
  }))
);

// Bed Allocation
const BedAllocationPage = lazy(
  () => import("../pages/BedAllocation/BedAllocationList/BedAllocation")
);
const AssignBedAllocation = lazy(
  () => import("../pages/BedAllocation/BedAllocationList/components/AssignBed")
);
const EditBedAllocation = lazy(
  () =>
    import(
      "../pages/BedAllocation/BedAllocationList/components/EditBedAllocation"
    )
);
const RoomDetailsPage = lazy(
  () => import("../pages/BedAllocation/RoomsDetail/RoomDetails")
);
const AddRooms = lazy(
  () => import("../pages/BedAllocation/RoomsDetail/components/AddRoom")
);
const BedCategory = lazy(
  () => import("../pages/BedAllocation/BedCategory/BedCategory")
);
const BedList = lazy(() => import("../pages/BedAllocation/BedList/BedList"));

// Operation Theater
const SurgeryAvailability = lazy(
  () => import("../pages/OperationTheater/surgery availability/Availability")
);
const GeneralOT = lazy(
  () => import("../pages/OperationTheater/General/GeneralOT")
);
const POF = lazy(
  () => import("../pages/OperationTheater/Pre-OperativeForm/POF")
);
const PatientLayout = lazy(
  () => import("../pages/OperationTheater/components/PatientLayout")
);
const Otconfiguration = lazy(
  () => import("../pages/OperationTheater/OTConfiguration/Otconfiguration")
);
const EditPage = lazy(
  () => import("../pages/OperationTheater/components/EditPage")
);

// Accounting Tax Invoice
const PaymentVoucher = lazy(
  () => import("../pages/Accounting/PaymentVoucher/PaymentVouchers")
);

const PurchaseReturnIvoice = lazy(
  () => import("../pages/Accounting//PurchaseReturn/PurchaseReturnIvoice")
);

const IncomeStatement = lazy(
  () => import("../pages/Accounting/IncomeStatement/IncomeStatement")
);

// Pharmacy
const Prescriptions = lazy(() => import("../pages/Pharmacy/Prescriptions"));
const Add_Prescriptions = lazy(
  () => import("../pages/Pharmacy/Add_Prescriptions")
);
const MedicineRequestPage = lazy(() =>
  import("../pages/Pharmacy/Medicine-Request/MedicineRequest.page").then(
    (module) => ({ default: module.MedicineRequestPage })
  )
);
const MedicineRequestDetailsPage = lazy(() =>
  import(
    "../pages/Pharmacy/Medicine-Request/[id]/MedicineRequestDetails.page"
  ).then((module) => ({ default: module.MedicineRequestDetailsPage }))
);
const PharmacyInventoryPage = lazy(() =>
  import("../pages/Pharmacy/inventory/PharmacyInventory.page").then(
    (module) => ({ default: module.PharmacyInventoryPage })
  )
);
const PharmacyProductListPage = lazy(() =>
  import("../pages/Pharmacy/inventory/PharmacyProductList.page").then(
    (module) => ({ default: module.PharmacyProductListPage })
  )
);
const PosPage = lazy(() =>
  import("../pages/Pharmacy/pos/PharmacyPOS.page").then((module) => ({
    default: module.PharmacyPosPage,
  }))
);
const PharmacyExpense = lazy(() =>
  import("../pages/Pharmacy/Finance/expense/expense.page").then((module) => ({
    default: module.PharmacyExpense,
  }))
);
const SalesPage = lazy(() =>
  import("../pages/Pharmacy/Finance/sales/sales.page").then((module) => ({
    default: module.SalesPage,
  }))
);
const PharmacyFinanceReportsPage = lazy(() =>
  import("../pages/Pharmacy/Finance/reports/reports.page").then((module) => ({
    default: module.PharmacyFinanceReportsPage,
  }))
);

// Patient Management
const AddPatient = lazy(
  () => import("../pages/Emergency/AddPatient/AddPatient")
);
const PatientList = lazy(
  () => import("../pages/Patient/PatientList/PatientList")
);
const PatientDetails = lazy(
  () => import("../pages/Patient/Patient-Details/PatientDetailsA")
);
const PatientViewsDetails = lazy(
  () =>
    import(
      "../pages/ODP/GeneralCheckup/Components/ViewPatientDetails/PatientViewsDetails"
    )
);

// Staff Management
const StaffList = lazy(() => import("../pages/Staff/StaffList/StaffList"));
const StaffDetails = lazy(
  () => import("../pages/Staff/StaffDetails/StaffDetails")
);
const AddStaff = lazy(() =>
  import("../pages/Staff/AddStaff/AddStaff").then((module) => ({
    default: module.AddStaff,
  }))
);

const JobApplicationPage = lazy(() =>
  import("../website/pages/career/application.page").then((module) => ({
    default: module.JobApplicationPage,
  }))
);

// Medical Staff
const DoctorList = lazy(
  () => import("../pages/people/Medical-Staff/Doctor/DoctorList/DoctorList")
);
const AddDoctor = lazy(() =>
  import("../pages/people/Medical-Staff/Doctor/AddDoctor/AddDoctor").then(
    (module) => ({ default: module.AddDoctor })
  )
);
const DoctorAvailability = lazy(() =>
  import("../pages/people/Medical-Staff/Doctor/DoctorAvailability").then(
    (module) => ({ default: module.DoctorAvailability })
  )
);
const DoctorReviews = lazy(
  () =>
    import("../pages/people/Medical-Staff/Doctor/DoctorReviews/DoctorReviews")
);
const NurseList = lazy(
  () => import("../pages/people/Medical-Staff/Nurse/NurseList")
);
const LabTechnicianListPage = lazy(() =>
  import(
    "../pages/people/Medical-Staff/Lab-Technicain/LabTechnicianList.page"
  ).then((module) => ({ default: module.LabTechnicianListPage }))
);
const PharmarcistsListPage = lazy(() =>
  import(
    "../pages/people/Medical-Staff/Pharmacists/Pharamacists-list.page"
  ).then((module) => ({ default: module.PharmarcistsListPage }))
);
const OverAllStaffDetails = lazy(
  () => import("../pages/people/Medical-Staff/[Id]/staff-details.page")
);
const NurseDetailsPage = lazy(
  () =>
    import("../pages/people/Medical-Staff/[Id]/NurseDetails/NurseDetailsPage")
);

// Dashboards
const DoctorDashboard = lazy(
  () => import("../pages/Dashboard/DoctorDashboard/DoctorDashboard")
);
const AdminDashboard = lazy(
  () => import("../pages/Dashboard/AdminDashboard/AdminDashboard")
);

const CareerPage = lazy(() =>
  import("../website/pages/career/career.page").then((module) => ({
    default: module.CareerPage,
  }))
);

// Continue with other lazy imports...
// (I'll show a few more key ones, but you'd apply this pattern to all remaining imports)

// Shift Management
const DailyShiftPage = lazy(() =>
  import("../pages/shift/daily-shift/page").then((module) => ({
    default: module.DailyShiftPage,
  }))
);
const ShiftAssign = lazy(() => import("../pages/shift/ShiftAssign"));
const ShiftList = lazy(() => import("../pages/shift/ShiftList"));

// Canteen
const AssignDietPlan = lazy(() =>
  import("../pages/canteen/AssignDietPlan/page").then((module) => ({
    default: module.AssignDietPlan,
  }))
);
const DietPlanPage = lazy(() =>
  import("../pages/canteen/DietPlan/page").then((module) => ({
    default: module.DietPlanPage,
  }))
);
const AddDietPlan = lazy(() =>
  import("../pages/canteen/DietPlan/components/AddDietPlan").then((module) => ({
    default: module.AddDietPlan,
  }))
);
const StockList = lazy(() => import("../pages/canteen/StockList/StockList"));
const CanteenProductList = lazy(() =>
  import("../pages/canteen/inventory/product/product-list.page").then(
    (module) => ({ default: module.CanteenProductList })
  )
);

// Laboratory
const AddTestRequest = lazy(() =>
  import("../pages/laboratory/TestRequest/components/AddTestRequest").then(
    (module) => ({ default: module.AddTestRequest })
  )
);
const TestRequestPage = lazy(() =>
  import("../pages/laboratory/TestRequest/page").then((module) => ({
    default: module.TestRequestPage,
  }))
);
const AddTestResult = lazy(() =>
  import("../pages/laboratory/TestResults/components/AddTestResult").then(
    (module) => ({ default: module.AddTestResult })
  )
);
const TestResultPage = lazy(() =>
  import("../pages/laboratory/TestResults/page").then((module) => ({
    default: module.TestResultPage,
  }))
);

const RadiologyDashboard = lazy(
  () => import("../pages/Dashboard/RadiologyDashboard/RadiologyDashboard.page")
);

const TeamManagementPage = lazy(() =>
  import("../website/pages/team-management/team-management.page").then(
    (module) => ({
      default: module.TeamManagementPage,
    })
  )
);

// const PaymentVoucherPage = lazy(() =>
//   import("../pages/Accounting/PaymentVoucher/payment.voucher.page").then(
//     (module) => ({
//       default: module.PaymentVoucherPage,
//     })
//   )
// );

export const routesConfig: IRoutesConfig[] = [
  {
    path: FrontendRoutes.HOME,
    element: <NotFoundPage />,
  },

  {
    path: FrontendRoutes.UNAUTHORIZED,
    element: <Unauthorized />,
  },
  {
    path: "*",
    element: <NotFoundPage />,
  },

  {
    path: FrontendRoutes.LOGIN,
    element: <LoginPage />,
  },
  {
    path: FrontendRoutes.FORGOT,
    element: <ForgotPage />,
  },
  {
    path: FrontendRoutes.Hello,
    element: <ForgotPage />,
  },
  {
    path: FrontendRoutes.OPT_VERIFICATION,
    element: <VerificationPage />,
  },

  // -------------------------------- DashBoard ----------------------------------------------------------------

  {
    path: "/dashboard",
    element: <DashboardIndex />,
  },
  {
    path: FrontendRoutes.DOCTORDASHBOARD,
    element: <DoctorDashboard />,
  },
  {
    path: FrontendRoutes.ADMINDASHBOARD,
    element: <AdminDashboard />,
  },
  { path: FrontendRoutes.PHARMACYDASHBOARD, element: <PharmacyDashboard /> },
  {
    path: FrontendRoutes.LABDASHBOARD,
    element: <LabDashboard />,
  },

  {
    path: FrontendRoutes.RADIOLOGYDASHBOARD,
    element: <RadiologyDashboard />,
  },

  {
    path: FrontendRoutes.NURSEDASHBOARD,
    element: <NurseDashboard />,
  },

  // -------------------------------- Appointment ----------------------------------------------------------------
  {
    path: FrontendRoutes.APPOINTMENTLIST,
    element: <AppointmentPage />,
  },
  {
    path: FrontendRoutes.DoctorsAppointment,
    element: <Doctors />,
  },
  {
    path: `${FrontendRoutes.ADDAPPOINTMENT}/:id?`,
    element: <AddAppointment />,
  },
  {
    path: FrontendRoutes.OPDAPPOINTMENTFORM,
    element: <OPDAppointmentPage />,
  },
  {
    path: FrontendRoutes.GENERALCHECKUPLIST,
    element: <GeneralCheckupPage />,
  },
  {
    path: `${FrontendRoutes.GENERALPATIENTDETAILS}/:id?`,
    element: <PatientViewsDetails />,
  },

  {
    path: FrontendRoutes.GENERALPHYSICIANLIST,
    element: <GeneralPhysicianPage />,
  },
  {
    path: `${FrontendRoutes.GENERALPRECHECKUPADD}/:id?`,
    element: <AddGeneralPreCheckupForm />,
  },
  {
    path: `${FrontendRoutes.GENERALCHECKUPADD}/:id?`,
    element: <AddGeneralCheckupForm />,
  },
  {
    path: FrontendRoutes.CARDILOGYLIST,
    element: <CardiologyDepartmentPage />,
  },
  {
    path: FrontendRoutes.PEDIATRICLIST,
    element: <PediatricDepartmentPage />,
  },
  {
    path: FrontendRoutes.ORTHOPEDICSLIST,
    element: <OrthopedicsDepartmentPage />,
  },
  {
    path: FrontendRoutes.NEUROLOGYLIST,
    element: <NeurologyDepartmentPage />,
  },
  {
    path: FrontendRoutes.DERMATOLOGYLIST,
    element: <DermatologyDepartmentPage />,
  },
  {
    path: FrontendRoutes.ENTLIST,
    element: <ENTDepartmentPage />,
  },
  {
    path: FrontendRoutes.DENTALCARELIST,
    element: <DentalCareDepartmentPage />,
  },
  {
    path: FrontendRoutes.GYNECOLOGYLIST,
    element: <GynecologyDepartmentPage />,
  },
  {
    path: FrontendRoutes.ORTHOMOLOGYLIST,
    element: <OrthomologyDepartmentPage />,
  },

  //-------------------ShiftManagement-------------

  {
    path: FrontendRoutes.DAILYSHIFT,
    element: <DailyShiftPage />,
  },

  {
    path: FrontendRoutes.SHIFTLIST,
    element: <ShiftList />,
  },
  {
    path: FrontendRoutes.SHIFTASSIGN,
    element: <ShiftAssign />,
  },
  {
    path: FrontendRoutes.EVENTLIST,
    element: <Events />,
  },
  {
    path: FrontendRoutes.EVENTADD,
    element: <Add_Events />,
  },

  // -------------------------------- Appointment Add ----------------------------------------------------------------
  {
    path: FrontendRoutes.CARDILOGY,
    element: <AddCardiology />,
  },
  {
    path: FrontendRoutes.DERMATOLOGY,
    element: <AddDermatology />,
  },
  {
    path: FrontendRoutes.ENT,
    element: <AddENT />,
  },
  {
    path: FrontendRoutes.GENERALPHYSICIAN,
    element: <AddPhysicsian />,
  },
  {
    path: FrontendRoutes.NEUROLOGY,
    element: <AddNeuroLogy />,
  },
  {
    path: FrontendRoutes.ORTHOMOLOGY,
    element: <AddOrthopedics />,
  },
  {
    path: FrontendRoutes.DENTALCARE,
    element: <AddDentalCarePage />,
  },
  {
    path: FrontendRoutes.GYNECOLOGY,
    element: <AddGynecologyPage />,
  },

  // {
  //   path: FrontendRoutes.ADDATTENDANCE,
  //   element: <AddStaff />,
  // },
  // {
  //   path: FrontendRoutes.VIEWATTENDANCE,
  //   element: <ViewStaff />,
  // },
  //---------------------------------Attendance----------------------------------------------------------
  {
    path: FrontendRoutes.ATTENDANCELIST,
    element: <AttendanceList />,
  },
  {
    path: `${FrontendRoutes.ADDATTENDANCE}/:id?`,
    element: <AddAttendance />,
  },
  {
    path: `${FrontendRoutes.VIEWATTENDANCE}/:id`,
    element: <ViewAttendance />,
  },

  //---------------------------------Ambulance----------------------------------------------------------
  {
    path: FrontendRoutes.AMBULANCELIST,
    element: <AmbulanceList />,
  },
  {
    path: FrontendRoutes.AMBULANCETYPE,
    element: <AmbulanceType />,
  },

  {
    path: FrontendRoutes.ADDAMBULANCETYPE,
    element: <AddAmbulanceType />,
  },
  {
    path: FrontendRoutes.AMBULANCEINQUIRY,
    element: <AmbulanceInquiry />,
  },

  {
    path: FrontendRoutes.ADDAMBULANCEINQUIRY,
    element: <AddAmbulanceInquiryForm />,
  },
  {
    path: FrontendRoutes.EDITAMBULANCEINQUIRY,
    element: <EditAmbulanceInquiryForm />,
  },

  {
    path: FrontendRoutes.PRICECONFIG,
    element: <PriceConfig />,
  },

  // -------------------------------- RADIOLOGY ----------------------------------------------------------------

  {
    path: FrontendRoutes.RADIOLOGYPURCHASE,
    element: <RadiologyPurchase />,
  },

  {
    path: FrontendRoutes.RADIOLOGYREPORT,
    element: <RadioReport />,
  },
  {
    path: FrontendRoutes.RADIOLOGYTESTREQUEST,
    element: <RadiologyTestRequest />,
  },
  {
    path: FrontendRoutes.ADDRADIOLOGYTESTREQUEST,
    element: <AddRadiologyTestRequest />,
  },
  {
    path: FrontendRoutes.RADIOLOGYINVENTORY,
    element: <RadiologyInventory />,
  },
  {
    path: FrontendRoutes.RADIOLOGYPRODUCTLIST,
    element: <RadiologyProductList />,
  },
  {
    path: FrontendRoutes.RADIOLOGYINVOICE,
    element: <RadiologyInvoiceList />,
  },
  {
    path: FrontendRoutes.RADIOLOGYDUELIST,
    element: <RadiologyDueList />,
  },
  {
    path: `${FrontendRoutes.RADIOLOGYEDITREPORT}/:id?`,
    element: <RadiologyEditReport />,
  },
  {
    path: FrontendRoutes.SERVICETYPE,
    element: <ServiceType />,
  },
  {
    path: FrontendRoutes.TESTLISTCONFIG,
    element: <TestListConfig />,
  },
  {
    path: FrontendRoutes.RADIOLOGYDEPARTMENT,
    element: <RadiologyDepartment />,
  },
  // -------------------------------- IPD ----------------------------------------------------------------

  {
    path: FrontendRoutes.GENERALWARD,
    element: <GeneralWardPage />,
  },
  { path: "/general-ward/add-patient", element: <IpdPatientForm /> },
  { path: "/general-ward/patient/:id", element: <EditPatientForm /> },
  {
    path: FrontendRoutes.TRANSFER_GENERALWARD,
    element: <TransferGeneralWard />,
  },
  {
    path: FrontendRoutes.TREATMENT_GENERALWARD,
    element: <TreatmentGeneralWard />,
  },
  {
    path: `/general-ward/:id`,
    element: <SurgeryGeneralWard />,
  },
  {
    path: FrontendRoutes.DISCHARGE_GENERALWARD,
    element: <DischargeGeneralWard />,
  },

  {
    path: FrontendRoutes.NICU,
    element: <NICU />,
  },
  {
    path: FrontendRoutes.ICU,
    element: <ICU />,
  },
  {
    path: FrontendRoutes.POSTOPERATIVEWARD,
    element: <PostOperativeWard />,
  },
  {
    path: FrontendRoutes.GYNEOBSERVATIONWARD,
    element: <GyneObservationWard />,
  },
  {
    path: FrontendRoutes.HIGHDEPENDENCYUNIT,
    element: <HighDependencyUnit />,
  },
  {
    path: FrontendRoutes.DAYCAREUNIT,
    element: <DayCareUnit />,
  },

  //----------------Canteen
  {
    path: FrontendRoutes.DIETPLANLIST,
    element: <DietPlanPage />,
  },
  {
    path: FrontendRoutes.CANTEENSTOCKLIST,
    element: <StockList />,
  },

  {
    path: FrontendRoutes.CANTEENINVENTROY,
    element: <DietPlanPage />,
  },

  {
    path: FrontendRoutes.CANTEENPRODUCT,
    element: <CanteenProductList />,
  },

  {
    path: FrontendRoutes.ASSIGNDIETPLAN,
    element: <AssignDietPlan />,
  },
  {
    path: FrontendRoutes.ADDTESTRESULT,
    element: <AddTestResult />,
  },
  {
    path: FrontendRoutes.ADDTESTREQUEST,
    element: <AddTestRequest />,
  },

  //-------------Lab
  {
    path: FrontendRoutes.TESTREQUEST,
    element: <TestRequestPage />,
  },
  {
    path: FrontendRoutes.TESTRESULT,
    element: <TestResultPage />,
  },

  {
    path: `${FrontendRoutes.ADDDIETPLAN}/:id?`,
    element: <AddDietPlan />,
  },
  // Purchase Management List
  {
    path: FrontendRoutes.VENDORLIST,
    element: <VendorListPage />,
  },

  {
    path: FrontendRoutes.PRODUCTLIST,
    element: <VendorProductList />,
  },

  {
    path: FrontendRoutes.VENDORORDERLIST,
    element: <VendorOrderPage />,
  },

  {
    path: FrontendRoutes.VENDORBILLING,
    element: <VendorBillingPage />,
  },
  {
    path: "/purchase-management/vendor-list/view",
    element: <ViewVendor />,
  },
  { path: "/purchase-management/vendor-list/:id", element: <AddVendor /> },

  {
    path: FrontendRoutes.EDITVENDOR,
    element: <EditVendor />,
  },
  {
    path: FrontendRoutes.PURCHASERETURN,
    element: <PurchaseReturn />,
  },
  {
    path: FrontendRoutes.ADDPURCHASERETURN,
    element: <AddPurchase />,
  },
  {
    path: FrontendRoutes.PURCHASELIST,
    element: <PurchaseListPage />,
  },
  {
    path: FrontendRoutes.ADDPURCHASE,
    element: <AddNewPurchase />,
  },
  {
    path: FrontendRoutes.PURCHASEORDER,
    element: <PurchaseOrderPage />,
  },
  {
    path: `/purchase-management/purchase-list/:id`,
    element: <NewPurchaseOrder />,
  },
  {
    path: FrontendRoutes.EDITPURCHASEORDER,
    element: <EditOrders />,
  },

  //Inventory Management
  {
    path: FrontendRoutes.INVENTORY,
    element: <InventoryPage />,
  },
  {
    path: FrontendRoutes.INVENTORYCATEGORYLIST,
    element: <CategoryList />,
  },
  {
    path: FrontendRoutes.INVENTORYPRODUCTLIST,
    element: <ProductList />,
  },
  {
    path: FrontendRoutes.INVENTORYSUBCATEGORYLIST,
    element: <SubCategory />,
  },
  {
    path: FrontendRoutes.ADDCATEGORY,
    element: <AddCategory />,
  },
  {
    path: FrontendRoutes.ADDSUBCATEGORY,
    element: <AddSubCategory />,
  },

  //certificate
  {
    path: FrontendRoutes.CERTIFICATE,
    element: <CertificatePage />,
  },
  {
    path: `${FrontendRoutes.ADDCERTIFICATE}/:id?/:tab?`,
    element: <AddCertificate />,
  },

  // Emergency Management
  {
    path: FrontendRoutes.EMERGENCYROOM,
    element: <EmergencyRoomPage />,
  },
  {
    path: FrontendRoutes.EMERGENCYADDPATIENT,
    element: <AddPatient />,
  },

  // Pricing Config
  {
    path: FrontendRoutes.PRICINGCONFIGSERVICEITEM,
    element: <ServiceItemList />,
  },
  {
    path: FrontendRoutes.PRICINGCONFIGCATEGORYLIST,
    element: <Category />,
  },
  {
    path: `${FrontendRoutes.PRICINGADDPRICINGCATEGORY}/:id?`,
    element: <Addpricingcategory />,
  },

  ///
  {
    path: `${FrontendRoutes.PRICINGCONFIGSERVICEITEMADD}/:id?`,
    element: <AddService />,
  },

  // Financial Ops
  {
    path: FrontendRoutes.FINANCIALOPSINVOICE,
    element: <InvoicePage />,
  },
  {
    path: FrontendRoutes.FINANCEADDNEWINVOICE,
    element: <CreateInvoicePage />,
  },
  {
    path: FrontendRoutes.FINANCIAlPAYMENT,
    element: <PaymentPage />,
  },
  // {
  //   path: FrontendRoutes.FINANCEADDNEWPAYMENT,
  //   element: <CreateReceiptPage />,
  // },

  // Settings
  {
    path: FrontendRoutes.SETTINGSDEPARTMENTCONFIG,
    element: <DepartmentConfigPage />,
  },
  {
    path: `${FrontendRoutes.SETTINGSADDDEPARTMENT}/:id?/:tab?`,
    element: <AddAllDepartment />,
  },
  {
    path: FrontendRoutes.ROLEMANAGEMENT,
    element: <PermissionIndex />,
  },
  {
    path: FrontendRoutes.ROLEMANAGEMENTFORM,
    element: <RoleManagementForm />,
  },
  {
    path: FrontendRoutes.WARD,
    element: <WardConfigPage />,
  },
  {
    path: FrontendRoutes.USERMANAGEMENT,
    element: <UserManagement />,
  },

  {
    path: FrontendRoutes.USERMANAGEMENTADDUSER,
    element: <AddUser />,
  },

  {
    path: FrontendRoutes.NOTIFICATIONSETUP,
    element: <NotificationsPage />,
  },

  {
    path: FrontendRoutes.NOTIFICATIONSETUPADD,
    element: <AddNotification />,
  },

  {
    path: FrontendRoutes.REPORT,
    element: <Report />,
  },

  // Bed Allocation
  {
    path: FrontendRoutes.BEDALLOCATIONLIST,
    element: <BedAllocationPage />,
  },
  {
    path: FrontendRoutes.EDITBEDALLOCATION,
    element: <EditBedAllocation />,
  },
  {
    path: `${FrontendRoutes.ADDASSIGNBED}/:id?`,
    element: <AssignBedAllocation />,
  },
  {
    path: FrontendRoutes.BEDROOMDETAILS,
    element: <RoomDetailsPage />,
  },
  {
    path: `${FrontendRoutes.BEDADDROOMS}/:id?`,
    element: <AddRooms />,
  },
  {
    path: FrontendRoutes.BEDCATEGORY,
    element: <BedCategory />,
  },

  {
    path: FrontendRoutes.BEDLIST,
    element: <BedList />,
  },

  //Operation

  {
    path: FrontendRoutes.GENERALOT,
    element: <GeneralOT />,
  },
  {
    path: `${FrontendRoutes.GENERALOT_VIEWDETAIL}/:id`,
    element: <PatientLayout />,
  },
  {
    path: `${FrontendRoutes.SURGERY_DETAILS}/:id?`,
    element: <SurgeryGeneralWard />,
  },

  // {
  //   path: `${FrontendRoutes.GENERALOT_EDIT}/:id`,
  //   element: <SurgeryGeneralWard />,
  // },
  {
    path: FrontendRoutes.AVAILABILITY,
    element: <Availability />,
  },
  {
    path: `${FrontendRoutes.AVAILABILITYDETAIL}/:id`,
    element: <AvailabilityDetail />,
  },
  {
    path: FrontendRoutes.CONFIGURATIONOT,
    element: <Otconfiguration />,
  },
  {
    path: "/assign-ot/:id",
    element: <OperationForm />,
  },
  {
    path: FrontendRoutes.POF,
    element: <POF />,
  },

  //Events
  // {
  //   path: FrontendRoutes.EVENTS,
  //   element: <Events />,
  // },
  // {
  //   path: FrontendRoutes.ADD_EVENTS,
  //   element: <Add_Events />,
  // },

  //Pharmacy
  //Events
  {
    path: FrontendRoutes.PRESCRIPTIONS,
    element: <Prescriptions />,
  },
  {
    path: FrontendRoutes.ADD_PRESCRIPTIONS,
    element: <Add_Prescriptions />,
  },

  //---------------------------------Patient----------------------------------------------------------
  {
    path: `${FrontendRoutes.PATIENTLIST}`,
    element: <PatientList />,
  },
  {
    path: `${FrontendRoutes.INPATIENTLIST}`,
    element: <InPatientList />,
  },
  // {
  //   path: `${FrontendRoutes.PATIENTDETAIL}/:id`,
  //   element: <PatientDetails />,
  // },
  {
    path: `${FrontendRoutes.PATIENTDETAIL}/:id`,
    element: <PatientDetailsA />,
  },

  //---------------------------------Doctor----------------------------------------------------------

  {
    path: FrontendRoutes.DOCTORLIST,
    element: <DoctorList />,
  },
  {
    path: `${FrontendRoutes.DOCTORDETAIL}/:id`,
    element: <DoctorDetailsPage />,
  },

  {
    path: FrontendRoutes.DOCTORREVIEWS,
    element: <DoctorReviews />,
  },

  {
    path: `${FrontendRoutes.ADDDOCTOR}/:id?`,
    element: <AddDoctor />,
  },

  {
    path: FrontendRoutes.DOCTORAVAILABILITY,
    element: <DoctorAvailability />,
  },

  //----------------Pharmacists----------

  {
    path: FrontendRoutes.PHARMACISTSLIST,
    element: <PharmarcistsListPage />,
  },
  {
    path: `${FrontendRoutes.PHARMACISTSDETAILS}/:id`,
    element: <OverAllStaffDetails />,
  },
  {
    path: `${FrontendRoutes.ADDPHARMACIST}/:id?`,
    element: <AddDoctor />,
  },
  {
    path: `${FrontendRoutes.PHARMACYINVENTORY}`,
    element: <PharmacyInventoryPage />,
  },
  {
    path: `${FrontendRoutes.PHARMACYINVENTORYDetails}`,
    element: <InventoryDetails />,
  },

  {
    path: `${FrontendRoutes.PHARMACYPRODUCT}`,
    element: <PharmacyProductListPage />,
  },
  {
    path: `${FrontendRoutes.MEDICINEREQUEST}`,
    element: <MedicineRequestPage />,
  },
  {
    path: `${FrontendRoutes.MEDICINERQUESTDETAILS}/:id`,
    element: <MedicineRequestDetailsPage />,
  },
  {
    path: `${FrontendRoutes.POS}`,
    element: <PosPage />,
  },

  {
    path: `${FrontendRoutes.PHARMACYEXPENSE}`,
    element: <PharmacyExpense />,
  },
  {
    path: `${FrontendRoutes.PHARMACYSALES}`,
    element: <SalesPage />,
  },

  {
    path: `${FrontendRoutes.PHARMACYREPORT}`,
    element: <PharmacyFinanceReportsPage />,
  },

  //----------------LAB Technicians----------

  {
    path: FrontendRoutes.LABTECHNICIANLIST,
    element: <LabTechnicianListPage />,
  },
  {
    path: `${FrontendRoutes.LABTECHNICIANDETAILS}/:id`,
    element: <OverAllStaffDetails />,
  },
  {
    path: `${FrontendRoutes.ADDLABTECHNICIAN}/:id?`,
    element: <AddDoctor />,
  },

  //---------------------------------Staff----------------------------------------------------------

  {
    path: FrontendRoutes.STAFFLIST,
    element: <StaffList />,
  },

  {
    path: `${FrontendRoutes.STAFFDETAIL}/:id`,
    element: <StaffDetails />,
  },

  {
    path: `${FrontendRoutes.ADDSTAFF}/:id?`,
    element: <AddStaff />,
  },
  //---------------------------------Payroll Management----------------------------------------------------------
  {
    path: FrontendRoutes.PAYROLL_LIST,
    element: <PayrollList />,
  },
  {
    path: FrontendRoutes.PAYROLL_CONFIG,
    element: <PayrollConfig />,
  },
  //---------------------------------Payroll Management----------------------------------------------------------
  {
    path: FrontendRoutes.PAYROLL_LIST,
    element: <PayrollList />,
  },
  {
    path: FrontendRoutes.PAYROLL_CONFIG,
    element: <PayrollConfig />,
  },
  //---------------------------------Expensese Management----------------------------------------------------------
  {
    path: FrontendRoutes.EXPENSE_LIST,
    element: <ExpensesList />,
  },
  {
    path: FrontendRoutes.EXPENSE_ADD,
    element: <AddExpense />,
  },
  {
    path: "/expense-management/:id",
    element: <AddExpense />,
  },
  {
    path: FrontendRoutes.EXPENSE_CATEGORY,
    element: <ExpenseCategory />,
  },
  {
    path: FrontendRoutes.VOUCHER,
    element: <Voucher />,
  },

  //-----------------Bank

  {
    path: FrontendRoutes.BANK,
    element: <Bank />,
  },

  {
    path: FrontendRoutes.TRANSACTIONDETAILS,
    element: <TransactionDetails />,
  },

  //---------------------------------Nurse----------------------------------------------------------

  {
    path: FrontendRoutes.NURSELIST,
    element: <NurseList />,
  },
  {
    path: FrontendRoutes.NURSESHIFTOVERVIEW,
    element: <ShiftOverView />,
  },
  {
    path: `${FrontendRoutes.IPDPATIENTLIST}/:id`,
    element: <PatientDetailIPD />,
  },
  {
    path: `${FrontendRoutes.NURSEDETAIL}/:id`,
    element: <NurseDetailsPage />,
  },

  {
    path: `${FrontendRoutes.ADDNURSE}/:id?`,
    element: <AddDoctor />,
  },
  {
    path: FrontendRoutes.NURSEINVENTORY,
    element: <InventryManag />,
  },

  //---------------------------------token management----------------------------------------------------------

  {
    path: FrontendRoutes.ACCOUNTTOKENMANAGEMENT,
    element: <AccountTokenList />,
  },

  {
    path: FrontendRoutes.APPOINTMENTTOKENMANAGEMENT,
    element: <AppointmentTokenList />,
  },
  {
    path: FrontendRoutes.TOKENCONFIG,
    element: <TokenConfig />,
  },

  //---------------------------------Donor List----------------------------------------------------------

  {
    path: FrontendRoutes.BLOODDONORLIST,
    element: <BloodDonorList />,
  },
  {
    path: `${FrontendRoutes.ADDDONOR}/:id?`,
    element: <AddDonor />,
  },
  {
    path: `${FrontendRoutes.CUSTOMFORM}/:id?`,
    element: <CustomForm />,
  },
  {
    path: FrontendRoutes.ORGANDONORLIST,
    element: <OrganDonorList />,
  },

  {
    path: FrontendRoutes.BLOODBANK,
    element: <BloodBank />,
  },
  {
    path: FrontendRoutes.RECEIVER,
    element: <Receiver />,
  },
  {
    path: FrontendRoutes.CASHEQUIPMENT,
    element: <CashEquipment />,
  },
  {
    path: `${FrontendRoutes.CASHEQUIPMENT}/:id`,
    element: <DynamicEquipmentForm />,
  },

  //---------------------------------Setting Commission Modify Ram Mgt----------------------------------------------------------
  {
    path: FrontendRoutes.TYPESERVICE,
    element: <TypeService />,
  },
  {
    path: FrontendRoutes.ADDTYPE,
    element: (
      <AddType
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
      />
    ),
  },
  {
    path: FrontendRoutes.ADDSERVICECOM,
    element: (
      <AddServicecom
        onClose={function (): void {
          throw new Error("Function not implemented.");
        }}
      />
    ),
  },
  {
    path: FrontendRoutes.COMMISSIONCONFIG,
    element: <CommissionList />,
  },
  {
    path: `${FrontendRoutes.RESELLERDETAILPAGE}/:id?`,
    element: <ViewDetailsReseller />,
  },
  {
    path: `${FrontendRoutes.ASSIGNCOMMISSION}/:id?`,
    element: <AddCommission />,
  },

  //----------------------------------------Lab Dashboard--------------------------------------------------

  {
    path: FrontendRoutes.LABPURCHASE,
    element: <LabPurchaseList />,
  },
  {
    path: FrontendRoutes.LABTESTRESULT,
    element: <LabTestResult />,
  },
  {
    path: FrontendRoutes.LABTESTREQUESTLIST,
    element: <TestRequestList />,
  },
  {
    path: FrontendRoutes.AddLabTestRequest,
    element: <AddLabTestRequest />,
  },
  {
    path: `${FrontendRoutes.ADDREPORT}/:id?`,
    element: <EditReport />,
  },
  {
    path: FrontendRoutes.LABSAMPLECOLLECTION,
    element: <SampleCollection />,
  },
  {
    path: FrontendRoutes.LABINVENTORY,
    element: <LabInventory />,
  },
  {
    path: FrontendRoutes.LABPRODUCTLIST,
    element: <LabProductList />,
  },
  {
    path: FrontendRoutes.LABDEPARTMENTCONFIG,
    element: <LabDepartment />,
  },
  {
    path: FrontendRoutes.LABSUBCATEGORYCONFIG,
    element: <LabSubDepartment />,
  },
  {
    path: FrontendRoutes.LABINVOICELIST,
    element: <InvoiceList />,
  },
  // {
  //   path: FrontendRoutes.LABDUELIST,
  //   element: <DueList />,
  // },
  {
    path: FrontendRoutes.LABTESTTYPECONFIG,
    element: <TestTypeConfig />,
  },

  // Website
  {
    path: FrontendRoutes.WEBSITE_BLOG_MANAGEMENT,
    element: <BlogPageIndex />,
  },
  {
    path: FrontendRoutes.WEBSITE_COMPAIGN_MANAGEMENT,
    element: <CampaignPage />,
  },
  {
    path: FrontendRoutes.WEBSITE_GALLERY,
    element: <GalleryPageIndex />,
  },

  {
    path: FrontendRoutes.WEBSITE_CONTACT,
    element: <ContactPageIndex />,
  },
  {
    path: FrontendRoutes.WEBSITE_CARRER,
    element: <CareerPage />,
  },
  {
    path: `${FrontendRoutes.WEBSITE_CARRER_APPLICATION}/:id?`,
    element: <JobApplicationPage />,
  },
  {
    path: FrontendRoutes.WEBSITE_TEAM_MANAGEMENT,
    element: <TeamManagementPage />,
  },
  {
    path: FrontendRoutes.WEBSITE_TRAINING,
    element: <TrainingIndex />,
  },

  //Accounting

  {
    path: FrontendRoutes.BALANCESHEET,
    element: <BalanceSheet />,
  },

  //profile
  {
    path: FrontendRoutes.PROFILE,
    element: <ProfileIndex />,
  },
  //Account Dashboard
  {
    path: FrontendRoutes.ACCOUNTDASHBOARD,
    element: <AccountDashboard />,
  },

  {
    path: FrontendRoutes.PURCHASE_RETURN,
    element: <PurchaseReturnIvoice />,
  },

  {
    path: FrontendRoutes.GENERAL_LEDGER_BALANCE,
    element: <GeneralLedgerBalance />,
  },

  {
    path: FrontendRoutes.SALES_RETURN,
    element: <SalesReturn />,
  },

  {
    path: FrontendRoutes.SALES_VOUCHER,
    element: <SalesVoucher />,
  },

  {
    path: FrontendRoutes.TRANSACTION_LIST,
    element: <TransactionsList />,
  },

  //Account Voucher
  {
    path: FrontendRoutes.PAYMENT_VOUCHER,
    element: <PaymentVoucher />,
  },

  {
    path: FrontendRoutes.PAYMENT_VOUCHER,
    element: <PurchaseReturnIvoice />,
  },
  {
    path: FrontendRoutes.INCOME_STATEMENT,
    element: <IncomeStatement />,
  },
];
