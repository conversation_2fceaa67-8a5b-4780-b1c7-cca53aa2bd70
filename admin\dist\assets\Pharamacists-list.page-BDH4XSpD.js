import{ad as C,a5 as x,aM as w,bw as N,aw as F,bI as M,a2 as n,af as _,ag as o,ah as E,aQ as H,aj as $}from"./index-ClX9RVH0.js";const L=()=>{var p,u,d,S;const l=C(),[t,c]=x.useState({limit:5,page:1}),[a,r]=x.useState({selectedDepartment:"",selectedSpecialist:"",search:""}),b=[N.PHARMACIST,N.PHARMACIST_ASSISTANT],f=a.search,k=w({multiroles:JSON.stringify(b),search:a.search,"departmentDetails.department":a.selectedDepartment,"departmentDetails.speciality":a.selectedSpecialist,...f?{}:{page:t.page,limit:t.limit}}),{data:s}=F(k),{mutateAsync:R}=M(),j=()=>{l(`/${o.ADDPHARMACIST}`)},i={columns:[{title:"Name",key:"doctorName"},{title:"ID",key:"tokenid"},{title:"Department",key:"department"},{title:"Specialist",key:"specialist"},{title:"Available Status",key:"status"},{title:"Action",key:"action"}],rows:(u=(p=s==null?void 0:s.data)==null?void 0:p.users)==null?void 0:u.map((e,v)=>{var A,g,h,D,y,P,I,T;return{key:v,tokenid:`D-${(g=e==null?void 0:e._id)==null?void 0:g.slice(((A=e==null?void 0:e._id)==null?void 0:A.length)-5,e==null?void 0:e._id.length)}`,patientName:e.patientName,department:(D=(h=e==null?void 0:e.departmentDetails)==null?void 0:h.hirachyFirst)==null?void 0:D.name,doctorName:(P=(y=e==null?void 0:e.commonInfo)==null?void 0:y.personalInfo)==null?void 0:P.fullName,specialist:(T=(I=e==null?void 0:e.departmentDetails)==null?void 0:I.speciality)==null?void 0:T.name,status:n.jsx(E,{status:e!=null&&e.isActive?"ACTIVE":"INACTIVE"}),treatment:e.treat,action:n.jsx(_,{onShow:()=>{l(`${o.PHARMACISTSDETAILS}/${e==null?void 0:e._id}`)},onEdit:()=>{l(`/${o.ADDPHARMACIST}/${e==null?void 0:e._id}`)},onDelete:()=>{R({id:JSON.stringify([e==null?void 0:e._id])})}})}})};return n.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[n.jsx(H,{headerTitle:"Pharmacist List",onSearch:!0,onSpecialist:!0,onDepartment:!0,onFilter:!0,button:!0,buttonText:"Add Pharmacist",onSearchFunc:e=>r({...a,search:e}),buttonAction:j,onDepartmentSelectFunc:e=>r({...a,selectedDepartment:e}),onSpecialistSelectFunc:e=>r({...a,selectedSpecialist:e})}),n.jsx("div",{className:"bg-white rounded-md",children:n.jsx($,{columns:i.columns,rows:i.rows,loading:!1,pagination:{currentPage:t.page,totalPage:(S=(d=s==null?void 0:s.data)==null?void 0:d.pagination)==null?void 0:S.pages,limit:t.limit,onClick:e=>{e.page&&c({...t,page:e.page}),e.limit&&c({...t,limit:e.limit})}}})})]})};export{L as PharmarcistsListPage};
