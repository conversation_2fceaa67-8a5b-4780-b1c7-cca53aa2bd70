import { useState } from "react";
import HeadingPopup from "../../../LabDashboard/components/HeadingPopup";
import { useLocation } from "react-router-dom";
import OldPatientForm from "./OldPatientForm";
import NewPatientForm from "./NewPatientForm";
import { FormField } from "../../../LabDashboard/components/FormField";
import { useFormik } from "formik";
const AddDoctorAppointmentForm = ({
  onClose,
  EditData,
}: {
  onClose: () => void;
  EditData?: any;
}) => {
  const location = useLocation();
  const { tokenId } = location.state || {};

  const [patientType, setPatientType] = useState<string>("oldPatient");

  const handleClose = () => {
    onClose?.();
  };
  const formik = useFormik({
    initialValues: {
      patientType: "",
    },
    onSubmit: async (values) => {
      console.log(values);
    },
  });

  return (
    <HeadingPopup onClose={handleClose} className="max-w-screen-md pt-4 w-full">
      <div className="bg-[#d7e1ef] p-4 rounded-xl flex gap-5 items-center mb-4">
        <div className="h-24 w-24 rounded-lg overflow-hidden flex justify-center items-center">
          <img
            src={
              EditData?.identityInformation?.profileImage ||
              "/profile picture.png"
            }
            className="object-cover h-full w-full"
            alt=""
          />
        </div>
        <div>
          <h1 className="font-semibold text-xl">
            Dr.{" "}
            {EditData?.commonInfo?.personalInfo?.fullName || "Dr. Laura Jaeden"}
          </h1>
          <p className="text-primary">
            {EditData?.departmentDetails?.speciality?.name || "Specialty"}
          </p>
          <p className="text-gray-600">
            {EditData?.departmentDetails?.department?.name || "Department"}
          </p>
        </div>
      </div>
      <div className="mb-4">
        <FormField
          required
          name="patientType"
          label="Select Patient Type"
          type="dropdown"
          placeholder="Select"
          formik={formik}
          onChange={(e) => setPatientType(e.target.value)}
          value={patientType}
          options={[
            { value: "newPatient", label: "New Patient" },
            { value: "oldPatient", label: "Old Patient" },
          ]}
        />
      </div>
      {/* Render form based on patient type */}
      {patientType === "oldPatient" && (
        <OldPatientForm
          EditData={EditData}
          token={tokenId}
          onClose={handleClose}
          // onBack={() => setPatientType("")}
        />
      )}
      {patientType === "newPatient" && (
        <NewPatientForm
          EditData={EditData}
          token={tokenId}
          onClose={handleClose}
          // onBack={() => setPatientType("")}
        />
      )}
    </HeadingPopup>
  );
};

export default AddDoctorAppointmentForm;
