import React, { forwardRef } from "react";
import TableHead from "../layouts/Table/TableHead";
import TableBody from "../layouts/Table/TableBody";

interface CardItem {
  title: string;
  value: string | number;
}

interface TableData {
  columns: Array<{
    key: string;
    title: string;
    [key: string]: any;
  }>;
  rows: Array<Record<string, any>>;
}

interface RenderPrintableDataProps {
  cardData?: CardItem[] | Record<string, string | number>;
  tableData?: TableData;
  loading?: boolean;
  color?: string;
  textcolor?: string;
}

// Print styles as a string for injection
const printStyles = `
  @media print {
    body { margin: 0; padding: 0; }
    .print-container { 
      padding: 20px; 
      font-family: Arial, sans-serif;
      color: black;
      background: white;
      width: 100%;
    }
    .print-card {
      border: 1px solid #ddd;
      padding: 10px;
      margin-bottom: 10px;
      page-break-inside: avoid;
      background-color: white !important;
      box-shadow: 0 1px 3px rgba(0,0,0,0.12) !important;
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
    }
    .print-card h3 {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 5px;
      color: #333 !important;
    }
    .print-card p {
      font-size: 12px;
      color: #666 !important;
    }
    .print-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;
    }
    .print-table th, .print-table td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    .print-table th {
      background-color: #f5f5f5 !important;
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
    }
    .print-grid-2 { 
      display: grid; 
      grid-template-columns: repeat(2, 1fr); 
      gap: 10px; 
      width: 100%;
    }
    .print-grid-3 { 
      display: grid; 
      grid-template-columns: repeat(3, 1fr); 
      gap: 10px; 
      width: 100%;
    }
    .print-grid-4 { 
      display: grid; 
      grid-template-columns: repeat(4, 1fr); 
      gap: 10px; 
      width: 100%;
    }
  }
`;

const RenderPrintableData = forwardRef<
  HTMLTableElement | HTMLDivElement | null,
  RenderPrintableDataProps
>(({ cardData, tableData, loading = false, color, textcolor }, ref) => {
  // Process card data
  const cards: CardItem[] = React.useMemo(() => {
    if (!cardData) return [];

    // If cardData is an array, use it directly
    if (Array.isArray(cardData)) {
      return cardData;
    }

    // If cardData is an object, convert to array of CardItem
    return Object.entries(cardData).map(([key, value]) => ({
      title: key,
      value: value,
    }));
  }, [cardData]);

  // Filter out action columns
  const filteredColumns = React.useMemo(() => {
    if (!tableData?.columns) return [];
    return tableData.columns.filter((column) => column.key !== "action");
  }, [tableData?.columns]);

  // Determine grid column count based on number of cards
  const getGridClass = () => {
    const count = cards.length;
    if (count <= 2) return "print-grid-2";
    if (count <= 4) return "print-grid-3";
    return "print-grid-4";
  };

  return (
    <div className="print-container hidden">
      <style>{printStyles}</style>
      <div ref={ref as React.RefObject<HTMLDivElement>}>
        {cards.length > 0 && (
          <div className={getGridClass()}>
            {cards.map((card, index) => (
              <div key={`${card.title}-${index}`} className="print-card">
                <h3>{card.title}</h3>
                <p>{card.value}</p>
              </div>
            ))}
          </div>
        )}

        {tableData && filteredColumns.length > 0 && (
          <table className="print-table">
            <TableHead
              columns={filteredColumns}
              loading={loading}
              color={color}
              textcolor={textcolor}
            />
            <TableBody
              columns={filteredColumns}
              rows={tableData.rows}
              loading={loading}
            />
          </table>
        )}
      </div>
    </div>
  );
});

RenderPrintableData.displayName = "RenderPrintableData";

export default RenderPrintableData;
