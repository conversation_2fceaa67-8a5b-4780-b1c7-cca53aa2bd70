import * as Yup from "yup";
interface IColumns {
  title: string;
  key: string;
}
interface IWardObj {
  [key: string]: IColumns[];
}
export const NuriseObjColumns: IWardObj = {
  VITAL: [
    { title: "Date", key: "date" },
    { title: "Time", key: "time" },
    { title: "Name", key: "name" },
    { title: "Contact Number", key: "contanctNumber" },
    { title: "Status", key: "status" },
    { title: "Action", key: "Action" },
  ],
  ALLERGIES: [
    { title: "S.N", key: "id" },
    { title: "Allergy Type", key: "allergyType" },
    { title: "Allergen", key: "allergen" },
    { title: "Severity", key: "severity" },
    { title: "Last seen", key: "lastseen" },
    { title: "Reaction", key: "reaction" },
  ],
  CURRENT: [
    { title: "S.N", key: "ID" },
    { title: "Medicine", key: "medicine" },
    { title: "Dosage", key: "dosage" },
    { title: "Frequenecy", key: "frequency" },
    { title: "Food Relation", key: "foodRelation" },
    { title: "Route", key: "route" },
    { title: "Prescribe Date", key: "prescribedate" },
    { title: "Stop", key: "stop" },
  ],
  DIAGNOSIS: [
    { title: "S.N", key: "id" },
    { title: "Date", key: "date" },
    { title: "Type", key: "attendance" },
    { title: "Category ", key: "category" },
    { title: "Diagnosis", key: "diagnosis" },
    { title: "ICD-10", key: "ICD-10" },
    { title: "State", key: "state" },
    { title: "Adverse Effect", key: "adverseEffect" },
  ],
  INVESTIGATION: [
    { title: "S.N", key: "id" },
    { title: "Investigation/Prodcedure", key: "investation" },
    { title: "Result", key: "result" },
    { title: "State", key: "state" },
    { title: "OT Request", key: "otrequest" },
  ],
  PRESCRIPTION: [
    { title: "S.N", key: "id" },
    { title: "Medicine", key: "medicine" },
    { title: "Dosage", key: "dosage" },
    { title: "Frequency", key: "frequency" },
    { title: "Food Relation", key: "foodRelation" },
    { title: "Route", key: "route" },
    { title: "Prescribe Date", key: "prescribe Date" },
    { title: "End Date", key: "endDate" },
  ],
  ADVICE: [
    { title: "S.N", key: "id" },
    { title: "Advice", key: "advice" },
    { title: "FollowUp Date", key: "followUpDate" },
    { title: "Referred Department", key: "referreddepartment" },
    { title: "Referred Doctor", key: "referredDoctor" },
    { title: "Date", key: "date" },
  ],
};
