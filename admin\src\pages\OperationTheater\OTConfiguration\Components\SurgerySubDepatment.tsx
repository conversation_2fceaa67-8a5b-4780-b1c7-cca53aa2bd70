import { Icon } from "@iconify/react/dist/iconify.js";
import { useCallback, useState, useEffect } from "react";
import { DeleteDialog, InputField, Text } from "../../../../components";
import { Form, FormikProvider, useFormik } from "formik";
import { debounce, get } from "lodash";
import SearchableSelect from "../../../../components/SearchableSelect";
import {
  useCreateSurgerySubDepartment,
  useDeleteSurgerySubDepartment,
  useGetAllSurgerySubDepartment,
  useUpdateSurgerySubDepartment,
  useGetAllSurgeryDepartment,
} from "../../../../server-action/api/OTAssignment";

const SurgerySubDepartment: React.FC<{ onClose: () => void }> = ({
  onClose,
}) => {
  const [search, setSearch] = useState({
    searchTerm: "",
    deboundSearchTerm: "",
  });
  const [editMode, setEditMode] = useState(false);
  const [confirmation, setConfirmation] = useState({ state: false, id: "" });

  // Fetch all surgery departments for the dropdown
  const { data: departmentsData } = useGetAllSurgeryDepartment({});
  const departmentOptions = get(
    departmentsData,
    "data.surgeryDepartments",
    []
  ).map((dept: any) => ({
    value: dept._id,
    label: dept.name,
  }));

  // Fetch sub-departments with search filter
  const { data, isLoading, refetch } = useGetAllSurgerySubDepartment({
    ...(search.deboundSearchTerm !== "" && { name: search.deboundSearchTerm }),
  });

  const subDepartments = get(data, "data.surgerySubDepartments", []);
  const { mutate: deleteSubDepartment } = useDeleteSurgerySubDepartment();
  const {
    mutate: create,
    isPending: isCreating,
    isSuccess: isCreateSuccess,
  } = useCreateSurgerySubDepartment();
  const {
    mutate: update,
    isPending: isUpdating,
    isSuccess: isUpdateSuccess,
  } = useUpdateSurgerySubDepartment();

  const isPending = isCreating || isUpdating;
  const isSuccess = isCreateSuccess || isUpdateSuccess;

  const formik = useFormik({
    initialValues: {
      name: "",
      department: "",
      _id: "",
    },
    enableReinitialize: true,
    onSubmit: (values) => {
      if (editMode) {
        update({
          _id: values._id,
          entityData: {
            name: values.name,
            department: values.department,
          },
        });
      } else {
        create({
          name: values.name,
          department: values.department,
        });
      }
    },
  });

  const { handleSubmit, setFieldValue, values, resetForm } = formik;

  // Reset form and state after successful operation
  useEffect(() => {
    if (isSuccess) {
      resetForm();
      setEditMode(false);
      refetch(); // Refresh the list
    }
  }, [isSuccess, resetForm, refetch]);

  const handleSearch = useCallback(
    debounce(
      (value) => setSearch((prev) => ({ ...prev, deboundSearchTerm: value })),
      500
    ),
    []
  );

  const handleEdit = (item: any) => {
    setEditMode(true);
    setFieldValue("name", get(item, "name", ""));
    setFieldValue(
      "department",
      get(item, "department._id", get(item, "department", ""))
    );
    setFieldValue("_id", get(item, "_id", ""));
  };

  const handleCancel = () => {
    if (editMode) {
      setEditMode(false);
      resetForm();
    } else {
      resetForm();
      onClose();
    }
  };

  return (
    <div className="space-y-6">
      <FormikProvider value={formik}>
        <Form className="space-y-2" onSubmit={handleSubmit}>
          <div className="space-y-2">
            <div className="mb-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Department
              </label>
              <SearchableSelect
                options={departmentOptions}
                onChange={(value: string) => setFieldValue("department", value)}
                value={values.department}
                placeholder="Select Department"
              />
            </div>
            <InputField
              inputSize="body-sm-default"
              inputClassName="rounded-[6px]"
              label={editMode ? "Edit Sub-Department" : "Sub-Department"}
              name="name"
            />
          </div>
          <div className="flex justify-end items-center gap-4">
            <button
              type="button"
              onClick={handleCancel}
              className="cursor-pointer text-sm border py-1.5 px-4 rounded-md"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isPending || !values.name.trim() || !values.department}
              className={`text-sm py-1.5 px-4 rounded-md text-white ${
                isPending || !values.name.trim() || !values.department
                  ? "bg-primary/70 cursor-not-allowed"
                  : "bg-primary hover:bg-light_primary cursor-pointer"
              }`}
            >
              {isPending ? (
                <span className="flex items-center gap-1">
                  <Icon
                    icon="icon-park-outline:loading-one"
                    className="size-4 animate-spin"
                  />
                  {editMode ? "Updating..." : "Creating..."}
                </span>
              ) : editMode ? (
                "Update"
              ) : (
                "Create"
              )}
            </button>
          </div>
        </Form>
      </FormikProvider>
      <div className="space-y-2">
        <div className="space-y-1">
          <Text size="body-sm-default">Sub-Department List</Text>

          <div className="relative">
            <input
              type="text"
              placeholder="Search"
              value={search.searchTerm}
              onChange={(e) => {
                setSearch({ ...search, searchTerm: e.target.value });
                handleSearch(e.target.value);
              }}
              className="w-full py-[6px] ps-8 px-4 border border-gray-300 rounded-[6px] text-sm focus:outline-none"
            />
            <Icon
              icon="mdi:magnify"
              className="absolute left-3 top-1/2 size-[16px] transform -translate-y-1/2 text-gray-400"
            />
          </div>
        </div>
        <div className="space-y-2">
          {isLoading ? (
            <div className="flex justify-center py-4">
              <Icon
                icon="icon-park-outline:loading-one"
                className="size-6 animate-spin text-primary"
              />
            </div>
          ) : subDepartments.length ? (
            subDepartments.map((item) => (
              <div key={get(item, "_id")} className="flex gap-2">
                <div className="rounded-[6px] font-medium border w-full py-1.5 px-2 text-gray-500">
                  <div className="flex flex-col">
                    <span>{get(item, "name", "N/A")}</span>
                    <span className="text-xs text-gray-400">
                      Department: {get(item, "department.name", "N/A")}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2 justify-center">
                  <button
                    onClick={() => handleEdit(item)}
                    disabled={isPending}
                    className={`p-1.5 rounded-sm border border-green ${
                      isPending
                        ? "opacity-50 cursor-not-allowed"
                        : "hover:bg-green-50"
                    }`}
                  >
                    <Icon
                      icon="lucide:edit"
                      className="size-[14px] min-w-[14px]"
                    />
                  </button>
                  <button
                    onClick={() =>
                      setConfirmation({ state: true, id: get(item, "_id") })
                    }
                    disabled={isPending}
                    className={`p-1.5 rounded-sm border border-red ${
                      isPending
                        ? "opacity-50 cursor-not-allowed"
                        : "hover:bg-red-50"
                    }`}
                  >
                    <Icon
                      icon="lucide:trash"
                      className="size-[14px] min-w-[14px]"
                    />
                  </button>
                </div>
              </div>
            ))
          ) : (
            <p className="text-center py-4 text-gray-500">
              No sub-departments found
            </p>
          )}
        </div>
        <DeleteDialog
          confirmAction={confirmation.state}
          onClose={() => setConfirmation({ state: false, id: "" })}
          onConfirm={() => {
            deleteSubDepartment({ id: confirmation.id });
            setConfirmation({ state: false, id: "" });
          }}
        />
      </div>
    </div>
  );
};

export default SurgerySubDepartment;
