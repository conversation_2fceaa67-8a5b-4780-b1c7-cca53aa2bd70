import{bR as P,bV as d,a5 as C,aM as y,d5 as D,a2 as l,af as g,ah as U,a7 as k,aQ as h,aj as H}from"./index-ClX9RVH0.js";const M=P(d.CANTEENINVENTORY,"Canteen Inventory"),Y=M.useGetAll,F=()=>{var E,c,n,A;const[e,t]=C.useState({priority:"",date:"",search:"",page:1,limit:5}),v=e.search,_=y({search:e.search,date:e.date,priority:e.priority,...v?{}:{page:e.page,limit:e.limit}}),b=[{label:"Select ",value:"Select"},{label:"SNACKS",value:"SNACKS"},{label:"BEVERAGES",value:"BEVERAGES"},{label:"MEALS",value:"MEALS"},{label:"DESSERTS",value:"DESSERTS"},{label:"PACKAGED_FOOD",value:"PACKAGED_FOOD"},{label:"FRUITS",value:"FRUITS"},{label:"DAIRY_PRODUCTS",value:"DAIRY_PRODUCTS"},{label:"BAKERY_ITEMS",value:"BAKERY_ITEMS"},{label:"VEGETARIAN",value:"VEGETARIAN"},{label:"NON_VEGETARIAN",value:"NON_VEGETARIAN"},{label:"HEALTHY_CHOICES",value:"HEALTHY_CHOICES"},{label:"GRAINS_AND_PULSES",value:"GRAINS_AND_PULSES"},{label:"SPICES_AND_SEASONINGS",value:"SPICES_AND_SEASONINGS"},{label:"VEGETABLES",value:"VEGETABLES"},{label:"COOKING_OIL",value:"COOKING_OIL"},{label:"FLOUR_AND_RICE",value:"FLOUR_AND_RICE"},{label:"MEAT_AND_POULTRY",value:"MEAT_AND_POULTRY"},{label:"SWEETENERS",value:"SWEETENERS"},{label:"DAIRY_INGREDIENTS",value:"DAIRY_INGREDIENTS"},{label:"COOKING_EQUIPMENT",value:"COOKING_EQUIPMENT"},{label:"STORAGE_CONTAINERS",value:"STORAGE_CONTAINERS"},{label:"UTENSILS",value:"UTENSILS"},{label:"APPLIANCES",value:"APPLIANCES"},{label:"CLEANING_SUPPLIES",value:"CLEANING_SUPPLIES"},{label:"OTHER",value:"OTHER"}],{data:O}=Y(),s=O,[p,L]=C.useState(""),{data:r,isLoading:G}=D(_),S={columns:[{title:"Item Name",key:"itemName"},{title:"Categories",key:"categories"},{title:"Available Stock",key:"currentStock"},{title:"Total Stock",key:"totalStock"},{title:"Unit",key:"unit"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:(c=(E=s==null?void 0:s.data)==null?void 0:E.canteenInventory)==null?void 0:c.map((a,o)=>{var u,N,I,i,R,T;return{sn:o+1,itemName:((u=a==null?void 0:a.product)==null?void 0:u.name.charAt(0).toUpperCase())+((N=a==null?void 0:a.product)==null?void 0:N.name.slice(1).toLowerCase()),categories:((I=a==null?void 0:a.product)==null?void 0:I.category.charAt(0).toUpperCase())+((i=a==null?void 0:a.product)==null?void 0:i.category.slice(1).toLowerCase()),currentStock:a==null?void 0:a.availableStock,totalStock:a==null?void 0:a.totalStock,unit:((R=a==null?void 0:a.product)==null?void 0:R.unit.charAt(0).toUpperCase())+((T=a==null?void 0:a.product)==null?void 0:T.unit.slice(1).toLowerCase()),status:l.jsx(U,{status:"Active"}),action:l.jsx(g,{onShow:()=>{}})}})};return console.log(p,"list is shown"),l.jsxs("div",{children:[l.jsx(k,{listTitle:"Stock List",hideHeader:!0}),l.jsx(h,{onSearch:!0,onMainCategory:!0,mainOption:b,onPriority:!0,setCategorySelect:L,onSearchFunc:a=>t({...e,search:a}),onPrioritySelectFunc:a=>t({...e,priority:a})}),l.jsx(H,{rows:S.rows,columns:S.columns,loading:G,pagination:{currentPage:e.page,totalPage:((A=(n=r==null?void 0:r.data)==null?void 0:n.pagination)==null?void 0:A.pages)||1,limit:e.limit,onClick:({page:a,limit:o})=>{a&&t({...e,page:a}),o&&t({...e,limit:o})}}})]})};export{F as default};
