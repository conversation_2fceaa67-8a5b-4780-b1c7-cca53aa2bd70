import { Form, FormikProvider, useFormik } from "formik";
import { get } from "lodash";
import React, { useEffect, useState } from "react";
import * as Yup from "yup";
import { PopupModal } from "../../../../components";
import {
  useCreateOperationTreature,
  useUpdateOperationTreature,
} from "../../../../server-action/api/operationTreature";
import { useGetAllSurgeryDepartment } from "../../../../server-action/api/OTAssignment";
import { GlobalForm } from "../../../IPD/GeneralWard/GlobalForm";
import { ModalHeader } from "./AddOperation";

interface OtModalProps {
  onClose: () => void;
  edit?: boolean;
  data?: any;
  isOpen?: boolean;
}

const OtModal: React.FC<OtModalProps> = ({
  onClose,
  edit = false,
  data = null,
  isOpen = false,
}) => {
  const [subDepartments, setSubDepartments] = useState<any[]>([]);

  const { data: departmentsData } = useGetAllSurgeryDepartment({});
  const departments = get(departmentsData, "data.surgeryDepartments", []).map(
    (item: any) => ({
      ...item,
      label: get(item, "name"),
      value: get(item, "_id"),
    })
  );

  // Create and update mutations
  const { mutateAsync: createOT, isPending: isCreating } =
    useCreateOperationTreature();
  const { mutateAsync: updateOT, isPending: isUpdating } =
    useUpdateOperationTreature();

  const isPending = isCreating || isUpdating;

  // Validation schema
  const validationSchema = Yup.object({
    otName: Yup.string().required("OT Name is required"),
    floor: Yup.string().required("Floor is required"),
    department: Yup.string().required("Department is required"),
    subDepartment: Yup.string().required("Sub-Department is required"),
  });

  // Initialize formik
  const formik = useFormik({
    initialValues: {
      otName: get(data, "otName", ""),
      floor: get(data, "floor", ""),
      department: get(data, "department._id", get(data, "department", "")),
      subDepartment: get(
        data,
        "subDepartment._id",
        get(data, "subDepartment", "")
      ),
    },
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      try {
        const payload = {
          name: values.otName,
          floor: values.floor,
          department: values.department,
          subDepartment: values.subDepartment,
        };

        if (edit && data?._id) {
          await updateOT({
            _id: data._id,
            entityData: payload,
          });
        } else {
          await createOT(payload);
        }

        onClose();
      } catch (error) {
        console.error("Error saving OT configuration:", error);
      }
    },
  });

  // Prepare form fields
  const formFields = [
    {
      type: "text",
      field: "otName",
      label: "OT Name",
      placeholder: "Enter OT Name",
      required: true,
    },
    {
      type: "text",
      field: "floor",
      label: "Floor",
      placeholder: "Enter Floor",
      required: true,
    },
    {
      type: "searchableSelect",
      field: "department",
      label: "Department",
      placeholder: "Select Department",
      required: true,
      options: departments,
    },
    {
      type: "searchableSelect",
      field: "subDepartment",
      label: "Sub-Department",
      placeholder: "Select Sub-Department",
      required: true,
      options: subDepartments,
    },
  ];

  useEffect(() => {
    if (formik.values.department) {
      const subDepartments = get(
        departments.find((item) => item._id === formik.values.department),
        "subDepartments",
        []
      ).map((item: { _id: string; name: string }) => ({
        label: get(item, "name"),
        value: get(item, "_id"),
      }));
      setSubDepartments(subDepartments);
    }
  }, [formik.values.department, departments]);

  return (
    isOpen && (
      <PopupModal classname="max-w-2xl p-4 w-full space-y-2" onClose={onClose}>
        <ModalHeader
          className="ps-0"
          text={edit ? "Edit Operation Theater" : "Add Operation Theater"}
        />
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <div className="gap-4 grid grid-cols-2">
              <GlobalForm {...formik} formDatails={formFields} />
            </div>
            <div className="flex justify-end gap-3 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm border border-gray-300 rounded-md"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isPending || !formik.isValid || !formik.dirty}
                className={`px-4 py-2 text-sm text-white rounded-md ${
                  isPending || !formik.isValid || !formik.dirty
                    ? "bg-primary/70 cursor-not-allowed"
                    : "bg-primary hover:bg-primary/90"
                }`}
              >
                {isPending
                  ? edit
                    ? "Updating..."
                    : "Creating..."
                  : edit
                  ? "Update"
                  : "Create"}
              </button>
            </div>
          </Form>
        </FormikProvider>
      </PopupModal>
    )
  );
};

export default OtModal;
