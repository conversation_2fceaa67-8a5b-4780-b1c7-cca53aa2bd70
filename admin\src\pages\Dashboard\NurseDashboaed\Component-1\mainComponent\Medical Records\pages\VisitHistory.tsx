import { useState } from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { VisitHistoryData } from "../../Consultations/pages/sampleMasterTableData";

const VisitHistory = () => {
  const [selectedDate, setSelectedDate] = useState("");

  return (
    <div className='w-full p-4 mx-auto'>
      <h2 className='mb-4 text-lg font-semibold text-center'>Visit History</h2>

      {/* Date Filter */}
      <div className='flex justify-end mb-2'>
        <input
          type='date'
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
          className='w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none'
        />
      </div>

      {/* Visit History Table */}
      <div className='overflow-x-auto'>
        <MasterTable
          columns={VisitHistoryData.columns}
          rows={VisitHistoryData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default VisitHistory;
