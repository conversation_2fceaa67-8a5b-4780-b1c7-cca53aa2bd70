import { Icon } from "@iconify/react/dist/iconify.js";
import React, { useEffect, useState } from "react";

interface DrawerProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  position?: "left" | "right" | "top" | "bottom";
  size?: "sm" | "md" | "lg" | "xl" | "full";
  showOverlay?: boolean;
  closeOnOverlayClick?: boolean;
  showCloseButton?: boolean;
  className?: string;
}

const Drawer: React.FC<DrawerProps> = ({
  isOpen,
  onClose,
  children,
  title,
  position = "right",
  size = "md",
  showOverlay = true,
  closeOnOverlayClick = true,
  showCloseButton = true,
  className = "",
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [shouldRender, setShouldRender] = useState(isOpen);

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      // Small delay to trigger animation
      setTimeout(() => setIsAnimating(true), 10);
    } else {
      setIsAnimating(false);
      // Wait for animation to complete before unmounting
      setTimeout(() => setShouldRender(false), 300);
    }
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  if (!shouldRender) return null;

  // Size configurations
  const sizeClasses = {
    sm: {
      left: "w-64",
      right: "w-64",
      top: "h-64",
      bottom: "h-64",
    },
    md: {
      left: "w-80",
      right: "w-80",
      top: "h-80",
      bottom: "h-80",
    },
    lg: {
      left: "w-96",
      right: "w-96",
      top: "h-96",
      bottom: "h-96",
    },
    xl: {
      left: "w-[32rem]",
      right: "w-[32rem]",
      top: "h-[32rem]",
      bottom: "h-[32rem]",
    },
    full: {
      left: "w-full",
      right: "w-full",
      top: "h-full",
      bottom: "h-full",
    },
  };

  // Position and animation classes
  const positionClasses = {
    left: `left-0 top-0 h-full ${sizeClasses[size].left} ${
      isAnimating ? "translate-x-0" : "-translate-x-full"
    }`,
    right: `right-0 top-0 h-full ${sizeClasses[size].right} ${
      isAnimating ? "translate-x-0" : "translate-x-full"
    }`,
    top: `top-0 left-0 w-full ${sizeClasses[size].top} ${
      isAnimating ? "translate-y-0" : "-translate-y-full"
    }`,
    bottom: `bottom-0 left-0 w-full ${sizeClasses[size].bottom} ${
      isAnimating ? "translate-y-0" : "translate-y-full"
    }`,
  };

  const handleOverlayClick = () => {
    if (closeOnOverlayClick) {
      onClose();
    }
  };

  return (
    <>
      {/* Overlay */}
      {showOverlay && (
        <div
          className={`fixed inset-0 bg-black transition-opacity duration-300 ease-in-out z-40 ${
            isAnimating ? "bg-opacity-50" : "bg-opacity-0"
          }`}
          onClick={handleOverlayClick}
        />
      )}

      {/* Drawer */}
      <div
        className={`fixed bg-white shadow-2xl transform transition-transform duration-300 ease-in-out z-50 ${positionClasses[position]} ${className}`}
        style={{
          backdropFilter: "blur(10px)",
        }}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
            {title && (
              <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
            )}
            {showCloseButton && (
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200 ml-auto"
              >
                <Icon icon="lucide:x" className="text-gray-600 size-4" />
              </button>
            )}
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">{children}</div>
      </div>
    </>
  );
};

export default Drawer;
