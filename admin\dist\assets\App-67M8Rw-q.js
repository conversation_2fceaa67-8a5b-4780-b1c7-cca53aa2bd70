import{ag as t,bH as P,dD as D,ad as M,a5 as j,dE as B,aP as w,dF as F,a2 as e,b3 as g,bS as N,cm as y,dG as k,dH as G,dI as U,av as C,cy as T,bj as V,dC as v,dJ as Y,dK as H,dL as _,dM as S,dN as W,al as z}from"./index-ClX9RVH0.js";const K={title:"Dashboard",routes:[{id:10001,path:"/dashboard",title:"Dashboard",icon:"mage:dashboard-2"}]},Q={title:"Peoples",routes:[{id:10007,path:"#",title:"Patient",icon:"uil:wheelchair",children:[{path:t.PATIENTLIST,title:"Patient",id:10009}]},{id:10008,path:"#",title:"Medical-Staff",icon:"fa6-solid:user-doctor",children:[{path:t.DOCTORLIST,title:"Doctor",id:10009},{id:10010,path:t.NURSELIST,title:"Nurse"},{id:10011,path:t.PHARMACISTSLIST,title:"Pharmacist"},{id:10012,path:t.LABTECHNICIANLIST,title:"Lab Technician / Radiologists"},{path:t.DOCTORREVIEWS,title:"Reviews",id:10013}]},{id:10014,path:t.STAFFLIST,title:"Non Medical-Staff",icon:"healthicons:doctor-male-outline"}]},q={title:"Token Management",routes:[{id:10015,path:t.ACCOUNTTOKENMANAGEMENT,title:"Token List",icon:"fa6-solid:user-doctor"},{id:10016,path:t.TOKENCONFIG,title:" Token Configuration",icon:"fa6-solid:user-doctor"}]},$={title:"Daily Schedule",routes:[{id:10017,path:"#",title:"Shift Management",icon:"tdesign:task-time",children:[{path:t.DAILYSHIFT,title:"Daily List",id:10018},{path:t.SHIFTLIST,title:"Shift Config",id:10019},{path:t.SHIFTASSIGN,title:"Shift Assign",id:10020}]},{id:10021,path:t.ATTENDANCELIST,title:"Attendance",icon:"ix:user-success-filled"},{id:10022,path:"#",title:"Bed Allocation",icon:"mdi:bed-outline",children:[{path:t.BEDALLOCATIONLIST,title:"Bed Allocation List",id:10023},{path:t.BEDROOMDETAILS,title:"Rooms Details",id:10024}]}]},J={title:"Service Departments",routes:[{id:10025,path:"#",title:"OPD",icon:"mdi:walk",children:[{path:t.APPOINTMENTLIST,title:"Appointment List",id:10026},{path:t.OPDAPPOINTMENTFORM,title:"OPD Appointment Form",id:10137},{path:t.GENERALCHECKUPLIST,title:"General Checkup",id:10027},{path:t.DoctorsAppointment,title:"Doctors Appointment",id:10136}]},{id:10028,path:t.GENERALWARD,title:"IPD",icon:"material-symbols:ward-outline"},{id:10029,path:t.EMERGENCYROOM,title:"Emergency",icon:"material-symbols-light:e911-emergency-outline"},{id:10030,path:"#",title:"Operation Theater",icon:"material-symbols-light:ward-outline-sharp",children:[{path:t.AVAILABILITY,title:"Surgery Availability",id:10031},{path:t.GENERALOT,title:"Assign OT",id:10031},{path:t.CONFIGURATIONOT,title:"OT Configuration",id:10032}]},{id:10033,path:t.CERTIFICATE,title:"Certificate",icon:"ph:certificate-bold"},{id:10034,path:"#",title:"Ambulance",icon:"ph:ambulance-light",children:[{path:t.AMBULANCETYPE,title:"Ambulance Type",id:10035},{path:t.AMBULANCELIST,title:"Ambulance List",id:10036},{path:t.PRICECONFIG,title:"Price Config",id:10037},{path:t.AMBULANCEINQUIRY,title:"Ambulance Inquiry",id:10038}]},{id:10039,path:"#",title:"Inventory",icon:"tabler:brand-databricks",children:[{path:t.INVENTORY,title:"Inventory List",id:10040},{path:t.INVENTORYPRODUCTLIST,title:"Product List",id:10041},{path:t.INVENTORYCATEGORYLIST,title:"Category List",id:10042}]},{id:10043,path:"#",title:"Donation Management",icon:"arcticons:blood-donor",children:[{path:t.BLOODDONORLIST,title:"Donor List",id:10044},{path:t.RECEIVER,title:"Receiver List",id:10134},{path:t.BLOODBANK,title:"Blood Bank",id:10045},{path:t.CASHEQUIPMENT,title:"Cash & Equipment",id:10046}]}]},X={title:"Laboratory",routes:[{id:10046,path:t.LABTESTREQUESTLIST,title:"Test Request",icon:"carbon:result-draft"},{id:10047,path:t.LABTESTRESULT,title:"Test Result",icon:"carbon:result"},{id:10048,path:"#",title:"Lab Inventory",icon:"akar-icons:shipping-box-02",children:[{path:t.LABINVENTORY,title:"Inventory",id:10049},{path:t.LABPRODUCTLIST,title:"Product List",id:10050}]},{id:10051,path:t.LABPURCHASE,title:"Purchase",icon:"famicons:bag-check-outline"},{id:10052,path:"#",title:"Billing And Payments",icon:"ph:money-duotone",children:[{path:t.LABINVOICELIST,title:"Invoice List",id:10053}]},{id:10055,path:"#",title:"Test Config",icon:"clarity:settings-line",children:[{path:t.LABDEPARTMENTCONFIG,title:"Department Config",id:10056},{path:t.LABSUBCATEGORYCONFIG,title:"Sub-Category Config",id:10057},{path:t.LABTESTTYPECONFIG,title:"Test Type Config",id:10058}]}]},Z={title:"Radiology",id:"",routes:[{id:10060,path:t.RADIOLOGYTESTREQUEST,title:"Test Request",icon:"carbon:result"},{id:10061,path:t.RADIOLOGYREPORT,title:"Test Result",icon:"carbon:result"},{id:10062,path:"#",title:"Radiology Inventory",icon:"carbon:result-draft",children:[{path:t.RADIOLOGYINVENTORY,title:"Inventory",id:10063},{path:t.RADIOLOGYPRODUCTLIST,title:"Product List",id:10064}]},{path:t.RADIOLOGYPURCHASE,title:"Purchase",id:10059,icon:"clarity:settings-line"},{id:10065,path:"#",title:"Billing And Payments",icon:"ph:money-duotone",children:[{path:t.RADIOLOGYINVOICE,title:"Invoice List",id:10066}]},{id:10068,path:"#",title:"Test Config",icon:"clarity:settings-line",children:[{path:t.RADIOLOGYDEPARTMENT,title:"Department ",id:10070},{path:t.SERVICETYPE,title:"Sub Department",id:10069},{path:t.TESTLISTCONFIG,title:"Test List Config",id:10070}]}]},ee={title:"Billing and Payment",routes:[{id:10080,path:"#",title:"Financial Ops",icon:"ph:money-duotone",children:[{path:t.FINANCIALOPSINVOICE,title:"Invoice",id:10081},{path:t.FINANCIAlPAYMENT,title:"Advance Payment",id:10082}]},{id:10083,path:"#",title:"Payroll",icon:"mdi:account-hard-hat",children:[{path:t.PAYROLL_LIST,title:"Payroll List",id:10084},{path:t.PAYROLL_CONFIG,title:"Payroll Config",id:10085}]},{id:10086,path:"#",title:"Exp Management",icon:"arcticons:expense-register",children:[{path:t.EXPENSE_LIST,title:"Expense List",id:10087},{path:t.EXPENSE_CATEGORY,title:"Expense Category",id:10088},{path:t.VOUCHER,title:"Voucher",id:10122}]},{id:10089,path:"#",title:"Vendor Management",icon:"tabler:brand-databricks",children:[{path:t.PURCHASELIST,title:"Purchase List",id:10090},{path:t.PRODUCTLIST,title:"Product List",id:10136},{path:t.VENDORORDERLIST,title:"Order List",id:10136},{path:t.VENDORBILLING,title:"Billing",id:10136},{path:t.PURCHASERETURN,title:"Purchase Return",id:10092},{path:t.VENDORLIST,title:"Vendor List",id:10093}]},{id:10094,path:"#",title:"Banking Details",icon:"hugeicons:bank",children:[{path:t.BANK,title:"Bank Details",id:10095},{path:t.TRANSACTIONDETAILS,title:"Transaction Details",id:10096}]},{id:10123,path:"#",title:"Accounting",icon:"map:accounting",children:[{path:t.BALANCESHEET,title:"Balance Sheet",id:10124},{path:t.PAYMENT_VOUCHER,title:"Payment Voucher",id:10126},{path:t.PURCHASE_RETURN,title:"Purchase Return",id:10127},{path:t.SALES_VOUCHER,title:"Sales Voucher",id:10128},{path:t.SALES_RETURN,title:"Sales Return",id:10129},{path:t.TRANSACTION_LIST,title:"Transaction List",id:10130},{path:t.GENERAL_LEDGER_BALANCE,title:"General Ledger Balance",id:10131},{path:t.INCOME_STATEMENT,title:"Income Statement",id:10132}]}]},te={title:"Settings",routes:[{id:10097,path:"#",title:"Settings",icon:"lets-icons:setting-line",children:[{path:t.ROLEMANAGEMENT,title:"Role Management",id:10098},{path:t.SETTINGSDEPARTMENTCONFIG,title:"Department Config",id:10100},{path:t.WARD,title:"Ward Config",id:10101},{path:t.NOTIFICATIONSETUP,title:"Notification",id:10102}]},{id:10103,path:"#",title:"Commission Mgt",icon:"lets-icons:setting-line",children:[{path:t.COMMISSIONCONFIG,title:"Commission Config",id:10106}]},{id:10107,path:t.REPORT,title:"Report",icon:"lets-icons:setting-line"},{id:10108,path:"#",title:"Pricing Config",icon:"lets-icons:setting-line-light",children:[{path:t.PRICINGCONFIGCATEGORYLIST,title:"Category List",id:10109},{path:t.PRICINGCONFIGSERVICEITEM,title:"Service/Item List",id:10110}]}]},ie={title:"Pharmacy",routes:[{id:10112,path:t.POS,title:"POS",icon:"ph:money-fill"},{id:10113,path:t.MEDICINEREQUEST,title:"Medicine Request",icon:"uit:check-square"},{id:10114,path:"#",title:"Inventory",icon:"akar-icons:shipping-box-02",children:[{path:t.PHARMACYINVENTORY,title:"Inventory List",id:10115},{path:t.PHARMACYPRODUCT,title:"Product List",id:10116}]},{id:10117,path:"#",title:"Finance",icon:"la:money-bill",children:[{path:t.PHARMACYEXPENSE,title:"Expenses",id:10118},{path:t.PHARMACYSALES,title:"Sales",id:10119},{path:t.PHARMACYREPORT,title:"Reports",id:10120}]}]};function se(){var b,u;const{data:s}=P(),{data:l}=D({user:(b=s==null?void 0:s.user)==null?void 0:b.id}),a=(u=l==null?void 0:l.data)==null?void 0:u.notifications,c=M(),[d,o]=j.useState(!1),[r,n]=j.useState(!1),i=a==null?void 0:a.filter(x=>!x.isRead),h=a==null?void 0:a.slice(0,5),{mutateAsync:m}=B(),p=w(()=>o(!1)),{mutateAsync:f}=F();return e.jsxs("div",{className:"relative",ref:p,children:[e.jsxs("button",{onClick:()=>{o(!d),n(!0)},className:"p-1 border  rounded-sm text-white bg-[#FFFFFF20]",children:[e.jsx(g,{icon:"mingcute:notification-fill",width:"18",height:"18"}),!r&&(i==null?void 0:i.length)>0&&e.jsx("span",{className:"absolute -top-1 -right-1 bg-red text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium",children:(i==null?void 0:i.length)>9?"9+":i==null?void 0:i.length})]}),d&&e.jsxs("div",{className:N("absolute top-14 right-0 mt-0 w-[350px] bg-white rounded-lg shadow-lg","border border-gray-200 z-50 max-h-[400px] overflow-hidden"),children:[e.jsx("div",{className:N("absolute -top-2 right-3 w-4 h-4 bg-white transform rotate-45","border-t border-l border-gray-200")}),e.jsxs("div",{className:"relative z-10 overflow-hidden rounded-lg",children:[e.jsx("div",{className:"p-4 border-b border-gray-200 bg-gray-50",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"font-semibold text-gray-900",children:"Notifications"}),e.jsx(y,{to:t.NOTIFICATIONSETUP,onClick:()=>o(!1),className:"text-sm text-gray-500",children:"View All"})]})}),e.jsx("div",{className:"max-h-[300px] overflow-y-auto",children:(h==null?void 0:h.length)===0?e.jsxs("div",{className:"p-6 text-center text-gray-500",children:[e.jsx("span",{className:"text-2xl block mb-2",children:"🔔"}),e.jsx("p",{className:"text-sm",children:"No notifications"})]}):h==null?void 0:h.map(x=>{const E=k(x.type),R=G(x.type),A=R==="other"?null:U[R];return e.jsx("div",{className:N("p-3 border-b border-gray-100 hover:bg-gray-50 transition-colors",!x.isRead&&"bg-blue-50 border-l-4 border-l-blue-500"),children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("span",{className:"text-lg",children:E})}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:N("text-sm font-medium truncate",x.isRead?"text-gray-700":"text-gray-900"),children:x.title}),e.jsx("p",{className:"text-xs text-gray-600 mt-1 line-clamp-2",children:x.body}),e.jsxs("div",{className:"flex items-center gap-2 mt-2",children:[e.jsx("span",{className:"text-xs text-gray-500",children:x.date}),e.jsx("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full",children:A==null?void 0:A.name}),e.jsx("div",{})]})]}),e.jsxs("div",{className:"flex place-items-center gap-1 ml-2",children:[!x.isRead&&e.jsx("button",{onClick:async I=>{I.stopPropagation(),await m({_id:x._id,entityData:{...x,isRead:!0}})},className:"p-1 text-green-600 hover:bg-green-50 rounded text-xs",title:"Mark as read",children:"✓"}),e.jsx(g,{icon:"mingcute:delete-fill",onClick:async I=>{I.stopPropagation(),await f({id:JSON.stringify([x._id])})},className:"p-1 text-red hover:bg-red-50 rounded text-2xl"})]})]})})]})},x==null?void 0:x._id)})}),(h==null?void 0:h.length)>0&&e.jsx("div",{className:"p-3 border-t border-gray-200 bg-gray-50",children:e.jsx("button",{onClick:()=>{o(!1),c(t.NOTIFICATIONSETUP)},className:"w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium",children:"View All Notifications"})})]})]})]})}const ae=[e.jsx(g,{icon:"lucide:list-todo",width:"18",height:"18"})],L=()=>{const[s,l]=j.useState(!1),[a,c]=j.useState(!1),d=w(()=>{s&&l(!1)}),o=()=>{c(!0),l(!1)},r=()=>{c(!1),localStorage.clear(),window.location.reload()},n=JSON.parse(localStorage.getItem("user")||"{}");return e.jsx("nav",{className:"bg-primaryBlue flex items-center text-white z-50",children:e.jsxs("div",{className:"flex items-center py-2 text-base justify-between w-full max-w-screen-2xl mx-auto px-4",children:[e.jsx("h2",{className:"font-bold text-2xl",children:"Medical"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-1 border rounded-sm text-white bg-[#FFFFFF20] flex items-center justify-center size-[30px]",children:e.jsx("img",{src:"/country.png",alt:"logo",className:"h-full w-full object-fit rounded-sm"})}),ae.map((i,h)=>e.jsx("div",{className:"border rounded-sm text-white bg-[#FFFFFF20] flex items-center justify-center size-[30px]",children:i},h)),e.jsx("div",{className:"flex items-center justify-center h-8 w-8",children:e.jsx(se,{})}),e.jsxs("div",{className:"relative flex items-center h-8",ref:d,children:[e.jsx("button",{className:"size-7 relative flex items-center justify-center focus:outline-none",onClick:()=>l(!s),children:e.jsx("img",{className:"rounded-sm h-full w-full object-cover",src:"/no-user.png",alt:"Profile"})}),s&&e.jsxs("div",{className:"absolute right-0 mt-2 top-full w-48 bg-white rounded-md shadow-lg py-1 z-20",children:[e.jsx("div",{className:"absolute -top-2 right-1 w-4 h-4 bg-white transform rotate-45"}),e.jsxs("div",{className:"relative z-10",children:[e.jsxs("div",{className:"py-2",children:[e.jsx("h3",{className:"flex items-center px-4 font-semibold text-lg text-gray-700",children:C.get(n,"fullName","N/A").replace(/^./,i=>i.toUpperCase())}),e.jsx("p",{className:"flex items-center px-4 text-sm text-gray-700",children:C.get(n,"role")})]}),e.jsx("div",{className:"border-t border-gray-100"}),e.jsxs("button",{onClick:o,className:"flex items-center w-full text-left px-4 py-2 text-red hover:bg-gray-100 rounded-sm text-sm text-red-600",children:[e.jsx(g,{icon:"lucide:log-out",className:"mr-2"}),"Logout"]})]})]})]}),a&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-lg p-4 sm:p-6 max-w-sm w-full",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Confirm Logout"}),e.jsx("p",{className:"text-sm text-gray-500 mb-4",children:"Are you sure you want to logout? Any unsaved changes will be lost."}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{onClick:()=>c(!1),className:"px-3 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors text-sm",children:"Cancel"}),e.jsxs("button",{onClick:r,className:"px-3 py-2 bg-red text-white rounded-md hover:bg-red-700 transition-colors flex items-center text-sm",children:[e.jsx(g,{icon:"lucide:log-out",className:"mr-2"}),"Logout"]})]})]})})]})]})})},le=()=>{const l=T().pathname.split("/").filter(r=>r),a=r=>r.replace(/-/g," ").replace(/\b\w/g,n=>n.toUpperCase()),c=r=>/^[a-f0-9]{24}$/i.test(r),d=r=>{const n=r.split("-");return n.length>1&&n[0]==="edit"&&c(n[1])},o=r=>{if(c(r))return null;if(d(r))return"edit";const n=r.split("-");return n.length>1&&n.some(i=>c(i))?n.filter(i=>!c(i)).join("-"):r};return e.jsxs("div",{className:"flex items-center text-gray-500 text-sm",children:[e.jsxs(y,{to:"/",className:"flex items-center divide-x-2 divide-gray-400 space-x-1 hover:text-gray-700",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.0003 2.23145C9.23894 2.23145 8.49211 2.44007 7.84095 2.83466L3.67428 5.35966C3.06157 5.73095 2.55493 6.25397 2.20331 6.87819C1.8517 7.50241 1.66698 8.20675 1.66699 8.92318V14.1665C1.66699 15.2716 2.10598 16.3314 2.88738 17.1128C3.66878 17.8942 4.72859 18.3332 5.83366 18.3332H14.167C15.2721 18.3332 16.3319 17.8942 17.1133 17.1128C17.8947 16.3314 18.3337 15.2716 18.3337 14.1665V8.92235C18.3335 8.20608 18.1487 7.50176 17.7971 6.87774C17.4455 6.25372 16.9389 5.73087 16.3264 5.35966L12.1597 2.83466C11.5086 2.44008 10.7617 2.23145 10.0003 2.23145ZM8.70471 4.26003C9.0954 4.02328 9.5435 3.89811 10.0003 3.89811C10.4572 3.89811 10.9052 4.02328 11.2959 4.26003L15.4626 6.78503C15.8301 7.00776 16.1341 7.32147 16.345 7.69589C16.556 8.07025 16.6669 8.49266 16.667 8.92235V14.1665C16.667 14.8296 16.4036 15.4654 15.9348 15.9343C15.4659 16.4031 14.83 16.6665 14.167 16.6665H13.3337V14.1665C13.3337 13.2825 12.9825 12.4346 12.3573 11.8095C11.7322 11.1844 10.8844 10.8332 10.0003 10.8332C9.11627 10.8332 8.26842 11.1844 7.6433 11.8095C7.01818 12.4346 6.66699 13.2825 6.66699 14.1665V16.6665H5.83366C5.17062 16.6665 4.53473 16.4031 4.06589 15.9343C3.59705 15.4654 3.33366 14.8296 3.33366 14.1665V8.92318C3.33365 8.49332 3.44448 8.07069 3.65545 7.69616C3.86642 7.32163 4.1704 7.00782 4.53803 6.78504L8.70471 4.26003ZM11.1788 12.988C11.4914 13.3006 11.667 13.7245 11.667 14.1665V16.6665H8.33366V14.1665C8.33366 13.7245 8.50925 13.3006 8.82181 12.988C9.13437 12.6754 9.5583 12.4999 10.0003 12.4999C10.4424 12.4999 10.8663 12.6754 11.1788 12.988Z",fill:"#5C5E64"})}),e.jsx("span",{className:"ps-2",children:"Dashboard"})]}),l.map((r,n)=>{const i=o(r);if(i===null)return null;const h=`/${l.slice(0,n+1).join("/")}`,m=n===l.length-1;return e.jsxs(V.Fragment,{children:[e.jsx("span",{className:"px-2",children:e.jsx(g,{icon:"lucide:chevron-right",className:"w-4 h-4"})}),m?e.jsx("span",{className:"text-primary",children:a(i)}):e.jsx(y,{to:h,className:"hover:text-gray-700",children:a(i)})]},h)})]})},ne=()=>{const{toggleSidebar:s,miniSidebar:l}=v();return e.jsx("div",{className:"bg-white h-9 px-4 flex items-center justify-between border-b",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:s,className:"p-1 rounded hover:bg-gray-100 transition-colors",title:l?"Collapse sidebar":"Expand sidebar",children:e.jsx(g,{icon:l?"mdi:menu-open":"mdi:menu",className:"text-lg"})}),e.jsx(le,{})]})})},O=({children:s,variant:l="default",size:a="body-sm-default",className:c=""})=>{const d={default:"text-gray-900","grey-600":"text-gray-600","gray-900":"text-gray-900","primary-blue":"text-primaryBlue","fadish-black":"text-gray-700",white:"text-white"},o={"body-xs-mid":"text-xs font-medium","body-sm-default":"text-sm font-medium"};return e.jsx("span",{className:N(d[l]||d.default,o[a]||o["body-sm-default"],c),children:s})},re=({SidebarRoutes:s,toggleExpand:l,miniSidebar:a,expandedItems:c,setMiniSidebar:d})=>{var h,m;const{pathname:o}=T(),r=p=>o.startsWith(p),n=p=>p?p.some(f=>r(f.path)):!1,i=p=>!!(r(p.path)||n(p.children));return e.jsxs("section",{className:`flex flex-col pt-2 ${a?"":"items-center"}`,id:"main-menu",children:[((h=s==null?void 0:s.routes)==null?void 0:h.length)>0&&e.jsxs("div",{className:`${a?"px-6":"px-3 "} flex items-center gap-3`,children:[e.jsx("span",{className:"bg-gray-400 h-[1px] w-[40%] flex-1"}),e.jsx("span",{className:"",children:e.jsx(O,{variant:"grey-600",size:"body-xs-mid",children:a?s.title:C.get(s,"title","").split(" ")[0]})}),e.jsx("span",{className:"bg-gray-400 flex-1 h-[.5px] w-[40%]"})]}),((m=s==null?void 0:s.routes)==null?void 0:m.length)>0&&e.jsx("div",{className:"flex flex-col gap-1",id:"menu",onClick:()=>d(!0),children:s.routes.map(p=>{const f=i(p);return e.jsxs("div",{children:[e.jsxs(y,{to:p.path,className:N("flex place-items-center",a?"px-6":"px-3","py-2 gap-5 justify-between","hover:bg-teal-600/10 ",f?"bg-secondaryBlue":""),onClick:()=>{p.children&&(l==null||l(p.id))},children:[e.jsxs("section",{className:"flex place-items-center gap-3",children:[e.jsx(g,{icon:p.icon,color:f?"#1364DE":"#000000"}),a&&e.jsx("p",{className:N(f?"text-primaryBlue":"text-gray-800","font-medium whitespace-nowrap"),children:p.title})]}),a&&e.jsx("div",{children:p.children&&e.jsx(g,{icon:c.includes(p.id)?"lucide:chevron-down":"lucide:chevron-right",color:f?"#1364DE":"black",fontSize:16,className:`transition-transform duration-300 ease-in-out ${c.includes(p.id)?"rotate-180":"rotate-0"}`})})]}),a&&e.jsx("div",{children:p.children&&c.includes(p.id)&&e.jsxs("div",{className:"ml-8 flex flex-col relative",children:[p.children.map(b=>{const u=r(b.path);return e.jsxs(y,{to:b.path,className:N("flex place-items-center pl-4 relative rounded-lg",u?"text-primaryBlue":""),children:[e.jsx("div",{className:"absolute left-[0.1rem] top-1/2 -translate-y-1/2",children:e.jsx("div",{className:N("h-[2px] w-4",u?"bg-primaryBlue":"bg-gray-200")})}),e.jsx(O,{variant:u?"primary-blue":"grey-600",size:"body-sm-default",className:N("py-2 px-3 rounded",u?"text-primaryBlue":"text-gray-300"),children:b.title})]},b.id)}),e.jsx("div",{className:"absolute h-full w-[2px] bg-secondaryBlue",style:{height:"calc(100% - 17px)"}})]})})]},p.id)})})]})},ce=(s,l=!1)=>{const{permissions:a,isLoading:c}=Y();return{filteredRoutes:j.useMemo(()=>{var n;if(l||!a||c)return s;const o=i=>{var m,p,f,b;if(!i)return!1;const h=(m=a==null?void 0:a.module)==null?void 0:m.find(u=>(u==null?void 0:u.permissionKey)===i);if(h)return(p=h==null?void 0:h.permissions)==null?void 0:p.includes("READ");for(const u of(a==null?void 0:a.module)||[]){const x=(f=u==null?void 0:u.subModule)==null?void 0:f.find(E=>(E==null?void 0:E.permissionKey)===i);if(x)return(b=x==null?void 0:x.permissions)==null?void 0:b.includes("READ")}return!1},r=(n=s==null?void 0:s.routes)==null?void 0:n.filter(i=>i!=null&&i.children&&(i==null?void 0:i.children.length)>0?i.children.filter(m=>o(m.id)).length>0:o(i.id)).map(i=>{if(i!=null&&i.children&&(i==null?void 0:i.children.length)>0){const h=i.children.filter(m=>o(m.id));return{...i,children:h}}return i});return{...s,routes:r}},[s,a,l,c]),hasPermissionData:!!a&&!c,isLoading:c}},oe=[{key:"dashboard",routes:K},{key:"peoples",routes:Q},{key:"tokenManagement",routes:q},{key:"dailySchedule",routes:$},{key:"serviceDepartments",routes:J},{key:"laboratory",routes:X},{key:"radiology",routes:Z},{key:"pharmacy",routes:ie},{key:"billingAndPayments",routes:ee},{key:"settings",routes:te}],de=()=>{const s=T(),l=oe.map(({key:d,routes:o})=>({key:d,filteredRoutes:ce(o,!1).filteredRoutes})).filter(d=>d.filteredRoutes.routes.length>0),a=l.flatMap(d=>d.filteredRoutes.routes);return{filteredSections:l,currentParentId:(()=>{for(const o of a)if(o.children&&o.children.find(n=>s.pathname.startsWith(n.path)))return o.id;const d=a.find(o=>{var r;return((r=s.pathname)==null?void 0:r.startsWith(o.path))&&o.path!=="/"});return(d==null?void 0:d.id)||1})()}},he=()=>{const[s,l]=j.useState([]);return{expandedItems:s,toggleExpand:c=>{l(d=>d.includes(c)?d.filter(o=>o!==c):[c])}}},pe=()=>{const{filteredSections:s,currentParentId:l}=de(),{expandedItems:a,toggleExpand:c}=he(),{miniSidebar:d,setMiniSidebar:o}=v(),r={currentParentId:l,expandedItems:a,miniSidebar:d,setMiniSidebar:o,toggleExpand:c},n=JSON.parse(localStorage.getItem("user")||"{}");return e.jsx("div",{className:"relative shadow-[4px_0_10px_-2px_rgba(0,0,0,0.1)] border-r",id:"sidebar",children:e.jsxs("section",{className:"flex flex-col",children:[e.jsxs("div",{className:"h-auto pt-4 ps-6 flex items-center gap-4",children:[e.jsx("img",{src:"/no-user.png",alt:"company-logo",className:"size-10 border-white rounded-full",id:"logo"}),e.jsxs("div",{children:[e.jsx("h3",{className:"flex items-center font-semibold text-lg text-gray-700",children:C.get(n,"fullName","N/A").replace(/^./,i=>i.toUpperCase())}),e.jsx("p",{className:"flex items-center text-sm text-gray-700",children:C.get(n,"role")})]})]}),s.map(({key:i,filteredRoutes:h})=>e.jsx(re,{SidebarRoutes:h,...r},i))]})})},xe=[{name:"Blog Management",icon:"material-symbols:description-outline",path:t.WEBSITE_BLOG_MANAGEMENT},{name:"Team Management",icon:"ri:team-fill",path:t.WEBSITE_TEAM_MANAGEMENT},{name:"Gallery",icon:"material-symbols:photo-library-outline",path:t.WEBSITE_GALLERY},{name:"Campaign",icon:"mdi:person-group",path:"/website/campaign-management"},{name:"Career",icon:"ph:steps-duotone",path:t.WEBSITE_CARRER},{name:"Training",icon:"material-symbols:contact-mail-outline",path:t.WEBSITE_TRAINING}],me=()=>{const s=T();return e.jsxs("div",{className:"w-72 h-screen bg-[#1E293B] border-r border-gray-200 flex flex-col shadow-sm",children:[e.jsx("div",{className:"p-6 border-b border-gray-200",children:e.jsxs(y,{to:"/website",className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",children:e.jsx(g,{icon:"material-symbols:crescent-moon",className:"text-white text-xl"})}),e.jsxs("div",{children:[e.jsx("div",{className:"flex items-baseline",children:e.jsx("span",{className:"text-xl font-bold text-center text-white",children:"Arogya"})}),e.jsx("p",{className:"text-xs text-gray-400",children:"Website Management"})]})]})}),e.jsx("nav",{className:"flex-1 p-4",children:e.jsx("div",{className:"space-y-4",children:xe.map((l,a)=>{const c=s.pathname===l.path;return e.jsxs(y,{to:l.path,className:`flex items-center space-x-3 px-4 py-3 rounded-md  transition-all duration-200 group border border-gray-400 ${c?"bg-[#3B82F6] text-white":"text-white hover:text-black hover:bg-gray-50"}`,children:[e.jsx(g,{icon:l.icon,className:`text-xl transition-colors ${c?"text-blue-600":"text-gray-400 group-hover:text-black"}`}),e.jsx("span",{className:"font-medium",children:l.name}),c&&e.jsx("div",{className:"ml-auto w-2 h-2 bg-blue-600 rounded-full"})]},a)})})}),e.jsxs("div",{className:"p-4 border-t border-gray-200",children:[e.jsxs(y,{to:t.ADMINDASHBOARD,className:"flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 text-white hover:bg-gray-50 hover:text-gray-900 border border-transparent hover:border-gray-200 group",children:[e.jsx("span",{className:"font-medium",children:"Go to Admin Dashboard"}),e.jsx(g,{icon:"material-symbols:arrow-outward",className:"text-sm text-gray-400 group-hover:text-white transition-colors ml-auto"})]}),e.jsx("div",{className:"mt-3 px-4 py-2 bg-gray-50 rounded-lg border border-gray-100",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center",children:e.jsx(g,{icon:"material-symbols:person",className:"text-white text-sm"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-xs font-medium text-gray-900 truncate",children:"Website Admin"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Content Manager"})]})]})})]})]})};function ge(){const s=H(),{miniSidebar:l}=v(),c=T().pathname.startsWith("/website");return j.useEffect(()=>{const d=_();s.setQueryData(["auth"],d)},[s]),j.useEffect(()=>{const d=async()=>{try{return await W(n=>{var i,h,m;z.info(e.jsxs("div",{children:[e.jsx("p",{style:{margin:"4px 0"},children:(i=n==null?void 0:n.notification)==null?void 0:i.title}),((h=n==null?void 0:n.notification)==null?void 0:h.body)&&e.jsx("small",{children:(m=n==null?void 0:n.notification)==null?void 0:m.body})]}),{position:"top-right",autoClose:3e3})})}catch(r){return console.error("Error setting up FCM listener:",r),()=>{}}};let o;return d().then(r=>{o=r}),()=>{o&&o()}},[]),c?e.jsxs("div",{className:"w-full",children:[e.jsx(L,{}),e.jsxs("div",{className:"flex bg-[#eff7f9]",children:[e.jsx("aside",{className:"fixed left-0 bg-primary w-72 h-screen overflow-y-auto",children:e.jsx(me,{})}),e.jsx("div",{className:"flex-1 flex flex-col pl-72",children:e.jsx("main",{className:"flex-1 overflow-auto bg-bg min-h-screen bg-[#FFF]",children:e.jsx(S,{})})})]})]}):e.jsxs("div",{children:[e.jsx(L,{}),e.jsxs("div",{className:"flex bg-[#eff7f9]",children:[e.jsx("aside",{className:N("bg-white h-[calc(100vh-48px)] overflow-y-auto transition-width duration-100",{"w-20":!l,"max-w-72 w-full z-[30]":l}),children:e.jsx(pe,{})}),e.jsxs("div",{className:"max-h-[calc(100vh-48px)] w-full h-full overflow-auto ",children:[e.jsx(ne,{}),e.jsx("main",{className:"overflow-auto pt-2 px-2 bg-bg h-full max-h-[calc(100vh-84px)]",children:e.jsx("div",{className:"",children:e.jsx(S,{})})})]})]})]})}export{ge as default};
