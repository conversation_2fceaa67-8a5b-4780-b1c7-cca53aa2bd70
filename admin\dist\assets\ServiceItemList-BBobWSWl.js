import{ad as T,a5 as r,a2 as t,af as j,aQ as C,aj as I,bA as P,ag as D}from"./index-ClX9RVH0.js";import{u as E,a as R}from"./serviceApi-DTA9RlDi.js";const _=()=>{var u,g;const n=T(),[c,v]=r.useState(1),[l,F]=r.useState(20),[y,N]=r.useState(""),[S,s]=r.useState(!1),[p,i]=r.useState(null),{data:a,isLoading:x,refetch:h}=E({page:c,limit:l,search:y}),f=((u=a==null?void 0:a.data)==null?void 0:u.services)||[],o=((g=a==null?void 0:a.data)==null?void 0:g.pagination)||{page:1,pages:1,limit:20},{mutateAsync:A}=R({id:JSON.stringify([p])}),d={columns:[{title:"S.N.",key:"sn"},{title:"Category",key:"categoryName"},{title:"Service/Item",key:"serviceName"},{title:"Price",key:"rate"},{title:"VAT(13%)",key:"vat"},{title:"Total Price(Rs.)",key:"price"},{title:"Notes",key:"reamrk"},{title:"Action",key:"action"}],rows:f.map((e,w)=>{var m;return{sn:(c-1)*l+(w+1),categoryName:((m=e.categoryName)==null?void 0:m.categoryName)||"N/A",serviceName:e.serviceName||"N/A",rate:e.rate||0,vat:e.vat?`${e.vat}%`:"0%",price:(e.price*(1+.13)).toFixed(2),reamrk:e.reamrk||"N/A",action:t.jsx(j,{onEdit:()=>n(`/pricing-config/service-list/add-new-service/${e._id}`),onDelete:()=>{i(e._id),s(!0)}})}})},b=async()=>{try{await A(),s(!1),i(null),h()}catch(e){console.error("Error deleting service:",e)}},k=()=>{n(D.PRICINGCONFIGSERVICEITEMADD)};return t.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[t.jsx(C,{headerTitle:"Pricing Service List",onSearch:!0,onContactNumber:!0,button:!0,buttonText:"Add New Service",buttonAction:k,onSearchFunc:e=>N(e)}),t.jsxs("div",{className:"bg-white rounded-md",children:[t.jsx("hr",{}),t.jsx(I,{columns:d.columns,rows:d.rows,loading:x,color:"bg-white",textcolor:"text-gray-400",pagination:{currentPage:o.page,totalPage:o.pages,limit:o.limit,onClick:({page:e})=>{e&&v(e)}}}),t.jsx(P,{confirmAction:S,title:"Do you want to delete this Pricing record?",des:"This action cannot be undone.",onClose:()=>{s(!1),i(null)},onConfirm:b})]})]})};export{_ as default};
