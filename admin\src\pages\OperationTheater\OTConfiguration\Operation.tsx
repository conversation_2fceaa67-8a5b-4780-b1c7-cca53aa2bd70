import { Icon } from "@iconify/react/dist/iconify.js";
import { Form, FormikProvider, useFormik } from "formik";
import { get } from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { InputField } from "../../../components";
import Drawer from "../../../components/Drawer";
import SearchableSelect from "../../../components/SearchableSelect";
import { StepperList } from "../../../components/Stepper";
import { useGetAllBedAssign } from "../../../server-action/api/bedAssing";
import { useGetbloodAlternative } from "../../../server-action/api/DonorApi";
import { GlobalForm } from "../../IPD/GeneralWard/GlobalForm";
import { Label } from "../../PurchaseManagement/PurchaseOrder/components/NewPurchaseOrder";
import {
  checklist,
  getBloodUnitsByType,
  stepperList,
  useGetAllApisData,
} from "./Components/ot.obj";
import SurgeryDepartment from "./Components/SurgeryDepartment";
import SurgerySubDepartment from "./Components/SurgerySubDepatment";

const Operation = () => {
  const [activeStep, setActive] = useState(1);
  const [drawer, setDrawer] = useState({
    open: false,
    of: "",
  });
  const [patient, setPatient] = useState("");
  const { data: bed } = useGetAllBedAssign(
    { patient: patient },
    {
      enabled: patient !== "",
    }
  );

  const [alternative, setAlternative] = useState(false);

  // Fetch blood alternatives when needed
  const { data: bloodAlternative } = useGetbloodAlternative(
    {},
    {
      enabled: alternative,
    }
  );
  const [subDepartment, setSubDepartment] = useState([]);
  const {
    patientIdList,
    patientList,
    surgeryDepartment,
    theatres,
    doctorList,
    nurseList,
    anesthesistList,
    bloodInventry,
  } = useGetAllApisData();
  const formik = useFormik({
    initialValues: {
      patient: "",
      date: "",
      department: "",
      subDepartment: "",
      operationType: "",
      ot: "",
      floor: "",
      time: "",
      cost: "",
      surgeon: "",
      assistantSurgeon: "",
      anesthesist: "",
      periOperatingNurse: "",
      scrubNurses: "",
      circulatingNurses: "",
      bloodInStock: false,
      bloodGroup: "",
      bloodUnits: "",
      ward: "",
      bed: "",
      preCheckups: {
        fastingStatus: "pending",
        presurgery: "pending",
        emergencyEquipment: "pending",
      },
    },
    enableReinitialize: true,
    onSubmit(values) {
      console.log(values);
    },
  });

  const { setFieldValue, values } = formik;

  // Check if blood alternative should be shown
  useEffect(() => {
    if (values.bloodInStock && values.bloodGroup) {
      const availableUnits = getBloodUnitsByType(
        bloodInventry as any,
        values.bloodGroup
      );

      // If no units available or units are 0, show alternatives
      if (
        !availableUnits ||
        availableUnits.length === 0 ||
        (availableUnits.length > 0 && availableUnits[0].unit === 0)
      ) {
        setAlternative(true);
      } else {
        setAlternative(false);
      }
    } else {
      setAlternative(false);
    }
  }, [values.bloodInStock, values.bloodGroup, bloodInventry]);

  // Get bed and ward information when patient is selected
  useEffect(() => {
    if (get(bed, "data.bedAssign", []).length > 0) {
      const bedAssignData = get(bed, "data.bedAssign", [])[0];
      const transferInfoArray = get(bedAssignData, "transferInfo", []);

      if (transferInfoArray.length > 0) {
        // Get the last transfer info (current bed assignment)
        const currentTransfer = transferInfoArray[transferInfoArray.length - 1];

        // Set ward and bed values from the last transfer info
        setFieldValue("bed", get(currentTransfer, "bedNumber", ""));
        setFieldValue(
          "ward",
          get(currentTransfer, "categoryName.categoryName", "")
        );
      }
    }
  }, [bed, setFieldValue]);
  // Filter doctor options to prevent duplicate selections
  const filteredDoctorList = useMemo(() => {
    return doctorList.filter(
      (doctor) =>
        doctor.value !== values.surgeon &&
        doctor.value !== values.assistantSurgeon
    );
  }, [doctorList, values.surgeon, values.assistantSurgeon]);

  // Filtered options for each doctor role
  const surgeonOptions = useMemo(
    () =>
      doctorList.filter((doctor) => doctor.value !== values.assistantSurgeon),
    [doctorList, values.assistantSurgeon]
  );

  const assistantSurgeonOptions = useMemo(
    () => doctorList.filter((doctor) => doctor.value !== values.surgeon),
    [doctorList, values.surgeon]
  );

  const anesthesistOptions = useMemo(
    () =>
      anesthesistList.filter(
        (anesthesist) => anesthesist.value !== values.anesthesist
      ),
    [anesthesistList, values.anesthesist]
  );

  // Filter nurse options to prevent duplicate selections
  const periOperatingNurseOptions = useMemo(
    () =>
      nurseList.filter(
        (nurse) =>
          nurse.value !== values.scrubNurses &&
          nurse.value !== values.circulatingNurses
      ),
    [nurseList, values.scrubNurses, values.circulatingNurses]
  );

  const scrubNursesOptions = useMemo(
    () =>
      nurseList.filter(
        (nurse) =>
          nurse.value !== values.periOperatingNurse &&
          nurse.value !== values.circulatingNurses
      ),
    [nurseList, values.periOperatingNurse, values.circulatingNurses]
  );

  const circulatingNursesOptions = useMemo(
    () =>
      nurseList.filter(
        (nurse) =>
          nurse.value !== values.periOperatingNurse &&
          nurse.value !== values.scrubNurses
      ),
    [nurseList, values.periOperatingNurse, values.scrubNurses]
  );
  const handleClose = useCallback(() => {
    setDrawer({ open: false, of: "" });
  }, []);
  const components: Record<string, React.ReactNode> = {
    department: <SurgeryDepartment onClose={handleClose} />,
    subDepartment: <SurgerySubDepartment onClose={handleClose} />,
  };
  const stepper = stepperList.map((item, index) => ({
    ...item,
    isActive: index + 1 === activeStep,
  }));
  return (
    <div className="grid grid-cols-1 gap-4 h-full md:grid-cols-4">
      <div className="">
        <div className="bg-white rounded-sm">
          <StepperList steps={stepper} />
        </div>
      </div>
      <div className="col-span-3 max-h-[100vh-98px] overflow-auto h-auto">
        <FormikProvider value={formik}>
          <Form className="h-full space-y-4" onSubmit={formik.handleSubmit}>
            {/* Patient section */}
            <div
              onMouseOver={() => setActive(1)}
              className="grid grid-cols-4 gap-4 bg-white rounded-sm p-4"
            >
              <Label label="Patient Id">
                <SearchableSelect
                  onChange={(value: string) => {
                    setPatient(value);
                    setFieldValue("patient", value);
                  }}
                  options={patientIdList}
                  value={values.patient}
                />
              </Label>
              <Label label="Patient Name">
                <SearchableSelect
                  onChange={(value: string) => {
                    setPatient(value);
                    setFieldValue("patient", value);
                  }}
                  options={patientList}
                  value={values.patient}
                />
              </Label>
              <InputField disabled value={values.ward} label="Ward" />
              <InputField disabled value={values.bed} label="Bed" />
            </div>

            {/* Surgery details section */}
            <div
              onMouseOver={() => setActive(2)}
              className="grid grid-cols-3 gap-4 bg-white rounded-sm p-4"
            >
              <InputField label="Surgery Date" name="date" />
              <div className="flex  items-end gap-2">
                <Label label="Department">
                  <SearchableSelect
                    onChange={(value: string, options: any) => {
                      setFieldValue("department", value);
                      if (value !== "")
                        setSubDepartment(get(options, "subDepartments", []));
                    }}
                    options={surgeryDepartment}
                    value={values.department}
                  />
                </Label>
                <button
                  onClick={() => setDrawer({ open: true, of: "department" })}
                  className="size-sm p-2 rounded-full shadow-md drop-shadow-md cursor-pointer"
                >
                  <Icon icon="lucide:plus" className="size-3" />
                </button>
              </div>
              <div className="flex items-end gap-2">
                <Label label="Sub Department">
                  <SearchableSelect
                    onChange={(value: string, options: any) => {
                      setFieldValue("subDepartment", value);
                      setFieldValue("severity", get(options, "severity", ""));
                    }}
                    options={subDepartment}
                    value={values.subDepartment}
                  />
                </Label>
                <button
                  onClick={() => setDrawer({ open: true, of: "subDepartment" })}
                  className="size-sm p-2 rounded-full shadow-md drop-shadow-md cursor-pointer"
                >
                  <Icon icon="lucide:plus" className="size-3" />
                </button>
              </div>
              <InputField
                disabled
                type="text"
                label="Operation Type"
                name="severity"
              />
              <Label label="Assign OT">
                <SearchableSelect
                  onChange={(value: string, options: any) => {
                    setFieldValue("ot", value);
                    setFieldValue("floor", get(options, "floor", ""));
                  }}
                  options={theatres}
                  value={values.ot}
                />
              </Label>
              <InputField disabled type="text" label="Floor" name="floor" />
              <InputField
                disabled
                type="text"
                label="Surgery Duration"
                name="time"
              />
            </div>

            {/* Medical staff section */}
            <div
              onMouseOver={() => setActive(3)}
              className="grid grid-cols-3 gap-4 bg-white rounded-sm p-4"
            >
              <Label label="Surgeon/Specialist">
                <SearchableSelect
                  onChange={(value: string) => setFieldValue("surgeon", value)}
                  options={surgeonOptions}
                  value={values.surgeon}
                />
              </Label>
              <Label label="Assistant Surgeon">
                <SearchableSelect
                  onChange={(value: string) =>
                    setFieldValue("assistantSurgeon", value)
                  }
                  options={assistantSurgeonOptions}
                  value={values.assistantSurgeon}
                />
              </Label>
              <Label label="Anesthesist">
                <SearchableSelect
                  onChange={(value: string) =>
                    setFieldValue("anesthesist", value)
                  }
                  options={anesthesistOptions}
                  value={values.anesthesist}
                />
              </Label>
              <Label label="Peri Operating Nurse">
                <SearchableSelect
                  onChange={(value: string) =>
                    setFieldValue("periOperatingNurse", value)
                  }
                  options={periOperatingNurseOptions}
                  value={values.periOperatingNurse}
                />
              </Label>
              <Label label="Scrub Nurses">
                <SearchableSelect
                  onChange={(value: string) =>
                    setFieldValue("scrubNurses", value)
                  }
                  options={scrubNursesOptions}
                  value={values.scrubNurses}
                />
              </Label>
              <Label label="Circulatin Nurses">
                <SearchableSelect
                  onChange={(value: string) =>
                    setFieldValue("circulatingNurses", value)
                  }
                  options={circulatingNursesOptions}
                  value={values.circulatingNurses}
                />
              </Label>
            </div>

            {/* Blood Section */}
            <div className="gap-4 bg-white rounded-sm p-4">
              <h3 className="font-medium mb-2">Blood Requirements</h3>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="bloodInStock"
                  className="mr-2 h-4 w-4"
                  checked={values.bloodInStock}
                  onChange={(e) =>
                    setFieldValue("bloodInStock", e.target.checked)
                  }
                />
                <label htmlFor="bloodInStock" className="text-sm font-medium">
                  Is Blood in Stock?
                </label>
              </div>

              {values.bloodInStock && (
                <div className="grid grid-cols-2">
                  <div className="flex gap-4 w-full">
                    <Label label="Blood Group" className="w-full">
                      <SearchableSelect
                        className="w-full"
                        onChange={(value: string) =>
                          setFieldValue("bloodGroup", value)
                        }
                        options={bloodInventry}
                        value={values.bloodGroup}
                      />
                    </Label>
                    <InputField
                      inputClassName="max-w-20 w-full"
                      label="Blood Group"
                      type="number"
                      name="bloodUnits"
                      min="1"
                    />
                  </div>
                  <div className="flex items-center">
                    {getBloodUnitsByType(
                      bloodInventry as any,
                      values.bloodGroup
                    )?.map((item) => (
                      <div
                        key={item.type}
                        className="flex text-red items-center whitespace-nowrap"
                      >
                        <span>{`${item.type} : `}</span>
                        <span>{` ${item.unit} units`} </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Show blood alternatives if needed */}
              {alternative && values.bloodInStock && (
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <h4 className="text-sm font-medium text-yellow-800 mb-2">
                    <Icon
                      icon="mdi:alert-circle-outline"
                      className="inline mr-1"
                    />
                    Blood Alternative Options
                  </h4>
                  <div className="grid grid-cols-3 gap-2">
                    {get(bloodAlternative, "data.bloodAlternatives", []).map(
                      (alt: any, index: number) => (
                        <div
                          key={index}
                          className="p-2 bg-white border border-gray-200 rounded text-sm"
                        >
                          <div className="font-medium">{alt.bloodGroup}</div>
                          <div className="text-gray-600">
                            Available: {alt.quantityInUnits} units
                          </div>
                        </div>
                      )
                    )}
                  </div>
                </div>
              )}
            </div>
            <div className="gap-4 grid grid-cols-3 bg-white rounded-sm p-4">
              <GlobalForm {...formik} formDatails={checklist} />
            </div>
          </Form>
        </FormikProvider>
      </div>
      <Drawer
        isOpen={drawer.open}
        showCloseButton={false}
        onClose={() => setDrawer({ open: false, of: "" })}
      >
        {components[drawer.of]}
      </Drawer>
    </div>
  );
};

export default Operation;
