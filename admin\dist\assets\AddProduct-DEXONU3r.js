import{bW as S,bX as q,aV as v,aX as t,bY as w,a1 as N,a2 as a,bZ as f,a9 as C,aa as P,b_ as o}from"./index-ClX9RVH0.js";const _=({onClose:c,EditData:e})=>{var d,u,m;const{mutate:g}=S(),{mutate:x}=q(),i=["Equipment","Chemical","Consumable","Glassware","Plasticware","Safety Supplies","Instruments","Kits","Others"],y=v({category:t().required("Category is required"),name:t().required("Name is required"),department:t().required("Department is required"),description:t().required("Description is required")}),b=i.map(s=>({label:s,value:s})),{data:n,isSuccess:h}=w({upperHirachy:"6823163b7abfaa19e7374883"});console.log(n,"departmentsData");const j=[...h?(u=(d=n==null?void 0:n.data)==null?void 0:d.departments)==null?void 0:u.map(s=>({label:s==null?void 0:s.name,value:s==null?void 0:s._id})):[]],r=N({initialValues:{category:e?i.includes(e.category)?e.category:"Others":"",name:(e==null?void 0:e.name)||"",department:((m=e==null?void 0:e.department)==null?void 0:m._id)||"",description:(e==null?void 0:e.description)||""},validationSchema:y,onSubmit:async s=>{const p={...s,upperHirachy:"6823163b7abfaa19e7374883"};console.log("Submitted values:",p),e?await g({entityData:p,_id:e._id}):await x(p),l()}}),l=()=>{c==null||c()};return a.jsx(f,{onClose:l,heading:"Collect Sample",className:"max-w-screen-md w-full",children:a.jsx(C,{value:r,children:a.jsxs(P,{onSubmit:r.handleSubmit,className:"space-y-4",children:[a.jsx("div",{className:"grid grid-cols-2 gap-x-4 gap-y-2",children:a.jsx(o,{name:"category",label:"Product Category",type:"dropdown",placeholder:"Cateogry",options:b,formik:r})}),a.jsxs("div",{className:"px-5 py-4 border-2 rounded-xl grid grid-cols-1 gap-y-3",children:[a.jsx("div",{className:"grid grid-cols-2 gap-x-4 gap-y-2",children:a.jsx(o,{name:"name",label:"Product Name",type:"text",placeholder:"Enter Name",formik:r})}),a.jsx(o,{name:"department",label:"Usage Department",type:"dropdown",placeholder:"Select Usage Department",options:j,formik:r}),a.jsx(o,{name:"description",label:"Short Description",type:"textarea",placeholder:"Short Description",formik:r})]}),a.jsxs("div",{className:"pt-2 flex justify-end space-x-3",children:[a.jsx("button",{type:"button",onClick:l,className:"px-4 py-2 bg-gray-400 text-white rounded",children:"Cancel"}),a.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary text-white rounded",children:"Save"})]})]})})})};export{_ as A};
