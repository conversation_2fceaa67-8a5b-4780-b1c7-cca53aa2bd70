import { useParams } from "react-router-dom";
import { useMemo } from "react";
import { useGetUserById } from "../../../../../server-action/api/user";
import { useGetShiftAssign } from "../../../../../server-action/api/shiftAssignApi";
import { useGetAllAppointments } from "../../../../../server-action/api/appointmentApi";
import { HospitalLoader } from "../../../../Loader";
import dayjs from "dayjs";
import StaffHeader from "../components/StaffHeader";
import PersonalProfessionalDetails from "../components/PersonalProfessionalDetails";
import ContactInfo from "../components/ContactInfo";
import AboutSection from "../components/AboutSection";
import Documents from "../components/Documents";
import BankDetails from "../components/BankDetails";
import MonthlySchedule from "../components/MonthlySchedule";
import PatientReviews from "../components/PatientReviews";

const NurseDetailsPage = () => {
  const { id } = useParams();
  const { data: userData, isLoading } = useGetUserById(id as string);
  const { data: nurseShiftData } = useGetShiftAssign(
    id ? { user: id } : undefined,
    {
      enabled: Boolean(id),
      refetchOnMount: true,
      refetchOnWindowFocus: true,
    }
  );

  // Memoized today's shift details (shift name, start time, end time, department) with deduplication
  const todayShiftDetails = useMemo(() => {
    const today = dayjs().format("YYYY-MM-DD"); // 2025-07-07
    interface Shift {
      _id: string;
      shiftName: string;
      startTime: string;
      endTime: string;
    }

    interface ShiftItem {
      shift: Shift;
    }

    interface ShiftAssignment {
      date: string;
      shifts: ShiftItem[];
      department?: string;
    }

    interface ShiftAssignedEntry {
      shiftAssignment?: ShiftAssignment[];
    }

    interface TodayShift {
      id: string;
      shiftName: string;
      startTime: string;
      endTime: string;
      department: string;
    }

    const shiftAssignments: ShiftAssignedEntry[] =
      ((nurseShiftData as any)?.data?.shiftassigned as ShiftAssignedEntry[]) ||
      [];

    const todayShifts: TodayShift[] = shiftAssignments
      .flatMap(
        (entry: ShiftAssignedEntry) =>
          entry.shiftAssignment?.filter(
            (sa: ShiftAssignment) => sa.date === today && sa.shifts?.length > 0
          ) || []
      )
      .flatMap((sa: ShiftAssignment) =>
        sa.shifts.map((shiftItem: ShiftItem) => ({
          id: shiftItem.shift._id || `shift-${Date.now()}`,
          shiftName: shiftItem.shift.shiftName || "N/A",
          startTime: shiftItem.shift.startTime || "N/A",
          endTime: shiftItem.shift.endTime || "N/A",
          department: sa.department || "N/A",
        }))
      )
      .filter(
        (shift: TodayShift, index: number, self: TodayShift[]) =>
          index ===
          self.findIndex(
            (s) =>
              s.shiftName === shift.shiftName &&
              s.startTime === shift.startTime &&
              s.endTime === shift.endTime &&
              s.department === shift.department
          )
      );

    return todayShifts.length > 0
      ? todayShifts
      : [
          {
            id: "no-shifts",
            shiftName: "No shifts scheduled",
            startTime: "N/A",
            endTime: "N/A",
            department: "N/A",
          },
        ];
  }, [nurseShiftData]);

  const todayScheduleForMonthly = useMemo(() => {
    if (
      todayShiftDetails.length === 1 &&
      todayShiftDetails[0].shiftName === "No shifts scheduled"
    ) {
      return [
        {
          id: "no-shifts",
          time: "No shifts scheduled",
          appointmentCount: 0,
        },
      ];
    }

    return todayShiftDetails.map((shift: any) => ({
      id: shift.id,
      time: `${shift.startTime} - ${shift.endTime}`,
      appointmentCount: 0,
      shiftName: shift.shiftName,
      department: shift.department,
    }));
  }, [todayShiftDetails]);

  const nurseData = useMemo(() => {
    if (!userData) return null;
    return {
      id: userData?._id,
      name: userData.commonInfo?.personalInfo?.fullName || "N/A",
      role: "Nurse",
      specialization:
        userData.professionalDetails?.fieldOfStudy || "General Nursing",
      profileImage: userData.identityInformation?.profileImage || "",
      status: userData.isActive ? "Available" : "Unavailable",
      nurseId: `${userData?.nurseInformations?.nurseId}`,
      designation:
        userData.nurseInformations?.designation ||
        userData.professionalDetails?.designation ||
        "Staff Nurse",
    };
  }, [userData]);

  // Personal details
  const personalDetails = useMemo(() => {
    if (!userData) return null;
    return {
      fullName: userData.commonInfo?.personalInfo?.fullName || "N/A",
      gender: userData.commonInfo?.personalInfo?.gender || "N/A",
      dateOfBirth: userData.commonInfo?.personalInfo?.dob || "N/A",
      age: userData.commonInfo?.personalInfo?.dob
        ? dayjs().diff(dayjs(userData.commonInfo.personalInfo.dob), "year")
        : "N/A",
      maritalStatus: userData.commonInfo?.personalInfo?.maritalStatus || "N/A",
      bloodGroup: userData.commonInfo?.personalInfo?.bloodGroup || "N/A",
      religion: userData.commonInfo?.personalInfo?.religion || "N/A",
      language: userData.commonInfo?.personalInfo?.language || "N/A",
    };
  }, [userData]);

  // Professional details
  const professionalDetails = useMemo(() => {
    if (!userData) return null;
    return {
      nurseId: `N-${userData._id?.slice(-5)}`,
      department: userData.nurseInformations?.nurseDepartment || "N/A",
      designation:
        userData.nurseInformations?.designation ||
        userData.professionalDetails?.designation ||
        "Staff Nurse",
      yearsofExperience: userData.experienceDetails?.yearOfExperience || "N/A",
      licenseNumber:
        userData.professionalDetails?.medicalLicenseNumber || "N/A",
      joiningDate: userData.experienceDetails?.joinedDate || "N/A",
      shiftTiming: userData.employmentDetails?.shiftTiming || "N/A",
      qualification: (() => {
        const educationQualifications =
          userData.professionalDetails?.education
            ?.map((edu: any) => edu.qualification)
            .filter((qual: string) => qual && qual.trim() !== "")
            .filter(
              (qual: string, index: number, arr: string[]) =>
                arr.indexOf(qual) === index
            ) || [];
        const mainQualification =
          userData.professionalDetails?.qualificationLevel;
        const allQualifications: string[] = [];
        if (mainQualification && mainQualification.trim() !== "") {
          allQualifications.push(mainQualification);
        }
        educationQualifications.forEach((qual: string) => {
          if (!allQualifications.includes(qual)) {
            allQualifications.push(qual);
          }
        });
        return allQualifications.length > 0
          ? allQualifications.join(", ")
          : "N/A";
      })(),
      institution: userData.professionalDetails?.institution || "N/A",
      fieldOfStudy: (() => {
        const mainFieldOfStudy = userData.professionalDetails?.fieldOfStudy;
        const educationFields =
          userData.professionalDetails?.education
            ?.map((edu: any) => edu.fieldOfStudy)
            .filter((field: string) => field && field.trim() !== "")
            .filter(
              (field: string, index: number, arr: string[]) =>
                arr.indexOf(field) === index
            ) || [];
        const allFields: string[] = [];
        if (mainFieldOfStudy && mainFieldOfStudy.trim() !== "") {
          allFields.push(mainFieldOfStudy);
        }
        educationFields.forEach((field: string) => {
          if (!allFields.includes(field)) {
            allFields.push(field);
          }
        });
        return allFields.length > 0 ? allFields.join(", ") : "N/A";
      })(),
      employmentType: userData.professionalDetails?.employeeType || "N/A",
    };
  }, [userData]);

  // Contact info
  const contactInfo = useMemo(() => {
    if (!userData) return null;
    return {
      primaryPhone:
        userData.commonInfo?.contactInfo?.phone?.primaryPhone || "N/A",
      secondaryPhone:
        userData.commonInfo?.contactInfo?.phone?.secondaryPhone || "N/A",
      email: userData.email || "N/A",
      currentAddress:
        userData.commonInfo?.contactInfo?.address?.currentAddress || "N/A",
      permanentAddress:
        userData.commonInfo?.contactInfo?.address?.permanentAddress || "N/A",
    };
  }, [userData]);

  // About data
  const aboutData = useMemo(() => {
    if (!userData) return null;
    return {
      description:
        userData.commonInfo?.generalDescription || "No description available",
    };
  }, [userData]);

  // Bank details
  const bankDetails = useMemo(() => {
    if (!userData) return null;
    return {
      accountHolderName:
        userData?.professionalDetails?.accountDetails?.accountHolderName ||
        userData?.commonInfo?.personalInfo?.fullName ||
        "N/A",
      bankName: userData.professionalDetails?.accountDetails?.bankName || "N/A",
      accountNumber:
        userData.professionalDetails?.accountDetails?.accountNumber || "N/A",
      panNumber: userData.professionalDetails?.pan || "N/A",
      basicSalary: userData.professionalDetails?.salary || "N/A",
    };
  }, [userData]);

  // Documents
  const documents = useMemo(() => {
    if (!userData) return [];
    const allDocuments: any[] = [];
    if (userData.identityInformation?.identityDocuments) {
      userData.identityInformation.identityDocuments.forEach(
        (doc: any, index: number) => {
          if (doc.documentImages && doc.documentImages.length > 0) {
            doc.documentImages.forEach((image: string, imgIndex: number) => {
              if (image && image.trim() !== "") {
                allDocuments.push({
                  name: `${doc.documentType || "Identity Document"}${
                    doc.documentImages.length > 1 ? ` (${imgIndex + 1})` : ""
                  }`,
                  type: "identity",
                  hasView: Boolean(image),
                  hasDownload: Boolean(image),
                  url: image,
                  id: `${doc._id || doc.documentNumber}-${index}-${imgIndex}`,
                });
              }
            });
          }
        }
      );
    }
    if (userData.professionalDetails?.education) {
      userData.professionalDetails.education.forEach(
        (edu: any, eduIndex: number) => {
          const image = edu.certificate;
          if (image && image.trim() !== "") {
            allDocuments.push({
              name: `${edu.qualificationLevel || "Academic Document"}`,
              type: "academic",
              hasView: Boolean(image),
              hasDownload: Boolean(image),
              url: image,
              id: `edu-${eduIndex}`,
            });
          }
        }
      );
    }
    return allDocuments;
  }, [userData]);

  const reviewsData = useMemo(() => {
    if (!userData?.reviews || userData.reviews.length === 0) return [];
    return userData.reviews.map((review: any) => ({
      id: review._id,
      patientName: review.reviewerName,
      rating: review.reviewerRating,
      comment: review.reviewerComment,
      isRecommended: review.reviewerRating > 3,
    }));
  }, [userData?.reviews]);

  if (isLoading || !userData) {
    return <HospitalLoader />;
  }

  return (
    <div
      className="min-h-screen bg-[#EFF7F9] mb-2"
      style={{ fontFamily: "roboto" }}
    >
      <div className="mx-auto space-y-2">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-2">
          <div className="lg:col-span-3 flex flex-col gap-2">
            <StaffHeader
              staff={nurseData}
              contactInfo={contactInfo}
              staffType="Nurse"
            />
            <div className="grid grid-cols-3 gap-2">
              <div className="col-span-2">
                <PersonalProfessionalDetails
                  personalDetails={personalDetails}
                  professionalDetails={professionalDetails}
                  staffType="Nurse"
                />
              </div>
              <ContactInfo contactInfo={contactInfo} />
            </div>
            <AboutSection about={aboutData} />
            <div className="grid gap-2 grid-cols-2">
              <Documents documents={documents} />
              <BankDetails bankDetails={bankDetails} />
            </div>
          </div>
          <div className="flex flex-col gap-2">
            <MonthlySchedule
              todaySchedule={todayScheduleForMonthly}
              shiftData={nurseShiftData}
              staffId={id}
              staffData={userData}
              staffType="Nurse"
            />
            <PatientReviews
              reviews={reviewsData}
              staffType="Nurse"
              showEmptyMessage={true}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default NurseDetailsPage;
