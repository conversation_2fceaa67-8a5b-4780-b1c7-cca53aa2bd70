import{ba as b,a2 as s,a7 as f,a5 as u,at as C,b4 as S,b1 as F,a1 as V,b8 as k,am as N,a9 as B,aa as L,c7 as m,cd as A,aH as D,ac as g,ao as U}from"./index-ClX9RVH0.js";import{a as P,b as T,f as E,c as I,d as q,p as H}from"./vendorObj-Dry546GQ.js";const G=()=>{let{id:i}=b();return i=i==null?void 0:i.split("-")[1],s.jsxs("div",{children:[s.jsx(f,{title:"Add Vendor",hideHeader:!0,listTitle:i?"Edit Vendor":"Add Vendor"}),s.jsx(O,{})]})},O=()=>{let{id:i}=b();i=i==null?void 0:i.split("-")[1];const[p,r]=u.useState(1),{data:n}=C(i??""),{mutate:x,isPending:v,isSuccess:c}=S(),{mutate:h,isPending:j}=F(),e=V({initialValues:T(n),enableReinitialize:!0,validationSchema:P,onSubmit:a=>{let l=E(a);l=k(l),i?h({entityData:l,_id:i}):x(l)}});console.log("user",n);const y=I.map((a,l)=>({...a,isActive:l+1===p})),{handleSubmit:o}=e;return u.useEffect(()=>{c&&e.resetForm()},[c]),console.log("formikva",e.values),s.jsxs("div",{className:"grid grid-cols-1 gap-4 lg:grid-cols-3",children:[s.jsx("div",{children:s.jsx(N,{onStepClick:a=>r(a),steps:y})}),s.jsx("div",{className:" col-span-2",children:s.jsx(B,{value:e,children:s.jsxs(L,{onSubmit:o,className:"space-y-4",children:[Object.entries(q).map(([a,l],d)=>s.jsx("div",{onClick:()=>r(d+1),className:"grid grid-cols-1 gap-x-4 bg-white p-4 rounded-sm sm:grid-cols-2 md:grid-cols-3",children:s.jsx(m,{formDatails:l,...e})},a)),Object.entries(H).map(([a,l],d)=>s.jsxs("div",{onClick:()=>r(d+1),className:"grid grid-cols-1 gap-x-4 bg-white p-4 rounded-sm sm:grid-cols-2 md:grid-cols-3",children:[s.jsx(m,{formDatails:l,...e}),s.jsx(A,{required:!0,label:"Billing",children:s.jsx(D,{value:e.values.billingType,options:[{label:"Days",value:"days"},{label:"Bill by Bill",value:"bill"},{label:"By Credit",value:"credit"}],onChange:t=>{t==="bill"?e.setFieldValue("billingCycle",1):t==="credit"&&e.setFieldValue("billingCycle",0),e.setFieldValue("billingType",t)}})}),e.values.billingType==="days"&&s.jsxs("div",{children:[s.jsx(g,{label:"No of Days",required:!0,value:e.values.billingCycle,onChange:t=>e.setFieldValue("billingCycle",t.target.value)}),e.errors.billingCycle&&e.touched.billingCycle&&s.jsx("span",{className:"text-red text-sm",children:e.errors.billingCycle})]}),e.values.billingType==="credit"&&s.jsxs("div",{children:[s.jsx(g,{required:!0,label:"Credit limit (Rs)",onChange:t=>e.setFieldValue("creditLimit",t.target.value),value:e.values.creditLimit}),e.errors.creditLimit&&e.touched.creditLimit&&s.jsx("span",{className:"text-red text-sm",children:e.errors.creditLimit})]})]},a)),s.jsx(U,{isPending:v||j,onCancel:()=>history.back(),onSubmit:o})]})})})]})};export{O as BasicInformation,G as default};
