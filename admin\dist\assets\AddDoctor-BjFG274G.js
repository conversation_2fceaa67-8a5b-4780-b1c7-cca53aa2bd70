import{a5 as D,br as Ie,aU as je,bs as De,a2 as e,ab as P,an as M,ac as v,bw as F,dy as $,dO as Y,aY as ve,b$ as ge,dP as U,aV as R,aX as O,aW as Ne,bm as ye,bv as Ee,at as Se,a1 as Fe,b4 as Te,b1 as Oe,ba as Ce,cy as He,ad as Pe,a7 as we,bx as Ae,am as qe,a9 as $e,dQ as Re,ao as Me}from"./index-ClX9RVH0.js";import{E as Be}from"./ExtraDocuments-C1RiR-ux.js";const w=(t,s)=>s.split(".").reduce((r,y)=>r?r[y]:void 0,t),Le=({formik:t,fieldNamePath:s,setActiveStep:r,showSpecialization:y=!0})=>{const a=`${s}.department`,p=`${s}.subDepartment`,c=`${s}.specialization`,n=D.useMemo(()=>w(t.values,a),[t.values,a]),o=D.useMemo(()=>w(t.values,p),[t.values,p]),d=D.useMemo(()=>w(t.values,c),[t.values,c]),m=D.useCallback(()=>{const E=w(t.touched,a),I=w(t.errors,s);return E&&(I!=null&&I.department)?I.department:null},[t.touched,t.errors,a,s]),b=D.useCallback(()=>{const E=w(t.touched,p),I=w(t.errors,s);return E&&(I!=null&&I.subDepartment)?I.subDepartment:null},[t.touched,t.errors,p,s]),x=D.useCallback(()=>{const E=w(t.touched,c),I=w(t.errors,s);return E&&(I!=null&&I.specialization)?I.specialization:null},[t.touched,t.errors,c,s]),{data:f,isLoading:N}=Ie(),{data:l,isLoading:g}=je({upperHirachy:n},{enabled:!!n}),{data:i,isLoading:u}=De({upperHirachy:o},{enabled:!!o}),j=D.useMemo(()=>{var E,I;return((I=(E=f==null?void 0:f.data)==null?void 0:E.departmentCategory)==null?void 0:I.map(C=>({value:C._id,label:C.name})))||[]},[f]),S=D.useMemo(()=>{var E,I;return((I=(E=l==null?void 0:l.data)==null?void 0:E.departments)==null?void 0:I.map(C=>({value:C._id,label:C.name})))||[]},[l]),T=D.useMemo(()=>{var E,I;return((I=(E=i==null?void 0:i.data)==null?void 0:E.departmentSubCat)==null?void 0:I.map(C=>({value:C._id,label:C.name})))||[]},[i]),h=D.useCallback(E=>{const I=E.target.value;t.setFieldValue(a,I),t.setFieldValue(p,""),t.setFieldValue(c,"")},[t,a,p,c]),G=D.useCallback(E=>{const I=E.target.value;t.setFieldValue(p,I),t.setFieldValue(c,"")},[t,p,c]),z=D.useCallback(E=>{t.setFieldValue(c,E.target.value)},[t,c]),A=D.useCallback(()=>{r==null||r(1)},[r]),_=D.useMemo(()=>y?"grid grid-cols-1 md:grid-cols-3 gap-5":"grid grid-cols-1 md:grid-cols-2 gap-5",[y]),B=m(),L=b(),V=x();return e.jsxs("div",{className:_,children:[e.jsxs("div",{children:[e.jsx(P,{required:!0,label:"Department",firstInput:N?"Loading...":"Select",options:j,name:a,value:n||"",onChange:h,onFocus:A,disabled:N}),B&&e.jsx("div",{className:"text-red mt-2 text-sm",children:B})]}),e.jsxs("div",{children:[e.jsx(P,{label:"Sub-Department",firstInput:g?"Loading...":"Select",options:S,name:p,value:o||"",onChange:G,onFocus:A,disabled:!n||g}),L&&e.jsx("div",{className:"text-red mt-2 text-sm",children:L})]}),y&&e.jsxs("div",{children:[e.jsx(P,{label:"Specialization",firstInput:u?"Loading...":"Select",options:T,name:c,value:d||"",onChange:z,onFocus:A,disabled:!o||u}),V&&e.jsx("div",{className:"text-red mt-2 text-sm",children:V})]})]})},Ve=[{value:"IPD",label:"IPD"},{value:"OPD",label:"OPD"},{value:"BOTH",label:"IPD/OPD"},{value:"OT",label:"Operation Theater"},{value:"EMERGENCY",label:"EMERGENCY"}],Ue=({formik:t,setActiveStep:s,role:r})=>{const y=Le({formik:t,fieldNamePath:"personalInfo",setActiveStep:s,showSpecialization:!0});return e.jsx(M,{name:"personalInfo",children:()=>{var a,p,c,n,o,d,m,b,x,f,N,l,g,i,u,j,S,T;return e.jsx("div",{children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(v,{label:"Full Name",name:"personalInfo.fullName",type:"text",placeholder:"Enter Name",onFocus:()=>s(1)}),((a=t.touched.personalInfo)==null?void 0:a.fullName)&&typeof t.errors.personalInfo=="object"&&((p=t.errors.personalInfo)==null?void 0:p.fullName)&&e.jsx("div",{className:"text-red text-sm mt-2",children:(c=t.errors.personalInfo)==null?void 0:c.fullName})]}),e.jsxs("div",{children:[e.jsx(P,{label:"Gender",firstInput:"Select",options:[{value:"MALE",label:"Male"},{value:"FEMALE",label:"Female"},{value:"OTHER",label:"Other"}],name:"personalInfo.gender",value:t.values.personalInfo.gender,onChange:h=>{t.setFieldValue("personalInfo.gender",h.target.value)},onFocus:()=>s(1)}),((n=t.touched.personalInfo)==null?void 0:n.gender)&&typeof t.errors.personalInfo=="object"&&((o=t.errors.personalInfo)==null?void 0:o.gender)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(d=t.errors.personalInfo)==null?void 0:d.gender})]}),e.jsxs("div",{children:[e.jsx(P,{label:"Role",firstInput:"Select",options:[{value:F.DOCTOR,label:"Doctor"},{value:F.MEDICAL_OFFICER,label:"Medical Officer"},{value:F.CONSULTANT,label:"Consultant"},{value:F.PATHOLOGIST,label:"Pathologist"},{value:F.PHYSIOTHERAPIST,label:"Physiotherapist"},{value:F.SURGEON,label:"Surgeon"},{value:F.ANESTHESIOLOGIST,label:"Anesthesiologist"},{value:F.OPTOMETRIST,label:"Optometrist"},{value:F.DENTIST,label:"Dentist"},{value:F.PSYCHOLOGIST,label:"Psychologist"},{value:F.PHARMACIST_ASSISTANT,label:"Pharmacist Assistant"},{value:F.DIETICIAN,label:"Dietician"},{value:F.NURSE,label:"Nurse"},{value:F.PHARMACIST,label:"Pharmacist"},{value:F.LAB_TECHNICIAN,label:"Lab Technician"},{value:F.LAB_TECHNOLOGIST,label:"Lab Technologist"},{value:F.RADIOLOGIST,label:"Radiologists"},{value:F.MEDICAL_OFFICER,label:"Medical Officer"}],name:"personalInfo.role",value:t.values.personalInfo.role,onChange:h=>{t.setFieldValue("personalInfo.role",h.target.value)},onFocus:()=>s(1)}),((m=t.touched.personalInfo)==null?void 0:m.role)&&typeof t.errors.personalInfo=="object"&&((b=t.errors.personalInfo)==null?void 0:b.role)&&e.jsx("div",{className:"text-red mt-2",children:(x=t.errors.personalInfo)==null?void 0:x.role})]}),e.jsxs("div",{children:[e.jsx(P,{label:"Designation",firstInput:"Select",options:[{value:$.SENIOR,label:"Senior"},{value:$.HOD,label:"MD"},{value:$.INTERMEDIATE,label:"Intermediate"},{value:$.ASSISTANT,label:"Assistant"},{value:$.JUNIOR,label:"Junior"},{value:$.INTERN,label:"Intern"}],name:"personalInfo.designation",value:t.values.personalInfo.designation,onChange:h=>{t.setFieldValue("personalInfo.designation",h.target.value)},onFocus:()=>s(1)}),((f=t.touched.personalInfo)==null?void 0:f.designation)&&typeof t.errors.personalInfo=="object"&&((N=t.errors.personalInfo)==null?void 0:N.designation)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(l=t.errors.personalInfo)==null?void 0:l.designation})]}),r&&(r==="DOCTOR"||r==="NURSE")?e.jsxs("div",{children:[e.jsx(P,{label:"Type",firstInput:"Select Type",options:Ve,value:t.values.personalInfo.departmentName,name:"personalInfo.departmentName",onChange:h=>{t.setFieldValue("personalInfo.departmentName",h.target.value)},onFocus:()=>s(1)}),((g=t.touched.personalInfo)==null?void 0:g.departmentName)&&typeof t.errors.personalInfo=="object"&&((i=t.errors.personalInfo)==null?void 0:i.departmentName)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(u=t.errors.personalInfo)==null?void 0:u.departmentName})]}):e.jsx("div",{children:e.jsx(P,{label:"Marital Status",firstInput:"Select",options:[{value:"Married",label:"Married"},{value:"Unmarried",label:"Unmarried"},{value:"Others",label:"Others"}],value:t.values.personalInfo.maritalStatus,name:"personalInfo.maritalStatus",onChange:h=>{t.setFieldValue("personalInfo.maritalStatus",h.target.value)},onFocus:()=>s(1)})}),e.jsxs("div",{children:[e.jsx(v,{label:"Date of Birth",name:"personalInfo.dateOfBirth",type:"date",placeholder:"Select a date",onFocus:()=>s(1)}),((j=t.touched.personalInfo)==null?void 0:j.dateOfBirth)&&typeof t.errors.personalInfo=="object"&&((S=t.errors.personalInfo)==null?void 0:S.dateOfBirth)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(T=t.errors.personalInfo)==null?void 0:T.dateOfBirth})]}),e.jsx("div",{className:"col-span-3",children:y}),t.values.personalInfo.department&&e.jsx("div",{children:e.jsx(Y,{label:"Head of Department",value:t.values.personalInfo.hirachyFirstHOD,onChange:()=>{t.setFieldValue("personalInfo.hirachyFirstHOD",!t.values.personalInfo.hirachyFirstHOD)}})}),t.values.personalInfo.subDepartment&&e.jsx("div",{children:e.jsx(Y,{label:"Head of Sub Department",value:t.values.personalInfo.departmentHOD,onChange:()=>{t.setFieldValue("personalInfo.departmentHOD",!t.values.personalInfo.departmentHOD)}})}),t.values.personalInfo.specialization&&e.jsx("div",{children:e.jsx(Y,{label:"Head of Specialization",value:t.values.personalInfo.specialityHOD,onChange:()=>{t.setFieldValue("personalInfo.specialityHOD",!t.values.personalInfo.specialityHOD)}})}),r&&(r==="DOCTOR"||r==="NURSE")&&e.jsx("div",{children:e.jsx(P,{label:"Marital Status",firstInput:"Select",options:[{value:"Married",label:"Married"},{value:"Unmarried",label:"Unmarried"},{value:"Others",label:"Others"}],value:t.values.personalInfo.maritalStatus,name:"personalInfo.maritalStatus",onChange:h=>{t.setFieldValue("personalInfo.maritalStatus",h.target.value)},onFocus:()=>s(1)})}),e.jsxs("div",{className:"col-span-3",children:[e.jsx(ve,{label:"General Description",name:"generalDescription",placeholder:"Enter General Description"}),t.touched.generalDescription&&t.errors.generalDescription&&e.jsx("div",{className:"text-red text-sm mt-2",children:t.errors.generalDescription})]})]}),e.jsxs("div",{className:"mt-4  ",children:[e.jsx("label",{className:"block text-sm font-normal",children:"Upload Profile Image"}),e.jsx(ge,{name:"personalInfo.profilePicture",onChange:h=>{t.setFieldValue("personalInfo.profilePicture",h)},value:t.values.personalInfo.profilePicture})]})]})})}})},Ge=({values:t,setActiveStep:s,formik:r})=>e.jsx(M,{name:"professionalDetails",children:({push:y,remove:a})=>e.jsx("div",{children:t.professionalDetails.length>0&&t.professionalDetails.map((p,c)=>{var d,m,b,x;const n=typeof((d=r.errors.professionalDetails)==null?void 0:d[c])=="object"?(m=r.errors.professionalDetails)==null?void 0:m[c]:void 0,o=typeof((b=r.touched.professionalDetails)==null?void 0:b[c])=="object"?(x=r.touched.professionalDetails)==null?void 0:x[c]:void 0;return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(v,{label:"Institution",name:`professionalDetails.${c}.qualificationInstitution`,type:"text",placeholder:"Enter (i.e. Tribhuvan University)",onFocus:()=>s(3),onBlur:()=>{r.setFieldTouched(`professionalDetails.${c}.qualificationInstitution`,!0)}}),(o==null?void 0:o.qualificationInstitution)&&(n==null?void 0:n.qualificationInstitution)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(n.qualificationInstitution)})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Medical License Number",name:`professionalDetails.${c}.lisenceNo`,type:"text",placeholder:"Enter license number",onFocus:()=>s(3),onBlur:()=>{r.setFieldTouched(`professionalDetails.${c}.lisenceNo`,!0)}}),(o==null?void 0:o.lisenceNo)&&(n==null?void 0:n.lisenceNo)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(n.lisenceNo)})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Qualification Level",name:`professionalDetails.${c}.qualification`,type:"text",placeholder:"Enter (i.e. Doctor of medicine)",onFocus:()=>s(3),onBlur:()=>{r.setFieldTouched(`professionalDetails.${c}.qualification`,!0)}}),(o==null?void 0:o.qualification)&&(n==null?void 0:n.qualification)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(n.qualification)})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Field of Study",name:`professionalDetails.${c}.fieldOfStudy`,type:"text",placeholder:"Enter field of study",onFocus:()=>s(3),onBlur:()=>{r.setFieldTouched(`professionalDetails.${c}.fieldOfStudy`,!0)}}),(o==null?void 0:o.fieldOfStudy)&&(n==null?void 0:n.fieldOfStudy)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(n.fieldOfStudy)})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Start Date",name:`professionalDetails.${c}.startDate`,type:"date",onFocus:()=>s(3),onBlur:()=>{r.setFieldTouched(`professionalDetails.${c}.startDate`,!0)}}),(o==null?void 0:o.startDate)&&(n==null?void 0:n.startDate)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(n.startDate)})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Graduate Date",name:`professionalDetails.${c}.qualifiedDate`,type:"date",onFocus:()=>s(3),onBlur:()=>{r.setFieldTouched(`professionalDetails.${c}.qualifiedDate`,!0)}}),(o==null?void 0:o.qualifiedDate)&&(n==null?void 0:n.qualifiedDate)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(n.qualifiedDate)})]})]}),e.jsxs("div",{className:"",children:[e.jsx("label",{className:"block text-sm text-gray-800",children:"Upload Photo"}),e.jsx(ge,{name:`professionalDetails.${c}.documentImages`,multiple:!0,onChange:f=>{r.setFieldValue(`professionalDetails.${c}.documentImages`,f)},value:r.values.professionalDetails[c].documentImages}),(o==null?void 0:o.documentImages)&&(n==null?void 0:n.documentImages)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(n.documentImages)})]}),e.jsxs("div",{className:"flex justify-center gap-2 items-center w-full",children:[e.jsxs("button",{type:"button",className:"flex text-xl gap-2 border text-center bg-blue-500 py-2 px-4 rounded-md bg-primary text-white hover:text-primary duration-500 transition-all hover:bg-white hover:bg-blue-600",onClick:()=>y({lisenceNo:"",qualification:"",qualificationInstitution:"",fieldOfStudy:"",description:"",startDate:"",qualifiedDate:"",documentImages:null}),children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",children:e.jsxs("g",{fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd",children:[e.jsx("path",{d:"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m10-8a8 8 0 1 0 0 16a8 8 0 0 0 0-16"}),e.jsx("path",{d:"M13 7a1 1 0 1 0-2 0v4H7a1 1 0 1 0 0 2h4v4a1 1 0 1 0 2 0v-4h4a1 1 0 1 0 0-2h-4z"})]})}),e.jsx("div",{className:"text-base",children:"Add Qualification"})]}),t.professionalDetails.length>1&&e.jsxs("button",{type:"button",className:"flex justify-center items-center text-xl gap-2 border text-center bg-gray-600 pl-2 pr-4 py-2 text-white hover:text-textGray duration-500 transition-all hover:bg-white rounded-md hover:bg-blue-600",onClick:()=>a(c),children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 32 32",children:e.jsx("path",{fill:"currentColor",d:"M8 15h16v2H8z"})}),e.jsx("div",{className:"text-base",children:"Remove"})]})]})]},c)})})}),ze=({values:t,setActiveStep:s,formik:r})=>e.jsx(M,{name:"employmentDetails",children:()=>{var y,a,p,c,n,o,d,m,b,x;return e.jsx("div",{children:e.jsx("div",{className:"flex flex-col gap-8",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5",children:[e.jsx("div",{children:e.jsx(P,{label:"Employment Type",firstInput:"Select",options:[{value:"Full Time",label:"Full Time"},{value:"Part Time",label:"Part Time"},{value:"Contract",label:"Contract"},{value:"Temporary",label:"Temporary"}],name:"employmentDetails.employmentType",onChange:f=>{r.setFieldValue("employmentDetails.employmentType",f.target.value)},onFocus:()=>s(4)})}),e.jsxs("div",{children:[e.jsx(v,{label:"Years of experience",name:"employmentDetails.yearsOfExperience",type:"text",placeholder:"Enter (i.e. 2)",onFocus:()=>s(4),onBlur:()=>{r.setFieldTouched("employmentDetails.yearsOfExperience",!0)}}),((a=(y=r.touched)==null?void 0:y.employmentDetails)==null?void 0:a.yearsOfExperience)&&((c=(p=r.errors)==null?void 0:p.employmentDetails)==null?void 0:c.yearsOfExperience)&&e.jsx("div",{className:"text-red mt-2",children:String(r.errors.employmentDetails.yearsOfExperience)})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Joining Date",name:"employmentDetails.joiningDate",type:"date",placeholder:"Select",onFocus:()=>s(4)}),((n=r.touched.employmentDetails)==null?void 0:n.joiningDate)&&typeof r.errors.employmentDetails=="object"&&((o=r.errors.employmentDetails)==null?void 0:o.joiningDate)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(d=r.errors.employmentDetails)==null?void 0:d.joiningDate})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Consultation Fee",name:"employmentDetails.consultationFee",type:"text",placeholder:"Enter",onFocus:()=>s(4)}),((m=r.touched.employmentDetails)==null?void 0:m.consultationFee)&&typeof r.errors.employmentDetails=="object"&&((b=r.errors.employmentDetails)==null?void 0:b.consultationFee)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(x=r.errors.employmentDetails)==null?void 0:x.consultationFee})]})]})})})}}),_e=({setActiveStep:t,formik:s})=>e.jsx(M,{name:"contactInfo",children:()=>{var r,y,a,p,c,n,o,d,m,b,x,f,N,l,g;return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(v,{label:"Phone Number",name:"contactInfo.phoneNumber",type:"text",placeholder:"Enter Phone",onFocus:()=>t(2)}),((r=s.touched.contactInfo)==null?void 0:r.phoneNumber)&&typeof s.errors.contactInfo=="object"&&((y=s.errors.contactInfo)==null?void 0:y.phoneNumber)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(a=s.errors.contactInfo)==null?void 0:a.phoneNumber})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Alternate Phone Number",name:"contactInfo.alternatePhoneNumber",type:"text",placeholder:"Enter Phone",onFocus:()=>t(2)}),((p=s.touched.contactInfo)==null?void 0:p.alternatePhoneNumber)&&typeof s.errors.contactInfo=="object"&&((c=s.errors.contactInfo)==null?void 0:c.alternatePhoneNumber)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(n=s.errors.contactInfo)==null?void 0:n.alternatePhoneNumber})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Email Address",name:"contactInfo.emailAddress",type:"email",placeholder:"Enter Email",onFocus:()=>t(2)}),((o=s.touched.contactInfo)==null?void 0:o.emailAddress)&&typeof s.errors.contactInfo=="object"&&((d=s.errors.contactInfo)==null?void 0:d.emailAddress)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(m=s.errors.contactInfo)==null?void 0:m.emailAddress})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Permanent Address",name:"contactInfo.permanentAddress",type:"text",placeholder:"Enter Permanent Address",onFocus:()=>t(2)}),((b=s.touched.contactInfo)==null?void 0:b.permanentAddress)&&typeof s.errors.contactInfo=="object"&&((x=s.errors.contactInfo)==null?void 0:x.permanentAddress)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(f=s.errors.contactInfo)==null?void 0:f.permanentAddress})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Current Address",name:"contactInfo.currentAddress",type:"text",placeholder:"Enter current Address",onFocus:()=>t(2)}),((N=s.touched.contactInfo)==null?void 0:N.currentAddress)&&typeof s.errors.contactInfo=="object"&&((l=s.errors.contactInfo)==null?void 0:l.currentAddress)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(g=s.errors.contactInfo)==null?void 0:g.currentAddress})]})]})}}),Ye=({formik:t,setActiveStep:s})=>{const r=[{label:"Hourly",value:U.PERHOUR},{label:"Daily",value:U.PERDAY},{label:"Monthly",value:U.PERMONTH},{label:"Per Patient",value:U.PERPATIENT},{label:"Per Shift",value:U.PERSHIFT}];return e.jsx(M,{name:"bankDetails",children:()=>{var y,a,p,c,n,o,d,m,b,x,f;return e.jsx("div",{children:e.jsx("div",{className:"flex flex-col gap-8",children:e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5",children:[e.jsxs("div",{children:[e.jsx(v,{label:"Bank Name",name:"bankDetails.bankName",type:"text",placeholder:"Bank Name",onFocus:()=>s(6)}),((y=t.touched.bankDetails)==null?void 0:y.bankName)&&t.errors.bankDetails&&typeof t.errors.bankDetails!="string"&&((a=t.errors.bankDetails)==null?void 0:a.bankName)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(p=t.errors.bankDetails)==null?void 0:p.bankName})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Account No",name:"bankDetails.accountNo",type:"text",placeholder:"Account No",onFocus:()=>s(6)}),((c=t.touched.bankDetails)==null?void 0:c.accountNo)&&t.errors.bankDetails&&typeof t.errors.bankDetails!="string"&&((n=t.errors.bankDetails)==null?void 0:n.accountNo)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:t.errors.bankDetails.accountNo})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Account Holder Name",name:"bankDetails.accountHolderName",type:"text",placeholder:"Account holder name",onFocus:()=>s(6)}),((o=t.touched.bankDetails)==null?void 0:o.accountHolderName)&&t.errors.bankDetails&&typeof t.errors.bankDetails!="string"&&((d=t.errors.bankDetails)==null?void 0:d.accountHolderName)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:t.errors.bankDetails.accountHolderName})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Pan No",name:"bankDetails.panNo",type:"text",placeholder:"Pan No",onFocus:()=>s(6)}),((m=t.touched.bankDetails)==null?void 0:m.panNo)&&t.errors.bankDetails&&typeof t.errors.bankDetails!="string"&&((b=t.errors.bankDetails)==null?void 0:b.panNo)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:t.errors.bankDetails.panNo})]}),e.jsxs("div",{children:[e.jsx(P,{label:"Salary Type",name:"bankDetails.salaryType",options:r,value:t.values.bankDetails.salaryType,onFocus:()=>s(6),onChange:N=>{t.setFieldValue("bankDetails.salaryType",N.target.value)}}),((x=t.touched.bankDetails)==null?void 0:x.salaryType)&&t.errors.bankDetails&&typeof t.errors.bankDetails!="string"&&t.errors.bankDetails.salaryType&&e.jsx("div",{className:"text-red mt-2 text-sm",children:t.errors.bankDetails.salaryType})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Basic Salary",name:"bankDetails.basicSalary",type:"text",placeholder:"Basic Salary",onFocus:()=>s(6)}),((f=t.touched.bankDetails)==null?void 0:f.basicSalary)&&t.errors.bankDetails&&typeof t.errors.bankDetails!="string"&&t.errors.bankDetails.basicSalary&&e.jsx("div",{className:"text-red mt-2 text-sm",children:t.errors.bankDetails.basicSalary})]})]})})})}})},Je=()=>R().shape({personalInfo:R().shape({fullName:O(),gender:O(),department:O(),departmentName:O(),dateOfBirth:Ee(),role:O()}),contactInfo:R().shape({phoneNumber:O().matches(/^\d{10}$/,"Phone number must be 10 digits"),emailAddress:O().email("Invalid email address"),currentAddress:O()}),professionalDetails:Ne().of(R().shape({lisenceNo:O(),qualification:O(),qualificationInstitution:O(),fieldOfStudy:O()})),employmentDetails:R().shape({consultationFee:ye().typeError("Consultation fee must be a number").min(0,"Consultation fee must be a positive number")}),bankDetails:R().shape({basicSalary:ye().typeError("Salary  must be a number").min(0,"Salary must be a positive number")}),generalDescription:O().min(10,"General description must be at least 10 characters").max(1e3,"General description must not exceed 1000 characters")}),We=({values:t,setActiveStep:s,formik:r})=>e.jsx(M,{name:"workExperience",children:({push:y,remove:a})=>{var p;return e.jsx("div",{children:((p=t==null?void 0:t.workExperience)==null?void 0:p.length)>0&&t.workExperience.map((c,n)=>{var m,b,x,f,N,l;const o=typeof((m=r.errors.workExperience)==null?void 0:m[n])=="object"?(b=r.errors.workExperience)==null?void 0:b[n]:void 0,d=typeof((f=(x=r==null?void 0:r.touched)==null?void 0:x.workExperience)==null?void 0:f[n])=="object"?(l=(N=r==null?void 0:r.touched)==null?void 0:N.workExperience)==null?void 0:l[n]:void 0;return e.jsxs("div",{className:" space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx(v,{label:"Hospital Name",name:`workExperience.${n}.hospitalName`,type:"text",placeholder:"Enter (i.e. KMC)",onFocus:()=>s(5),onBlur:()=>{r.setFieldTouched(`workExperience.${n}.hospitalName`,!0)}}),(d==null?void 0:d.hospitalName)&&(o==null?void 0:o.hospitalName)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(o.hospitalName)})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Job Title",name:`workExperience.${n}.jobTitle`,type:"text",placeholder:"Enter Job Title",onFocus:()=>s(5),onBlur:()=>{r.setFieldTouched(`workExperience.${n}.jobTitle`,!0)}}),(d==null?void 0:d.jobTitle)&&(o==null?void 0:o.jobTitle)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(o.jobTitle)})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Job Type",name:`workExperience.${n}.jobType`,type:"text",placeholder:"Enter Job Type",onFocus:()=>s(5),onBlur:()=>{r.setFieldTouched(`workExperience.${n}.jobType`,!0)}}),(d==null?void 0:d.jobType)&&(o==null?void 0:o.jobType)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(o.jobType)})]}),e.jsxs("div",{children:[e.jsx(v,{label:"Start Date",name:`workExperience.${n}.tenureStartDate`,type:"date",onFocus:()=>s(5),onBlur:()=>{r.setFieldTouched(`workExperience.${n}.tenureStartDate`,!0)}}),(d==null?void 0:d.tenureStartDate)&&(o==null?void 0:o.tenureStartDate)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(o.tenureStartDate)})]}),e.jsxs("div",{children:[e.jsx(v,{label:"End Date",name:`workExperience.${n}.tenureEndDate`,type:"date",onFocus:()=>s(5),onBlur:()=>{r.setFieldTouched(`workExperience.${n}.tenureEndDate`,!0)}}),(d==null?void 0:d.tenureEndDate)&&(o==null?void 0:o.tenureEndDate)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(o.tenureEndDate)})]})]}),e.jsxs("div",{className:"flex gap-2 justify-center items-center w-full",children:[e.jsxs("button",{type:"button",className:"flex text-xl gap-2 border text-center bg-blue-500 py-2 px-4 rounded-md bg-primary text-white hover:text-primary duration-500 transition-all hover:bg-white hover:bg-blue-600",onClick:()=>y({hospitalName:"",jobTitle:"",jobType:"",tenureStartDate:"",tenureEndDate:""}),children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",children:e.jsxs("g",{fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd",children:[e.jsx("path",{d:"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m10-8a8 8 0 1 0 0 16a8 8 0 0 0 0-16"}),e.jsx("path",{d:"M13 7a1 1 0 1 0-2 0v4H7a1 1 0 1 0 0 2h4v4a1 1 0 1 0 2 0v-4h4a1 1 0 1 0 0-2h-4z"})]})}),e.jsx("div",{className:"text-base",children:"Add Work Experience"})]}),t.workExperience.length>1&&e.jsxs("button",{type:"button",className:"flex justify-center items-center text-xl gap-2 border text-center bg-gray-600 pl-2 pr-4 py-2 text-white hover:text-textGray duration-500 transition-all hover:bg-white rounded-md hover:bg-blue-600 ",onClick:()=>a(n),children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 32 32",children:e.jsx("path",{fill:"currentColor",d:"M8 15h16v2H8z"})}),e.jsx("div",{className:"text-base",children:"Remove"})]})]})]},n)})})}}),Ke=(t,s)=>{var c,n,o,d,m,b,x,f,N,l,g,i,u,j,S,T,h,G,z,A,_,B,L,V,E,I,C,W,K,Q,X,Z,k,ee,te,ae,ne,se,le,oe,re,ie,ce,de,pe,ue,me,he,be,xe,fe;const[r,y]=D.useState({personalInfo:!1,contactInfo:!1,professionalDetails:!1,employmentDetails:!1,bankDetails:!1}),{data:a}=Se(t??""),p=Fe({initialValues:{personalInfo:{fullName:((n=(c=a==null?void 0:a.commonInfo)==null?void 0:c.personalInfo)==null?void 0:n.fullName)??"",gender:((d=(o=a==null?void 0:a.commonInfo)==null?void 0:o.personalInfo)==null?void 0:d.gender)??"",role:(a==null?void 0:a.role)??s,departmentName:((m=a==null?void 0:a.commonInfo)==null?void 0:m.ipdOpd)??"BOTH",designation:((b=a==null?void 0:a.professionalDetails)==null?void 0:b.designation)??"",dateOfBirth:((f=(x=a==null?void 0:a.commonInfo)==null?void 0:x.personalInfo)==null?void 0:f.dob)??"",maritalStatus:((l=(N=a==null?void 0:a.commonInfo)==null?void 0:N.personalInfo)==null?void 0:l.maritalStatus)??"",department:((i=(g=a==null?void 0:a.departmentDetails)==null?void 0:g.hirachyFirst)==null?void 0:i._id)??"",subDepartment:((j=(u=a==null?void 0:a.departmentDetails)==null?void 0:u.department)==null?void 0:j._id)??"",specialization:((T=(S=a==null?void 0:a.departmentDetails)==null?void 0:S.speciality)==null?void 0:T._id)??"",profilePicture:((h=a==null?void 0:a.identityInformation)==null?void 0:h.profileImage)??"",hirachyFirstHOD:((G=a==null?void 0:a.departmentDetails)==null?void 0:G.hirachyFirstHOD)??!1,departmentHOD:((z=a==null?void 0:a.departmentDetails)==null?void 0:z.departmentHOD)??!1,specialityHOD:((A=a==null?void 0:a.departmentDetails)==null?void 0:A.specialityHOD)??!1},contactInfo:{phoneNumber:((L=(B=(_=a==null?void 0:a.commonInfo)==null?void 0:_.contactInfo)==null?void 0:B.phone)==null?void 0:L.primaryPhone)??"",alternatePhoneNumber:((I=(E=(V=a==null?void 0:a.commonInfo)==null?void 0:V.contactInfo)==null?void 0:E.phone)==null?void 0:I.secondaryPhone)??"",emailAddress:(a==null?void 0:a.email)??"",permanentAddress:((K=(W=(C=a==null?void 0:a.commonInfo)==null?void 0:C.contactInfo)==null?void 0:W.address)==null?void 0:K.permanentAddress)??"",currentAddress:((Z=(X=(Q=a==null?void 0:a.commonInfo)==null?void 0:Q.contactInfo)==null?void 0:X.address)==null?void 0:Z.currentAddress)??""},professionalDetails:((k=a==null?void 0:a.professionalDetails)==null?void 0:k.education)??[{lisenceNo:"",qualification:"",qualificationInstitution:"",fieldOfStudy:"",startDate:"",qualifiedDate:"",documentImages:""}],employmentDetails:{employmentType:((ee=a==null?void 0:a.professionalDetails)==null?void 0:ee.employeeType)??"",joiningDate:((te=a==null?void 0:a.experienceDetails)==null?void 0:te.joinedDate)??"",consultationFee:((ae=a==null?void 0:a.professionalDetails)==null?void 0:ae.consultationFee)??"",yearsOfExperience:((ne=a==null?void 0:a.experienceDetails)==null?void 0:ne.yearOfExperience)??""},bankDetails:{tdsApplicable:!1,bankName:((le=(se=a==null?void 0:a.professionalDetails)==null?void 0:se.accountDetails)==null?void 0:le.bankName)??"",accountNo:((re=(oe=a==null?void 0:a.professionalDetails)==null?void 0:oe.accountDetails)==null?void 0:re.accountNumber)??"",panNo:((ie=a==null?void 0:a.professionalDetails)==null?void 0:ie.pan)??"",basicSalary:((ce=a==null?void 0:a.professionalDetails)==null?void 0:ce.salary)??"",salaryType:(de=a==null?void 0:a.professionalDetails)==null?void 0:de.salaryType,accountHolderName:((ue=(pe=a==null?void 0:a.professionalDetails)==null?void 0:pe.accountDetails)==null?void 0:ue.accountHolderName)??""},identityInformation:((me=a==null?void 0:a.identityInformation)==null?void 0:me.identityDocuments)??[{documentType:"",documentNumber:"",documentImages:""}],workExperience:((be=(he=a==null?void 0:a.experienceDetails)==null?void 0:he.experience)==null?void 0:be.length)>0?(xe=a==null?void 0:a.experienceDetails)==null?void 0:xe.experience:[{hospitalName:"",jobTitle:"",jobType:"",tenureStartDate:"",tenureEndDate:""}],generalDescription:((fe=a==null?void 0:a.commonInfo)==null?void 0:fe.generalDescription)??""},validationSchema:Je,enableReinitialize:!0,onSubmit:()=>{}});return D.useEffect(()=>{if(p.errors){const H={...r};let q=!1;p.errors.personalInfo?(H.personalInfo=!0,q=!0):H.personalInfo=!1,p.errors.contactInfo?(H.contactInfo=!0,q=!0):H.contactInfo=!1,p.errors.professionalDetails?(H.professionalDetails=!0,q=!0):H.professionalDetails=!1,p.errors.employmentDetails?(H.employmentDetails=!0,q=!0):H.employmentDetails=!1,p.errors.bankDetails?(H.bankDetails=!0,q=!0):H.bankDetails=!1,q&&y(H)}},[p.errors]),{formik:p,sectionErrors:r,setSectionErrors:y}},Qe=(t,s,r)=>{const[y,a]=D.useState(!1),p=D.useRef(!1),{mutateAsync:c,isPending:n}=Te(),{mutateAsync:o,isPending:d}=Oe(),m=n||d,b=l=>{var j,S,T;const g=(j=l.professionalDetails)==null?void 0:j.map(h=>({qualification:h.qualification,qualificationInstitution:h.qualificationInstitution,startDate:h.startDate,qualifiedDate:h.qualifiedDate,fieldOfStudy:h.fieldOfStudy,lisenceNo:h.lisenceNo,documentImages:h.documentImages})),i=(S=l.identityInformation)==null?void 0:S.map(h=>({documentType:h.documentType,documentNumber:h.documentNumber,documentImages:h.documentImages})),u=(T=l.workExperience)==null?void 0:T.map(h=>({hospitalName:h.hospitalName,jobTitle:h.jobTitle,jobType:h.jobType,tenureStartDate:h.tenureStartDate,tenureEndDate:h.tenureEndDate}));return{email:l.contactInfo.emailAddress,role:l.personalInfo.role??F.DOCTOR,commonInfo:{generalDescription:l.generalDescription,ipdOpd:l.personalInfo.departmentName&&l.personalInfo.departmentName,personalInfo:{fullName:l.personalInfo.fullName,dob:l.personalInfo.dateOfBirth,gender:l.personalInfo.gender,maritalStatus:l.personalInfo.maritalStatus},contactInfo:{phone:{primaryPhone:l.contactInfo.phoneNumber,secondaryPhone:l.contactInfo.alternatePhoneNumber},address:{currentAddress:l.contactInfo.currentAddress,permanentAddress:l.contactInfo.permanentAddress}}},identityInformation:{profileImage:l.personalInfo.profilePicture,identityDocuments:i},professionalDetails:{employeeType:l.employmentDetails.employmentType,consultationFee:Number(l.employmentDetails.consultationFee),salary:l.bankDetails.basicSalary,salaryType:l.bankDetails.salaryType,education:g,designation:l.personalInfo.designation,pan:l.bankDetails.panNo,accountDetails:{bankName:l.bankDetails.bankName,accountNumber:l.bankDetails.accountNo,accountHolderName:l.bankDetails.accountHolderName}},experienceDetails:{yearOfExperience:l.employmentDetails.yearsOfExperience,joinedDate:l.employmentDetails.joiningDate,experience:u},departmentDetails:{hirachyFirst:l.personalInfo.department,hirachyFirstHOD:l.personalInfo.hirachyFirstHOD??!1,...l.personalInfo.subDepartment?{department:l.personalInfo.subDepartment,departmentHOD:l.personalInfo.departmentHOD??!1}:{},...l.personalInfo.specialization?{speciality:l.personalInfo.specialization,specialityHOD:l.personalInfo.specialityHOD??!1}:{}}}},x=(l,g="")=>{Object.keys(l).forEach(i=>{const u=g?`${g}.${i}`:i;typeof l[i]=="object"&&l[i]!==null?Array.isArray(l[i])?l[i].forEach((j,S)=>{typeof j=="object"?x(j,`${u}[${S}]`):t.setFieldTouched(`${u}[${S}]`,!0,!1)}):x(l[i],u):t.setFieldTouched(u,!0,!1)})},f=async l=>{if(y||p.current)return;a(!0),document.querySelectorAll('[data-error-highlighted="true"]').forEach(i=>{i.removeAttribute("data-error-highlighted"),i.style.backgroundColor="transparent"}),x(t.values),t.validateForm().then(i=>{Object.keys(i).length>0?(t.setErrors(i),setTimeout(()=>{document.querySelectorAll("section[data-section-step]").forEach(j=>{const S=j.getAttribute("data-section-step"),T=j.querySelector(`[data-section-content="${S}"]`);if(T&&T.clientHeight===0){const h=j.querySelector(`[data-section-header="${S}"]`);h&&h.click()}}),setTimeout(()=>{l(),a(!1)},300)},100)):(p.current=!0,N())}).catch(i=>{console.error("Form validation error:",i),a(!1),p.current=!1})},N=async()=>{try{const l=b(t.values);r?await o({_id:r,entityData:l}):await c(l),s(-1),setTimeout(()=>{a(!1),p.current=!1},1e3)}catch(l){console.error("Error submitting form:",l),a(!1),p.current=!1}};return{handleFormSubmit:f,isSubmitting:y,isPending:m}},Xe=(t,s,r,y,a)=>({scrollToFirstError:()=>{if(t)return;const c=Object.keys(s).map(n=>{const o=a.findIndex(d=>d.title==="Personal Information"?n==="personalInfo":d.title==="Contact Information"?n==="contactInfo":d.title==="Educational Background"?n==="professionalDetails":d.title==="Employment Details"?n==="employmentDetails":d.title==="Payment Details"?n==="bankDetails":!1);return{key:n,sectionIndex:o}}).filter(n=>n.sectionIndex!==-1);if(c.sort((n,o)=>n.sectionIndex-o.sectionIndex),c.length>0){const n=c[0],o=a[n.sectionIndex].step;y(o),setTimeout(()=>{const d=document.querySelector(`section[data-section-step="${o}"]`);if(d){const m=d.querySelector(`[data-section-content="${o}"]`);if(m&&m.clientHeight===0){const b=d.querySelector(`[data-section-header="${o}"]`);b&&b.click()}}setTimeout(()=>{var N;let m=null;const b=n.key;let x="";b==="personalInfo"?x='[name^="personalInfo."]':b==="contactInfo"?x='[name^="contactInfo."]':b==="professionalDetails"?x='[name^="professionalDetails."]':b==="employmentDetails"?x='[name^="employmentDetails."]':b==="bankDetails"&&(x='[name^="bankDetails."]');const f=document.querySelector(`section[data-section-step="${o}"]`);if(f&&x){const l=f.querySelectorAll(x);for(let g=0;g<l.length;g++){const i=l[g];if(i.getAttribute("name")){const j=(N=i.closest("div"))==null?void 0:N.querySelector(".text-red");if(j&&j.textContent){m=j;break}}}if(!m){const g=f.querySelectorAll(".text-red");g&&g.length>0&&(m=g[0])}}if(!m){const l=document.querySelectorAll(".text-red");l&&l.length>0&&(m=l[0])}m&&(m.setAttribute("data-error-highlighted","true"),setTimeout(()=>{m.scrollIntoView({behavior:"smooth",block:"center"}),m.style.transition="background-color 0.5s ease",m.style.backgroundColor="rgba(255, 0, 0, 0.1)",setTimeout(()=>{m.style.backgroundColor="transparent"},1500);const l=m.parentElement;if(l){const g=l.querySelector("input, select, textarea");g&&setTimeout(()=>{g.focus()},300)}},300))},300)},300)}}}),J=[{step:1,title:"Personal Information",component:Ue},{step:2,title:"Contact Information",component:_e},{step:3,title:"Educational Background",component:Ge},{step:4,title:"Employment Details",component:ze},{step:5,title:"Work Experience",component:We},{step:6,title:"Payment Details",component:Ye},{step:7,title:"Extra Document",component:Be}],et=()=>{const[t,s]=D.useState(1),{id:r}=Ce(),y=He(),a=Pe(),p=D.useRef(null),c=D.useMemo(()=>{var i,u;return(u=(i=y.pathname)==null?void 0:i.split("/")[1])==null?void 0:u.toUpperCase()},[y.pathname]),{formik:n,sectionErrors:o}=Ke(r,c),{handleFormSubmit:d,isPending:m}=Qe(n,a,r),{scrollToFirstError:b}=Xe(n.isValid,n.errors,o,s,J),x=D.useMemo(()=>J.map(i=>{let u="";switch(i.title){case"Personal Information":u="personalInfo";break;case"Contact Information":u="contactInfo";break;case"Educational Background":u="professionalDetails";break;case"Employment Details":u="employmentDetails";break;case"Work Experience":u="workExperience";break;case"Payment Details":u="bankDetails";break}return{step:i.step,title:i.title,isActive:t===i.step,hasError:u?o[u]:!1}}),[t,o]),f=D.useCallback(()=>{if(!p.current)return;const i=document.querySelectorAll("section[data-section-step]");for(let u=0;u<i.length;u++){const j=i[u],S=j.getBoundingClientRect();if(S.top<=250&&S.bottom>=150){const T=Number(j.getAttribute("data-section-step"));if(T&&T!==t){s(T);break}}}},[t]),N=D.useCallback(()=>{let i;return()=>{clearTimeout(i),i=setTimeout(f,16)}},[f]),l=D.useCallback(i=>{s(i),requestAnimationFrame(()=>{const u=document.querySelector(`section[data-section-step="${i}"]`);if(u){const j=u.querySelector(`[data-section-content="${i}"]`),S=u.querySelector(`[data-section-header="${i}"]`);j&&j.clientHeight===0&&S&&S.click(),u.scrollIntoView({behavior:"smooth",block:"start"})}})},[]);D.useEffect(()=>{const i=p.current;if(!i)return;const u=N();return i.addEventListener("scroll",u,{passive:!0}),()=>{i.removeEventListener("scroll",u)}},[N]);const g=D.useCallback(i=>{i.preventDefault(),d(b)},[d,b]);return e.jsxs("div",{children:[e.jsx(we,{listTitle:r?"Edit Medical Staff":"Add Medical Staff",hideHeader:!0}),e.jsx(Ae,{isLoading:m,subText:"Finalizing Request",text:"Almost Done..."}),e.jsxs("section",{className:"grid grid-cols-4 gap-4",children:[e.jsx("div",{children:e.jsx("div",{className:"bg-white h-[calc(100vh-84px)] rounded-md shadow-md dropshadow-md",children:e.jsx(qe,{steps:x,onStepClick:l})})}),e.jsx("div",{className:"w-full h-[calc(100vh-84px)] overflow-y-auto col-span-3",ref:p,children:e.jsx($e,{value:n,children:e.jsx("form",{onSubmit:g,children:e.jsxs("div",{className:" pb-4",children:[J.map(({step:i,title:u,component:j})=>e.jsx(Re,{title:u,stepNumber:i,activeStep:t,setActiveStep:s,children:e.jsx(j,{values:n.values,formik:n,setActiveStep:s,role:c})},i)),e.jsx(Me,{onCancel:()=>n.resetForm(),onSubmit:()=>d(b)})]})})})})]})]})};export{et as AddDoctor};
