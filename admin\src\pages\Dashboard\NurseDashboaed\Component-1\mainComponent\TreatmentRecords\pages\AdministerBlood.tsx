import { Icon } from "@iconify/react/dist/iconify.js";
import React from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { useFormik } from "formik";
import * as Yup from "yup";

// Data for the "Recent Blood Work" table
const bloodWorkTableData = {
  columns: [
    { title: "Blood Product", key: "bloodProduct" },
    { title: "Blood Group", key: "bloodGroup" },
    { title: "Volume", key: "volume" },
    { title: "Time", key: "time" },
    { title: "Bag Number", key: "bagNumber" },
    { title: "Crossmatch", key: "crossmatch" },
    { title: "Pre-Vitals", key: "preVitals" },
    { title: "Post-Vitals", key: "postVitals" },
    { title: "Remarks", key: "remarks" },
  ],
  rows: [
    {
      id: "1",
      bloodProduct: "Packed RBC",
      bloodGroup: "A+",
      volume: "300 ml",
      time: "9:20 PM",
      bagNumber: "B12345",
      crossmatch: "Compatible",
      preVitals: "T: 36.5°C BP: 120/80 HR: 80",
      postVitals: "T: 36.6°C BP: 118/76 HR: 82",
      remarks: "Well tolerated",
    },
    {
      id: "2",
      bloodProduct: "Platelets",
      bloodGroup: "B+",
      volume: "200 ml",
      time: "2:00 PM",
      bagNumber: "B67890",
      crossmatch: "Compatible",
      preVitals: "T: 37.0°C BP: 110/70 HR: 78",
      postVitals: "T: 37.1°C BP: 108/68 HR: 80",
      remarks: "Mild headache post-transfusion",
    },
    {
      id: "3",
      bloodProduct: "Fresh Frozen Plasma",
      bloodGroup: "O+",
      volume: "250 ml",
      time: "5:45 PM",
      bagNumber: "B54321",
      crossmatch: "Compatible",
      preVitals: "T: 36.7°C BP: 115/75 HR: 85",
      postVitals: "T: 36.8°C BP: 117/78 HR: 86",
      remarks: "No adverse reaction observed",
    },
    {
      id: "4",
      bloodProduct: "Whole Blood",
      bloodGroup: "AB+",
      volume: "450 ml",
      time: "11:30 AM",
      bagNumber: "B98765",
      crossmatch: "Compatible",
      preVitals: "T: 36.4°C BP: 125/85 HR: 90",
      postVitals: "T: 36.5°C BP: 122/80 HR: 88",
      remarks: "Good response, no issues",
    },
  ],
};

const AdministerBlood: React.FC = () => {
  const initialValues = {
    bloodProduct: "",
    bloodGroup: "",
    volume: "",
    bagNumber: "",
    crossmatch: "",
    time: "",
    tempPre: "",
    bpPre: "",
    pulsePre: "",
    tempPost: "",
    bpPost: "",
    pulsePost: "",
    status: "",
    remarks: "",
    reaction: "",
  };

  const bloodAdministrationValidationSchema = Yup.object({
    bloodProduct: Yup.string().required("Blood Product is required"),
    bloodGroup: Yup.string().required("Blood Group is required"),
    volume: Yup.string().required("Volume is required"),
    bagNumber: Yup.string().required("Bag Number is required"),
    crossmatch: Yup.string().required("Crossmatch status is required"),
    time: Yup.string().required("Time is required"),
    tempPre: Yup.string().required("Pre-transfusion Temperature is required"),
    bpPre: Yup.string().required("Pre-transfusion BP is required"),
    pulsePre: Yup.string().required("Pre-transfusion Pulse is required"),
    tempPost: Yup.string().required("Post-transfusion Temperature is required"),
    bpPost: Yup.string().required("Post-transfusion BP is required"),
    pulsePost: Yup.string().required("Post-transfusion Pulse is required"),
    status: Yup.string().required("Status is required"),
    remarks: Yup.string(),
    reaction: Yup.string(),
  });

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: bloodAdministrationValidationSchema,
    onSubmit: (values, { resetForm }) => {
      console.log("Blood Administration Form submitted", values);
      resetForm();
    },
  });

  return (
    <div className='max-w-full p-4 mx-auto'>
      <h2 className='mb-4 text-lg font-semibold text-center'>
        Administer Blood
      </h2>

      <form
        onSubmit={formik.handleSubmit}
        className='w-full p-4 mb-8 space-y-4 border rounded-md'
      >
        {/* First Row: Blood Product, Blood Group, Volume */}
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Blood Product */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='bloodProduct' className='whitespace-nowrap'>
                Blood Product
              </label>
              <select
                id='bloodProduct'
                name='bloodProduct'
                className='w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.bloodProduct}
              >
                <option value='' className='text-[#3a3a3a] '>
                  Blood Product
                </option>
                <option value='Packed RBC'>Packed RBC</option>
                <option value='Plasma'>Plasma</option>
                <option value='Platelets'>Platelets</option>
                <option value='Cryoprecipitate'>Cryoprecipitate</option>
                <option value='Whole Blood'>Whole Blood</option>
              </select>
            </div>
            {formik.touched.bloodProduct && formik.errors.bloodProduct && (
              <p className='text-xs text-red-500'>
                {formik.errors.bloodProduct}
              </p>
            )}
          </div>

          {/* Blood Group */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='bloodGroup' className='whitespace-nowrap'>
                Blood Group
              </label>
              <select
                id='bloodGroup'
                name='bloodGroup'
                className='w-full p-2 border text-[#3a3a3a]  border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.bloodGroup}
              >
                <option value='' className='text-[#3a3a3a] '>
                  Select Blood Group
                </option>
                <option value='A+'>A+</option>
                <option value='A-'>A-</option>
                <option value='B+'>B+</option>
                <option value='B-'>B-</option>
                <option value='AB+'>AB+</option>
                <option value='AB-'>AB-</option>
                <option value='O+'>O+</option>
                <option value='O-'>O-</option>
              </select>
            </div>
            {formik.touched.bloodGroup && formik.errors.bloodGroup && (
              <p className='text-xs text-red-500'>{formik.errors.bloodGroup}</p>
            )}
          </div>

          {/* Volume */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='volume' className='whitespace-nowrap'>
                Volume
              </label>
              <input
                type='text'
                id='volume'
                name='volume'
                placeholder='Given Volume'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.volume}
              />
            </div>
            {formik.touched.volume && formik.errors.volume && (
              <p className='text-xs text-red-500'>{formik.errors.volume}</p>
            )}
          </div>
        </div>

        {/* Second Row: Bag Number, Crossmatch, Time */}
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Bag Number */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='bagNumber' className='whitespace-nowrap'>
                Bag Number
              </label>
              <input
                type='text'
                id='bagNumber'
                name='bagNumber'
                placeholder='Donor / Bag Number'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.bagNumber}
              />
            </div>
            {formik.touched.bagNumber && formik.errors.bagNumber && (
              <p className='text-xs text-red-500'>{formik.errors.bagNumber}</p>
            )}
          </div>

          {/* Crossmatch */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='crossmatch' className='whitespace-nowrap'>
                Crossmatch
              </label>
              <select
                id='crossmatch'
                name='crossmatch'
                className='w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.crossmatch}
              >
                <option value='' className='text-[#3a3a3a] '>
                  Status
                </option>
                <option value='Compatible'>Compatible</option>
                <option value='Incompatible'>Incompatible</option>
                <option value='Pending'>Pending</option>
              </select>
            </div>
            {formik.touched.crossmatch && formik.errors.crossmatch && (
              <p className='text-xs text-red-500'>{formik.errors.crossmatch}</p>
            )}
          </div>

          {/* Time */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='time' className='whitespace-nowrap'>
                Time
              </label>
              <input
                type='time'
                id='time'
                name='time'
                placeholder='Given Time'
                className='w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.time}
              />
            </div>
            {formik.touched.time && formik.errors.time && (
              <p className='text-xs text-red-500'>{formik.errors.time}</p>
            )}
          </div>
        </div>

        {/* Pre-transfusion Vitals Section */}
        <h3 className='col-span-12 mt-4 font-semibold text-center text-gray-700 underline text-md'>
          Pre-transfusion Vitals
        </h3>
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Temp (Pre) */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='tempPre' className='whitespace-nowrap'>
                Temp
              </label>
              <input
                type='text'
                id='tempPre'
                name='tempPre'
                placeholder='Temperature'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.tempPre}
              />
            </div>
            {formik.touched.tempPre && formik.errors.tempPre && (
              <p className='text-xs text-red-500'>{formik.errors.tempPre}</p>
            )}
          </div>

          {/* BP (Pre) */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='bpPre' className='whitespace-nowrap'>
                BP
              </label>
              <input
                type='text'
                id='bpPre'
                name='bpPre'
                placeholder='Blood Pressure'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.bpPre}
              />
            </div>
            {formik.touched.bpPre && formik.errors.bpPre && (
              <p className='text-xs text-red-500'>{formik.errors.bpPre}</p>
            )}
          </div>

          {/* Pulse (Pre) */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='pulsePre' className='whitespace-nowrap'>
                Pulse
              </label>
              <input
                type='text'
                id='pulsePre'
                name='pulsePre'
                placeholder='Heart Rate'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.pulsePre}
              />
            </div>
            {formik.touched.pulsePre && formik.errors.pulsePre && (
              <p className='text-xs text-red-500'>{formik.errors.pulsePre}</p>
            )}
          </div>
        </div>

        {/* Post-transfusion Vitals Section */}
        <h3 className='col-span-12 mt-4 font-semibold text-center text-gray-700 underline text-md'>
          Post-transfusion Vitals
        </h3>
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Temp (Post) */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='tempPost' className='whitespace-nowrap'>
                Temp
              </label>
              <input
                type='text'
                id='tempPost'
                name='tempPost'
                placeholder='Temperature'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.tempPost}
              />
            </div>
            {formik.touched.tempPost && formik.errors.tempPost && (
              <p className='text-xs text-red-500'>{formik.errors.tempPost}</p>
            )}
          </div>

          {/* BP (Post) */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='bpPost' className='whitespace-nowrap'>
                BP
              </label>
              <input
                type='text'
                id='bpPost'
                name='bpPost'
                placeholder='Blood Pressure'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.bpPost}
              />
            </div>
            {formik.touched.bpPost && formik.errors.bpPost && (
              <p className='text-xs text-red-500'>{formik.errors.bpPost}</p>
            )}
          </div>

          {/* Pulse (Post) */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='pulsePost' className='whitespace-nowrap'>
                Pulse
              </label>
              <input
                type='text'
                id='pulsePost'
                name='pulsePost'
                placeholder='Heart Rate'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.pulsePost}
              />
            </div>
            {formik.touched.pulsePost && formik.errors.pulsePost && (
              <p className='text-xs text-red-500'>{formik.errors.pulsePost}</p>
            )}
          </div>
        </div>

        {/* Final Row: Status, Remarks, Reaction, Save Button */}
        <div className='grid items-end grid-cols-12 gap-4'>
          {/* Status */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4'>
            <div className='flex items-center gap-2'>
              <label htmlFor='status' className='whitespace-nowrap'>
                Status
              </label>
              <select
                id='status'
                name='status'
                className='w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.status}
              >
                <option value='' className='text-[#3a3a3a]'>
                  Status
                </option>
                <option value='Complete'>Complete</option>
                <option value='In Progress'>In Progress</option>
                <option value='Aborted'>Aborted</option>
              </select>
            </div>
            {formik.touched.status && formik.errors.status && (
              <p className='text-xs text-red-500'>{formik.errors.status}</p>
            )}
          </div>

          {/* Reaction */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-3'>
            <label htmlFor='reaction' className=' whitespace-nowrap'>
              Reaction
            </label>
            <input
              id='reaction'
              name='reaction'
              placeholder='Reaction Observed'
              // rows={2}
              className='w-full p-2 border border-gray-300 rounded resize-none focus:outline-none focus:border-blue-400'
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.reaction}
            />
            {formik.touched.reaction && formik.errors.reaction && (
              <p className='text-xs text-red-500'>{formik.errors.reaction}</p>
            )}
          </div>

          {/* Remarks */}
          <div className='flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-5'>
            <label htmlFor='remarks' className='whitespace-nowrap'>
              Remarks
            </label>
            <textarea
              id='remarks'
              name='remarks'
              placeholder='Remarks'
              rows={2}
              className='w-full p-2 border border-gray-300 rounded resize-none focus:outline-none focus:border-blue-400'
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.remarks}
            />
            {formik.touched.remarks && formik.errors.remarks && (
              <p className='text-xs text-red-500'>{formik.errors.remarks}</p>
            )}
          </div>

          {/* Save Button */}
          <div className='flex items-end justify-end col-span-12'>
            <button
              type='submit'
              className='flex items-center justify-center px-6 py-2 gap-3 text-white text-md bg-[#116aef] rounded-lg hover:bg-blue-700 h-fit'
            >
              <Icon
                icon='fa6-solid:floppy-disk'
                width='18'
                height='18'
                color='white'
              />
              Save
            </button>
          </div>
        </div>
      </form>

      {/* Recent Blood Work Table */}
      <h2 className='mb-4 text-lg font-semibold text-center'>
        Recent Blood Work
      </h2>
      <div className='overflow-x-auto'>
        <MasterTable
          columns={bloodWorkTableData.columns}
          rows={bloodWorkTableData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default AdministerBlood;
