import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";

interface Document {
  name: string;
  type?: "identity" | "education" | "medical" | "other";
  hasView: boolean;
  hasDownload: boolean;
  url?: string;
  id?: string;
  details?: {
    degree?: string;
    institution?: string;
    year?: string | number;
    grade?: string;
  };
}

interface DocumentsProps {
  documents: Document[];
}

const Documents: React.FC<DocumentsProps> = ({ documents }) => {
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(
    null
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleView = (document: Document) => {
    setSelectedDocument(document);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedDocument(null);
  };

  useEffect(() => {
    if (isModalOpen) {
      const originalStyle = window.getComputedStyle(document.body).overflow;
      document.body.style.overflow = "hidden";

      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === "Escape") {
          closeModal();
        }
      };

      document.addEventListener("keydown", handleKeyDown);

      return () => {
        document.body.style.overflow = originalStyle;
        document.removeEventListener("keydown", handleKeyDown);
      };
    }
  }, [isModalOpen]);

  // Handle download document
  const handleDownload = (doc: Document) => {
    if (doc.url) {
      const link = document.createElement("a");
      link.href = doc.url;
      link.download = doc.name;
      link.click();
    } else {
      alert(`Download not available for: ${doc.name}`);
    }
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        {/* Header */}
        <div className="bg-blue-50 px-4 py-2 border-b">
          <h3 className="text-base font-semibold text-blue">Documents</h3>
        </div>

        <div className="p-4">
          {documents.length > 0 ? (
            <div className="space-y-1">
              {documents.map((document, index) => (
                <div
                  key={document.id || index}
                  className="flex items-center justify-between py-2 px-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors group"
                  onClick={() => {
                    if (document.hasView && document.url) {
                      setSelectedDocument(document);
                      setIsModalOpen(true);
                    }
                  }}
                >
                  <div className="flex items-center gap-3">
                    {/* <Icon
                      icon={
                        document.type === "identity"
                          ? "ph:identification-card"
                          : document.type === "education"
                          ? "ph:graduation-cap"
                          : document.type === "medical"
                          ? "ph:first-aid-kit"
                          : "ph:file"
                      }
                      className="w-4 h-4 text-blue"
                    /> */}
                    <span className="text-sm font-medium text-gray-900">
                      {document.name}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {document.hasView && document.url && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedDocument(document);
                          setIsModalOpen(true);
                        }}
                        className="bg-blue text-white px-3 py-1 rounded text-xs font-medium hover:bg-blue-600 transition-colors  group-hover:opacity-100"
                      >
                        View
                      </button>
                    )}
                    {document.hasDownload && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownload(document);
                        }}
                        className="bg-gray-100 text-gray-700 px-3 py-1 rounded text-xs font-medium hover:bg-gray-200 border transition-colors "
                      >
                        Download
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-2">
                <Icon icon="ph:file-x" className="w-12 h-12 mx-auto" />
              </div>
              <p className="text-gray-500 text-sm">No documents available</p>
            </div>
          )}
        </div>
      </div>

      {isModalOpen && selectedDocument && (
        <div className="fixed inset-0  flex items-center justify-center p-4">
          <div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
            onClick={closeModal}
          ></div>

          {/* Modal Content */}
          <div className="relative bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden animate-in zoom-in-95 duration-200">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue/20 rounded-xl flex items-center justify-center">
                  <Icon
                    icon={
                      selectedDocument.type === "education"
                        ? "ph:graduation-cap"
                        : "ph:identification-card"
                    }
                    className="w-5 h-5 text-blue"
                  />
                </div>
                <div>
                  <h3 className="text-base font-semibold text-gray-900">
                    {selectedDocument?.name || "Document"}
                  </h3>
                  <p className="text-xs text-gray-500 capitalize">
                    {selectedDocument?.type || "other"} Document
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={closeModal}
                  className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-100 text-gray-500 hover:text-gray-700 transition-colors"
                >
                  <Icon icon="ph:x" className="w-5 h-5 text-red" />
                </button>
              </div>
            </div>

            {/* Modal Body */}
            <div className="p-2 max-h-[calc(90vh-120px)] overflow-auto">
              {selectedDocument?.url ? (
                <div className="flex items-center justify-center bg-gray-50 rounded-xl p-8 min-h-[300px]">
                  {selectedDocument.url.startsWith("data:image/") ? (
                    <img
                      src={selectedDocument.url}
                      alt={selectedDocument.name}
                      className="max-w-full max-h-[60vh] object-contain rounded-lg shadow-lg"
                    />
                  ) : (
                    <iframe
                      src={selectedDocument.url}
                      className="w-full h-[60vh] border-0 rounded-lg shadow-lg"
                      title={selectedDocument.name}
                    />
                  )}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-gray-500">
                  <Icon icon="ph:file-x" className="w-16 h-16 mb-4" />
                  <p className="text-lg font-medium">Document not available</p>
                  <p className="text-sm">
                    The document content could not be loaded.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Documents;
