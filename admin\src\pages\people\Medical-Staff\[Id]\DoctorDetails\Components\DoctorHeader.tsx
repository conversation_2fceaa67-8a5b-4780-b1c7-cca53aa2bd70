import React from "react";
import { Icon } from "@iconify/react";
import { useNavigate, useParams } from "react-router-dom";
import { FrontendRoutes } from "../../../../../../routes";

interface DoctorHeaderProps {
  doctor: {
    name: string;
    specialization: string;
    doctorId: string;
    image: string;
    isAvailable: boolean;
    phoneNumber: string;
    email: string;
  };
  contactInfo?: {
    phoneNumber: string;
    email: string;
  };
}

const DoctorHeader: React.FC<DoctorHeaderProps> = ({ doctor, contactInfo }) => {
  const navigate = useNavigate();
  const { id } = useParams();

  const handleEditProfile = () => {
    navigate(`${FrontendRoutes.ADDDOCTOR}/${id}`);
  };

  const handleCall = () => {
    if (contactInfo?.phoneNumber && contactInfo.phoneNumber !== "N/A") {
      const whatsappUrl = `https://wa.me/${contactInfo.phoneNumber.replace(
        /[^0-9]/g,
        ""
      )}`;
      window.open(whatsappUrl, "_blank");
    } else {
      alert("Phone number not available");
    }
  };

  const handleEmail = () => {
    if (contactInfo?.email && contactInfo.email !== "N/A") {
      const gmailUrl = `https://mail.google.com/mail/?view=cm&to=${contactInfo.email}`;
      window.open(gmailUrl, "_blank");
    } else {
      alert("Email address not available");
    }
  };
  return (
    <div className="bg-white rounded-lg p-4 shadow-sm border">
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3">
          <div className="relative">
            <img
              src={doctor.image}
              alt={doctor.name}
              className="w-16 h-16 rounded-md object-cover"
            />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h1 className="text-base font-semibold text-gray-900">
                {doctor.name}
              </h1>
              {doctor.isAvailable && (
                <div className="flex items-center gap-1 bg-green/10 text-green border px-2 py-0.5 rounded text-xs">
                  <div className="w-1.5 h-1.5 bg-green  rounded-full"></div>
                  Available
                </div>
              )}
            </div>
            <p className="text-gray-600 text-xs">{doctor.specialization}</p>
            <p className="text-gray-500 text-xs">
              Doctor ID: {doctor.doctorId}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={handleCall}
            className="p-2 text-gray-600 hover:bg-gray-200 bg-gray-100 rounded"
            title="Call via WhatsApp"
          >
            <Icon icon="material-symbols:call" className="w-4 h-4" />
          </button>
          <button
            onClick={handleEmail}
            className="p-2 text-gray-600 hover:bg-gray-200 bg-gray-100 rounded"
            title="Send Email via Gmail"
          >
            <Icon icon="material-symbols:mail-outline" className="w-4 h-4" />
          </button>
          <button
            onClick={handleEditProfile}
            className="bg-blue text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-blue-700"
          >
            Edit Profile
          </button>
        </div>
      </div>
    </div>
  );
};

export default DoctorHeader;
