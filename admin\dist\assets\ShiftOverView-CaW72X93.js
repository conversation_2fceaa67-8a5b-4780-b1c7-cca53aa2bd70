import{a2 as e,b3 as r,bj as m,aj as x,af as a}from"./index-ClX9RVH0.js";const h=()=>{const l=[{icon:"fluent:drop-20-filled",title:"Blood Collection from bed number 203",time:"10:00 AM",type:"blood"},{icon:"fluent:stethoscope-20-filled",title:"Regular Vitals Checkup for bed number 205",time:"2:30 PM",type:"vitals"},{icon:"mdi:medication",title:"Medication Reminder for bed number 210",time:"4:00 PM",type:"medication"},{icon:"mdi:water-outline",title:"IV Fluid Replacement for bed number 198",time:"5:15 PM",type:"iv"},{icon:"mdi:food",title:"Meal Delivery for bed number 215",time:"12:00 PM",type:"meal"},{icon:"mdi:bed-clock",title:"Patient Rest Evaluation for bed number 220",time:"9:30 PM",type:"rest"}];return e.jsxs("div",{className:"w-full bg-[#E0E0E0] rounded-lg shadow-md border border-gray-200",children:[e.jsx("div",{className:"flex items-center justify-between p-4 mb-4 bg-white rounded-t-lg",children:e.jsx("h3",{className:"text-lg font-semibold text-black",children:"Important Notes/Alerts"})}),e.jsx("div",{className:"space-y-3 overflow-y-auto max-h-[280px] pr-2",children:l.map((s,n)=>e.jsxs("div",{className:"flex items-start gap-3 p-2 transition-colors rounded-md hover:bg-white",children:[e.jsx("div",{className:`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center ${s.type==="blood"?"bg-red-100":"bg-blue-100"}`,children:e.jsx(r,{icon:s.icon,width:"28",height:"28",className:s.type==="blood"?"text-rose-600":"text-sky-600"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:"text-sm font-medium leading-tight text-gray-900",children:s.title}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:s.time})]})]},n))})]})},b=()=>{const l=[{day:"Sunday",time:"Morning",ward:"ICU",patients:6,nurse:"Nurse A. Sharma"},{day:"Monday",time:"Night",ward:"General Ward",patients:12,nurse:"Nurse R. Rai"},{day:"Tuesday",time:"Morning",ward:"Emergency",patients:4,nurse:"Nurse B. Gurung"},{day:"Wednesday",time:"Afternoon",ward:"Pediatrics",patients:8,nurse:"Nurse M. Lama"},{day:"Thursday",time:"Morning",ward:"ICU",patients:7,nurse:"Nurse K. Tamang"},{day:"Friday",time:"Night",ward:"General Ward",patients:11,nurse:"Nurse D. Thapa"},{day:"Saturday",time:"Morning",ward:"Maternity",patients:5,nurse:"Nurse S. Magar"}],s=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],n=[{label:"Morning",hours:"7:00 AM - 3:00 PM"},{label:"Afternoon",hours:"3:00 PM - 11:00 PM"},{label:"Night",hours:"11:00 PM - 7:00 AM"}];return e.jsx("div",{className:"overflow-auto rounded-lg shadow-md ",children:e.jsxs("div",{className:"min-w-[800px] bg-white shadow-lg p-2 rounded",children:[e.jsx("h2",{className:"mb-2 text-xl font-semibold text-left",children:"My Shift Overview"}),e.jsxs("div",{className:"grid grid-cols-[150px_repeat(7,1fr)] border  ",children:[e.jsx("div",{className:"p-3 font-semibold bg-[#b3b3b3] border "}),s.map(t=>e.jsx("div",{className:"p-3 font-semibold text-center bg-[#b3b3b3] border ",children:t},t)),n.map(t=>e.jsxs(m.Fragment,{children:[e.jsxs("div",{className:"flex flex-col justify-center p-2 border bg-[#e9f2ff] border-[#b3b3b3]",children:[e.jsx("div",{className:"font-semibold text-center",children:t.label}),e.jsx("div",{className:"text-sm text-center",children:t.hours})]}),s.map(o=>{const i=l.find(d=>d.day===o&&d.time===t.label);return e.jsx("div",{className:"relative h-24 border bg-[#e9f2ff] border-[#b3b3b3]",children:i?e.jsxs("div",{className:"absolute inset-0 flex flex-col p-3 text-xs text-black justify-evenly",style:{backgroundColor:"#8cc4ff"},children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-1 font-semibold",children:i.ward}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(r,{icon:"ic:baseline-people"}),i.patients," Patients"]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(r,{icon:"healthicons:nurse"}),i.nurse]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(r,{icon:"mdi:clock"}),"10–10:30 AM"]})]}):null},o+t.label)})]},t.label))]})]})})},c={columns:[{title:"Patient Name",key:"patientName"},{title:"Room No.",key:"roomNo"},{title:"Bed No.",key:"bedNo"},{title:"Tasks",key:"tasks"},{title:"Action",key:"action"}],rows:[{patientName:"Ram Prasad Shrestha",roomNo:"224",bedNo:"69",tasks:"Check Vitals regularly, 20 minutes",action:e.jsx(a,{onShow:()=>{}})},{patientName:"Sita Lama",roomNo:"310",bedNo:"15",tasks:"Pantop D ( 11 PM, 11:20 PM )",action:e.jsx(a,{onShow:()=>{}})},{patientName:"Bikash Thapa",roomNo:"412",bedNo:"33",tasks:"Blood Test Scheduled at 2:00 PM",action:e.jsx(a,{onShow:()=>{}})},{patientName:"Kiran Rai",roomNo:"205",bedNo:"12",tasks:"MRI Scan at 4:00 PM",action:e.jsx(a,{onShow:()=>{}})},{patientName:"Sunita Gurung",roomNo:"120",bedNo:"5",tasks:"Physiotherapy Session at 9:00 AM",action:e.jsx(a,{onShow:()=>{}})}]},f=()=>e.jsxs("div",{className:"flex flex-col w-full gap-4 p-2 mx-auto",children:[e.jsx("div",{className:"w-full h-1/2",children:e.jsx(b,{})}),e.jsxs("div",{className:"flex w-full gap-3 h-1/2",children:[e.jsx("div",{className:"w-[70%] rounded-lg shadow-md border border-gray-200",children:e.jsxs("div",{className:"px-4 py-2 bg-white rounded-lg",children:[e.jsx("h2",{className:"px-4 pt-2.5 mb-1 text-lg font-semibold text-left ",children:"Patients Overview"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(x,{columns:c.columns,rows:c.rows,loading:!1})})]})}),e.jsx("div",{className:"w-[30%]",children:e.jsx(h,{})})]})]});export{f as default};
