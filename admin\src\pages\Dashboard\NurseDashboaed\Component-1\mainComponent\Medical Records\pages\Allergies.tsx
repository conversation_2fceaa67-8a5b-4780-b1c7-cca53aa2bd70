import { useState } from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { AllergyData } from "../../Consultations/pages/sampleMasterTableData";
import { ClientHistory } from "../../../../../../../server-action/api/patienthistory.api";
import { get } from "lodash";
interface VitalSignsProps {
  data: {
    _id: string;
    data: ClientHistory;
  };
}
const Allergies: React.FC<VitalSignsProps> = ({ data }) => {
  const [statusFilter, setStatusFilter] = useState("");
  const [dateFilter, setDateFilter] = useState("");
  // const tableData = {
  //   columns: [
  //     { title: "S.N", key: "sn" },
  //     { title: "Allergy Type", key: "allergyType" },
  //     { title: "Allergen", key: "allergen" },
  //     { title: "Severity", key: "severity" },
  //     { title: "Last Seen", key: "lastSeen" },
  //     { title: "Reaction", key: "reaction" },
  //   ],
  //   rows: get(data, "medicationAdministration", []).map(
  //     (item: any, index: number) => ({
  //       key: index,
  //       sn: index + 1,
  //       allergyType: item.allergyType,
  //       allergen: item.allergen,
  //       severity: item.severity,
  //       lastSeen: item.lastSeen,
  //       reaction: item.reaction,
  //     })
  //   ),
  // };

  const tableData = {
    columns: [
      { title: "S.N", key: "sn" },
      { title: "Allergy Type", key: "allergyType" },
      { title: "Allergen", key: "allergen" },
      { title: "Severity", key: "severity" },
      { title: "Last Seen", key: "lastSeen" },
      { title: "Reaction", key: "reaction" },
    ],
    rows: [
      {
        sn: 1,
        allergyType: "Food",
        allergen: "Peanuts",
        severity: "High",
        lastSeen: "2024-12-15",
        reaction: "Anaphylaxis",
      },
      {
        sn: 2,
        allergyType: "Drug",
        allergen: "Penicillin",
        severity: "Moderate",
        lastSeen: "2023-08-21",
        reaction: "itching",
      },
      {
        sn: 3,
        allergyType: "Environmental",
        allergen: "Pollen",
        severity: "Low",
        lastSeen: "2025-03-10",
        reaction: "watery eyes",
      },
      {
        sn: 4,
        allergyType: "Insect",
        allergen: "Bee sting",
        severity: "High",
        lastSeen: "2024-06-09",
        reaction: "breathing difficulty",
      },
    ],
  };

  return (
    <div className='w-full p-4 mx-auto'>
      <h2 className='mb-4 text-lg font-semibold text-center'>Allergies</h2>

      {/* Filters */}
      <div className='flex justify-end gap-2 mb-2'>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className='w-full max-w-[100px] border border-gray-300 p-1 rounded focus:outline-none'
        >
          <option value=''>Severity</option>
          <option value='Given'>Mild</option>
          <option value='Not Given'>Low</option>
        </select>

        <input
          type='date'
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className='w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none'
        />
      </div>

      {/* Medication Table */}
      <div className='overflow-x-auto'>
        <MasterTable
          // color="bg-[#b3b3b3]"
          // textcolor="text-[#000000]/100"
          columns={tableData.columns}
          rows={tableData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default Allergies;
