import{a2 as e,a7 as t,a8 as o,a1 as r,al as c,a9 as n,aa as d,ac as s,ab as u,aZ as p,ao as m}from"./index-ClX9RVH0.js";const x=[{label:"General",value:"General"},{label:"Cardiology",value:"Cardiology"},{label:"Neurology",value:"Neurology"},{label:"Orthopedics",value:"Orthopedics"},{label:"Gynecology",value:"Gynecology"}],g=()=>e.jsxs("div",{children:[e.jsx(t,{title:"Generic Group",hideHeader:!0,listTitle:"Generic Group"}),e.jsxs("div",{className:"flex w-full gap-10",children:[e.jsxs("div",{className:"h-auto",children:[e.jsx("div",{className:"flex flex-col gap-4 bg-white mt-5 px-4 py-2",children:e.jsx(o,{step:1,title:"Basic Information",isActive:!0})}),e.jsx("div",{className:"flex flex-col gap-3 col-span-2"})]}),e.jsx("div",{className:"w-full h-full",children:e.jsx(h,{})})]})]}),h=()=>{const a=r({initialValues:{name:"",contactNumber:"",paitentId:"",department:"",date:"",doctor:""},enableReinitialize:!0,onSubmit:i=>{c.success("Form submitted successfully!"),history.back(),console.log(i)}}),{handleSubmit:l}=a;return e.jsx(e.Fragment,{children:e.jsx(n,{value:a,children:e.jsx(d,{onSubmit:l,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsx(s,{type:"text",label:"Generic Group Name",placeholder:"Generic Group Name",name:"genericGroupName"}),e.jsx(u,{required:!0,label:"Status",options:x,name:"selectStatus"}),e.jsx(s,{type:"text",label:"Total Medicines",placeholder:"Autofilled",name:"totalMedicines"}),e.jsx(p,{label:"Description",placeholder:"Description",name:"description"})]}),e.jsx(m,{onCancel:()=>{history.back()},onSubmit:l})]})})})})};export{h as EditVendor,g as default};
