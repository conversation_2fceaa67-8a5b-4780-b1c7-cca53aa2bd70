import{a5 as u,ae as p,a2 as e,af as x,ah as h,ai as b,aj as g}from"./index-ClX9RVH0.js";import{C as f,D as y}from"./Svg-BMTGOzwv.js";import{D as j}from"./DepartmentHeader-Aj6XBXn4.js";const D=()=>{const[s,o]=u.useState("Patient"),a={columns:[{title:"Baby Id",key:"tokenid"},{title:"Name",key:"patientName"},{title:"Birth Date",key:"date"},{title:"Gender",key:"date"},{title:"Condition",key:"date"},{title:"Incubator No.",key:"treatment"},{title:"Addmission Date",key:"date"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:p.map(({tokenId:t,patientName:i,date:r,doctorName:l,status:c,treatment:d},m)=>({key:m,tokenid:t,patientName:i,date:r,doctorName:l,status:e.jsx(h,{status:c}),treatment:d,action:e.jsx(x,{onEdit:()=>{},onMore:()=>{}})}))},n=t=>{console.log(t,"onSearch")};return e.jsxs("div",{children:[e.jsx(j,{title:"Neonatal Intensive Care Unit(NICU)",doctorName:"Dr. Shishir Thapa",services:["Advanced Newborn Care","24/7 Monitoring","Ventilator","Incubators","Feeding Supprot","Phototherapy","Emergency Care","Infection Control"],totalAdmissions:200,currentPatients:50,dischargedBabies:50,criticalCases:10,doctorImage:e.jsx(y,{}),headerTitle:"Inpatient Department",icon:e.jsx(f,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"mt-5 pb-1 pt-2 pr-4 flex justify-between items-center",children:[e.jsx(b,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:s,onTabChange:t=>o(t)}),e.jsx("div",{className:"flex flex-row gap-10",children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name, id",onChange:t=>n(t.target.value),className:"pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(g,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{D as NICU};
