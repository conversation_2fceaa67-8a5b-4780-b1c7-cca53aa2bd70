import { useState } from 'react';
import { CustomTabs } from '../../../components/CustomTab';
import { OrderList } from './components/VendorOrderList.component';
import { PurchaseOrderAlerts } from './components/VendorOrderAlert.component';

// Order List Component

// Purchase Order Alerts Component

// Main Vendor Order Page Component
export const VendorOrderPage = () => {
  const [activeTab, setActiveTab] = useState('Order List');

  const departments = [
    {
      title: 'Order List',
      value: 'orders',
      id: 1,
    },
    {
      title: 'New Purchase Alerts',
      value: 'alerts',
      id: 2,
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Order List':
        return <OrderList />;
      case 'New Purchase Alerts':
        return <PurchaseOrderAlerts />;
      default:
        return <OrderList />;
    }
  };

  return (
    <div className="flex flex-col gap-3">
      {/* Header */}

      {/* Tabs */}
      <div className="">
        <CustomTabs
          tabs={departments.map((dept) => dept.title)}
          defaultTab={activeTab}
          onTabChange={(tab) => {
            const selectedDept = departments.find((d) => d.title === tab);

            if (selectedDept) {
              setActiveTab(selectedDept.title);
            }
          }}
        />
      </div>

      {/* Tab Content */}
      <div className="transition-all duration-300">{renderTabContent()}</div>
    </div>
  );
};
