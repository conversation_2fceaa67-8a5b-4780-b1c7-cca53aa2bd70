import{a2 as e,a4 as P,c8 as G,a5 as o,av as a,aV as U,aX as y,a1 as V,aR as B,a9 as R,aa as H,aB as X,b3 as D,aj as F,af as q,cd as A,aH as E,ck as I}from"./index-ClX9RVH0.js";import{b as J,d as K,e as Q,o as W,f as Y,D as Z,S as z,a as ee}from"./SurgerySubDepatment-D7s6By8Z.js";import{a as O,b as te}from"./OTAssignment-BHmGwNc9.js";const ae=({className:r,text:s})=>e.jsx(P,{as:"h3",size:"body-lg-lg",variant:"primary-blue",className:G("mt-1 px-2 text-primaryBlue",r),children:s}),se=({onClose:r,edit:s=!1,data:t=null,isOpen:m=!1})=>{const[u,g]=o.useState([]),{data:c}=O({}),p=a.get(c,"data.surgeryDepartments",[]).map(n=>({...n,label:a.get(n,"name"),value:a.get(n,"_id")})),{mutateAsync:S,isPending:j}=J(),{mutateAsync:v,isPending:f}=K(),b=j||f,C=U({otName:y().required("OT Name is required"),floor:y().required("Floor is required"),department:y().required("Department is required"),subDepartment:y().required("Sub-Department is required")}),l=V({initialValues:{otName:a.get(t,"otName",""),floor:a.get(t,"floor",""),department:a.get(t,"department._id",a.get(t,"department","")),subDepartment:a.get(t,"subDepartment._id",a.get(t,"subDepartment",""))},validationSchema:C,enableReinitialize:!0,onSubmit:async n=>{try{const i={name:n.otName,floor:n.floor,department:n.department,subDepartment:n.subDepartment};s&&(t!=null&&t._id)?await v({_id:t._id,entityData:i}):await S(i),r()}catch(i){console.error("Error saving OT configuration:",i)}}}),N=[{type:"text",field:"otName",label:"OT Name",placeholder:"Enter OT Name",required:!0},{type:"text",field:"floor",label:"Floor",placeholder:"Enter Floor",required:!0},{type:"searchableSelect",field:"department",label:"Department",placeholder:"Select Department",required:!0,options:p},{type:"searchableSelect",field:"subDepartment",label:"Sub-Department",placeholder:"Select Sub-Department",required:!0,options:u}];return o.useEffect(()=>{if(l.values.department){const n=a.get(p.find(i=>i._id===l.values.department),"subDepartments",[]).map(i=>({label:a.get(i,"name"),value:a.get(i,"_id")}));g(n)}},[l.values.department,p]),m&&e.jsxs(B,{classname:"max-w-2xl p-4 w-full space-y-2",onClose:r,children:[e.jsx(ae,{className:"ps-0",text:s?"Edit Operation Theater":"Add Operation Theater"}),e.jsx(R,{value:l,children:e.jsxs(H,{onSubmit:l.handleSubmit,children:[e.jsx("div",{className:"gap-4 grid grid-cols-2",children:e.jsx(X,{...l,formDatails:N})}),e.jsxs("div",{className:"flex justify-end gap-3 mt-6",children:[e.jsx("button",{type:"button",onClick:r,className:"px-4 py-2 text-sm border border-gray-300 rounded-md",children:"Cancel"}),e.jsx("button",{type:"submit",disabled:b||!l.isValid||!l.dirty,className:`px-4 py-2 text-sm text-white rounded-md ${b||!l.isValid||!l.dirty?"bg-primary/70 cursor-not-allowed":"bg-primary hover:bg-primary/90"}`,children:b?s?"Updating...":"Creating...":s?"Update":"Create"})]})]})})]})},re=()=>{const[r,s]=o.useState({searchTerm:"",deboundSearchTerm:""}),{data:t}=Q(),[m,u]=o.useState({state:!1,edit:!1,data:null}),g=o.useCallback(a.debounce(c=>s(p=>({...p,deboundSearchTerm:c})),500),[]);return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between bg-white rounded-md p-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search",value:r.searchTerm,onChange:c=>{s({...r,searchTerm:c.target.value}),g(c.target.value)},className:"w-full py-[6px] ps-8 px-4 border border-gray-300 rounded-[6px] text-sm focus:outline-none"}),e.jsx(D,{icon:"mdi:magnify",className:"absolute left-3 top-1/2 size-[16px] transform -translate-y-1/2 text-gray-400"})]}),e.jsxs("button",{onClick:()=>u(c=>({...c,state:!0,edit:!1,data:null})),className:"py-2 px-3 flex items-center justify-center gap-2 whitespace-nowrap rounded-sm bg-primary hover:bg-light_primary text-white text-sm",children:[e.jsx(D,{icon:"lucide:plus",className:"size-[14px]"}),"Opeartion Theatres"]})]}),e.jsx(F,{columns:W,loading:!1,rows:[]}),e.jsx(se,{onClose:()=>u({state:!1,edit:!1,data:null}),isOpen:m.state,data:m.data})]})},ne=()=>{const[r,s]=o.useState({open:!1,of:""}),[t,m]=o.useState(""),[u,g]=o.useState(""),[c,p]=o.useState([]),{data:S,isLoading:j,isSuccess:v}=O();let{data:f}=O({...t!==""&&{name:t}},{enabled:v});f=a.get(f,"data.surgeryDepartments",[]);const b=a.get(S,"data.surgeryDepartments",[]),C=[{value:"",label:"All"},...b.map(d=>({value:d._id,label:d.name}))],{data:l,isLoading:N}=te({...t?{department:t}:{}}),n=a.get(l,"data.surgerySubDepartments",[]),i=n.map(d=>({value:d._id,label:d.name})),T=o.useCallback(()=>{s({open:!1,of:""})},[]),$=d=>{m(d),g("")},L=d=>{g(d)};o.useEffect(()=>{if(b.length>0){const d=f.map((x,w)=>{const _=n.filter(h=>a.get(h,"department._id","")===x._id);return _.length===0?{key:`${x._id}-${w}`,sno:w+1,department:x.name,subCategory:"-",action:e.jsx(q,{onDelete:()=>{}})}:_.map((h,k)=>({key:`${x._id}-${h._id}-${k}`,sno:w+k+1,department:x.name,subCategory:h.name,action:e.jsx(q,{onDelete:()=>{}})}))});p(d.flat())}},[b,n]);const M={department:e.jsx(ee,{onClose:T}),subDepartment:e.jsx(z,{onClose:T})};return e.jsxs("div",{children:[e.jsxs("div",{className:"grid grid-cols-3 bg-white rounded-md p-4 gap-8",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(A,{className:"flex items-center gap-4",label:"Department",children:e.jsx(E,{options:C,onChange:$,value:t,placeholder:"Select Department"})}),e.jsx("button",{onClick:()=>s({open:!0,of:"department"}),className:"size-sm p-2 rounded-full shadow-md drop-shadow-md cursor-pointer",children:e.jsx(D,{icon:"lucide:plus",className:"size-3"})})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(A,{className:"flex items-center gap-4",label:"Sub Department",children:e.jsx(E,{options:i,onChange:L,value:u,placeholder:"Select Sub Department"})}),e.jsx("button",{onClick:()=>s({open:!0,of:"subDepartment"}),className:"size-sm p-2 rounded-full shadow-md drop-shadow-md cursor-pointer",children:e.jsx(D,{icon:"lucide:plus",className:"size-3"})})]})]}),e.jsx(F,{columns:Y,rows:c,loading:j||N}),e.jsx(Z,{isOpen:r.open,showCloseButton:!1,onClose:()=>s({open:!1,of:""}),children:r.of==="subDepartment"&&t?e.jsx(z,{onClose:T}):M[r.of]})]})},de=()=>{const[r,s]=o.useState("surgery"),t=[{label:"Surgery Configuration",value:"surgery"},{label:"OT Configuration",value:"ot-configuration"}],m={surgery:e.jsx(ne,{}),"ot-configuration":e.jsx(re,{})};return e.jsxs("div",{children:[e.jsx(I,{defaultTab:"surgery",tabsList:t,onTabChange:u=>s(u)}),m[r]]})};export{de as default};
