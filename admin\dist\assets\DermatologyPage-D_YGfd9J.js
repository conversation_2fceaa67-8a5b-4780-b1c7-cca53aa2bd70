import{a5 as p,ad as g,ae as x,a2 as e,ag as h,ah as b,ai as y,aj as D}from"./index-ClX9RVH0.js";import{b as j,D as f}from"./Svg-BMTGOzwv.js";import{D as v}from"./DepartmentHeader-Aj6XBXn4.js";const S=()=>{const[o,s]=p.useState("Patient"),n=g(),a={columns:[{title:"Paitent Id",key:"tokenid"},{title:"Patient Name",key:"patientName"},{title:"Date Assigned",key:"date"},{title:"Contact Number",key:"treatment"},{title:"Appointment Date",key:"doctorName"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:x.map(({tokenId:t,patientName:r,date:l,doctorName:c,status:m,treatment:d},u)=>({key:u,tokenid:t,patientName:r,date:l,doctorName:c,status:e.jsx(b,{status:m}),treatment:d,action:e.jsx("button",{onClick:()=>n(h.DERMATOLOGY),className:"px-4 py-2 text-white rounded bg-primary",children:"Checkup"})}))},i=t=>{console.log(t,"onSearch")};return e.jsxs(e.Fragment,{children:[e.jsx(v,{headerTitle:"ODP",title:"Dermatology",doctorName:"Dr. John Smith",services:["Diagnostic","Interventional","Electrophysiology","Cardiac Rehabilitation","Preventive Cardiology"],patientServed:1020,doctorImage:e.jsx(f,{}),followUpPatient:1120,newPatient:220,revenueGenerated:402300,icon:e.jsx(j,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pb-2 mt-5",children:[e.jsx(y,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:o,onTabChange:t=>s(t)}),e.jsx("div",{children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name id",onChange:t=>i(t.target.value),className:"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(D,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{S as DermatologyDepartmentPage};
