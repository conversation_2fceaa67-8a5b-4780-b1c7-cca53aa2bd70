import{a5 as u,ae as p,a2 as e,af as x,ah as g,ai as f,aj as h}from"./index-ClX9RVH0.js";import{C as y,D as j}from"./Svg-BMTGOzwv.js";import{D as b}from"./DepartmentHeader-Aj6XBXn4.js";const v=()=>{const[s,o]=u.useState("Patient"),a={columns:[{title:"Patient Id",key:"tokenid"},{title:"Patient Name",key:"patientName"},{title:"Gender",key:"date"},{title:"Age",key:"date"},{title:"Surgery Type",key:"treatment"},{title:"Bed No.",key:"date"},{title:"Addmission Date",key:"date"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:p.map(({tokenId:t,patientName:n,date:r,doctorName:l,status:c,treatment:d},m)=>({key:m,tokenid:t,patientName:n,date:r,doctorName:l,status:e.jsx(g,{status:c}),treatment:d,action:e.jsx(x,{onEdit:()=>{},onMore:()=>{}})}))},i=t=>{console.log(t,"onSearch")};return e.jsxs("div",{children:[e.jsx(b,{title:"Post Operative Ward(POW)",doctorName:"Dr. Shishir Thapa",services:["Continuous Monitoring","Comfort Care","Wound Care","Ventilation Support","Medications","Infection Control","Nurse Rounds"],totalSurgeries:200,patientInRecovery:50,discharged:50,criticalCases:10,doctorImage:e.jsx(j,{}),headerTitle:"Inpatient Department",icon:e.jsx(y,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pt-2 pb-1 pr-4 mt-5",children:[e.jsx(f,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:s,onTabChange:t=>o(t)}),e.jsx("div",{className:"flex flex-row gap-10",children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name, id",onChange:t=>i(t.target.value),className:"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(h,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{v as PostOperativeWard};
