import{a2 as t,a7 as C,a5 as r,b4 as F,aU as L,b5 as k,av as a,ad as w,a1 as G,b6 as R,b7 as D,b8 as V,ay as _,ax as B,am as U,a9 as H,aa as M,b9 as O,aB as T,ao as z}from"./index-ClX9RVH0.js";const J=()=>t.jsxs("div",{className:"mb-6",children:[t.jsx(C,{title:"Add Emergency Patient",hideHeader:!0,listTitle:"Add Emergency Patient"}),t.jsx(W,{})]}),W=()=>{const[g,p]=r.useState(1),[Y,u]=r.useState(""),{mutate:b,isSuccess:c,isPending:f}=F(),{data:y}=L(),N=k.map(e=>({...e,isActive:e.step===g})),h=a.get(y,"data.departments",[]).map(e=>({label:a.get(e,"name",""),value:a.get(e,"_id","")})),x=w(),s=G({initialValues:D(),enableReinitialize:!0,validationSchema:R,onSubmit:e=>{let o={...e,commonInfo:{...e.commonInfo,ipdOpd:"EMERGENCY",contactInfo:{phone:{primaryPhone:e.commonInfo.contactInfo.primaryPhone,secondaryPhone:e.commonInfo.contactInfo.secondaryPhone},address:{currentAddress:e.commonInfo.contactInfo.currentAddress,permanentAddress:e.commonInfo.contactInfo.permanentAddress}}},patientInfo:{patientStatus:"CRITICAL"},emergencyInfo:{categoryName:e.categoryName,room:e.rooms,bedNumber:e.bedNo,reason:e.reason}};o=V(o),b(o),x("/emergency-room")}}),{data:i}=_({categoryName:s.values.ward},{enabled:s.values.ward!==""}),{data:d}=B({department:s.values.categoryName},{enabled:s.values.categoryName!==""}),I=r.useMemo(()=>a.get(d,"data.wardCategory",[]).map(e=>({...e,label:a.get(e,"categoryName"),value:a.get(e,"_id")})),[d]),v=a.get(i,"data.rooms",[]).map(e=>({label:a.get(e,"roomNo",""),value:a.get(e,"_id","")})),{handleSubmit:m,resetForm:l,getFieldProps:j,errors:A,touched:P,values:n,setFieldValue:S}=s;r.useEffect(()=>{n.categoryName&&u(n.categoryName)},[n.categoryName]),r.useEffect(()=>{c&&l()},[c,l]);const E=a.get(a.get(i,"data.rooms",[]).find(e=>a.get(e,"_id")==a.get(n,"rooms")),"beds",[]).map(e=>({label:a.get(e,"bedNo","-"),value:a.get(e,"bedNo","")}));return console.log("formik.errors",s.values),t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[t.jsx("div",{children:t.jsx(U,{steps:N})}),t.jsx("div",{className:"col-span-2",children:t.jsx(H,{value:s,children:t.jsxs(M,{onSubmit:m,className:"space-y-4",children:[O({roomList:v,bedList:E,departmentList:h,wardList:I}).map(({fields:e},o)=>t.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4  rounded-sm bg-white p-4",onClick:()=>p(o+1),children:t.jsx(T,{formDatails:e,errors:A,touched:P,setFieldValue:S,values:n,getFieldProps:j})},o)),t.jsx(z,{isPending:f,onCancel:()=>history.back(),onSubmit:m})]})})})]})};export{W as BasicInformation,J as default};
