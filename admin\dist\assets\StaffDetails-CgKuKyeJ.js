import{a2 as e,a5 as N,cy as _,at as q,a7 as z,dw as Q,dx as R}from"./index-ClX9RVH0.js";const V=({doc:i})=>{const r=i==null?void 0:i.map(l=>({title:l==null?void 0:l.documentType,value:l==null?void 0:l.documentImages}));return e.jsx(e.Fragment,{children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold  text-textGray",children:"Certifications"}),e.jsxs("div",{className:"flex mt-4",children:[e.jsx("div",{className:"bg-green w-1.5 rounded-2xl"}),r&&r.map(({title:l,value:s},t)=>e.jsxs("div",{className:"pl-4 w-full flex gap-2 items-center cursor-pointer",onClick:()=>{s&&(typeof s=="string"?window.open(s,"_blank"):Array.isArray(s)&&s.length>0&&window.open(s[0],"_blank"))},children:[e.jsx("h1",{className:"font-semibold text-base",children:l}),e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:16,height:16,viewBox:"0 0 24 24",children:e.jsx("path",{fill:"currentColor",d:"M13 9V3.5L18.5 9M6 2c-1.11 0-2 .89-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"})})]},t))]})]})})},$=()=>{const i=n=>{const a=new Date(n),d=a.getDay();return a.setDate(a.getDate()-d),a},r=n=>`${n.getDate()}`,l=["Sun","Mon","Tue","Wed","Thur","Fri","Sat"],s=n=>{const a=[];for(let d=0;d<7;d++){const j=new Date(n);j.setDate(n.getDate()+d),a.push(j)}return a},t=new Date,[c,m]=N.useState(t),o=i(c),h=s(o),p=()=>{const n=new Date(o);n.setDate(o.getDate()-7),m(n)},f=()=>{const n=new Date(o);n.setDate(o.getDate()+7),m(n)},x=n=>n.getDate()===t.getDate()&&n.getMonth()===t.getMonth()&&n.getFullYear()===t.getFullYear();return e.jsx("div",{className:"mx-auto p-2 bg-[#CEF9F6] rounded-lg",children:e.jsxs("div",{className:"flex items-center justify-between my-2",children:[e.jsx("button",{className:"px-4 flex justify-center items-centerpy-2 bg-blue-500 rounded-md hover:bg-blue-600 transition",onClick:p,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 16 16",children:e.jsx("path",{fill:"none",stroke:"currentColor",d:"M9.5 4.5L6 8l3.5 3.5","stroke-width":"1"})})}),e.jsx("div",{className:"flex gap-2 overflow-x-auto",children:h.map((n,a)=>e.jsxs("div",{className:`w-24 p-2 text-3xl text-center rounded-lg ${x(n)?"bg-[#146C71] text-white":"bg-[#CEF9F6] text-gray-700"}`,children:[e.jsx("div",{className:"font-semibold text-lg",children:l[a]}),e.jsx("div",{className:" text-xl rounded-full p-1",children:r(n)})]},a))}),e.jsx("button",{className:"px-4 py-2 font-extrabold bg-blue-500 rounded-md hover:bg-blue-600 transition",onClick:f,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 7 16",children:e.jsx("path",{fill:"currentColor",d:"M1.5 13a.47.47 0 0 1-.35-.15c-.2-.2-.2-.51 0-.71L5.3 7.99L1.15 3.85c-.2-.2-.2-.51 0-.71s.51-.2.71 0l4.49 4.51c.2.2.2.51 0 .71l-4.5 4.49c-.1.1-.23.15-.35.15"})})})]})})},J=()=>e.jsxs("div",{className:"bg-white p-4 pb-8  rounded-md",children:[e.jsx("div",{className:" flex justify-center items-center ",children:e.jsx("h1",{className:"text-xl  font-semibold text-textGray",children:"Shift"})}),e.jsxs("div",{className:"my-4",children:[" ",e.jsx($,{})]}),e.jsxs("div",{children:[e.jsx("h1",{className:" text-base",children:"2 Scheduled today"}),e.jsxs("div",{className:"flex mt-4 ",children:[e.jsx("div",{className:" bg-green w-1.5 rounded-2xl "}),e.jsxs("div",{className:" pl-4 w-full",children:[e.jsx("h1",{className:"font-semibold text-base",children:"Morning"}),e.jsxs("div",{className:"flex  gap-4 w-full text-sm",children:[e.jsx("li",{children:"09:00 Am"}),e.jsx("li",{children:"09:00 Am"})]})]})]}),e.jsxs("div",{className:"flex mt-4 ",children:[e.jsx("div",{className:" bg-orange-400 w-1.5 rounded-2xl "}),e.jsxs("div",{className:" pl-4 w-full",children:[e.jsx("h1",{className:"font-semibold text-base",children:"Evening"}),e.jsxs("div",{className:"flex  gap-4 w-full text-sm",children:[e.jsx("li",{children:"09:00 Am"}),e.jsx("li",{children:"09:00 Am"})]})]})]})]})]}),U=({role:i,employmentType:r,designation:l})=>e.jsxs("div",{className:"p-6 w-1/2 md:w-1/2  rounded-md bg-white ",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-xl font-semibold text-textGray",children:"Job Details"})}),e.jsxs("div",{className:"mt-5",children:[e.jsxs("div",{className:"mt-5",children:[e.jsx("h1",{className:"font-semibold text-sm",children:"Role"}),e.jsx("p",{className:" text-sm",children:i})]}),e.jsxs("div",{className:"mt-5",children:[e.jsx("h1",{className:"font-semibold text-sm",children:"Employment Type"}),e.jsx("p",{className:" text-sm",children:r})]}),e.jsxs("div",{className:"mt-5",children:[e.jsx("h1",{className:"font-semibold text-sm",children:"Designation"}),e.jsxs("p",{className:" text-sm",children:[l," "]})]})]})]}),K=({qualification:i,certification:r,experienceDetail:l})=>e.jsxs("div",{className:"p-6 w-1/2 md:w-1/2 rounded-md bg-white",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-xl font-semibold text-textGray",children:"Qualifications"})}),e.jsxs("div",{className:"mt-5",children:[e.jsxs("div",{className:"mt-5",children:[e.jsx("h1",{className:"font-semibold text-sm",children:"Qualifications"}),e.jsxs("p",{className:"text-sm",children:[i," "]})]}),e.jsxs("div",{className:"mt-5",children:[e.jsx("h1",{className:"font-semibold text-sm",children:"Certification"}),e.jsx("p",{className:"text-sm",children:r})]}),e.jsxs("div",{className:"mt-5",children:[e.jsx("h1",{className:"font-semibold text-sm",children:"Experience"}),e.jsx("p",{className:"text-sm",children:l})]})]})]}),ee=()=>{var v,b,D,y,k,S,I,C,M,A,F,E,T,W,G,O,B;const i=_(),[r,l]=N.useState("");N.useEffect(()=>{l(i.pathname.split("/")[2])},[i]);const{data:s}=q(r);console.log(s,"staff data");const t=s==null?void 0:s.commonInfo,c=s==null?void 0:s.professionalDetails;s==null||s.departmentDetails;const m=(v=t==null?void 0:t.personalInfo)==null?void 0:v.fullName,o=(b=s==null?void 0:s.identityInformation)==null?void 0:b.profileImage;c==null||c.lisenceNo;const h=s==null?void 0:s.role,p=(D=t==null?void 0:t.personalInfo)==null?void 0:D.gender,f=(k=(y=t==null?void 0:t.contactInfo)==null?void 0:y.address)==null?void 0:k.currentAddress;t==null||t.generalDescription;const x=(S=t==null?void 0:t.contactInfo)==null?void 0:S.phone,n=s==null?void 0:s.email,a=(I=s==null?void 0:s.professionalDetails)==null?void 0:I.employeeType,d=(C=s==null?void 0:s.professionalDetails)==null?void 0:C.designation,j=(A=(M=s==null?void 0:s.professionalDetails)==null?void 0:M.education[0])==null?void 0:A.qualification,g=new Date((F=s==null?void 0:s.experienceDetails)==null?void 0:F.joinedDate),u=new Date,w=u.getFullYear()-g.getFullYear(),Y=u.getMonth()>g.getMonth()||u.getMonth()===g.getMonth()&&u.getDate()>=g.getDate()?w:w-1,H=(E=c==null?void 0:c.education[0])==null?void 0:E.fieldOfStudy,L=Y,P=(T=s==null?void 0:s.identityInformation)==null?void 0:T.identityDocuments;return e.jsxs("div",{className:"flex flex-col gap-2 w-full pb-8",children:[e.jsx(z,{listTitle:"Staff Details",hideHeader:!0}),e.jsxs("section",{className:"grid grid-cols-2  md:grid-cols-3 gap-5 ",children:[e.jsxs("main",{className:"col-span-2 space-y-8 ",children:[e.jsx(Q,{_id:s==null?void 0:s._id,currentAddress:f,image:o,fullName:m,role:h}),e.jsx(R,{generalInfo:{DOB:(G=(W=s==null?void 0:s.commonInfo)==null?void 0:W.personalInfo)==null?void 0:G.dob,Gender:p,MartialStatus:(B=(O=s==null?void 0:s.commonInfo)==null?void 0:O.personalInfo)==null?void 0:B.maritalStatus,currentAddress:f},contactInfo:{email:n,primaryPhone:x==null?void 0:x.primaryPhone}})]}),e.jsx("main",{className:"col-span-1 ",children:e.jsx(J,{})})]}),e.jsxs("section",{className:"grid grid-cols-3 gap-4 overflow-hidden  mt-5",children:[e.jsx("main",{className:"col-span-2",children:e.jsxs("div",{className:"flex flex-col md:justify-evenly md:flex-row gap-5 rounded-lg ",children:[e.jsx(U,{role:h,employmentType:a,designation:d}),e.jsx(K,{qualification:j,certification:H,experienceDetail:L})]})}),e.jsx("main",{className:"rounded-lg col-span-1 bg-white p-6",children:e.jsx(V,{doc:P})})]})]})};export{ee as default};
