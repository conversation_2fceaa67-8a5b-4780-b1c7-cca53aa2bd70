import{a2 as a,a1 as Y,aV as Z,aX as ee,a9 as ae,aa as te,ac as se,ao as ne,ad as le,a5 as s,aP as re,av as m,af as oe,ag as ie,aL as ce,ah as de,ai as ue,b3 as pe,aj as xe,aR as me,a4 as ge,al as V}from"./index-ClX9RVH0.js";import{f as he,g as fe,F as je}from"./Svg-BMTGOzwv.js";import{u as ye}from"./OTAssignment-BHmGwNc9.js";import{b as be,c as G,a as Ne,d as ve}from"./operation.api-BRnS95Te.js";const Se=({title:u,cardList:t,image:r})=>a.jsx("div",{children:a.jsxs("div",{className:"bg-white rounded-lg flex justify-between items-center p-5",children:[a.jsx("div",{className:"flex items-center justify-between px-3 pt-3",children:a.jsxs("div",{className:"flex items-start gap-4 ",children:[a.jsx("div",{className:"flex items-center justify-center w-24 h-24 bg-gray-100 rounded-full",children:a.jsx("img",{src:r,alt:"no-image"})}),a.jsxs("div",{children:[a.jsx("h1",{className:"font-semibold text-[#464646] text-xl w-[200px] capitalize",children:u}),a.jsx("span",{className:"bg-[#B8E8FF42] text-[#28abe8] text-sm p-1 rounded-md ",children:"January"})]})]})}),a.jsx("div",{className:"w-[1px] bg-[#25d649] h-20 mx-4"}),a.jsx("div",{className:"flex gap-5",children:t==null?void 0:t.map((o,i)=>a.jsx("div",{className:"",children:a.jsx(we,{title:o.title,value:o.value,icon:o.icon})},i))})]})}),we=({title:u,value:t,icon:r})=>a.jsxs("div",{className:"p-4 bg-[#f6f8f9] shadow-sm w-[200px] rounded-md",children:[a.jsxs("div",{className:"flex justify-between",children:[a.jsxs("div",{className:"flex flex-col gap-2",children:[a.jsx("p",{className:"mt-1 text-sm text-gray-600 capitalize",children:u}),a.jsx("p",{className:"text-xl font-semibold",children:t})]}),a.jsx("span",{className:"text-2xl",children:r})]}),a.jsxs("div",{className:"text-sm",children:[a.jsx("span",{className:"text-[#25d649]",children:"8.5%"}),"  ",a.jsx("span",{children:"Up from Last Month"})]})]}),Te="/assets/operation-BVLmH_mE.png",Ce=({setIsModalOpen:u,editData:t,onClose:r})=>{const o=be(),i=G(),c=Y({initialValues:{note:(t==null?void 0:t.note)??""},validationSchema:Z({note:ee().required("Cancellation note is required")}),onSubmit:async f=>{try{if(!(t!=null&&t.item)){console.error("Missing item data for cancellation.");return}const l={...t.item,note:f.note,status:"CANCELLED"};console.log(l,"Cancel Payload"),t._id?await i.mutateAsync({_id:t._id,entityData:l}):await o.mutateAsync(l),r()}catch(l){console.error("Failed to cancel Operation Theatre:",(l==null?void 0:l.message)||l)}}}),{handleSubmit:g,touched:w,errors:p}=c;return a.jsx(a.Fragment,{children:a.jsx("div",{className:"bg-white rounded-sm text-sm p-6",children:a.jsx(ae,{value:c,children:a.jsx(te,{onSubmit:g,children:a.jsxs("div",{className:"w-full max-w-md mx-auto ",children:[a.jsxs("div",{className:"border border-slate-200 rounded-md p-4 shadow-sm bg-white",children:[a.jsx(se,{label:"Cancellation Reason",required:!0,name:"note",type:"text",placeholder:"Enter Cancellation Reason"}),c.touched.note&&c.errors.note&&a.jsx("div",{className:"text-red-500 text-sm mt-1",children:c.errors.note})]}),a.jsx("div",{className:"flex justify-end mt-6 mb-4",children:a.jsx(ne,{onCancel:r,onSubmit:g})})]})})})})})},Ae=[{title:"patients served",value:"54",icon:a.jsx(he,{})},{title:"revenue generated",value:"Rs. 78,000",icon:a.jsx(fe,{})},{title:"follow-up patients",value:"50",icon:a.jsx(je,{})}],Ee=({listTitle:u})=>{const t=le(),[r,o]=s.useState(""),[i,c]=s.useState(""),[g,w]=s.useState("All"),[p,f]=s.useState(),[l,j]=s.useState(!1),[D,y]=s.useState(null),[$,U]=s.useState(""),q=re(()=>{j(!1),y(null)}),[d,b]=s.useState({page:1,limit:10}),z=Ne(),T=m.get(z,"data.data.ot")||[],{data:x,refetch:N}=ye({page:d.page,limit:d.limit,...i.trim()&&{search:i.trim()},...p&&p!=="All"&&{ot:p}});console.log(x,"raja");const{mutateAsync:B}=ve({id:JSON.stringify([$])}),v=m.get(x,"data.pagination",{}),H=s.useCallback(m.debounce(e=>{b(n=>({...n,page:1})),c(e)},500),[]),J=e=>{o(e),H(e)};s.useEffect(()=>{N()},[p,d.page,d.limit,i]);const C=s.useMemo(()=>[{label:"All",value:"All"},...T.map(e=>({label:e.name,value:e._id}))],[T]),{mutateAsync:W}=G(),X=async e=>{try{await W({_id:e._id,entityData:{...e,status:"COMPLETED"}}),V.success("Operation marked as completed"),N()}catch(n){V.error("Failed to mark as completed"),console.error("Error updating operation status:",n)}},K=e=>{switch(e){case"COMPLETED":return"Completed";case"SCHEDULED":return"Scheduled";case"CANCELLED":return"Cancelled";case"IN_PROGRESS":return"In Progress";default:return"N/A"}},A=s.useMemo(()=>({columns:[{title:"Patient Name",key:"patient"},{title:"Doctor Assigned",key:"surgeon"},{title:"Operation Type",key:"surgeryType"},{title:"Start Time",key:"startTime"},{title:"End Time",key:"endTime"},{title:"Date",key:"date"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:m.get(x,"data.otAssignment",[]).map((e,n)=>{var h,E,O,k,I,P,_,L,F;return{key:n,patient:((O=(E=(h=e==null?void 0:e.patient)==null?void 0:h.commonInfo)==null?void 0:E.personalInfo)==null?void 0:O.fullName)??"N/A",surgeon:((P=(I=(k=e==null?void 0:e.assignedSurgery)==null?void 0:k.surgeon)==null?void 0:I.map(S=>{var R,M;return(M=(R=S==null?void 0:S.commonInfo)==null?void 0:R.personalInfo)==null?void 0:M.fullName}))==null?void 0:P.join(", "))??"N/A",surgeryType:((_=e==null?void 0:e.assignedSurgery)==null?void 0:_.surgeryType)??"N/A",startTime:((L=e==null?void 0:e.assignedSurgery)==null?void 0:L.startTime)??"N/A",endTime:((F=e==null?void 0:e.assignedSurgery)==null?void 0:F.endTime)??"N/A",date:(e==null?void 0:e.date)??"N/A",status:a.jsx(de,{status:K(e==null?void 0:e.status)}),action:a.jsx(oe,{onMore:()=>{},onMoreList:[{title:"View Detail",onClick:()=>t(`${ie.GENERALOT_VIEWDETAIL}/${e._id}`),index:1},{title:"Edit",onClick:()=>{ce(m.get(e,"patient._id")),t(`/general-ward/surgery-${e==null?void 0:e._id}`)},index:2},{title:"Cancel",onClick:()=>{y({_id:e._id,item:e,note:e.note??""}),j(!0)},index:3},{title:"Complete",onClick:()=>X(e),index:4}],onDelete:async()=>{U(e._id),await B()}})}})}),[x]),Q=({page:e,limit:n})=>{b({page:e??d.page,limit:n??d.limit})};return a.jsxs("div",{className:"flex flex-col gap-4",children:[a.jsx(Se,{title:u,image:Te,cardList:Ae}),a.jsxs("div",{className:"bg-white px-5 rounded-lg",children:[a.jsxs("div",{className:"mt-5 pb-2 flex justify-between items-center",children:[a.jsx(ue,{tabs:C.map(e=>e.label),defaultTab:g,onTabChange:e=>{const n=C.find(h=>h.label===e);f(n==null?void 0:n.value),w(e),b({...d,page:1})}}),a.jsxs("div",{className:"relative flex items-center gap-4",children:[a.jsx("input",{type:"text",placeholder:"Search by Date or Status",value:r,onChange:e=>J(e.target.value),className:"pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),a.jsxs("button",{onClick:()=>t("/assign-ot/add"),className:"px-4 py-2 text-white transition flex items-center gap-2 rounded-md bg-primary text-nowrap hover:bg-light_primary",children:[a.jsx(pe,{icon:"lucide:plus",className:"min-w-[14px]"})," Operation"]})]})]}),a.jsx(xe,{className:"px-0 py-4",columns:A.columns,rows:A.rows,loading:!x,pagination:{currentPage:v.page||1,totalPage:v.pages||1,limit:v.limit||10,onClick:Q}})]}),l&&a.jsxs(me,{ref:q,classname:"w-full max-w-lg text-center",children:[a.jsx(ge,{as:"h3",size:"body-lg-lg",variant:"primary-blue",className:"mt-6 px-2 text-center text-primary",children:"Cancel Operation"}),a.jsx(Ce,{editData:D,onClose:()=>{j(!1),y(null),N()}})]})]})},_e=()=>a.jsx("div",{children:a.jsx(Ee,{listTitle:"General Operation theatre"})});export{_e as default};
