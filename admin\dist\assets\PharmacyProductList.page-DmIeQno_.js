import{a5 as e,aM as _,c1 as D,cW as q,a2 as l,af as k,aP as F,c0 as y,aQ as G,c3 as Q,cX as K,aj as z,aR as H,a4 as W,c4 as X}from"./index-ClX9RVH0.js";const Y=({tabValue:u,setSelectedData:o,setViewModal:n,search:T})=>{var x,b,E,U,S,N,f,g,L,R,B,O;const[p,I]=e.useState({limit:5,page:1}),v=_({productCategory:u,search:T,...T?{}:{page:p.page,limit:p.limit}}),{data:a}=D(v),{mutateAsync:t}=q(),C={columns:[{title:"S.N.",key:"sn"},{title:"Product Name",key:"product"},{title:"Strength",key:"strength"},{title:"Dosage Form",key:"dosageForm"},{title:"Usages",key:"usages"},{title:"Action",key:"action"}],rows:(b=(x=a==null?void 0:a.data)==null?void 0:x.medicalProducts)==null?void 0:b.map((s,d)=>{var r,c,J;return{sn:d+1,product:(s==null?void 0:s.name)??"-",strength:((r=s==null?void 0:s.drug)==null?void 0:r.strength)??"-",dosageForm:((c=s==null?void 0:s.drug)==null?void 0:c.form)??"-",usages:((J=s==null?void 0:s.drug)==null?void 0:J.useCase)??"-",action:l.jsx(k,{onEdit:()=>{o==null||o(s),n==null||n(!0)},onDelete:()=>{t({id:JSON.stringify([s==null?void 0:s._id])})}})}})},P={columns:[{title:"S.N.",key:"sn"},{title:"Product Name",key:"product"},{title:"Usages",key:"usages"},{title:"Actions",key:"action"}],rows:(U=(E=a==null?void 0:a.data)==null?void 0:E.medicalProducts)==null?void 0:U.map((s,d)=>{var r;return{sn:d+1,product:(s==null?void 0:s.name)??"-",usages:((r=s==null?void 0:s.device)==null?void 0:r.useCase)??"-",action:l.jsx(k,{onEdit:()=>{o==null||o(s),n==null||n(!0)},onDelete:()=>{t({id:JSON.stringify([s==null?void 0:s._id])})}})}})},A={columns:[{title:"S.N.",key:"sn"},{title:"Product Name",key:"product"},{title:"Category",key:"category"},{title:"Usages",key:"usages"},{title:"Actions",key:"action"}],rows:(N=(S=a==null?void 0:a.data)==null?void 0:S.medicalProducts)==null?void 0:N.map((s,d)=>{var r,c;return{sn:d+1,product:(s==null?void 0:s.name)??"-",category:((r=s==null?void 0:s.beautySkinCare)==null?void 0:r.category)??"-",usages:((c=s==null?void 0:s.beautySkinCare)==null?void 0:c.useCase)??"-",action:l.jsx(k,{onEdit:()=>{o==null||o(s),n==null||n(!0)},onDelete:()=>{t({id:JSON.stringify([s==null?void 0:s._id])})}})}})},h={columns:[{title:"S.N.",key:"sn"},{title:"Product Name",key:"product"},{title:"Model",key:"model"},{title:"Usages",key:"usages"},{title:"Actions",key:"action"}],rows:(g=(f=a==null?void 0:a.data)==null?void 0:f.medicalProducts)==null?void 0:g.map((s,d)=>{var r,c;return{sn:d+1,product:(s==null?void 0:s.name)??"-",model:((r=s==null?void 0:s.other)==null?void 0:r.model)??"-",usages:((c=s==null?void 0:s.other)==null?void 0:c.useCase)??"-",action:l.jsx(k,{onEdit:()=>{o==null||o(s),n==null||n(!0)},onDelete:()=>{t({id:JSON.stringify([s==null?void 0:s._id])})}})}})},j={columns:[{title:"S.N.",key:"sn"},{title:"Product Name",key:"product"},{title:"Brand Name",key:"brand"},{title:"Usages",key:"usages"},{title:"Actions",key:"action"}],rows:(R=(L=a==null?void 0:a.data)==null?void 0:L.medicalProducts)==null?void 0:R.map((s,d)=>{var r,c;return{sn:d+1,product:(s==null?void 0:s.name)??"-",brand:((r=s==null?void 0:s.medicalSupplies)==null?void 0:r.brandName)??"-",usages:((c=s==null?void 0:s.medicalSupplies)==null?void 0:c.useCase)??"-",action:l.jsx(k,{onEdit:()=>{o==null||o(s),n==null||n(!0)},onDelete:()=>{t({id:JSON.stringify([s==null?void 0:s._id])})}})}})},i={columns:[{title:"S.N.",key:"sn"},{title:"Product Name",key:"product"},{title:"Brand Name",key:"brand"},{title:"Usages",key:"usages"},{title:"Actions",key:"action"}],rows:(O=(B=a==null?void 0:a.data)==null?void 0:B.medicalProducts)==null?void 0:O.map((s,d)=>{var r,c;return{sn:d+1,product:(s==null?void 0:s.name)??"-",brand:((r=s==null?void 0:s.labTestEquipment)==null?void 0:r.brandName)??"-",usages:((c=s==null?void 0:s.labTestEquipment)==null?void 0:c.useCase)??"-",action:l.jsx(k,{onEdit:()=>{o==null||o(s),n==null||n(!0)},onDelete:()=>{t({id:JSON.stringify([s==null?void 0:s._id])})}})}})};return{medicineTableData:C,pharmacyProduct:a,deviceTableData:P,skinTableData:A,otherTableData:h,medicalSuppliesTableData:j,labTestTableData:i,pagination:p,setPagination:I}},$=()=>{var N,f;const[u,o]=e.useState("DRUG"),[n,T]=e.useState(),[p,I]=e.useState({selectedDepartment:"",selectedSpecialist:"",search:""}),[v,a]=e.useState(!1),t=F(()=>a(!1)),{medicineTableData:C,pharmacyProduct:P,deviceTableData:A,skinTableData:h,otherTableData:j,medicalSuppliesTableData:i,labTestTableData:x,pagination:b,setPagination:E}=Y({tabValue:u,setSelectedData:T,setViewModal:a,search:p.search}),U=u===y.DRUG?C.columns:u===y.DEVICES?A.columns:u===y.BEAUTIESSKINCARE?h.columns:u===y.MEDICALSUPPLIES?i.columns:u===y.LABTEST?x.columns:j.columns,S=u==="DRUG"?C.rows:u==="DEVICES"?A.rows:u===y.BEAUTIESSKINCARE?h.rows:u===y.MEDICALSUPPLIES?i.rows:u===y.LABTEST?x.rows:j.rows;return l.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[l.jsx(G,{headerTitle:"Product List",onSearch:!0,button:!0,buttonText:"Add Product",buttonAction:()=>{T(void 0),a(!0)},onSearchFunc:g=>{I({...p,search:g})}}),l.jsxs("div",{className:"bg-white rounded-md",children:[l.jsx(Q,{tabs:K,defaultTab:u,onTabChange:g=>o(g)}),l.jsx(z,{columns:U,rows:S,loading:!1,color:"bg-white ",textcolor:"text-gray-400",pagination:{currentPage:b.page,totalPage:(f=(N=P==null?void 0:P.data)==null?void 0:N.pagination)==null?void 0:f.pages,limit:b.limit,onClick:g=>{g.page&&E({...b,page:g.page}),g.limit&&E({...b,limit:g.limit})}}})]}),v&&l.jsxs(H,{ref:t,classname:"p-4 max-w-[850px] w-full",children:[l.jsx(W,{as:"h3",size:"body-md-default",className:" text-[#4188f2] mb-2",children:n?"Edit Product":"Add Product"}),l.jsx(X,{editData:n,onClose:()=>a(!1)})]})]})};export{$ as PharmacyProductListPage};
