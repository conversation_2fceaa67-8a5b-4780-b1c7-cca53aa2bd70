import{d0 as T,a2 as e,a7 as k,b3 as z}from"./index-ClX9RVH0.js";import{f as x,g as o,h as c,F as l}from"./Svg-BMTGOzwv.js";function F(t){return T({attr:{viewBox:"0 0 640 512"},child:[{tag:"path",attr:{d:"M96 128a128 128 0 1 1 256 0A128 128 0 1 1 96 128zM0 482.3C0 383.8 79.8 304 178.3 304l91.4 0C368.2 304 448 383.8 448 482.3c0 16.4-13.3 29.7-29.7 29.7L29.7 512C13.3 512 0 498.7 0 482.3zM625 177L497 305c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L591 143c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"},child:[]}]})(t)}const L=["text-[#28ABE8]","text-[#6A49E6]","text-[#3D42DF]","text-[#4AD991]","text-[#15B7ED]","text-[#ED4242]"],U=({followUpPatient:t,checkupPatient:a,preCheckedPatient:i,newPatient:j,patientServed:m,revenueGenerated:u,services:n,headerTitle:I,title:A,icon:B,totalAdmissions:v,totalSurgeries:f,patientInRecovery:h,currentPatients:g,dischargedBabies:p,discharged:N,criticalCases:b,totalPatients:y,highRiskCases:w,bedAvailability:D,underObservation:P,dischargedPatients:C,searchText:H,onSearchChange:r,showSearchAndFilter:R=!1})=>e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"w-full",children:[e.jsx(k,{listTitle:I,hideHeader:!0}),e.jsx("div",{className:"bg-white rounded-lg mb-0.5",children:e.jsxs("div",{className:"flex items-center justify-between px-6 pt-6 pb-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full",children:B}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold",children:A}),e.jsx("div",{className:"flex gap-2 mt-1",children:n==null?void 0:n.map((d,E)=>e.jsx("span",{className:`px-2 py-1 text-xs font-medium bg-gray-200 ${L[E]} rounded-md`,children:d},E))})]})]}),R&&e.jsx("div",{className:"flex gap-4 items-center",children:e.jsx("div",{className:"flex-1 max-w-md",children:e.jsxs("div",{className:"relative",children:[e.jsx(z,{icon:"mdi:magnify",className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",width:"20",height:"20"}),e.jsx("input",{type:"text",placeholder:"Search patients...",value:H||"",onChange:d=>r==null?void 0:r(d.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})})]})})]}),e.jsx("div",{className:"w-full py-1 bg-gray-100",children:e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-5 3xl:grid-cols-6 gap-1.5",children:[m&&e.jsx(s,{title:"Patients Served",value:m,icon:e.jsx(x,{})}),u&&e.jsx(s,{title:"Revenue Generated",value:`Rs.${u}`,icon:e.jsx(o,{})}),j&&e.jsx(s,{title:"New Patients",value:j,icon:e.jsx(c,{})}),t&&e.jsx(s,{title:"Follow-up Patients",value:t,icon:e.jsx(l,{})}),i&&e.jsx(s,{title:"Pre-Checked Patients",value:i,icon:e.jsx(F,{className:"bg-gray-200 rounded-lg p-2  text-3xl text-sky-500"})}),a&&e.jsx(s,{title:"Checked Patients",value:a,icon:e.jsx(F,{className:" bg-teal-100 rounded-lg p-2  text-3xl text-teal-500"})}),v&&e.jsx(s,{title:"Total Admissions",value:v,icon:e.jsx(x,{})}),f&&e.jsx(s,{title:"Total Surgeries",value:f,icon:e.jsx(x,{})}),h&&e.jsx(s,{title:"Patient in Recovery",value:h,icon:e.jsx(c,{})}),y&&e.jsx(s,{title:"Total Patients",value:y,icon:e.jsx(x,{})}),g&&e.jsx(s,{title:"Current Patients",value:g,icon:e.jsx(c,{})}),p&&e.jsx(s,{title:"Discharged Babies",value:p,icon:e.jsx(l,{})}),N&&e.jsx(s,{title:"Discharged",value:N,icon:e.jsx(l,{})}),b&&e.jsx(s,{title:"Critical Cases",value:b,icon:e.jsx(o,{})}),w&&e.jsx(s,{title:"High-Risk Cases",value:w,icon:e.jsx(l,{})}),P&&e.jsx(s,{title:"Under Observation",value:P,icon:e.jsx(c,{})}),D&&e.jsx(s,{title:"Bed Availability",value:D,icon:e.jsx(l,{})}),C&&e.jsx(s,{title:"Discharged Patients",value:C,icon:e.jsx(o,{})})]})})]}),s=({title:t,value:a,icon:i})=>e.jsxs("div",{className:"w-full p-3 rounded-xl shadow-sm bg-white h-20 sm:h-22 md:h-24 flex flex-col justify-between transition-all duration-300 border border-gray-200 ",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2 sm:mb-3",children:[e.jsx("div",{className:"flex-1 min-w-0",children:e.jsx("h1",{className:"text-xs sm:text-sm text-gray-600 truncate",children:t})}),e.jsx("div",{className:"bg-white rounded-md p-0.5 h-full flex-shrink-0",children:e.jsx("span",{className:"text-lg sm:text-xl text-blue-600",children:i})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm sm:text-md md:text-lg font-semibold text-gray-800",children:a}),e.jsx("div",{className:"flex text-xs"})]})]});export{U as D};
