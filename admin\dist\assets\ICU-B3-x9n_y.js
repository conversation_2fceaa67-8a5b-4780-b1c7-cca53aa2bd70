import{a5 as p,ae as u,a2 as e,af as x,ah as g,ai as f,aj as h}from"./index-ClX9RVH0.js";import{C as j,D as b}from"./Svg-BMTGOzwv.js";import{D as y}from"./DepartmentHeader-Aj6XBXn4.js";const D=()=>{const[s,i]=p.useState("Patient"),a={columns:[{title:"Patient Id",key:"tokenid"},{title:"Patient Name",key:"patientName"},{title:"Gender",key:"date"},{title:"Age",key:"date"},{title:"Bed No.",key:"date"},{title:"Doctor Assigned",key:"treatment"},{title:"Addmission Date",key:"date"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:u.map(({tokenId:t,patientName:o,date:r,doctorName:l,status:c,treatment:d},m)=>({key:m,tokenid:t,patientName:o,date:r,doctorName:l,status:e.jsx(g,{status:c}),treatment:d,action:e.jsx(x,{onEdit:()=>{},onMore:()=>{}})}))},n=t=>{console.log(t,"onSearch")};return e.jsxs("div",{children:[e.jsx(y,{title:"Intensive Care Unit(NICU)",doctorName:"Dr. Shishir Thapa",services:["Advanced Life Support","24/7 Critical Care","Monitoring","Specialized Staff","Medication","Emergency Procedures","Infection Control","Advanced Diagnostic Support"],totalAdmissions:200,currentPatients:50,discharged:50,criticalCases:10,doctorImage:e.jsx(b,{}),headerTitle:"Inpatient Department",icon:e.jsx(j,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"mt-5 pb-1 pt-2 pr-4 flex justify-between items-center",children:[e.jsx(f,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:s,onTabChange:t=>i(t)}),e.jsx("div",{className:"flex flex-row gap-10",children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name, id",onChange:t=>n(t.target.value),className:"pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(h,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{D as ICU};
