import{a2 as e,P as Q,a5 as u,aj as d,b3 as g,bg as T,a1 as v,aw as Z,av as x,a9 as $,aa as K,ac as N,ao as X,af as ee,ab as S,aV as j,aX as r,ba as se,at as te,aE as ae,dR as le,dS as re}from"./index-ClX9RVH0.js";const oe=()=>e.jsx("div",{children:e.jsx(Q,{})}),ie=({label:t,isActive:a,onClick:s})=>e.jsx("button",{className:`w-full text-left px-4 py-3 text-sm font-medium transition-colors duration-200 border-b border-gray-200 relative ${a?"text-cyan-600 bg-cyan-50 border-l-4 border-l-cyan-600":"text-gray-700 hover:text-cyan-600 hover:bg-gray-50"}`,onClick:s,children:t}),C=({tabs:t,defaultTab:a,onTabChange:s})=>{const[l,o]=u.useState(a||t[0]),n=c=>{o(c),s&&s(c)};return e.jsx("div",{className:"w-48 bg-white border-r border-gray-200 h-full",children:e.jsx("div",{className:"flex flex-col",children:t.map(c=>e.jsx(ie,{label:c,isActive:l===c,onClick:()=>n(c)},c))})})},ne=({data:t})=>{const[a,s]=u.useState("");console.log(t,"sdfasdfasdfasdfasdfasdfasd");const l={columns:[{title:"Date & Time",key:"visitNo"},{title:"Temp",key:"temperature"},{title:"Systolic BP",key:"systolicBP"},{title:"Distolic BP",key:"distolicBP"},{title:"BMI",key:"bmi"}],rows:[{visitNo:"2025-07-04 10:00 AM",temperature:"98.4°F",systolicBP:"120 mmHg",distolicBP:"80 mmHg",bmi:"23.5"},{visitNo:"2025-07-03 03:45 PM",temperature:"99.1°F",systolicBP:"125 mmHg",distolicBP:"85 mmHg",bmi:"24.2"},{visitNo:"2025-07-02 09:15 AM",temperature:"97.9°F",systolicBP:"118 mmHg",distolicBP:"76 mmHg",bmi:"22.8"}]};return e.jsxs("div",{className:"w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Vital Signs"}),e.jsx("div",{className:"flex justify-end mb-2",children:e.jsx("input",{type:"date",value:a,onChange:o=>s(o.target.value),className:"w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none"})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:l.columns,rows:l.rows,loading:!1})})]})},ce=({data:t})=>{const[a,s]=u.useState(""),[l,o]=u.useState(""),n={columns:[{title:"Medicine Name",key:"medicineName"},{title:"Dose",key:"dose"},{title:"Route",key:"route"},{title:"Scheduled Time",key:"scheduledTime"},{title:"Given Time",key:"givenTime"},{title:"Status",key:"status"},{title:"Reason",key:"reason"}],rows:[{medicineName:"Paracetamol",dose:"500 mg",route:"Oral",scheduledTime:"2025-07-04 08:00 AM",givenTime:"2025-07-04 08:05 AM",status:"Given",reason:"-"},{medicineName:"Amoxicillin",dose:"250 mg",route:"Oral",scheduledTime:"2025-07-04 12:00 PM",givenTime:"2025-07-04 12:10 PM",status:"Given",reason:"-"},{medicineName:"Insulin",dose:"10 units",route:"Subcutaneous",scheduledTime:"2025-07-04 06:00 AM",givenTime:"-",status:"Not Given",reason:"Patient refused"},{medicineName:"Omeprazole",dose:"20 mg",route:"Oral",scheduledTime:"2025-07-04 07:30 AM",givenTime:"2025-07-04 07:32 AM",status:"Given",reason:"-"}]};return e.jsxs("div",{className:"w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Medication Administration"}),e.jsxs("div",{className:"flex justify-end gap-2 mb-2",children:[e.jsxs("select",{value:a,onChange:c=>s(c.target.value),className:"w-full max-w-[100px] border border-gray-300 p-1 rounded focus:outline-none",children:[e.jsx("option",{value:"",children:"Status"}),e.jsx("option",{value:"Given",children:"Given"}),e.jsx("option",{value:"Not Given",children:"Not Given"})]}),e.jsx("input",{type:"date",value:l,onChange:c=>o(c.target.value),className:"w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none"})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:n.columns,rows:n.rows,loading:!1})})]})},de=({data:t})=>{const[a,s]=u.useState(""),[l,o]=u.useState(""),n={columns:[{title:"S.N",key:"sn"},{title:"Allergy Type",key:"allergyType"},{title:"Allergen",key:"allergen"},{title:"Severity",key:"severity"},{title:"Last Seen",key:"lastSeen"},{title:"Reaction",key:"reaction"}],rows:[{sn:1,allergyType:"Food",allergen:"Peanuts",severity:"High",lastSeen:"2024-12-15",reaction:"Anaphylaxis"},{sn:2,allergyType:"Drug",allergen:"Penicillin",severity:"Moderate",lastSeen:"2023-08-21",reaction:"itching"},{sn:3,allergyType:"Environmental",allergen:"Pollen",severity:"Low",lastSeen:"2025-03-10",reaction:"watery eyes"},{sn:4,allergyType:"Insect",allergen:"Bee sting",severity:"High",lastSeen:"2024-06-09",reaction:"breathing difficulty"}]};return e.jsxs("div",{className:"w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Allergies"}),e.jsxs("div",{className:"flex justify-end gap-2 mb-2",children:[e.jsxs("select",{value:a,onChange:c=>s(c.target.value),className:"w-full max-w-[100px] border border-gray-300 p-1 rounded focus:outline-none",children:[e.jsx("option",{value:"",children:"Severity"}),e.jsx("option",{value:"Given",children:"Mild"}),e.jsx("option",{value:"Not Given",children:"Low"})]}),e.jsx("input",{type:"date",value:l,onChange:c=>o(c.target.value),className:"w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none"})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:n.columns,rows:n.rows,loading:!1})})]})},q={columns:[{title:"S.N",key:"sn"},{title:"Immunization",key:"immunization"},{title:"Date Given",key:"dateGiven"},{title:"Dose Number",key:"doseNumber"},{title:"Batch Number",key:"batchNumber"},{title:"Next Due",key:"nextDue"},{title:"Remarks",key:"remarks"}],rows:[{sn:1,immunization:"Hepatitis B",dateGiven:"12/12/2024",doseNumber:1,batchNumber:"HB-3245",nextDue:"12/01/2025",remarks:"No issues"},{sn:2,immunization:"Tetanus",dateGiven:"05/10/2024",doseNumber:1,batchNumber:"TET-2211",nextDue:"05/10/2034",remarks:"Slight soreness reported"},{sn:3,immunization:"MMR",dateGiven:"20/08/2023",doseNumber:2,batchNumber:"MMR-7890",nextDue:"Completed",remarks:"Fully vaccinated"},{sn:4,immunization:"Covid-19",dateGiven:"15/09/2024",doseNumber:1,batchNumber:"CVX-5543",nextDue:"15/10/2024",remarks:"Mild fever after dose"},{sn:5,immunization:"Influenza",dateGiven:"01/11/2024",doseNumber:1,batchNumber:"FLU-1100",nextDue:"01/11/2025",remarks:"No adverse reactions"}]},G={columns:[{title:"S.N",key:"sn"},{title:"Procedure",key:"procedure"},{title:"Date ",key:"date"},{title:"Hospital",key:"hospital"},{title:"Surgeon",key:"surgeon"},{title:"Outcome",key:"outcome"},{title:"Remarks",key:"remarks"}],rows:[{sn:1,procedure:"Appendectomy",date:"12/12/2024",hospital:"ABC Hospital",surgeon:"Dr. Tony",outcome:"Successful",remarks:"No complications"},{sn:2,procedure:"Cataract Surgery",date:"15/08/2023",hospital:"Vision Care Center",surgeon:"Dr. Sita Sharma",outcome:"Successful",remarks:"Mild redness post-surgery"},{sn:3,procedure:"Knee Replacement",date:"20/04/2022",hospital:"Orthopedic Hospital",surgeon:"Dr. Ram Koirala",outcome:"Successful",remarks:"Physiotherapy ongoing"},{sn:4,procedure:"Gallbladder Removal",date:"05/03/2021",hospital:"City General Hospital",surgeon:"Dr. Bina Thapa",outcome:"Successful",remarks:"Recovered fully"},{sn:5,procedure:"Hernia Repair",date:"10/11/2020",hospital:"Metro Hospital",surgeon:"Dr. Kishor Shrestha",outcome:"Successful",remarks:"Minimal scarring"}]},H={columns:[{title:"S.N",key:"sn"},{title:"Condition",key:"condition"},{title:"Relation",key:"relation"},{title:"Status",key:"status"},{title:"Age of Diagnosis",key:"ageOfDiagnosis"},{title:"Genetic Testing",key:"geneticTesting"},{title:"Result",key:"result"},{title:"Remarks",key:"remarks"}],rows:[{sn:1,condition:"Hypertension",relation:"Father",status:"Alive",ageOfDiagnosis:50,geneticTesting:"Yes",result:"Negative",remarks:"Well controlled on meds"},{sn:2,condition:"Diabetes",relation:"Mother",status:"Deceased",ageOfDiagnosis:55,geneticTesting:"No",result:"N/A",remarks:"Managed with insulin"},{sn:3,condition:"Breast Cancer",relation:"Sister",status:"Alive",ageOfDiagnosis:42,geneticTesting:"Yes",result:"Positive",remarks:"Underwent surgery and chemo"},{sn:4,condition:"Asthma",relation:"Brother",status:"Alive",ageOfDiagnosis:15,geneticTesting:"No",result:"N/A",remarks:"Uses inhaler occasionally"}]},E={columns:[{title:"S.N",key:"sn"},{title:"Social Factor",key:"socialFactor"},{title:"Recorded Details",key:"recordedDetails"}],rows:[{sn:1,socialFactor:"Tobacco Use",recordedDetails:"Recorded Details"},{sn:2,socialFactor:"Alcohol Use",recordedDetails:"Recorded Details"},{sn:3,socialFactor:"Illicit Drug use",recordedDetails:"Recorded Details"},{sn:4,socialFactor:"Occupation",recordedDetails:"Recorded Details"},{sn:5,socialFactor:"Living situation",recordedDetails:"Recorded Details"},{sn:6,socialFactor:"Marital Status",recordedDetails:"Recorded Details"},{sn:7,socialFactor:"Sexual History",recordedDetails:"Recorded Details"},{sn:8,socialFactor:"Physical Activity level",recordedDetails:"Recorded Details"},{sn:9,socialFactor:"Dietary Habits",recordedDetails:"Recorded Details"},{sn:10,socialFactor:"Travel History",recordedDetails:"Recorded Details"}]},O={columns:[{title:"S.N",key:"sn"},{title:"Visit Date",key:"visitDate"},{title:"Department",key:"department"},{title:"Doctor",key:"doctor"},{title:"Reason",key:"reason"},{title:"Diagnosis",key:"diagnosis"},{title:"Treatment",key:"treatment"},{title:"Follow Up",key:"followUp"},{title:"Remarks",key:"remarks"}],rows:[{sn:1,visitDate:"11/11/2024",department:"Ophthalmology",doctor:"Dr. Shrestha",reason:"Blurry vision",diagnosis:"Conjunctivitis",treatment:"Eye drops prescribed",followUp:"Next week",remarks:"Improvement expected"},{sn:2,visitDate:"15/11/2024",department:"General Medicine",doctor:"Dr. Rana",reason:"Fever and headache",diagnosis:"Viral Fever",treatment:"Paracetamol 500mg",followUp:"After 3 days",remarks:"Hydration advised"},{sn:3,visitDate:"20/11/2024",department:"ENT",doctor:"Dr. K.C.",reason:"Ear pain",diagnosis:"Ear Infection",treatment:"Antibiotic course",followUp:"In 10 days",remarks:"Avoid cold water"},{sn:4,visitDate:"25/11/2024",department:"Dermatology",doctor:"Dr. Joshi",reason:"Skin rash",diagnosis:"Allergic Dermatitis",treatment:"Antihistamine tablets",followUp:"If not improved",remarks:"Allergy test suggested"},{sn:5,visitDate:"30/11/2024",department:"Cardiology",doctor:"Dr. Shah",reason:"Chest pain",diagnosis:"Mild Hypertension",treatment:"BP medication",followUp:"Monthly check-up",remarks:"Lifestyle changes needed"},{sn:6,visitDate:"05/12/2024",department:"Orthopedics",doctor:"Dr. Thapa",reason:"Knee pain",diagnosis:"Osteoarthritis",treatment:"Pain relievers",followUp:"After 2 weeks",remarks:"Physiotherapy advised"}]},ue=()=>{const[t,a]=u.useState("");return e.jsxs("div",{className:"w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Immunizations"}),e.jsxs("div",{className:"flex items-center justify-end gap-2 mb-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search immunization",className:"px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none"}),e.jsx(g,{icon:"majesticons:search-line",width:20,height:20,className:"absolute text-base text-gray-500 -translate-y-1/2 right-2 top-1/2",onClick:()=>{console.log("Search")}})]}),e.jsx("input",{type:"date",value:t,onChange:s=>a(s.target.value),className:"w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none"})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:q.columns,rows:q.rows,loading:!1})})]})},y=({statusText:t})=>{const a=o=>{switch(o.toLowerCase()){case"active":return{bgColor:"",textColor:"text-[#28A745]"};case"inactive":return{bgColor:"",textColor:"text-[#17a2b8]"};case"critical":return{bgColor:"",textColor:"text-[#DC3545]"};case"resolved":return{bgColor:"",textColor:"text-[#6c757d]"};case"error":return{bgColor:"bg-red-600",textColor:"text-white"};case"cancelled":return{bgColor:"bg-red-400",textColor:"text-white"};case"draft":return{bgColor:"bg-gray-300",textColor:"text-white"};default:return{bgColor:"bg-gray-400",textColor:"text-white"}}},{bgColor:s,textColor:l}=a(t);return e.jsx("span",{className:` px-1.5 py-1 rounded-md font-medium uppercase text-xs  ${s} ${l}`,children:t})},L={columns:[{title:"S.N",key:"sn"},{title:"Condition",key:"condition"},{title:"Diagnosis Date",key:"diagnosisDate"},{title:"Status",key:"status"},{title:"Treatment",key:"treatment"},{title:"Doctor/Dept",key:"doctorDept"},{title:"Remarks",key:"remarks"}],rows:[{sn:1,condition:"Diabetes Mellitus",diagnosisDate:"12/12/24",status:e.jsx(y,{statusText:"Active"}),treatment:"Metformin 500mg",doctorDept:"Endocrinology",remarks:"Blood sugar under control"},{sn:2,condition:"Hypertension",diagnosisDate:"10/11/23",status:e.jsx(y,{statusText:"Inactive"}),treatment:"Amlodipine 5mg",doctorDept:"Cardiology",remarks:"BP normal in last checkup"},{sn:3,condition:"Asthma",diagnosisDate:"05/06/22",status:e.jsx(y,{statusText:"Critical"}),treatment:"Inhaler prescribed",doctorDept:"Pulmonology",remarks:"Frequent attacks during winter"},{sn:4,condition:"Chronic Kidney Disease",diagnosisDate:"18/03/21",status:e.jsx(y,{statusText:"Resolved"}),treatment:"Dialysis done",doctorDept:"Nephrology",remarks:"No further treatment needed"},{sn:5,condition:"Thyroid Disorder",diagnosisDate:"22/09/20",status:e.jsx(y,{statusText:"Inactive"}),treatment:"Levothyroxine",doctorDept:"Endocrinology",remarks:"Dosage reduced, stable"}]},me=()=>{const[t,a]=u.useState(""),[s,l]=u.useState("");return e.jsxs("div",{className:"w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Chronic Conditions"}),e.jsxs("div",{className:"flex justify-end gap-2 mb-2",children:[e.jsxs("select",{value:t,onChange:o=>a(o.target.value),className:"w-full max-w-[100px] border border-gray-300 p-1 rounded focus:outline-none",children:[e.jsx("option",{value:"",children:"Status"}),e.jsx("option",{value:"Active",children:"Active"}),e.jsx("option",{value:"Inactive",children:"Inactive"}),e.jsx("option",{value:"Critical",children:"Critical"}),e.jsx("option",{value:"Resolved",children:"Resolved"})]}),e.jsx("input",{type:"date",value:s,onChange:o=>l(o.target.value),className:"w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none"})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:L.columns,rows:L.rows,loading:!1})})]})},pe=()=>{const[t,a]=u.useState("");return e.jsxs("div",{className:"w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Surgical History"}),e.jsxs("div",{className:"flex items-center justify-end gap-2 mb-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search Surgery",className:"px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none"}),e.jsx(g,{icon:"majesticons:search-line",width:20,height:20,className:"absolute text-base text-gray-500 -translate-y-1/2 right-2 top-1/2",onClick:()=>{console.log("Search Surgery")}})]}),e.jsx("input",{type:"date",value:t,onChange:s=>a(s.target.value),className:"w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none"})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:G.columns,rows:G.rows,loading:!1})})]})},he=()=>e.jsxs("div",{className:"w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Family History"}),e.jsx(d,{rows:H.rows,columns:H.columns,loading:!1})]}),xe=()=>e.jsxs("div",{className:"w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Social History"}),e.jsx(d,{rows:E.rows,columns:E.columns,loading:!1})]}),ge=()=>{const[t,a]=u.useState("");return e.jsxs("div",{className:"w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Visit History"}),e.jsx("div",{className:"flex justify-end mb-2",children:e.jsx("input",{type:"date",value:t,onChange:s=>a(s.target.value),className:"w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none"})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:O.columns,rows:O.rows,loading:!1})})]})},ve=["Vital signs","Medical Administration","Allergies","Immunizations","Chronic Conditions","Surgical History","Family History","Social History","Visit History"],be=({data:t})=>{const[a,s]=u.useState("Vital signs"),l=()=>{switch(a){case"Vital signs":return e.jsx("div",{children:e.jsx(ne,{data:t})});case"Medical Administration":return e.jsx("div",{children:e.jsx(ce,{data:t})});case"Allergies":return e.jsx("div",{children:e.jsx(de,{data:t})});case"Immunizations":return e.jsx("div",{children:e.jsx(ue,{})});case"Chronic Conditions":return e.jsx("div",{children:e.jsx(me,{})});case"Surgical History":return e.jsx("div",{children:e.jsx(pe,{})});case"Family History":return e.jsx("div",{children:e.jsx(he,{})});case"Social History":return e.jsx("div",{children:e.jsx(xe,{})});case"Visit History":return e.jsx("div",{children:e.jsx(ge,{})})}};return e.jsxs("div",{className:"flex h-screen bg-[#F8F8F8]",children:[e.jsx(C,{tabs:ve,defaultTab:"Vital signs",onTabChange:s}),e.jsx("div",{className:"flex-1 overflow-auto",children:l()})]})},fe=({data:t})=>(console.log(t,"fvsdfdsfgsdfgsdf"),e.jsx("div",{children:e.jsx(be,{data:t})})),ye=({data:t})=>{console.log(t,"data");const{mutate:a}=T(),s=v({initialValues:{nurse:[],visitNo:"",height:"",weight:"",bmi:"",temperature:"",systolicBP:"",distolicBP:"",respiratoryRate:"",heartRate:"",urineOutput:"",bloodSugarF:"",bloodSugarR:"",SPO2:"",AVPU:"",trauma:"",mobility:"",oxygenSupplementation:"",intake:"",output:"",vitalTakenDateTime:"",comment:""},onSubmit:async i=>{const m={_id:t._id,vitalSigns:[{...i}]};console.log(m,"I am final"),a({_id:t._id,entityData:m})}}),{handleSubmit:l,values:o,setFieldValue:n}=s,{data:c}=Z({role:"NURSE"}),p=x.map(x.get(c,"data.users",[]),i=>({label:x.get(i,"commonInfo.personalInfo.fullName",""),value:x.get(i,"_id","")}));console.log(p,"NurseList"),console.log(t,"hello");const h={columns:[{title:"Visit No.",key:"visitNo"},{title:"Time",key:"vitalTakenDateTime"},{title:"Heart Rate",key:"heartRate"},{title:"Temperature ",key:"temperature"},{title:"oxygen",key:"oxygenSupplementation"},{title:"Action",key:"action"}],rows:[{visitNo:"001",vitalTakenDateTime:"07/04/2025 10:30 AM",heartRate:"75 bpm",temperature:"98°F",oxygenSupplementation:"Room air",action:"View"},{visitNo:"002",vitalTakenDateTime:"07/04/2025 04:00 PM",heartRate:"78 bpm",temperature:"98.4°F",oxygenSupplementation:"Nasal cannula",action:"View"},{visitNo:"003",vitalTakenDateTime:"07/05/2025 08:15 AM",heartRate:"72 bpm",temperature:"97.9°F",oxygenSupplementation:"Room air",action:e.jsx(ee,{onShow:()=>{}})}]};return console.log(s.values,""),e.jsx("div",{className:"p-2 space-y-0",children:e.jsxs("div",{className:"p-2 border-2 rounded-lg",children:[e.jsx("h2",{className:"mb-2 font-semibold text-center",children:"Vital Signs"}),e.jsx($,{value:s,children:e.jsxs(K,{autoComplete:"off",onSubmit:l,children:[e.jsxs("div",{className:"grid grid-cols-1 p-2 sm:grid-cols-2 lg:grid-cols-3 gap-x-10 gap-y-4",children:[[{label:"Visit No",name:"visitNo",placeholder:"Enter Visitor No.",type:"text"},{label:"Weight",name:"weight",placeholder:"Enter Weight",type:"text"},{label:"BMI",name:"bmi",placeholder:"Enter BMI",type:"text"},{label:"Temperature",name:"temperature",placeholder:"Enter Temperature",type:"text"},{label:"Systolic BP",name:"systolicBP",placeholder:"Enter Systolic BP",type:"text"},{label:"Distolic BP",name:"distolicBP",placeholder:"Enter Distolic BP",type:"text"},{label:"Respiratory Rate",name:"respiratoryRate",placeholder:"Enter Respiratory Rate",type:"text"},{label:"Heart Rate",name:"heartRate",placeholder:"Enter Heart Rate",type:"text"},{label:"Urine Output",name:"urineOutput",placeholder:"Enter Urine Output",type:"text"},{label:"Blood Sugar F",name:"bloodSugarF",placeholder:"Enter Blood Sugar F",type:"text"},{label:"Blood Sugar R",name:"bloodSugarR",placeholder:"Enter Blood Sugar R",type:"text"},{label:"SPO2",name:"SPO2",placeholder:"Enter SPO2",type:"text"},{label:"AVPU",name:"AVPU",placeholder:"Enter AVPU",type:"text"},{label:"Trauma",name:"trauma",placeholder:"Enter Trauma",type:"text"},{label:"Mobility",name:"mobility",placeholder:"Enter Mobility",type:"text"},{label:"Oxygen Supplementation",name:"oxygenSupplementation",placeholder:"Enter Oxygen Supplementation",type:"text"},{label:"Intake",name:"intake",placeholder:"Enter Intake",type:"text"},{label:"Output",name:"output",placeholder:"Enter Output",type:"text"}].map(({label:i,name:m,placeholder:b,type:f})=>e.jsx("div",{className:"flex flex-col gap-0",children:e.jsx(N,{label:i,name:m,type:f,placeholder:b})},m)),e.jsx("div",{className:"flex flex-col gap-1",children:e.jsx(N,{label:"Date & Time Picker",name:"vitalTakenDateTime",type:"datetime-local",placeholder:"Enter Date & Time"})}),e.jsx("div",{className:"flex flex-col gap-1 lg:col-span-2",children:e.jsx(N,{label:"Comment",name:"comment",type:"text",placeholder:"Enter Comment"})})]}),e.jsx("div",{className:"flex justify-end mt-2 mb-2",children:e.jsx(X,{hideCancel:"hidden",onSubmit:l})})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"my-2 font-semibold text-center",children:"Vitals History"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:h.columns,rows:h.rows,loading:!t})})]})]})})},Ne=({data:t})=>{const a=[{value:"FOOD",label:"Food"},{value:"DRUG",label:"Drug"},{value:"ENVIRONMENTAL",label:"Enviromental"},{value:"INSECT",label:"Insect"},{value:"LATEX",label:"Latex"},{value:"CHEMICAL",label:"Chemical"},{value:"OTHER",label:"Other"}],s=[{value:"PEANUTS",label:"Food"},{value:"SHELLFISH",label:"ShellFish"},{value:"MILK",label:"Milk"},{value:"EGGS",label:"Eggs"},{value:"WHEAT",label:"Wheat"},{value:"SOY",label:"Soy"},{value:"PENICILLIN",label:"Penicillin"},{value:"ASPIRIN",label:"Aspirin"},{value:"DUST MITES",label:"Dust Mites"},{value:"POLLEN",label:"Pollen"},{value:"MOLD",label:"Mold"},{value:"BEE STING",label:"Bee Sting"},{value:"LATEX",label:"Latex"},{value:"NICKEL",label:"Nickel"},{value:"OTHER",label:"Other"}],l=[{value:"HIGH",label:"High"},{value:"MEDIUM",label:"Medium"},{value:"LOW",label:"Low"}],{mutate:o}=T(),n=v({initialValues:{allergyType:"",allergen:"",reaction:"",lastSeen:"",serverity:""},onSubmit:async i=>{const m={_id:t._id,allergies:[{...i}]};o({_id:t._id,entityData:m})}}),{handleSubmit:c,values:p}=n;console.log(n,"formik"),console.log(p,"values");const h={columns:[{title:"S.N",key:"key"},{title:"Allergy Type",key:"allergyType"},{title:"Allergen",key:"allergen"},{title:"Severity",key:"serverity"},{title:"Last Seen",key:"lastSeen"},{title:"Reaction",key:"reaction"}],rows:x.get(t,"allergies",[]).map((i,m)=>({key:m,allergyType:i.allergyType,allergen:i.allergen,serverity:i.serverity,lastSeen:i.lastSeen,reaction:i.reaction}))};return console.log(n.values,""),e.jsxs("div",{className:"max-w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold text-center",children:"Allergies"}),e.jsx($,{value:n,children:e.jsx(K,{autoComplete:"off",onSubmit:c,children:e.jsxs("div",{className:"grid grid-cols-1 p-2 sm:grid-cols-2 lg:grid-cols-3 gap-x-10 gap-y-4",children:[e.jsx("div",{children:e.jsx(S,{label:"Allergy Type",firstInput:"Select",options:a,name:p.allergyType,value:n.values.allergyType,onChange:i=>{n.setFieldValue("allergyType",i.target.value)}})}),e.jsx("div",{children:e.jsx(S,{label:"Allergen List",firstInput:"Select",options:s,name:p.allergen,value:n.values.allergen,onChange:i=>{n.setFieldValue("allergen",i.target.value)}})})," ",e.jsx("div",{children:e.jsx(S,{label:"Severity List",firstInput:"Select",options:l,name:p.serverity,value:n.values.serverity,onChange:i=>{n.setFieldValue("serverity",i.target.value)}})}),[{label:"Last Seen",name:"lastSeen",placeholder:"Enter Last Seen",type:"date"},{label:"Reaction",name:"reaction",placeholder:"Enter reaction",type:"text"}].map(({label:i,name:m,placeholder:b,type:f})=>e.jsx("div",{className:"flex flex-col gap-0",children:e.jsx(N,{label:i,name:m,type:f,placeholder:b})},m)),e.jsx("div",{className:"flex justify-end mt-2 mb-2",children:e.jsx(X,{hideCancel:"hidden",onSubmit:c})})]})})}),e.jsx("div",{}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:h.columns,rows:h.rows,loading:!t})})]})},je=({data:t})=>{const a={columns:[{title:"Medicine Name",key:"medicationName"},{title:"Dosage",key:"dosage"},{title:"Frequency",key:"frequency"},{title:"Scheduled Time",key:"route"},{title:"Duration",key:"duration"},{title:"Doctor",key:"prescribingDoctor"},{title:"Diagnosis",key:"diagnosisRelated"},{title:"Notes",key:"notes"},{title:"endDate",key:"endDate"}],rows:[{medicationName:"Paracetamol",dosage:"500 mg",frequency:"3 times a day",route:"After meals",duration:"5 days",prescribingDoctor:"Dr. Sharma",diagnosisRelated:"Fever",notes:"Monitor temperature regularly",endDate:"07/09/2025"},{medicationName:"Amoxicillin",dosage:"250 mg",frequency:"2 times a day",route:"Before meals",duration:"7 days",prescribingDoctor:"Dr. Rana",diagnosisRelated:"Throat infection",notes:"Complete full course",endDate:"07/11/2025"},{medicationName:"Metformin",dosage:"500 mg",frequency:"Once daily",route:"With breakfast",duration:"30 days",prescribingDoctor:"Dr. Joshi",diagnosisRelated:"Type 2 Diabetes",notes:"Monitor blood sugar regularly",endDate:"08/04/2025"}]};return e.jsxs("div",{children:[e.jsx("h2",{className:"mt-4 font-semibold text-center",children:"Current Medications"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:a.columns,rows:a.rows,loading:!t})})]})},we=({data:t})=>{const a={columns:[{title:"S.N",key:"sn"},{title:"Advice",key:"lifeStyleRecommendations"},{title:"Dietary Advice",key:"dietaryAdvice"},{title:"Exercise Suggestion",key:"exerciseSuggestion"},{title:"Precautionary Measures",key:"precautionaryMeasures"},{title:"Other Advice",key:"otherAdvices"}],rows:[{sn:1,lifeStyleRecommendations:" avoid screen time before bed.",dietaryAdvice:"increase fiber-rich foods.",exerciseSuggestion:"30-minute brisk walk daily.",precautionaryMeasures:" monitor blood pressure regularly.",otherAdvices:"Take medications as prescribed."},{sn:2,lifeStyleRecommendations:"Practice stress management through meditation.",dietaryAdvice:"High-protein. ",exerciseSuggestion:"Yoga or stretching for 20 mins.",precautionaryMeasures:"Avoid lifting heavy weights post-surgery.",otherAdvices:"Attend follow-up appointment in 1 week."},{sn:3,lifeStyleRecommendations:"Limit alcohol and quit smoking.",dietaryAdvice:"increase intake of green vegetables.",exerciseSuggestion:"Cycling or swimming 3 times a week.",precautionaryMeasures:"Use mask in polluted areas",otherAdvices:"Report any adverse effects of medication immediately."}]};return e.jsxs("div",{children:[e.jsx("h2",{className:"mt-4 font-semibold text-center",children:"Advice"}),e.jsx(d,{rows:a.rows,columns:a.columns,loading:!1})]})},ke=({data:t})=>{const a=[{visitNo:"001",complaint:"Patient complains of persistent headache for the past 3 days.",date:"25 May 2024"},{visitNo:"002",complaint:"Reports chest discomfort and shortness of breath on exertion.",date:"18 March 2024"},{visitNo:"003",complaint:"Complains of lower abdominal pain and nausea.",date:"14 August 2023"},{visitNo:"004",complaint:"Complains of general weakness and fatigue.",date:"21 July 2023"}];return e.jsx("div",{className:"space-y-4",children:a.map((s,l)=>e.jsxs("div",{className:"bg-white border border-gray-300 rounded-md p-3 shadow-sm",children:[e.jsx("div",{className:"flex justify-between items-center mb-2",children:e.jsxs("span",{className:"text-sm font-semibold text-gray-700",children:["Visit No: ",s.visitNo]})}),e.jsx("p",{className:"text-sm text-gray-800 leading-relaxed",children:s.complaint}),e.jsx("div",{className:"flex justify-end mt-2",children:e.jsx("span",{className:"text-xs text-blue-600 font-medium",children:s.date||"21 August 2023"})})]},s.visitNo||l))})},Se=()=>{const t={columns:[{title:"S.N",key:"serialNumber"},{title:"Date",key:"date"},{title:"Attendance",key:"attendance"},{title:"Type",key:"type"},{title:"Category",key:"category"},{title:"Diagnosis",key:"diagnosis"},{title:"ICD-10",key:"icd10"},{title:"State",key:"state"},{title:"Adverse Effect",key:"adverseEffect"}],rows:[{serialNumber:1,date:"2025-07-04",attendance:"Present",type:"Outpatient",category:"General Medicine",diagnosis:"Acute pharyngitis",icd10:"J02.9",state:"Recovered",adverseEffect:"None"},{serialNumber:2,date:"2025-07-03",attendance:"Present",type:"Inpatient",category:"Cardiology",diagnosis:"Hypertensive heart disease",icd10:"I11.0",state:"Under Treatment",adverseEffect:"Mild dizziness from medication"},{serialNumber:3,date:"2025-07-01",attendance:"Absent",type:"Follow-up",category:"Endocrinology",diagnosis:"Type 2 diabetes mellitus",icd10:"E11.9",state:"Stable",adverseEffect:"None reported"}]};return e.jsxs("div",{children:[e.jsx("h2",{className:"mt-4 font-semibold text-center",children:"Diagnosis"}),e.jsx(d,{columns:t.columns,rows:t.rows,loading:!1}),";"]})},Te=({data:t})=>{const a={columns:[{title:"S.N",key:"test"},{title:"Date",key:"date"},{title:"Department",key:"department"},{title:"Special Instruction",key:"specialInstruction"},{title:"Priority",key:"priority"}],rows:[{test:1,date:"2025-07-04",department:"Radiology",specialInstruction:"Patient should be fasting for 8 hours",priority:"High"},{test:2,date:"2025-07-03",department:"Pathology",specialInstruction:"Collect blood sample before 10 AM",priority:"Medium"},{test:3,date:"2025-07-02",department:"Cardiology",specialInstruction:"Avoid caffeine 24 hours prior",priority:"Urgent"},{test:4,date:"2025-07-01",department:"Neurology",specialInstruction:"Bring previous MRI reports",priority:"Low"}]};return e.jsxs("div",{children:[e.jsx("h2",{className:"mt-4 font-semibold text-center",children:"Lab Test List"}),e.jsx(d,{rows:a.rows,columns:a.columns,loading:!1}),";"]})},Ce=({data:t})=>{console.log(t,"hdfsdf");const a={columns:[{title:"S.N",key:"id"},{title:"Medicine",key:"name"},{title:"Dosage",key:"doses"},{title:"Frequency",key:"frequency"},{title:"Food Relation",key:"duration"},{title:"Route",key:"route"},{title:"Prescribe Date",key:"prescribedDate"}],rows:[{id:1,name:"Paracetamol",doses:"500 mg",frequency:"3 times/day",duration:"After food",route:"Oral",prescribedDate:"2025-07-04"},{id:2,name:"Amoxicillin",doses:"250 mg",frequency:"2 times/day",duration:"Before food",route:"Oral",prescribedDate:"2025-07-03"},{id:3,name:"Insulin",doses:"10 units",frequency:"Before breakfast",duration:"Empty stomach",route:"Subcutaneous",prescribedDate:"2025-07-02"},{id:4,name:"Omeprazole",doses:"20 mg",frequency:"Once daily",duration:"Before meals",route:"Oral",prescribedDate:"2025-07-01"}]};return e.jsxs("div",{children:[e.jsx("h2",{className:"mt-4 font-semibold text-center",children:"Prescription Medicine"}),e.jsx(d,{columns:a.columns,rows:a.rows,loading:!t})]})},De=["Vital signs","Allergies","Current Medications","Presenting Complaints","Diagnosis","Lab Tests","Prescription Medicine","Advice"],Pe=({data:t})=>{const[a,s]=u.useState("Vital signs"),l=()=>{switch(a){case"Vital signs":return e.jsx("div",{children:e.jsx(ye,{data:t})});case"Allergies":return e.jsx("div",{children:e.jsx(Ne,{data:t})});case"Current Medications":return e.jsx("div",{children:e.jsx(je,{data:t})});case"Presenting Complaints":return e.jsx("div",{children:e.jsx(ke,{data:t})});case"Diagnosis":return e.jsx("div",{children:e.jsx(Se,{})});case"Lab Tests":return e.jsx("div",{children:e.jsx(Te,{data:t})});case"Prescription Medicine":return e.jsx("div",{children:e.jsx(Ce,{data:t})});case"Advice":return e.jsx("div",{children:e.jsx(we,{data:t})})}};return e.jsxs("div",{className:"flex h-screen bg-[#F8F8F8]",children:[e.jsx(C,{tabs:De,defaultTab:"Vital signs",onTabChange:s}),e.jsx("div",{className:"flex-1 overflow-auto",children:l()})]})},Be="/Nurseicon/stethoscope_7663491.png",Ae="/Nurseicon/file_2274790.png",Re="/Nurseicon/medical-folder_5559808.png",z={columns:[{title:"Medicine Name",key:"medicineName"},{title:"Dose",key:"dose"},{title:"Route",key:"route"},{title:"Scheduled Time",key:"scheduledTime"},{title:"Given Time",key:"givenTime"},{title:"Nurse",key:"nurse"},{title:"Status",key:"status"},{title:"Reason",key:"reason"}],rows:[{medicineName:"Paracetamol",dose:"500 mg",route:"Oral",scheduledTime:"12:00 PM",givenTime:"12:10 PM",nurse:"Rita Rai",status:"Given",reason:"---"},{medicineName:"Amoxicillin",dose:"250 mg",route:"Oral",scheduledTime:"9:00 AM",givenTime:"9:05 AM",nurse:"Sunita Lama",status:"Given",reason:"---"},{medicineName:"Pantoprazole",dose:"40 mg",route:"IV",scheduledTime:"3:00 PM",givenTime:"---",nurse:"Kiran Thapa",status:"Not Given",reason:"Patient Refused"},{medicineName:"Insulin",dose:"10 units",route:"Subcutaneous",scheduledTime:"8:00 AM",givenTime:"8:05 AM",nurse:"Bikash Rai",status:"Given",reason:"---"},{medicineName:"Ondansetron",dose:"4 mg",route:"Oral",scheduledTime:"11:30 AM",givenTime:"---",nurse:"Sita Gurung",status:"Missed",reason:"Patient NPO"}]},Me=()=>{const t={medicineName:"",scheduledTime:"",givenTime:"",dose:"",route:"",status:"",reason:""},a=j({medicineName:r().required("Medicine Name is required"),scheduledTime:r().required("Scheduled Time is required"),givenTime:r().required("Given Time is required"),dose:r().required("Dose is required"),route:r().required("Route is required"),status:r().required("Status is required"),reason:r().when("status",{is:"Not Given",then:l=>l.required("Reason is required if medication is omitted"),otherwise:l=>l.notRequired()})}),s=v({initialValues:t,validationSchema:a,onSubmit:(l,{resetForm:o})=>{console.log("Medication Administration Form submitted",l),o()}});return e.jsxs("div",{className:"max-w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Administer Medication"}),e.jsxs("form",{onSubmit:s.handleSubmit,className:"w-full p-4 mb-8 space-y-4 border rounded-md",children:[e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"medicineName",className:"whitespace-nowrap",children:"Medicine Name"}),e.jsx("input",{type:"text",id:"medicineName",name:"medicineName",placeholder:"Medicine Name",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.medicineName})]}),s.touched.medicineName&&s.errors.medicineName&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.medicineName})]}),e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"scheduledTime",className:"whitespace-nowrap",children:"Scheduled Time"}),e.jsx("input",{type:"time",id:"scheduledTime",name:"scheduledTime",placeholder:"Scheduled Time",className:"w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-200",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.scheduledTime})]}),s.touched.scheduledTime&&s.errors.scheduledTime&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.scheduledTime})]}),e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"givenTime",className:"whitespace-nowrap",children:"Given Time"}),e.jsx("input",{type:"time",id:"givenTime",name:"givenTime",placeholder:"Given time",className:"w-full p-1.5 border text-[#3a3a3a] border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.givenTime})]}),s.touched.givenTime&&s.errors.givenTime&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.givenTime})]})]}),e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"dose",className:"whitespace-nowrap",children:"Dose"}),e.jsx("input",{type:"text",id:"dose",name:"dose",placeholder:"Dose",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.dose})]}),s.touched.dose&&s.errors.dose&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.dose})]}),e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"route",className:"whitespace-nowrap",children:"Route"}),e.jsxs("select",{id:"route",name:"route",className:"w-full p-1.5 border text-[#3a3a3a] border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.route,children:[e.jsx("option",{value:"",className:"text-[#3a3a3a]",children:"Status"}),e.jsx("option",{value:"Oral",children:"Oral"}),e.jsx("option",{value:"Injection",children:"Injection"}),e.jsx("option",{value:"IV",children:"IV"}),e.jsx("option",{value:"Topical",children:"Topical"}),e.jsx("option",{value:"Other",children:"Other"})]})]}),s.touched.route&&s.errors.route&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.route})]}),e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"status",className:"whitespace-nowrap",children:"Status"}),e.jsxs("select",{id:"status",name:"status",className:"w-full p-1.5  text-[#3a3a3a] border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.status,children:[e.jsx("option",{value:"",className:"text-[#3a3a3a]",children:"Status"}),e.jsx("option",{value:"Given",children:"Given"}),e.jsx("option",{value:"Not Given",children:"Not Given"})]})]}),s.touched.status&&s.errors.status&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.status})]})]}),e.jsxs("div",{className:"grid items-end grid-cols-12 gap-4",children:[e.jsx("div",{className:"col-span-9 flex flex-col gap-1",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"reason",className:"whitespace-nowrap",children:"Reason"}),e.jsxs("div",{className:"flex flex-col w-full",children:[e.jsx("input",{id:"reason",name:"reason",placeholder:"Write Reason if Omitted",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.reason}),s.touched.reason&&s.errors.reason&&e.jsx("p",{className:"text-xs text-red-500 mt-1",children:s.errors.reason})]})]})}),e.jsx("div",{className:"flex items-end justify-end h-full col-span-3",children:e.jsxs("button",{type:"submit",className:"flex items-center justify-center px-6 py-2 gap-3 text-white text-md bg-[#116aef] rounded-lg hover:bg-blue-700 h-fit",children:[e.jsx(g,{icon:"fa6-solid:floppy-disk",width:"18",height:"18",color:"white"}),"Save"]})})]})]}),e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Recently Taken Medicine"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:z.columns,rows:z.rows,loading:!1})})]})},U={columns:[{title:"Vaccine Name",key:"vaccineName"},{title:"Batch No.",key:"batchNo"},{title:"Dose No.",key:"doseNo"},{title:"Route",key:"route"},{title:"Site",key:"site"},{title:"Status",key:"status"},{title:"Remarks",key:"remarks"}],rows:[{vaccineName:"Covid-19",batchNo:"CVX-12345",doseNo:"1st dose",route:"Intramuscular",site:"Left Deltoid",status:"Given",remarks:"Mild soreness reported at site"},{vaccineName:"Hepatitis B",batchNo:"HBV-22311",doseNo:"2nd dose",route:"Intramuscular",site:"Right Deltoid",status:"Given",remarks:"No adverse effects"},{vaccineName:"Influenza",batchNo:"FLU-99881",doseNo:"Annual dose",route:"Intramuscular",site:"Left Thigh",status:"Given",remarks:"Slight redness observed"},{vaccineName:"Tetanus",batchNo:"TT-55678",doseNo:"Booster",route:"Intramuscular",site:"Right Arm",status:"Not Given",remarks:"Patient refused"},{vaccineName:"MMR",batchNo:"MMR-33902",doseNo:"1st dose",route:"Subcutaneous",site:"Left Upper Arm",status:"Missed",remarks:"Patient absent"}]},Fe=()=>{const t={vaccineName:"",batch:"",dose:"",route:"",site:"",status:"",remarks:""},a=j({vaccineName:r().required("Medicine Name is required"),batch:r().required("Batch Number required"),dose:r().required("Dose is required"),route:r().required("Route is required"),site:r().required("site is required"),status:r().required("Status is required"),remarks:r()}),s=v({initialValues:t,validationSchema:a,onSubmit:(l,{resetForm:o})=>{console.log("Medication Administration Form submitted",l),o()}});return e.jsxs("div",{className:"max-w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Administer Vaccination"}),e.jsxs("form",{onSubmit:s.handleSubmit,className:"w-full p-4 mb-8 space-y-4 border rounded-md",children:[e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"medicineName",className:"whitespace-nowrap",children:"Vaccine Name"}),e.jsx("input",{type:"text",id:"vaccineName",name:"vaccineName",placeholder:"Vaccine name",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.vaccineName})]}),s.touched.vaccineName&&s.errors.vaccineName&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.vaccineName})]}),e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"scheduledTime",className:"whitespace-nowrap",children:"Batch Number"}),e.jsx("input",{type:"text",id:"batch",name:"batch",placeholder:"Batch Number",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.batch})]}),s.touched.batch&&s.errors.batch&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.batch})]}),e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"givenTime",className:"whitespace-nowrap",children:"Dose No."}),e.jsx("input",{type:"text",id:"dose",name:"dose",placeholder:"Dose",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.dose})]}),s.touched.dose&&s.errors.dose&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.dose})]})]}),e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"route",className:"whitespace-nowrap",children:"Route"}),e.jsxs("select",{id:"route",name:"route",className:"w-full p-1.5 text-[#3a3a3a] border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.route,children:[e.jsx("option",{value:"",className:"text-[#3a3a3a]",children:"Status"}),e.jsx("option",{value:"Oral",children:"Oral"}),e.jsx("option",{value:"Injection",children:"Injection"}),e.jsx("option",{value:"IV",children:"IV"}),e.jsx("option",{value:"Topical",children:"Topical"}),e.jsx("option",{value:"Other",children:"Other"})]})]}),s.touched.route&&s.errors.route&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.route})]}),e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"route",className:"whitespace-nowrap",children:"Site"}),e.jsxs("select",{id:"site",name:"site",className:"w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.site,children:[e.jsx("option",{value:"",className:"text-[#3a3a3a]",children:"Site"}),e.jsx("option",{value:"Oral",children:"Oral"}),e.jsx("option",{value:"Injection",children:"Injection"})]})]}),s.touched.site&&s.errors.site&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.site})]}),e.jsxs("div",{className:"flex flex-col col-span-4 gap-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"status",className:"whitespace-nowrap",children:"Status"}),e.jsxs("select",{id:"status",name:"status",className:"w-full p-1.5  border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.status,children:[e.jsx("option",{value:"",children:"Status"}),e.jsx("option",{value:"Given",children:"Given"}),e.jsx("option",{value:"Not Given",children:"Not Given"})]})]}),s.touched.status&&s.errors.status&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.status})]})]}),e.jsxs("div",{className:"grid items-end grid-cols-12 gap-4",children:[e.jsx("div",{className:"col-span-9 flex flex-col gap-1",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"remarks",className:"whitespace-nowrap",children:"Remarks"}),e.jsxs("div",{className:"flex flex-col w-full",children:[e.jsx("input",{id:"remarks",name:"remarks",placeholder:"Write remarks",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.remarks}),s.touched.remarks&&s.errors.remarks&&e.jsx("p",{className:"text-xs text-red-500 mt-1",children:s.errors.remarks})]})]})}),e.jsx("div",{className:"flex items-end justify-end h-full col-span-3",children:e.jsxs("button",{type:"submit",className:"flex items-center justify-center px-6 py-2 gap-3 text-white text-md bg-[#116aef] rounded-lg hover:bg-blue-700 h-fit",children:[e.jsx(g,{icon:"fa6-solid:floppy-disk",width:"18",height:"18",color:"white"}),"Save"]})})]})]}),e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Recently Taken Vaccine"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:U.columns,rows:U.rows,loading:!1})})]})},W={columns:[{title:"Blood Product",key:"bloodProduct"},{title:"Blood Group",key:"bloodGroup"},{title:"Volume",key:"volume"},{title:"Time",key:"time"},{title:"Bag Number",key:"bagNumber"},{title:"Crossmatch",key:"crossmatch"},{title:"Pre-Vitals",key:"preVitals"},{title:"Post-Vitals",key:"postVitals"},{title:"Remarks",key:"remarks"}],rows:[{id:"1",bloodProduct:"Packed RBC",bloodGroup:"A+",volume:"300 ml",time:"9:20 PM",bagNumber:"B12345",crossmatch:"Compatible",preVitals:"T: 36.5°C BP: 120/80 HR: 80",postVitals:"T: 36.6°C BP: 118/76 HR: 82",remarks:"Well tolerated"},{id:"2",bloodProduct:"Platelets",bloodGroup:"B+",volume:"200 ml",time:"2:00 PM",bagNumber:"B67890",crossmatch:"Compatible",preVitals:"T: 37.0°C BP: 110/70 HR: 78",postVitals:"T: 37.1°C BP: 108/68 HR: 80",remarks:"Mild headache post-transfusion"},{id:"3",bloodProduct:"Fresh Frozen Plasma",bloodGroup:"O+",volume:"250 ml",time:"5:45 PM",bagNumber:"B54321",crossmatch:"Compatible",preVitals:"T: 36.7°C BP: 115/75 HR: 85",postVitals:"T: 36.8°C BP: 117/78 HR: 86",remarks:"No adverse reaction observed"},{id:"4",bloodProduct:"Whole Blood",bloodGroup:"AB+",volume:"450 ml",time:"11:30 AM",bagNumber:"B98765",crossmatch:"Compatible",preVitals:"T: 36.4°C BP: 125/85 HR: 90",postVitals:"T: 36.5°C BP: 122/80 HR: 88",remarks:"Good response, no issues"}]},Ie=()=>{const t={bloodProduct:"",bloodGroup:"",volume:"",bagNumber:"",crossmatch:"",time:"",tempPre:"",bpPre:"",pulsePre:"",tempPost:"",bpPost:"",pulsePost:"",status:"",remarks:"",reaction:""},a=j({bloodProduct:r().required("Blood Product is required"),bloodGroup:r().required("Blood Group is required"),volume:r().required("Volume is required"),bagNumber:r().required("Bag Number is required"),crossmatch:r().required("Crossmatch status is required"),time:r().required("Time is required"),tempPre:r().required("Pre-transfusion Temperature is required"),bpPre:r().required("Pre-transfusion BP is required"),pulsePre:r().required("Pre-transfusion Pulse is required"),tempPost:r().required("Post-transfusion Temperature is required"),bpPost:r().required("Post-transfusion BP is required"),pulsePost:r().required("Post-transfusion Pulse is required"),status:r().required("Status is required"),remarks:r(),reaction:r()}),s=v({initialValues:t,validationSchema:a,onSubmit:(l,{resetForm:o})=>{console.log("Blood Administration Form submitted",l),o()}});return e.jsxs("div",{className:"max-w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Administer Blood"}),e.jsxs("form",{onSubmit:s.handleSubmit,className:"w-full p-4 mb-8 space-y-4 border rounded-md",children:[e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"bloodProduct",className:"whitespace-nowrap",children:"Blood Product"}),e.jsxs("select",{id:"bloodProduct",name:"bloodProduct",className:"w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.bloodProduct,children:[e.jsx("option",{value:"",className:"text-[#3a3a3a] ",children:"Blood Product"}),e.jsx("option",{value:"Packed RBC",children:"Packed RBC"}),e.jsx("option",{value:"Plasma",children:"Plasma"}),e.jsx("option",{value:"Platelets",children:"Platelets"}),e.jsx("option",{value:"Cryoprecipitate",children:"Cryoprecipitate"}),e.jsx("option",{value:"Whole Blood",children:"Whole Blood"})]})]}),s.touched.bloodProduct&&s.errors.bloodProduct&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.bloodProduct})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"bloodGroup",className:"whitespace-nowrap",children:"Blood Group"}),e.jsxs("select",{id:"bloodGroup",name:"bloodGroup",className:"w-full p-2 border text-[#3a3a3a]  border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.bloodGroup,children:[e.jsx("option",{value:"",className:"text-[#3a3a3a] ",children:"Select Blood Group"}),e.jsx("option",{value:"A+",children:"A+"}),e.jsx("option",{value:"A-",children:"A-"}),e.jsx("option",{value:"B+",children:"B+"}),e.jsx("option",{value:"B-",children:"B-"}),e.jsx("option",{value:"AB+",children:"AB+"}),e.jsx("option",{value:"AB-",children:"AB-"}),e.jsx("option",{value:"O+",children:"O+"}),e.jsx("option",{value:"O-",children:"O-"})]})]}),s.touched.bloodGroup&&s.errors.bloodGroup&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.bloodGroup})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"volume",className:"whitespace-nowrap",children:"Volume"}),e.jsx("input",{type:"text",id:"volume",name:"volume",placeholder:"Given Volume",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.volume})]}),s.touched.volume&&s.errors.volume&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.volume})]})]}),e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"bagNumber",className:"whitespace-nowrap",children:"Bag Number"}),e.jsx("input",{type:"text",id:"bagNumber",name:"bagNumber",placeholder:"Donor / Bag Number",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.bagNumber})]}),s.touched.bagNumber&&s.errors.bagNumber&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.bagNumber})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"crossmatch",className:"whitespace-nowrap",children:"Crossmatch"}),e.jsxs("select",{id:"crossmatch",name:"crossmatch",className:"w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.crossmatch,children:[e.jsx("option",{value:"",className:"text-[#3a3a3a] ",children:"Status"}),e.jsx("option",{value:"Compatible",children:"Compatible"}),e.jsx("option",{value:"Incompatible",children:"Incompatible"}),e.jsx("option",{value:"Pending",children:"Pending"})]})]}),s.touched.crossmatch&&s.errors.crossmatch&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.crossmatch})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"time",className:"whitespace-nowrap",children:"Time"}),e.jsx("input",{type:"time",id:"time",name:"time",placeholder:"Given Time",className:"w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.time})]}),s.touched.time&&s.errors.time&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.time})]})]}),e.jsx("h3",{className:"col-span-12 mt-4 font-semibold text-center text-gray-700 underline text-md",children:"Pre-transfusion Vitals"}),e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"tempPre",className:"whitespace-nowrap",children:"Temp"}),e.jsx("input",{type:"text",id:"tempPre",name:"tempPre",placeholder:"Temperature",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.tempPre})]}),s.touched.tempPre&&s.errors.tempPre&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.tempPre})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"bpPre",className:"whitespace-nowrap",children:"BP"}),e.jsx("input",{type:"text",id:"bpPre",name:"bpPre",placeholder:"Blood Pressure",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.bpPre})]}),s.touched.bpPre&&s.errors.bpPre&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.bpPre})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"pulsePre",className:"whitespace-nowrap",children:"Pulse"}),e.jsx("input",{type:"text",id:"pulsePre",name:"pulsePre",placeholder:"Heart Rate",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.pulsePre})]}),s.touched.pulsePre&&s.errors.pulsePre&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.pulsePre})]})]}),e.jsx("h3",{className:"col-span-12 mt-4 font-semibold text-center text-gray-700 underline text-md",children:"Post-transfusion Vitals"}),e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"tempPost",className:"whitespace-nowrap",children:"Temp"}),e.jsx("input",{type:"text",id:"tempPost",name:"tempPost",placeholder:"Temperature",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.tempPost})]}),s.touched.tempPost&&s.errors.tempPost&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.tempPost})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"bpPost",className:"whitespace-nowrap",children:"BP"}),e.jsx("input",{type:"text",id:"bpPost",name:"bpPost",placeholder:"Blood Pressure",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.bpPost})]}),s.touched.bpPost&&s.errors.bpPost&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.bpPost})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"pulsePost",className:"whitespace-nowrap",children:"Pulse"}),e.jsx("input",{type:"text",id:"pulsePost",name:"pulsePost",placeholder:"Heart Rate",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.pulsePost})]}),s.touched.pulsePost&&s.errors.pulsePost&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.pulsePost})]})]}),e.jsxs("div",{className:"grid items-end grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"status",className:"whitespace-nowrap",children:"Status"}),e.jsxs("select",{id:"status",name:"status",className:"w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.status,children:[e.jsx("option",{value:"",className:"text-[#3a3a3a]",children:"Status"}),e.jsx("option",{value:"Complete",children:"Complete"}),e.jsx("option",{value:"In Progress",children:"In Progress"}),e.jsx("option",{value:"Aborted",children:"Aborted"})]})]}),s.touched.status&&s.errors.status&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.status})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-3",children:[e.jsx("label",{htmlFor:"reaction",className:" whitespace-nowrap",children:"Reaction"}),e.jsx("input",{id:"reaction",name:"reaction",placeholder:"Reaction Observed",className:"w-full p-2 border border-gray-300 rounded resize-none focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.reaction}),s.touched.reaction&&s.errors.reaction&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.reaction})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-5",children:[e.jsx("label",{htmlFor:"remarks",className:"whitespace-nowrap",children:"Remarks"}),e.jsx("textarea",{id:"remarks",name:"remarks",placeholder:"Remarks",rows:2,className:"w-full p-2 border border-gray-300 rounded resize-none focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.remarks}),s.touched.remarks&&s.errors.remarks&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.remarks})]}),e.jsx("div",{className:"flex items-end justify-end col-span-12",children:e.jsxs("button",{type:"submit",className:"flex items-center justify-center px-6 py-2 gap-3 text-white text-md bg-[#116aef] rounded-lg hover:bg-blue-700 h-fit",children:[e.jsx(g,{icon:"fa6-solid:floppy-disk",width:"18",height:"18",color:"white"}),"Save"]})})]})]}),e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Recent Blood Work"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:W.columns,rows:W.rows,loading:!1})})]})},_={columns:[{title:"Device Name",key:"deviceName"},{title:"Model",key:"model"},{title:"Size",key:"size"},{title:"Site",key:"site"},{title:"Apply Time",key:"applyTime"},{title:"Status",key:"status"},{title:"Remove Time",key:"removeTime"},{title:"Reason",key:"reason"},{title:"Observation",key:"observation"},{title:"Remarks",key:"remarks"}],rows:[{id:"1",deviceName:"Foley Catheter",model:"Bard Model X",size:"14 Fr",site:"Urethra",applyTime:"10:00 AM",status:"Applied",removeTime:"—",reason:"—",observation:"No irritation",remarks:"To be reviewed after 3 days"},{id:"2",deviceName:"Central Venous Catheter",model:"Arrow CVC-220",size:"7 Fr",site:"Right Subclavian",applyTime:"2:00 PM",status:"Applied",removeTime:"—",reason:"—",observation:"Site clean, no signs of infection",remarks:"Daily site inspection required"},{id:"3",deviceName:"Nasogastric Tube",model:"Ryles Tube 16",size:"16 Fr",site:"Nasal",applyTime:"8:30 AM",status:"Removed",removeTime:"5:00 PM",reason:"Completed feeding course",observation:"No complications",remarks:"Tube removed successfully"},{id:"4",deviceName:"Endotracheal Tube",model:"Portex ET 7.0",size:"7.0 mm",site:"Trachea",applyTime:"6:00 AM",status:"Applied",removeTime:"—",reason:"—",observation:"Breath sounds equal, tube secure",remarks:"Reposition to be checked every shift"}]},Ve=()=>{const t={deviceName:"",model:"",size:"",applyTime:"",site:"",status:"",removeTime:"",reason:"",observation:"",remarks:""},a=j({deviceName:r().required("Device Name is required"),model:r().required("Model is required"),size:r(),applyTime:r().required("Apply Time is required"),site:r().required("Site is required"),status:r().required("Status is required"),removeTime:r().nullable(),reason:r().when("status",{is:"Removed",then:l=>l.required("Reason is required if device is removed"),otherwise:l=>l.nullable()}),observation:r(),remarks:r()}),s=v({initialValues:t,validationSchema:a,onSubmit:(l,{resetForm:o})=>{console.log("Medical Device Administration Form submitted",l),o()}});return e.jsxs("div",{className:"max-w-full p-4 mx-auto",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Administer Medical Devices"}),e.jsxs("form",{onSubmit:s.handleSubmit,className:"w-full p-4 mb-8 space-y-4 border rounded-md",children:[e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"deviceName",className:"whitespace-nowrap",children:"Device Name"}),e.jsx("input",{type:"text",id:"deviceName",name:"deviceName",placeholder:"Device Name",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.deviceName})]}),s.touched.deviceName&&s.errors.deviceName&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.deviceName})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"model",className:"whitespace-nowrap",children:"Model"}),e.jsx("input",{type:"text",id:"model",name:"model",placeholder:"Model",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.model})]}),s.touched.model&&s.errors.model&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.model})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"size",className:"whitespace-nowrap",children:"Size"}),e.jsx("input",{type:"text",id:"size",name:"size",placeholder:"Size",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.size})]}),s.touched.size&&s.errors.size&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.size})]})]}),e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"applyTime",className:"whitespace-nowrap",children:"Apply Time"}),e.jsx("input",{type:"datetime-local",id:"applyTime",name:"applyTime",placeholder:"Applied Time",className:"w-full p-1.5 text-[#3a3a3a] border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.applyTime})]}),s.touched.applyTime&&s.errors.applyTime&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.applyTime})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"site",className:"whitespace-nowrap",children:"Site"}),e.jsx("input",{type:"text",id:"site",name:"site",placeholder:"Site",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.site})]}),s.touched.site&&s.errors.site&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.site})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"status",className:"whitespace-nowrap",children:"Status"}),e.jsxs("select",{id:"status",name:"status",className:"w-full p-1.5 text-[#3a3a3a] border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.status,children:[e.jsx("option",{value:"",className:"text-[#3a3a3a]",children:"Status"}),e.jsx("option",{value:"Applied",children:"Applied"}),e.jsx("option",{value:"In Use",children:"In Use"}),e.jsx("option",{value:"Removed",children:"Removed"})]})]}),s.touched.status&&s.errors.status&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.status})]})]}),e.jsxs("div",{className:"grid items-start grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"removeTime",className:"whitespace-nowrap",children:"Remove Time"}),e.jsx("input",{type:"datetime-local",id:"removeTime",name:"removeTime",placeholder:"Removed Time",className:"w-full p-1.5 text-[#3a3a3a] border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.removeTime})]}),s.touched.removeTime&&s.errors.removeTime&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.removeTime})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"reason",className:"whitespace-nowrap",children:"Reason"}),e.jsx("input",{type:"text",id:"reason",name:"reason",placeholder:"Reason",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.reason})]}),s.touched.reason&&s.errors.reason&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.reason})]}),e.jsxs("div",{className:"flex flex-col col-span-12 gap-1 md:col-span-6 lg:col-span-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{htmlFor:"observation",className:"whitespace-nowrap",children:"Observation"}),e.jsx("input",{type:"text",id:"observation",name:"observation",placeholder:"Observation",className:"w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.observation})]}),s.touched.observation&&s.errors.observation&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.observation})]})]}),e.jsxs("div",{className:"grid items-end grid-cols-12 gap-4",children:[e.jsxs("div",{className:"flex  col-span-12 gap-2 items-center md:col-span-9",children:[e.jsx("label",{htmlFor:"remarks",className:"whitespace-nowrap",children:"Remarks"}),e.jsx("textarea",{id:"remarks",name:"remarks",placeholder:"Remarks",rows:2,className:"w-full p-2 border border-gray-300 rounded resize-none focus:outline-none focus:border-blue-400",onChange:s.handleChange,onBlur:s.handleBlur,value:s.values.remarks}),s.touched.remarks&&s.errors.remarks&&e.jsx("p",{className:"text-xs text-red-500",children:s.errors.remarks})]}),e.jsx("div",{className:"flex items-end justify-end col-span-12 md:col-span-3",children:e.jsxs("button",{type:"submit",className:"flex items-center justify-center px-6 py-2 gap-3 text-white text-md bg-[#116aef] rounded-lg hover:bg-blue-700 h-fit",children:[e.jsx(g,{icon:"fa6-solid:floppy-disk",width:"18",height:"18",color:"white"}),"Save"]})})]})]}),e.jsx("h2",{className:"mb-4 text-lg font-semibold text-center",children:"Medical Devices History"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsx(d,{columns:_.columns,rows:_.rows,loading:!1})})]})},qe=["Administer Medication","Administer Vaccination","Administer Blood","Administer Medical Devices"],Ge=()=>{const[t,a]=u.useState("Administer Medication"),s=()=>{switch(t){case"Administer Medication":return e.jsx("div",{children:e.jsx(Me,{})});case"Administer Vaccination":return e.jsx("div",{children:e.jsx(Fe,{})});case"Administer Blood":return e.jsx("div",{children:e.jsx(Ie,{})});case"Administer Medical Devices":return e.jsx("div",{children:e.jsx(Ve,{})})}};return e.jsxs("div",{className:"flex h-screen bg-[#F8F8F8]",children:[e.jsx(C,{tabs:qe,defaultTab:"Administer Medication",onTabChange:a}),e.jsx("div",{className:"flex-1 overflow-auto",children:s()})]})},He=()=>e.jsx("div",{children:e.jsx(Ge,{})}),Oe=()=>{var D,P,B,A,R,M,F;function t(J){const w=new Date,k=new Date(J);let I=w.getFullYear()-k.getFullYear();const V=w.getMonth()-k.getMonth();return(V<0||V===0&&w.getDate()<k.getDate())&&I--,I}const{id:a}=se(),{data:s}=te(a);console.log(s,"USER DATA");const l=(P=(D=s==null?void 0:s.commonInfo)==null?void 0:D.personalInfo)==null?void 0:P.fullName,o=(B=s==null?void 0:s.patientInfo)==null?void 0:B.patientId,n=(R=(A=s==null?void 0:s.commonInfo)==null?void 0:A.personalInfo)==null?void 0:R.gender,c=n==="FEMALE"?"F":n==="MALE"?"M":"O",p=t((F=(M=s==null?void 0:s.commonInfo)==null?void 0:M.personalInfo)==null?void 0:F.dob);console.log(l,"USER NAME");const{data:h}=ae({user:a,isActive:!0},{enabled:!!a}),i=x.get(h,"data.patientHistory[0]",null);console.log(i,"patientDataCurrent");const[m,b]=u.useState("patient-info");T();const f=[{label:"Patient Information",image:Ae,value:"patient-info"},{label:"Consultations",image:Be,value:"consultations"},{label:"Medical Records",image:Re,value:"medical-records"},{label:"Treatment Records",image:re,value:"treatment-records"}],Y=()=>{switch(m){case"patient-info":return e.jsx(oe,{});case"consultations":return e.jsx(Pe,{data:i});case"medical-records":return e.jsx(fe,{data:i});case"treatment-records":return e.jsx(He,{});default:return null}};return e.jsxs("div",{className:"p-2 space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between max-w-full p-2 border-b border-gray-200",children:[e.jsx("h1",{className:"text-xl font-semibold text-gray-800",children:"Patient Details"}),e.jsxs("div",{className:"flex gap-8 text-sm",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-600",children:"Patient ID:"}),e.jsx("span",{className:"font-medium text-cyan-600",children:o})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-600",children:"Name:"}),e.jsx("span",{className:"font-medium text-cyan-600",children:l})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-gray-600",children:"Age/Gender:"}),e.jsx("span",{className:"font-medium text-cyan-600",children:`${p}/${c}`})]})]})]}),e.jsx(le,{tabs:f,defaultTab:"patient-info",onTabChange:b,showButton:!0,buttonTitle:"Add New",buttonAction:()=>console.log("Add New clicked")}),e.jsx("div",{children:Y()})]})};export{Oe as default};
