import{a1 as m,a2 as e,a7 as x,a8 as s,a9 as b,aa as h,ac as a,ab as u,bf as t}from"./index-ClX9RVH0.js";const g=()=>{const r=[{label:"Male",value:"male"},{label:"Female",value:"female"}],i=[{label:"Blood Group and Cross Match Done",value:"bloodGroupandCrossMatch"},{label:"Blood Tests (CBC, Clotting, etc.)",value:"bloodTest"},{label:"ECG done",value:"ecg"},{label:"Chest X-ray done",value:"chestX-ray"}],c=[{label:"No Allergies",value:"no"},{label:"Drug Allergy",value:"drug"},{label:"Food Allergy",value:"food"},{label:"Other",value:"other"}],d=[{label:"Patient Identity Verified",value:"patientIdentityVerified"},{label:"Correct Site and Procedure Complete",value:"complete"},{label:"Correct Site and Procedure Complete",value:"complete"},{label:"Correct Site and Procedure Complete",value:"complete"}],n=m({initialValues:{},enableReinitialize:!0,onSubmit:()=>{console.log("submmit")}});return e.jsxs("div",{children:[e.jsx(x,{listTitle:"Pre-Operative Checkllist Form",hideHeader:!0}),e.jsxs("div",{className:"flex gap-5",children:[e.jsx("div",{className:"h-auto w-[30%]",children:e.jsxs("div",{className:"flex flex-col gap-4 bg-white px-4 py-5",children:[e.jsx(s,{step:1,title:"Patient Information",isActive:!0}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(s,{step:2,title:"Pre-Surgical Assesment",isActive:!1}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(s,{step:3,title:"Investigation",isActive:!1}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(s,{step:4,title:"Final Check",isActive:!1})]})}),e.jsx("div",{className:"w-[70%] h-[85vh] overflow-y-auto",children:e.jsx(b,{value:n,children:e.jsxs(h,{className:"flex flex-col gap-5 ",children:[e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsx(a,{type:"text",label:"Full Name",placeholder:"Jane Smith",name:"fullname"}),e.jsx(a,{type:"text",label:"Address",placeholder:"Chabhil Kathmandu",name:"address"}),e.jsx(a,{type:"text",label:"Contact Number",placeholder:"+977 **********",name:"contactNumber"}),e.jsx(a,{type:"text",label:"Treatment",placeholder:"Fever",name:"treatment"}),e.jsx(a,{type:"text",label:"Age",placeholder:"Enter",name:"age "}),e.jsx(u,{required:!0,label:"Gender",options:r,name:"gender"})]}),e.jsxs("div",{className:"bg-white px-5 flex flex-col gap-5 py-5",children:[e.jsxs("div",{className:"bg-white py-5 ",children:[e.jsx("label",{className:"block text-sm font-semibold mb-2",children:"Allergies"}),e.jsx("div",{className:"flex gap-10",children:c.map(l=>e.jsxs("label",{className:"flex items-center space-x-2 cursor-pointer",children:[e.jsx(t,{type:"radio",name:"allergies",value:l.value,className:"w-4 h-4 text-blue-600 focus:ring-blue-500"}),e.jsx("span",{className:"text-gray-700",children:l.label})]},l.value))})]}),e.jsx(a,{type:"text",label:"Chronic Conditions",placeholder:"Remarks",name:"chronicConditions "}),e.jsx(a,{type:"text",label:"Previous Surgeries",placeholder:"Remarks",name:"previousSurgeries "})]}),e.jsx("div",{className:"bg-white  px-5",children:e.jsxs("div",{className:"bg-white py-5 ",children:[e.jsx("label",{className:"block text-sm font-medium mb-2",children:"Investigations"}),e.jsx("div",{className:"flex flex-col gap-4",children:i.map(l=>e.jsxs("label",{className:"flex items-center space-x-2 cursor-pointer",children:[e.jsx(t,{type:"radio",name:"investigation",value:l.value,className:"w-4 h-4 text-blue-600 focus:ring-blue-500"}),e.jsx("span",{className:"text-gray-700",children:l.label})]},l.value))})]})}),e.jsxs("div",{className:"bg-white py-8 px-5 flex flex-col gap-5",children:[e.jsx(a,{type:"text",label:"Vitals Recorded",placeholder:"Remarks",name:"vitalsRecorded"}),e.jsxs("div",{className:"bg-white",children:[e.jsx("label",{className:"block text-sm font-medium ",children:"Final Check"}),e.jsx("div",{className:"flex flex-col gap-4 my-3",children:d.map((l,o)=>e.jsxs("label",{className:"flex items-center space-x-2 cursor-pointer",children:[e.jsx(t,{type:"radio",name:"finalCheck",value:l.value,className:"w-4 h-4 text-blue-600 focus:ring-blue-500"}),e.jsx("span",{className:"text-gray-700",children:l.label})]},o))})]})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{type:"button",className:"bg-gray",children:"Cancel"}),e.jsx("button",{type:"submit",className:"bg-[#00717e] ",children:"Submit"})]})]})})})]})]})};export{g as default};
