import{a5 as b,a1 as y,al as h,a2 as e,a7 as g,am as v,a9 as j,aa as f,an as c,ac as a,ab as i,bf as u,ao as N}from"./index-ClX9RVH0.js";function D(){const[n,l]=b.useState(1),x=[{step:1,title:"Symptoms",isActive:n===1},{step:2,title:"Medical History",isActive:n===2},{step:3,title:"Test Request",isActive:n===3},{step:4,title:"Prescription",isActive:n===4},{step:5,title:"Refers",isActive:n===5},{step:6,title:"Advices",isActive:n===6}],m=y({initialValues:{referForEmergency1:"No",referForEmergency2:"No",referForSurgery:"No",symptoms:[{symptom:"",details:""}],appointments:[{department:"",testType:"",requestedDate:"",priority:""}],prescription:[{medicineName:"",dose:"",frequence:"",duration:"",condition:"",prescriptionNote:""}]},enableReinitialize:!0,onSubmit:t=>{h.success("Form submitted successfully!"),history.back(),console.log(t)}}),{handleSubmit:d,values:r}=m;return e.jsxs("div",{children:[e.jsx(g,{listTitle:"Cardiology",hideHeader:!0}),e.jsxs("div",{className:"relative flex w-full gap-6",children:[e.jsx("div",{className:"w-auto h-full",children:e.jsx(v,{steps:x,customStyle:"fixed"})}),e.jsx("div",{className:"xl:ml-[15rem] lg:ml-[14rem] md:ml-[13rem] ml-[12rem] w-full",children:e.jsx(j,{value:m,children:e.jsx(f,{onSubmit:d,children:e.jsxs("div",{className:"flex flex-col w-full gap-8 pb-4",children:[e.jsx("div",{className:"p-4 bg-white rounded-sm",onClick:()=>l(1),children:e.jsx(c,{name:"symptoms",children:({push:t,remove:o})=>e.jsx("div",{children:r.symptoms.map((p,s)=>e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-5",children:[e.jsx(a,{label:"Symptom",name:`symptoms.${s}.symptom`,type:"text",placeholder:"Enter symptom",onFocus:()=>l(1)}),e.jsx(a,{label:"Details",name:`symptoms.${s}.details`,type:"text",placeholder:"Details",onFocus:()=>l(1)})]}),e.jsxs("div",{className:"flex justify-center gap-4 mt-4",children:[r.symptoms.length>1&&e.jsxs("button",{type:"button",onClick:()=>o(s),className:"flex items-center px-4 py-2 transition-colors bg-gray-200 rounded-md hover:bg-gray-300",children:[e.jsx("span",{className:"mr-2",children:"−"})," Remove"]}),s===r.symptoms.length-1&&e.jsxs("button",{type:"button",onClick:()=>t({symptom:"",details:""}),className:"flex items-center px-4 py-2 text-white transition-colors rounded-md bg-primary",children:[e.jsx("span",{className:"mr-2",children:"+"})," Add Symptoms"]})]})]},s))})})}),e.jsx("div",{className:"p-4 bg-white rounded-sm",onClick:()=>l(2),children:e.jsxs("div",{className:"grid grid-cols-3 gap-5",children:[e.jsx(a,{label:"Known Allergies",type:"text",placeholder:"Enter",name:"Known Allergies",onFocus:()=>l(2)}),e.jsx(a,{label:"Chronic Diseases",type:"text",placeholder:"Enter",name:"allergies",onFocus:()=>l(2)}),e.jsx(a,{label:"Past Surgeries",type:"text",placeholder:"Enter",name:"pastSurgeries",onFocus:()=>l(2)}),e.jsx(a,{label:"Current Medications",type:"text",placeholder:"Enter",name:"currentMedications",onFocus:()=>l(2)})]})}),e.jsx("div",{className:"p-4 bg-white rounded-sm",onClick:()=>l(3),children:e.jsx(c,{name:"appointments",children:({push:t,remove:o})=>e.jsx("div",{children:r.appointments.map((p,s)=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-3 gap-5",children:[e.jsx(i,{required:!0,label:"Department",options:[{value:"Cardiology",label:"Cardiology"},{value:"Orthopedics",label:"Orthopedics"},{value:"Neurology",label:"Neurology"},{value:"Gastroenterology",label:"Gastroenterology"},{value:"Urology",label:"Urology"},{value:"Pediatrics",label:"Pediatrics"},{value:"Dermatology",label:"Dermatology"},{value:"Endocrinology",label:"Endocrinology"},{value:"Immunology",label:"Immunology"}],name:`appointment.${s}.department`,onFocus:()=>l(3)}),e.jsx(i,{required:!0,label:"Test Type",options:[{value:"Blood Test",label:"Blood Test"},{value:"Urinalysis",label:"Urinalysis"},{value:"X-Ray",label:"X-Ray"},{value:"ECG",label:"ECG"},{value:"MRI",label:"MRI"}],name:`appointment.${s}.testType`,onFocus:()=>l(3)}),e.jsx(a,{label:"Requested Date",type:"date",placeholder:"Enter",name:`appointment.${s}.requestedDate`,onFocus:()=>l(3)}),e.jsx(i,{required:!0,label:"Priority",options:[{value:"High",label:"High"},{value:"Medium",label:"Medium"},{value:"Low",label:"Low"}],name:`appointment.${s}.priority`,onFocus:()=>l(3)})]}),e.jsxs("div",{className:"flex justify-center gap-4 mt-4",children:[r.appointments.length>1&&e.jsxs("button",{type:"button",onClick:()=>o(s),className:"flex items-center px-4 py-2 transition-colors bg-gray-200 rounded-md hover:bg-gray-300",children:[e.jsx("span",{className:"mr-2",children:"−"})," Remove"]}),s===r.appointments.length-1&&e.jsxs("button",{type:"button",onClick:()=>t({department:"",testType:"",requestedDate:"",priority:""}),className:"flex items-center px-4 py-2 text-white transition-colors rounded-md bg-primary",children:[e.jsx("span",{className:"mr-2",children:"+"})," Add Test Type"]})]})]}))})})}),e.jsx("div",{className:"p-4 bg-white rounded-sm",onClick:()=>l(4),children:e.jsx(c,{name:"prescription",children:({push:t,remove:o})=>e.jsx("div",{children:r.prescription.map((p,s)=>e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-3 gap-5",children:[e.jsx(i,{required:!0,label:"Medicine name",options:[{value:"Paracetamol",label:"Paracetamol"},{value:"Aspirin",label:"Aspirin"},{value:"Ibuprofen",label:"Ibuprofen"},{value:"Acetaminophen",label:"Acetaminophen"},{value:"Tylenol",label:"Tylenol"}],name:`prescription${s}.medicineName`,onFocus:()=>l(4)}),e.jsx(i,{required:!0,label:"Dose",options:[{value:"1 tablet",label:"1 tablet"},{value:"2 tablets",label:"2 tablets"},{value:"3 tablets",label:"3 tablets"},{value:"1 capsule",label:"1 capsule"},{value:"2 capsules",label:"2 capsules"}],name:`prescription${s}.dose`,onFocus:()=>l(4)}),e.jsx(a,{label:"Frequence",type:"text",placeholder:"Enter",name:`prescription${s}.frequence`,onFocus:()=>l(4)}),e.jsx(a,{label:"Duration",type:"text",placeholder:"Enter",name:`prescription${s}.duration`,onFocus:()=>l(4)}),e.jsx(i,{required:!0,label:"Condition",options:[{value:"Before meals",label:"Before meals"},{value:"After meals",label:"After meals"},{value:"Before bed",label:"Before bed"},{value:"After bed",label:"After bed"},{value:"Everyday",label:"Everyday"}],name:`prescription${s}.condition`,onFocus:()=>l(4)}),e.jsx(a,{label:"Prescription Note",type:"text",placeholder:"Enter",name:`prescription${s}.prescriptionNote`,onFocus:()=>l(4)})]}),e.jsxs("div",{className:"flex justify-center gap-4 mt-4",children:[r.prescription.length>1&&e.jsxs("button",{type:"button",onClick:()=>o(s),className:"flex items-center px-4 py-2 transition-colors bg-gray-200 rounded-md hover:bg-gray-300",children:[e.jsx("span",{className:"mr-2",children:"−"})," Remove"]}),s===r.prescription.length-1&&e.jsxs("button",{type:"button",onClick:()=>t({medicineName:"",dose:"",frequence:"",duration:"",condition:"",prescriptionNote:""}),className:"flex items-center px-4 py-2 text-white transition-colors rounded-md bg-primary",children:[e.jsx("span",{className:"mr-2",children:"+"})," Add Medicine"]})]})]}))})})}),e.jsxs("div",{className:"flex flex-col gap-4 p-4 bg-white rounded-sm",onClick:()=>l(5),children:[e.jsxs("div",{className:"grid grid-cols-2 gap-5",children:[e.jsx(a,{label:"Refer to Department",type:"text",placeholder:"Enter",name:"Known Allergies",onFocus:()=>l(5)}),e.jsx(i,{required:!0,label:"Doctor Name",options:[{value:"Dr. John Doe",label:"Dr. John Doe"},{value:"Dr. Jane Doe",label:"Dr. Jane Doe"},{value:"Dr. Alex Doe",label:"Dr. Alex Doe"},{value:"Dr. Sarah Doe",label:"Dr. Sarah Doe"}],name:"refersDoctor",onFocus:()=>l(5)})]}),e.jsx("div",{className:"grid grid-cols-3 gap-5",children:[{label:"Refer for Emergency",name:"referForEmergency1"},{label:"Refer for Emergency",name:"referForEmergency2"},{label:"Refer for Surgery",name:"referForSurgery"}].map((t,o)=>e.jsxs("div",{className:"flex flex-col items-start",children:[e.jsx("label",{className:"font-medium",children:t.label}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsxs("label",{className:"flex items-center gap-1",children:[e.jsx(u,{type:"radio",name:t.name,value:"Yes",className:"w-5 h-5 text-red-600 form-checkbox accent-primary"}),"Yes"]}),e.jsxs("label",{className:"flex items-center gap-1",children:[e.jsx(u,{type:"radio",name:t.name,value:"No",className:"w-5 h-5 text-red-600 form-checkbox accent-primary dark:checked:bg-purple-400"}),"No"]})]})]},o))})]}),e.jsxs("div",{className:"flex flex-col gap-4 p-4 bg-white rounded-sm",onClick:()=>l(6),children:[e.jsxs("div",{className:"grid grid-cols-4 gap-5",children:[e.jsx(a,{label:"Lifestyle Recommendations",type:"text",placeholder:"Enter",name:"lifestyleRecommendations",onFocus:()=>l(6)}),e.jsx(a,{label:"Dietary Advice",type:"text",placeholder:"Enter",name:"dietaryAdvice",onFocus:()=>l(6)}),e.jsx(a,{label:"Exercise Suggestions",type:"text",placeholder:"Enter",name:"exerciseSuggestions",onFocus:()=>l(6)}),e.jsx(a,{label:"Precautionary Measures",type:"text",placeholder:"Enter",name:"precautionaryMeasures",onFocus:()=>l(6)})]}),e.jsx("div",{className:"w-full",children:e.jsx(a,{label:"Other Advise",type:"textarea",placeholder:"Enter",name:"otherAdvise",onFocus:()=>l(6)})})]}),e.jsx("div",{className:"flex flex-col gap-4 p-4 bg-white rounded-sm",onClick:()=>l(6),children:e.jsxs("div",{className:"grid grid-cols-2 gap-5",children:[e.jsx(a,{label:"Recommended Follow-Up Date",type:"date",placeholder:"Enter",name:"recommendedFollowUpDate",onFocus:()=>l(6)}),e.jsx(a,{label:"Reason for Revisit",type:"text",placeholder:"Enter",name:"reasonForRevisit",onFocus:()=>l(6)})]})}),e.jsx(N,{onCancel:()=>history.back(),onSubmit:d})]})})})})]})]})}export{D as AddCardiology};
