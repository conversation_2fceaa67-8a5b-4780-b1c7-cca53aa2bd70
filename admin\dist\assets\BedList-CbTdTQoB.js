import{a5 as n,aP as v,ae as N,a2 as e,af as f,ah as k,aV as C,aX as i,aQ as w,aj as B,bA as D,aR as P,bn as A,aa as S,a4 as F,ab as T,ac as r}from"./index-ClX9RVH0.js";const M=()=>{const[d,l]=n.useState(!1),[c,a]=n.useState(!1),m=v(()=>{a(!1)}),o={columns:[{title:"Bed No",key:"tokenid"},{title:"Bed Category ",key:"patientName"},{title:"Description",key:"doctorName"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:N.map(({tokenId:t,patientName:s,doctorName:h,status:y,treatment:g},j)=>({key:j,tokenid:t,patientName:s,doctorName:h,status:e.jsx(k,{status:y}),treatment:g,action:e.jsx(f,{onEdit:()=>{a(!0)},onDelete:()=>{l(!0)}})}))},u=()=>{a(!0)},x=C().shape({bedCategory:i().required("Bed Category is required"),bedNo:i().required("Bed number is required"),description:i().required("Description is required")}),b=t=>{console.log("Form Data Submitted:",t),a(!1)},p=()=>e.jsx(A,{initialValues:{bedCategory:"",bedNo:"",description:""},validationSchema:x,onSubmit:b,children:({errors:t,touched:s})=>e.jsx(S,{children:e.jsxs("div",{className:"p-6 relative",children:[e.jsx(F,{as:"h3",text:"Add Bed",size:"body-lg-lg",variant:"primary-blue",className:"mb-4 text-primary"}),e.jsx("div",{className:"absolute cursor-pointer hover:text-red  top-5 right-10",onClick:()=>a(!1),children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",children:e.jsx("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeWidth:2,d:"m8 8l4 4m0 0l4 4m-4-4l4-4m-4 4l-4 4"})})}),e.jsx("div",{children:e.jsxs("div",{className:" grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"Bed Category",className:"font-semibold text-lg",children:[" ","Bed Category",e.jsx("span",{className:"text-red ml-2",children:"*"})]}),e.jsx(T,{firstInput:"Select",options:[{value:"Full Payment",label:"Full Payment"},{value:"Partial Payment",label:"Partial Payment"},{value:"Advanced Payment",label:"Advanced Payment"}]}),t.bedCategory&&s.bedCategory&&e.jsx("div",{className:"text-red text-sm mb-2",children:t.bedCategory})]}),e.jsx(r,{label:"Bed No",type:"text",name:"bedNo",placeholder:"Auto",className:"mb-4 outline-none border p-3 rounded-md"}),t.bedNo&&s.bedNo&&e.jsx("div",{className:"text-red text-sm mb-2",children:t.bedNo})]})}),e.jsxs("div",{children:[e.jsx(r,{label:"Description ",type:"text",name:"description",placeholder:"Enter",className:"mb-4 outline-none border p-2 rounded-md"}),t.description&&s.description&&e.jsx("div",{className:"text-red text-sm mb-2",children:t.description})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx("button",{type:"button",className:"px-4 py-2 bg-gray-300 text-black rounded",onClick:()=>a(!1),children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary text-white rounded",children:"Create"})]})]})})});return e.jsxs(e.Fragment,{children:[e.jsx(w,{headerTitle:"Bed List",onSearch:!0,onFilter:!0,buttonText:"Add Bed",button:!0,buttonAction:u}),e.jsx(B,{columns:o.columns,rows:o.rows,loading:!1,color:"bg-white ",textcolor:"text-gray-400",pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}}),e.jsx(D,{confirmAction:d,title:"Do you want to delete this token record?",des:"This action cannot be undone.",onClose:()=>l(!1),onConfirm:()=>{l(!1)}}),c&&e.jsx(P,{ref:m,children:p()})]})};export{M as default};
