import{d4 as e,ba as n,a1 as c,aV as m,aX as r,a2 as a,a9 as b,aa as p,c7 as v,ao as E}from"./index-ClX9RVH0.js";const L=[{label:"Regular",value:e.REGULAR},{label:"Soft",value:e.SOFT},{label:"Pured",value:e.PUREED},{label:"Liquid",value:e.LIQUID},{label:"Clear Liquid",value:e.CLEAR_LIQUID},{label:"Low Sodium",value:e.LOW_SODIUM},{label:"Low Fat",value:e.LOW_FAT},{label:"Diabetic",value:e.DIABETIC},{label:"Renal",value:e.RENAL},{label:"Cardiac",value:e.CARDIAC},{label:"High Protein",value:e.HIGH_PROTEIN},{label:"Low Residue",value:e.LOW_RESIDUE},{label:"Gluten Free",value:e.GLUTEN_FREE},{label:"Lactose Free",value:e.LACTOSE_FREE},{label:"Vegetarian",value:e.VEGETARIAN},{label:"Vegan",value:e.VEGAN},{label:"Keto",value:e.KETO}],F=()=>{const{id:i}=n(),s=[{type:"select",field:"dietType",label:"Diet Type",options:L,required:!0,className:"col-span-2"},{type:"multi-value",field:"targetedPatients",label:"Targeted Patients",options:[],required:!0,placeholder:"Add Targeted Patients",className:"col-span-2"}],l=c({initialValues:{},validationSchema:m({categoryName:r().required("Ward name is required"),department:r().required("Department is required"),headOfWard:r().required("Head of ward is required")}),onSubmit:async d=>{try{i&&console.log(d)}catch{}}}),{handleSubmit:t,errors:o,touched:u}=l;return a.jsx("div",{className:"p-4",children:a.jsx(b,{value:l,children:a.jsxs(p,{onSubmit:t,className:"flex flex-col gap-5 w-full ",children:[a.jsx("section",{className:"grid grid-cols-2  place-items-center w-full   gap-5",children:a.jsx(v,{formDatails:s,getFieldProps:l.getFieldProps,errors:o,touched:u})}),a.jsx("div",{className:"col-span-3",children:a.jsx(E,{onCancel:()=>{l.resetForm()},onSubmit:t})})]})})})};export{F as AddDietPlan};
