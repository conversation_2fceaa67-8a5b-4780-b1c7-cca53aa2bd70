import { Icon } from "@iconify/react/dist/iconify.js";
import { useFormik } from "formik";
// import * as Yup from "yup"; // for adding validation schema using Yup

const HistoryAndExaminations = () => {
  const formik = useFormik({
    initialValues: {
      history1: "",
      history2: "",
      history3: "",
      history4: "",
    },
    validationSchema: "",
    onSubmit: (values) => {
      console.log("Form submitted", values);
    },
  });

  return (
    <div className='w-full p-4 mx-auto'>
      <h2 className='mb-4 font-semibold text-center'>
        History and Examination
      </h2>

      <form
        onSubmit={formik.handleSubmit}
        className='grid w-full grid-cols-2 p-4 mt-6 border rounded-md gap-x-6 gap-y-4 bg-[#e0e0e0]'
      >
        {/* Input 1 */}
        <div className='flex flex-col col-span-2 gap-1'>
          <div className='grid grid-cols-[250px_1fr] gap-2 items-center'>
            <label className='p-2 text-sm font-medium border bg-[#f2f2f2] rounded-md'>
              History of Presenting Complaint 1
            </label>
            <input
              name='history1'
              placeholder=''
              className='w-full p-1.5 border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.history1}
              onBlur={formik.handleBlur}
            />
          </div>
          {formik.touched.history1 && formik.errors.history1 && (
            <p className='text-xs text-red-500'>{formik.errors.history1}</p>
          )}
        </div>

        {/* Input 2 */}
        <div className='flex flex-col col-span-2 gap-1'>
          <div className='grid grid-cols-[250px_1fr] gap-2 items-center'>
            <label className='p-2 text-sm font-medium border bg-[#f2f2f2] rounded-md'>
              History of Presenting Complaint 2
            </label>
            <input
              name='history2'
              placeholder=''
              className='w-full p-1.5  border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.history2}
              onBlur={formik.handleBlur}
            />
          </div>
          {formik.touched.history2 && formik.errors.history2 && (
            <p className='text-xs text-red-500'>{formik.errors.history2}</p>
          )}
        </div>

        {/* Input 3 */}
        <div className='flex flex-col col-span-2 gap-1'>
          <div className='grid grid-cols-[250px_1fr] gap-2 items-center'>
            <label className='p-2 text-sm font-medium border bg-[#f2f2f2] rounded-md'>
              History of Presenting Complaint 3
            </label>
            <input
              name='history3'
              placeholder=''
              className='w-full p-1.5  border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.history3}
              onBlur={formik.handleBlur}
            />
          </div>
          {formik.touched.history3 && formik.errors.history3 && (
            <p className='text-xs text-red-500'>{formik.errors.history3}</p>
          )}
        </div>

        {/* Input 4 */}
        <div className='flex flex-col col-span-2 gap-1'>
          <div className='grid grid-cols-[250px_1fr] gap-2 items-center'>
            <label className='p-2 text-sm font-medium border bg-[#f2f2f2] rounded-md'>
              History of Presenting Complaint 4
            </label>
            <input
              name='history4'
              placeholder=''
              className='w-full p-1.5  border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.history4}
              onBlur={formik.handleBlur}
            />
          </div>
          {formik.touched.history4 && formik.errors.history4 && (
            <p className='text-xs text-red-500'>{formik.errors.history4}</p>
          )}
        </div>

        {/* Save Button */}
        <div className='flex justify-end col-span-2'>
          <button
            type='submit'
            className='bg-blue-600 hover:bg-blue-700 text-white bg-[#116aef] px-4 py-1.5 rounded-md flex items-center gap-1'
          >
            <Icon
              icon='fa6-solid:floppy-disk'
              width='18'
              height='18'
              color='white'
            />
            Save
          </button>
        </div>
      </form>
    </div>
  );
};

export default HistoryAndExaminations;
