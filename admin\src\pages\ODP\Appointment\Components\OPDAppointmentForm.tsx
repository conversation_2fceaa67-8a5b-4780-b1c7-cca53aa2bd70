import React, { useState } from "react";
import Doctor<PERSON>electionStep from "./DoctorSelectionStep";
import AppointmentDetailsStep from "./AppointmentDetailsStep";
import PaymentDetailsStep from "./PaymentDetailsStep";
import StepCards from "./StepCards";
import { StepperNavigation } from "./StepperNavigation";
import PatientInformationStep from "./PatientInformationStep";
import { useCreateUser } from "../../../../server-action/api/auth";

export interface StepData {
  patientInfo?: any;
  doctorInfo?: any;
  appointmentInfo?: any;
  paymentInfo?: any;
}

const OPDAppointmentForm: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [stepData, setStepData] = useState<StepData>({});

  const { mutateAsync: createNewPatient } = useCreateUser();

  const steps = [
    {
      id: 1,
      title: "Patient Information",
      subtitle: "Personal & Medical Details",
      icon: "mdi:account",
      color: "teal",
    },
    {
      id: 2,
      title: "Doctor Info",
      subtitle: "Choose Department & Doctor",
      icon: "mdi:doctor",
      color: "red",
    },
    {
      id: 3,
      title: "Appointment",
      subtitle: "Choose Date, Reason & Priority",
      icon: "mdi:calendar",
      color: "blue",
    },
    {
      id: 4,
      title: "Payment",
      subtitle: "Billing & Insurance",
      icon: "mdi:credit-card",
      color: "purple",
    },
  ];

  const handleStepComplete = (step: number, data: any) => {
    const updatedStepData = {
      ...stepData,
      [`${getStepDataKey(step)}`]: data,
    };

    setStepData(updatedStepData);

    if (step < 4) {
      setCurrentStep(step + 1);
    } else {
      handleFinalSubmission(updatedStepData);
    }
  };

  const handleFinalSubmission = async (allStepData: StepData) => {
    try {
      // Combine all step data into NewPatientForm format
      const formattedData = {
        role: "PATIENT",
        email: allStepData.patientInfo?.email || "",
        password: "patient@123",
        isActive: true,
        FCMToken: "",
        commonInfo: {
          generalDescription: "",
          personalInfo: {
            fullName: allStepData.patientInfo?.fullName || "",
            gender: allStepData.patientInfo?.gender || "",
            dob: allStepData.patientInfo?.dob || "",
            language: allStepData.patientInfo?.language || "",
            bloodGroup: allStepData.patientInfo?.bloodGroup || "",
            maritalStatus: allStepData.patientInfo?.maritalStatus || "",
          },
          contactInfo: {
            phone: {
              primaryPhone: allStepData.patientInfo?.contactNumber || "",
              secondaryPhone:
                allStepData.patientInfo?.alternatePhoneNumber || "",
            },
            address: {
              currentAddress: allStepData.patientInfo?.currentAddress || "",
              permanentAddress: allStepData.patientInfo?.permanentAddress || "",
            },
          },
        },

        appointInfo: {
          date: allStepData.doctorInfo?.appointmentDate || "",
          timeSlot: allStepData.doctorInfo?.doctorDutyTime || "",
          doctor: allStepData.doctorInfo?.doctor || "",
          status: "PENDING",
          department: allStepData.doctorInfo?.department || "",
          remark: allStepData.appointmentInfo?.notes || "",
          paymentMethod: allStepData.paymentInfo?.paymentMethod || "CASH",
          bank: allStepData.paymentInfo?.bankName || undefined,
          transactionNo: allStepData.paymentInfo?.transactionId || undefined,
        },
      };

      await createNewPatient(formattedData);
      setCurrentStep(1);
      setStepData({});
    } catch (error) {
      console.error("Error creating appointment:", error);
    }
  };

  const handleStepEdit = (step: number) => {
    setCurrentStep(step);
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepDataKey = (step: number) => {
    switch (step) {
      case 1:
        return "patientInfo";
      case 2:
        return "doctorInfo";
      case 3:
        return "appointmentInfo";
      case 4:
        return "paymentInfo";
      default:
        return "patientInfo";
    }
  };

  const isStepCompleted = (step: number) => {
    const dataKey = getStepDataKey(step);
    return !!stepData[dataKey as keyof StepData];
  };

  const renderCurrentStep = () => {
    const commonProps = {
      onNext: (data: any) => handleStepComplete(currentStep, data),
      onBack: handleBack,
      initialData: stepData[getStepDataKey(currentStep) as keyof StepData],
      showBack: currentStep > 1,
    };

    switch (currentStep) {
      case 1:
        return <PatientInformationStep {...commonProps} />;
      case 2:
        return <DoctorSelectionStep {...commonProps} />;
      case 3:
        return <AppointmentDetailsStep {...commonProps} />;
      case 4:
        return <PaymentDetailsStep {...commonProps} />;
      default:
        return <PatientInformationStep {...commonProps} />;
    }
  };

  return (
    <div className="bg-[#EFF7F9]">
      <div className="mx-auto">
        <StepCards
          steps={steps}
          currentStep={currentStep}
          stepData={stepData}
          onEdit={handleStepEdit}
          isStepCompleted={isStepCompleted}
        />

        <StepperNavigation
          steps={steps}
          currentStep={currentStep}
          isStepCompleted={isStepCompleted}
        />

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  );
};

export default OPDAppointmentForm;
