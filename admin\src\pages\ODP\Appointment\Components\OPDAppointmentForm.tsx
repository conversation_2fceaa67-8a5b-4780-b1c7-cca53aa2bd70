import React, { useState, useEffect, useMemo } from "react";
import { Form, FormikProvider, useFormik } from "formik";
import { useCreateUser } from "../../../../server-action/api/auth";
import { useGetUser } from "../../../../server-action/api/user";
import { useGetAllFinanceBanks } from "../../../../server-action/api/financeBankApi";
import { useGetShiftAssign } from "../../../../server-action/api/shiftAssignApi";
import { useGetAllDepartmentCategory } from "../../../../server-action/api/department-category.api";
import { AreaField, DropdownField, InputField } from "../../../../components";
import { ActionButton } from "../../../../components/ActionButton";
import { NewPatientSchema } from "./appointmentformvalidation";
import { paymentStatus } from "../../../../constant/constant";

// Helper function to get current date
const getCurrentDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const OPDAppointmentForm: React.FC = () => {
  const { mutateAsync: createNewPatient, isPending } = useCreateUser();

  const [initialValues, setInitialValues] = useState({
    fullName: "",
    gender: "",
    age: "",
    bloodGroup: "",
    maritalStatus: "",
    citizenNumber: "",
    language: "",
    currentAddress: "",
    permanentAddress: "",
    contactNumber: "",
    alternatePhoneNumber: "",
    role: "PATIENT",
    email: "",
    password: "patient@123",
    remark: "",
    department: "",
    appointmentDate: getCurrentDate(),
    doctor: "",
    availableTimeSlot: "",
    doctorDutyTime: "",
    availablestatus: "",
    totalCharge: "",
    resellerName: "none",
    status: "PENDING",
    totalAmount: "",
    paymentMethod: "CASH",
    transactionId: "",
    paymentStatus: "PAID",
    bankName: "",
  });

  // API hooks
  const { data: userDoctorDetails } = useGetUser({ role: "DOCTOR" });
  const { data: departmentData } = useGetAllDepartmentCategory();
  const { data: bankData } = useGetAllFinanceBanks();
  const { data: shiftList } = useGetShiftAssign();

  // State for filtered data
  const [filteredDoctors, setFilteredDoctors] = useState([]);

  // Static options
  const genderOptions = [
    { value: "MALE", label: "Male" },
    { value: "FEMALE", label: "Female" },
    { value: "OTHER", label: "Other" },
  ];

  const bloodGroupOptions = [
    { value: "A+", label: "A+" },
    { value: "A-", label: "A-" },
    { value: "B+", label: "B+" },
    { value: "B-", label: "B-" },
    { value: "AB+", label: "AB+" },
    { value: "AB-", label: "AB-" },
    { value: "O+", label: "O+" },
    { value: "O-", label: "O-" },
  ];

  const maritalStatusOptions = [
    { value: "SINGLE", label: "Single" },
    { value: "MARRIED", label: "Married" },
    { value: "DIVORCED", label: "Divorced" },
    { value: "WIDOWED", label: "Widowed" },
  ];

  // Dropdown options
  const departmentDropdown = useMemo(() => {
    return (
      departmentData?.data?.map((dept: any) => ({
        value: dept._id,
        label: dept.name,
      })) || []
    );
  }, [departmentData]);

  const bankOptions = useMemo(() => {
    return (
      bankData?.data?.map((bank: any) => ({
        value: bank._id,
        label: bank.bankName,
      })) || []
    );
  }, [bankData]);

  const getDoctorsArray = () => {
    return userDoctorDetails?.data || [];
  };

  const handleStepComplete = (step: number, data: any) => {
    const updatedStepData = {
      ...stepData,
      [`${getStepDataKey(step)}`]: data,
    };

    setStepData(updatedStepData);

    if (step < 4) {
      setCurrentStep(step + 1);
      console.log(`Moving to step ${step + 1}`);
    } else {
      console.log("All steps completed, submitting final form...");
      handleFinalSubmission(updatedStepData);
    }
  };

  const handleFinalSubmission = (allStepData: StepData) => {
    setCurrentStep(1);
    setStepData({
      patientInfo: null,
      doctorInfo: null,
      appointmentInfo: null,
      paymentInfo: null,
    });
  };

  const handleStepEdit = (step: number) => {
    setCurrentStep(step);
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepDataKey = (step: number) => {
    switch (step) {
      case 1:
        return "patientInfo";
      case 2:
        return "doctorInfo";
      case 3:
        return "appointmentInfo";
      case 4:
        return "paymentInfo";
      default:
        return "patientInfo";
    }
  };

  const isStepCompleted = (step: number) => {
    const dataKey = getStepDataKey(step);
    return !!stepData[dataKey as keyof StepData];
  };

  const renderCurrentStep = () => {
    const commonProps = {
      onNext: (data: any) => handleStepComplete(currentStep, data),
      onBack: handleBack,
      initialData: stepData[getStepDataKey(currentStep) as keyof StepData],
      showBack: currentStep > 1,
    };

    switch (currentStep) {
      case 1:
        return <PatientInformationStep {...commonProps} />;
      case 2:
        return <DoctorSelectionStep {...commonProps} />;
      case 3:
        return <AppointmentDetailsStep {...commonProps} />;
      case 4:
        return <PaymentDetailsStep {...commonProps} />;
      default:
        return <PatientInformationStep {...commonProps} />;
    }
  };

  return (
    <div className="bg-[#EFF7F9]">
      <div className="mx-auto">
        <StepCards
          steps={steps}
          currentStep={currentStep}
          stepData={stepData}
          onEdit={handleStepEdit}
          isStepCompleted={isStepCompleted}
        />

        <StepperNavigation
          steps={steps}
          currentStep={currentStep}
          isStepCompleted={isStepCompleted}
        />

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  );
};

export default OPDAppointmentForm;
