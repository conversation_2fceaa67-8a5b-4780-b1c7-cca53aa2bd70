import React, { useState } from "react";
import Doctor<PERSON>electionStep from "./DoctorSelectionStep";
import AppointmentDetailsStep from "./AppointmentDetailsStep";
import PaymentDetailsStep from "./PaymentDetailsStep";
import StepCards from "./StepCards";
import { StepperNavigation } from "./StepperNavigation";
import PatientInformationStep from "./PatientInformationStep";

export interface StepData {
  patientInfo?: any;
  doctorInfo?: any;
  appointmentInfo?: any;
  paymentInfo?: any;
}

const OPDAppointmentForm: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [stepData, setStepData] = useState<StepData>({});

  const steps = [
    {
      id: 1,
      title: "Patient Information",
      subtitle: "Personal & Medical Details",
      icon: "mdi:account",
      color: "teal",
    },
    {
      id: 2,
      title: "Doctor Info",
      subtitle: "Choose Department & Doctor",
      icon: "mdi:doctor",
      color: "red",
    },
    {
      id: 3,
      title: "Appointment",
      subtitle: "Choose Date, Reason & Priority",
      icon: "mdi:calendar",
      color: "blue",
    },
    {
      id: 4,
      title: "Payment",
      subtitle: "Billing & Insurance",
      icon: "mdi:credit-card",
      color: "purple",
    },
  ];

  const handleStepComplete = (step: number, data: any) => {
    // Log step completion for debugging
    console.log(`Step ${step} completed with data:`, data);

    const updatedStepData = {
      ...stepData,
      [`${getStepDataKey(step)}`]: data,
    };

    setStepData(updatedStepData);

    if (step < 4) {
      setCurrentStep(step + 1);
      console.log(`Moving to step ${step + 1}`);
    } else {
      console.log("All steps completed, submitting final form...");
      handleFinalSubmission(updatedStepData);
    }
  };

  const handleFinalSubmission = (allStepData: StepData) => {
    console.log("Final form submission:", allStepData);

    // Combine all step data into final submission format
    const finalData = {
      // Patient Information
      patientInfo: allStepData.patientInfo,

      // Doctor & Appointment Information
      doctorInfo: allStepData.doctorInfo,
      appointmentInfo: allStepData.appointmentInfo,

      // Payment Information
      paymentInfo: allStepData.paymentInfo,

      // Additional metadata
      submittedAt: new Date().toISOString(),
      status: "PENDING",
    };

    // Display all collected data in console and alert
    console.log("=== APPOINTMENT BOOKING COMPLETED ===");
    console.log("Step 1 - Patient Information:", finalData.patientInfo);
    console.log("Step 2 - Doctor Selection:", finalData.doctorInfo);
    console.log("Step 3 - Appointment Details:", finalData.appointmentInfo);
    console.log("Step 4 - Payment Details:", finalData.paymentInfo);
    console.log("Complete Final Data:", finalData);
    console.log("=====================================");

    // Show alert with summary
    alert(`Appointment Booking Completed Successfully!

Patient: ${finalData.patientInfo?.firstName || "N/A"} ${
      finalData.patientInfo?.lastName || ""
    }
Doctor: ${finalData.doctorInfo?.doctor || "N/A"}
Date: ${finalData.doctorInfo?.appointmentDate || "N/A"}
Time: ${finalData.doctorInfo?.availableTimeSlot || "N/A"}
Type: ${finalData.appointmentInfo?.appointmentType || "N/A"}
Priority: ${finalData.appointmentInfo?.priority || "N/A"}

Check console for complete data!`);

    // Reset form after successful submission
    setCurrentStep(1);
    setStepData({
      patientInfo: null,
      doctorInfo: null,
      appointmentInfo: null,
      paymentInfo: null,
    });
  };

  const handleStepEdit = (step: number) => {
    setCurrentStep(step);
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getStepDataKey = (step: number) => {
    switch (step) {
      case 1:
        return "patientInfo";
      case 2:
        return "doctorInfo";
      case 3:
        return "appointmentInfo";
      case 4:
        return "paymentInfo";
      default:
        return "patientInfo";
    }
  };

  const isStepCompleted = (step: number) => {
    const dataKey = getStepDataKey(step);
    return !!stepData[dataKey as keyof StepData];
  };

  const renderCurrentStep = () => {
    const commonProps = {
      onNext: (data: any) => handleStepComplete(currentStep, data),
      onBack: handleBack,
      initialData: stepData[getStepDataKey(currentStep) as keyof StepData],
      showBack: currentStep > 1,
    };

    switch (currentStep) {
      case 1:
        return <PatientInformationStep {...commonProps} />;
      case 2:
        return <DoctorSelectionStep {...commonProps} />;
      case 3:
        return <AppointmentDetailsStep {...commonProps} />;
      case 4:
        return <PaymentDetailsStep {...commonProps} />;
      default:
        return <PatientInformationStep {...commonProps} />;
    }
  };

  return (
    <div className="bg-[#EFF7F9]">
      <div className="mx-auto">
        {/* Step Cards */}
        <StepCards
          steps={steps}
          currentStep={currentStep}
          stepData={stepData}
          onEdit={handleStepEdit}
          isStepCompleted={isStepCompleted}
        />

        {/* Stepper Navigation */}
        <StepperNavigation
          steps={steps}
          currentStep={currentStep}
          isStepCompleted={isStepCompleted}
        />

        {/* Current Step Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  );
};

export default OPDAppointmentForm;
