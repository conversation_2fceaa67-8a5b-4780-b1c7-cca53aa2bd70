import{ba as n,a2 as t,aQ as r,bt as d}from"./index-ClX9RVH0.js";import{b as o}from"./attendance.api-CVfqyNC8.js";const i=()=>{const{id:a}=n(),{data:e}=o(a),s=e?[{title:e.status,start:new Date(e.date),end:new Date(e.date),type:e.status}]:[];return t.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[t.jsx(r,{headerTitle:"Doctor Attendance",onSearch:!0,onDatePicker:!0,onFilter:!0}),t.jsx("div",{className:"w-full rounded-lg ",children:t.jsx(d,{events:s})})]})};export{i as ViewAttendance};
