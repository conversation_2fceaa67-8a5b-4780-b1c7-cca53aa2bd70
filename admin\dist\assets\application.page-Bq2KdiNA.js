import{bR as A,bV as p,a5 as u,a2 as e,dg as C,ba as E,aM as P}from"./index-ClX9RVH0.js";import{f}from"./Utils-BMpLpMWy.js";const v=A(p.JOBAPPLICATION,"job-application"),k=v.useGetAll,S=v.useUpdate;function D({applicants:t,selectedApplicant:d,setSelectedApplicant:m,onApprove:n,onReject:r,totalPages:h,pagination:x,setPagination:a}){const[c,g]=u.useState("fullName"),[l,o]=u.useState("asc"),i=s=>{c===s?o(l==="asc"?"desc":"asc"):(g(s),o("asc"))},j=s=>{switch(s){case"ACCEPTED":return"bg-green text-white";case"REJECTED":return"bg-red text-white";case"PENDING":return"bg-yellow text-black";default:return"bg-gray-100 text-gray-800"}};return e.jsx("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:e.jsxs("div",{className:"overflow-x-auto",children:[e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>i("fullName"),children:["Name",c==="fullName"&&e.jsx("span",{className:"ml-1",children:l==="asc"?"↑":"↓"})]}),e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>i("job.jobTitle"),children:["Position",c==="job.jobTitle"&&e.jsx("span",{className:"ml-1",children:l==="asc"?"↑":"↓"})]}),e.jsxs("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer",onClick:()=>i("createdAt"),children:["Applied Date",c==="createdAt"&&e.jsx("span",{className:"ml-1",children:l==="asc"?"↑":"↓"})]}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:(t==null?void 0:t.length)>0?t==null?void 0:t.map(s=>{var y,b,w;return e.jsxs("tr",{className:`hover:bg-gray-50 ${(d==null?void 0:d._id)===s._id?"bg-blue-50":""}`,onClick:()=>m(s),children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"flex items-center",children:e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:s==null?void 0:s.fullName}),e.jsx("div",{className:"text-sm text-gray-500",children:s==null?void 0:s.email})]})})}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[e.jsx("div",{className:"text-sm text-gray-900",children:(y=s==null?void 0:s.job)==null?void 0:y.jobTitle}),e.jsx("div",{className:"text-sm text-gray-500",children:(b=s==null?void 0:s.job)==null?void 0:b.experience})]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:f((s==null?void 0:s.createdAt)??"")})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${j(s==null?void 0:s.status)}`,children:((w=s==null?void 0:s.status)==null?void 0:w.toLowerCase())||"new"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:N=>{N.stopPropagation(),n(s)},className:"text-green hover:text-green-900",children:"Approve"}),e.jsx("button",{onClick:N=>{N.stopPropagation(),r(s)},className:"text-red hover:text-red-900",children:"Reject"})]})})]},s._id)}):e.jsx("tr",{children:e.jsx("td",{colSpan:5,className:"px-6 py-4 text-center text-gray-500",children:"No applicants found"})})})]}),e.jsx("section",{className:"mt-4 px-4 pb-6",children:e.jsx(C,{currentPage:x.page,limit:x.limit,onClick:s=>{s.page&&a({...x,page:s.page}),s.limit&&a({...x,limit:s.limit})},totalPage:h??1})})]})})}function T({applicant:t,onApprove:d,onReject:m}){const n=r=>{switch(r){case"ACCEPTED":return"bg-green text-white";case"REJECTED":return"bg-red text-white";case"PENDING":return"bg-yellow text-black";default:return"bg-gray-100 text-gray-800"}};return e.jsxs("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:[e.jsxs("div",{className:"px-4 py-5 sm:px-6 flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Applicant Details"}),e.jsx("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Personal information and application details."})]}),e.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${n(t.status||"new")}`,children:t.status||"new"})]}),e.jsxs("div",{className:"border-t border-gray-200 px-4 py-5 sm:px-6",children:[e.jsx("div",{className:"flex justify-center mb-4",children:t.job.thumbnail&&e.jsx("img",{src:t.job.thumbnail||"/placeholder.svg",alt:`${t.job.jobTitle} thumbnail`,className:"h-32 w-32 object-cover rounded-full border-2 border-gray-200"})}),e.jsxs("dl",{className:"grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2",children:[e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Full name"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:t.fullName})]}),e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Email address"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:t.email})]}),e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Guardian name"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:t.guardianName})]}),e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Address"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:t.address})]}),e.jsxs("div",{className:"sm:col-span-2",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Applied for"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:t.job.jobTitle})]}),e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Experience"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:t.job.experience})]}),e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Salary"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:t.job.salary})]}),e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Job date"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:f(t.job.date)})]}),e.jsxs("div",{className:"sm:col-span-1",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Applied on"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900",children:f((t==null?void 0:t.createdAt)??"")})]}),e.jsxs("div",{className:"sm:col-span-2",children:[e.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Requirements"}),e.jsx("dd",{className:"mt-1 text-sm text-gray-900 prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:t.job.requirement}})]})]}),e.jsxs("div",{className:"mt-6 flex justify-end space-x-3",children:[e.jsx("button",{onClick:m,className:"inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:"Reject"}),e.jsx("button",{onClick:d,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500",children:"Approve"})]})]})]})}const _=()=>{var o,i,j;const{id:t}=E(),[d,m]=u.useState({limit:5,page:1}),n=P({job:t,page:d.page,limit:d.limit}),{data:r}=k(n),{mutateAsync:h}=S(),x=(o=r==null?void 0:r.data)==null?void 0:o.career,[a,c]=u.useState(null),g=async s=>{await h({_id:(s==null?void 0:s._id)??"",entityData:{...s,status:"ACCEPTED"}})},l=async s=>{await h({_id:(s==null?void 0:s._id)??"",entityData:{...s,status:"REJECTED"}})};return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("header",{className:"bg-white shadow",children:e.jsx("div",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Job Applications"})})}),e.jsx("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:e.jsx("div",{className:"px-4 py-6 sm:px-0",children:e.jsxs("div",{className:"flex flex-col lg:flex-row gap-6 mt-6",children:[e.jsx("div",{className:"lg:w-2/3",children:e.jsx(D,{applicants:x,setSelectedApplicant:c,selectedApplicant:a,onApprove:g,onReject:l,pagination:d,setPagination:m,totalPages:(j=(i=r==null?void 0:r.data)==null?void 0:i.pagination)==null?void 0:j.pages})}),e.jsx("div",{className:"lg:w-1/3",children:a?e.jsx(T,{applicant:a,onApprove:()=>g(a),onReject:()=>l(a)}):e.jsx("div",{className:"bg-white shadow rounded-lg p-6 h-full flex items-center justify-center",children:e.jsx("p",{className:"text-gray-500 text-center",children:"Select an applicant to view details"})})})]})})})]})};export{_ as JobApplicationPage};
