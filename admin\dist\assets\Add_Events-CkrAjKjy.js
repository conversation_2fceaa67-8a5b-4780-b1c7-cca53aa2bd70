import{a5 as d,a6 as o,a1 as p,a2 as e,a7 as m,a8 as c,a9 as x,aa as u,ab as t,ac as a}from"./index-ClX9RVH0.js";const j=()=>{const{setEvent:i}=d.useContext(o),n=p({initialValues:{},enableReinitialize:!0,onSubmit:s=>{console.log(s),i(s)}}),l=[{label:"Male",value:"male"},{label:"Female",value:"female"}],r=[{label:"OPD",value:"opd"},{label:"CGI",value:"cgi"}];return e.jsxs("div",{children:[e.jsx(m,{listTitle:"Add Event",hideHeader:!0}),e.jsxs("div",{className:"flex gap-5",children:[e.jsx("div",{className:"h-auto w-[25%]",children:e.jsx("div",{className:"flex flex-col gap-4 bg-white px-4 py-5",children:e.jsx(c,{step:1,title:"Event Information",isActive:!0})})}),e.jsx("div",{className:"w-[75%]",children:e.jsx(x,{value:n,children:e.jsxs(u,{className:"bg-white",children:[e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsx(t,{required:!0,label:"Event Type",options:l,name:"eventType"}),e.jsx(a,{type:"datetime-local",label:"Start Date and Time",placeholder:"10-20-25",name:"startDate"}),e.jsx(a,{type:"datetime-local",label:"End Date and Time",placeholder:"10-20-25",name:"endDate"}),e.jsx(t,{required:!0,label:"For",options:l,name:"forField"}),e.jsx(t,{required:!0,label:"Department",options:r,name:"department"}),e.jsx(a,{type:"text",label:"Event Title",placeholder:"Trauma Life Support for Nurses",name:"title"})]}),e.jsx("div",{className:"mx-5 pb-5",children:e.jsx(a,{type:"text",label:"Description",placeholder:"Enter the Description",name:"description",inputClassName:"h-20 text-start py-0"})}),e.jsxs("div",{className:"flex gap-5 text-white font-medium justify-end",children:[e.jsx("button",{className:"bg-[#989898] py-1 px-3 rounded-md",type:"button",children:"Cancel"}),e.jsx("button",{className:"bg-[#13898E] py-1 px-3 rounded-md",type:"submit",children:"Submit"})]})]})})})]})]})};export{j as default};
