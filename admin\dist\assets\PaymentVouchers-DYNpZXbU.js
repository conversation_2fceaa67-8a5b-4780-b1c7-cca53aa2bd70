import{a5 as a,cr as K,a2 as e,d3 as Q,av as w,ct as U,aM as q,ah as W,a7 as z,b3 as G,aj as J}from"./index-ClX9RVH0.js";import{u as O}from"./balanceSheetApi-qQfxc5M2.js";const X=a.forwardRef(({data:s,type:b,status:l},m)=>{var o,x,h,y,p,f,j,N,d;const n=(o=s==null?void 0:s.data)==null?void 0:o.totalTransaction,u=K(Math.abs(n??0),{currency:"NRS",locale:"IN"});return e.jsxs("div",{className:" p-2 bg-white",ref:m,children:[e.jsx(Q,{}),e.jsx("div",{className:"text-center mb-2",children:e.jsxs("h2",{className:"text-lg font-semibold text-gray-800",children:[b," (",l,")"]})}),e.jsx("div",{className:"overflow-x-auto border border-gray-800 mb-4 rounded-md",children:e.jsxs("table",{className:"min-w-full text-sm text-left text-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 border-b border-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Dat"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Category"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Payment Method"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Amount"}),e.jsx("th",{className:"px-4 py-2 text-center font-medium",children:"Balance"})]})}),e.jsx("tbody",{children:w.get(s,"data.transactions",[]).map((r,v)=>{var C,c,A;return e.jsxs("tr",{className:"border-b border-gray-200 hover:bg-gray-100 break-inside-avoid-page",children:[e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center whitespace-nowrap w-[100px]",children:(r==null?void 0:r.accCode)||"-"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center max-w-[200px]",children:(r==null?void 0:r.accDescription)||"-"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center w-fit whitespace-nowrap",children:(r==null?void 0:r.txdCategory)==="DEBIT"?(C=r.txdAmount)==null?void 0:C.toFixed(2):"0.00"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center w-fit whitespace-nowrap",children:(r==null?void 0:r.txdCategory)==="CREDIT"?(c=r.txdAmount)==null?void 0:c.toFixed(2):"0.00"}),e.jsx("td",{className:"px-4 py-2 text-right w-[120px]",children:((A=r==null?void 0:r.txdAmount)==null?void 0:A.toFixed(2))??"0.00"})]},v)})})]})}),e.jsxs("div",{className:`flex justify-around items-center my-4 rounded-md border ${((h=(x=s==null?void 0:s.data)==null?void 0:x.transactions)==null?void 0:h.length)>17?"mt-24":""} border-gray-800 bg-gray-50 text-sm font-semibold`,children:[e.jsx("div",{className:"text-right",children:"Total"}),e.jsxs("div",{className:"px-4 py-2 text-right border-l border-gray-800",children:[e.jsxs("div",{className:"flex",children:[e.jsx("p",{children:"Total Debit"}),e.jsxs("p",{children:[": Rs.",((p=(y=s==null?void 0:s.data)==null?void 0:y.totalDebit)==null?void 0:p.toFixed(2))??"0.00"]})," "]}),e.jsxs("div",{className:"flex",children:[e.jsx("p",{children:"Total Credit"}),e.jsxs("p",{children:[": Rs.",((j=(f=s==null?void 0:s.data)==null?void 0:f.totalCredit)==null?void 0:j.toFixed(2))??"0.00"]})," "]}),e.jsxs("div",{className:"flex",children:[e.jsx("p",{children:"Total Transaction"}),e.jsxs("p",{children:[": Rs.",((d=(N=s==null?void 0:s.data)==null?void 0:N.totalTransaction)==null?void 0:d.toFixed(2))??"0.00"," "]})]})]})]}),e.jsx("div",{className:"border border-gray-800 mb-4",children:e.jsxs("div",{className:"p-2 text-sm flex gap-4",children:[e.jsx("span",{className:"font-medium",children:"Amount in Words: "}),e.jsx("span",{className:"border-b border-gray-300 inline-block ",children:u})]})})]})}),ee=()=>{const[s,b]=a.useState(""),[l,m]=a.useState(""),[n,u]=a.useState(""),[o,x]=a.useState(""),[h,y]=a.useState(""),[p,f]=a.useState(""),[j,N]=a.useState(!1),[d,r]=a.useState([]),v=a.useRef(null),C=U.useReactToPrint({contentRef:v,pageStyle:`
        @page {
          size: A4;
          margin: 0.5in;
        }
        @media print {
          body {
            -webkit-print-color-adjust: exact;
          }
        }
      `}),c=l||n||s,A=c?q({page:1,limit:1e3,startDate:l||void 0,endDate:n||void 0,paymentMethod:s&&s!=="ALL"?s:void 0}):null,{data:k,isLoading:T}=O(A),D=c?w.get(k,"data",{}):{};console.log(k,"dataraja"),a.useEffect(()=>{r([])},[]),a.useEffect(()=>{r([])},[l,n,s]);const I=()=>{const t=o||(h||p?"ALL":"");m(h),u(p),x(t),b(t),r([]),N(!0)},H=()=>{m(""),u(""),b(""),y(""),f(""),x(""),r([]),N(!0)},$=(t,i)=>{r(t?i:[])},V=(t,i)=>{r(i?g=>[...g,t]:g=>g.filter(S=>S!==t))},F=()=>{const t=w.get(D,"balanceSheet",[]);d.length>0&&t.filter(i=>d.includes(i._id)),setTimeout(()=>{C()},100)},E={columns:[{title:"Date",key:"date"},{title:"Name",key:"fullName"},{title:"Payment Method",key:"paymentMethod"},{title:"Amount",key:"amount"},{title:"Balance",key:"balance"},{title:"Payment status",key:"txdCategory"}],rows:w.get(D,"balanceSheet",[]).map(t=>{var i,g,S,P,R,M,B;return{_id:t._id,fullName:((S=(g=(i=t==null?void 0:t.createdBy)==null?void 0:i.commonInfo)==null?void 0:g.personalInfo)==null?void 0:S.fullName)||((M=(R=(P=t==null?void 0:t.user)==null?void 0:P.commonInfo)==null?void 0:R.personalInfo)==null?void 0:M.fullName)||"N/A",date:new Date(t.date).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),txdCategory:t.txdCategory,amount:`Rs. ${t.txdAmount.toLocaleString()}`,balance:`Rs. ${t.totalBalance.toLocaleString()}`,bank:((B=t.bank)==null?void 0:B.bankName)||"N/A",paymentMethod:e.jsx(W,{status:t.paymentMethod,className:`px-2 py-1 rounded-full text-xs font-medium ${t.paymentMethod==="CASH"||t.paymentMethod==="BANK"?"bg-green-100 text-green-800":t.paymentMethod==="CHEQUE"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`})}})},_=c?w.get(D,"balanceSheet",[]).length:0,L=l||n||s;return e.jsxs("div",{className:"h-screen overflow-y-auto",children:[e.jsx(z,{listTitle:"Payment Voucher",hideHeader:!0}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-end md:space-x-4 space-y-4 md:space-y-0",children:[e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:h,onChange:t=>y(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:p,onChange:t=>f(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category"}),e.jsxs("select",{value:o,onChange:t=>x(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",disabled:!0,children:"Select Category"}),e.jsx("option",{value:"ALL",children:"All"}),e.jsx("option",{value:"CASH",children:"Cash"}),e.jsx("option",{value:"BANK",children:"Bank"}),e.jsx("option",{value:"CHEQUE",children:"Cheque"})]})]}),e.jsxs("div",{className:"w-full md:w-auto flex gap-4 mt-2 md:mt-0",children:[e.jsx("button",{onClick:I,className:"w-full md:w-auto px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 transition-colors font-medium",children:"Apply"}),e.jsx("button",{onClick:H,className:"w-full md:w-auto px-4 py-2 bg-rose-500 text-white rounded-md hover:bg-rose-600 transition-colors font-medium",children:"Clear"}),e.jsx("button",{onClick:F,disabled:!j||!o,className:`w-full md:w-auto px-4 py-2  text-white rounded-md transition-colors font-medium 
                ${j&&o?"bg-sky-500 text-white hover:bg-sky-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:e.jsx(G,{icon:"material-symbols-light:print-outline",width:"24",height:"24"})})]})]}),L&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[e.jsx("span",{className:"text-sm text-gray-600 font-medium",children:"Active filters:"}),l&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["From: ",l,e.jsx("button",{onClick:()=>{m(""),m("")},className:"ml-1 text-green-600 hover:text-green-800",children:"×"})]}),n&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["To: ",n,e.jsx("button",{onClick:()=>{u(""),u("")},className:"ml-1 text-green-600 hover:text-green-800",children:"×"})]}),s&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:["Category: ",s==="ALL"?"All":s,e.jsx("button",{onClick:()=>{x(""),b("")},className:"ml-1 text-purple-600 hover:text-purple-800",children:"×"})]})]})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm",children:[e.jsx("div",{className:"p-6 border-b border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Transaction Details"}),e.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:[c?T?"Loading...":`Showing ${_} transactions`:"Apply filters to view transactions",L&&c&&" (filtered)"]})]}),d.length>0&&e.jsxs("div",{className:"text-sm text-gray-600",children:[d.length," transaction(s) selected"]})]})}),e.jsx(J,{columns:E.columns,rows:E.rows,loading:T,color:"bg-gray-50",textcolor:"text-gray-600",selectedIds:d,onSelectAll:$,onSelectRow:V,primaryKey:"_id",onBulkAction:F,bulkActionLabel:"Print Selected",showBulkActions:!0})]}),e.jsx("section",{className:"hidden",children:e.jsx(X,{ref:v,data:k,type:"Payment Voucher",status:o})})]})};export{ee as default};
