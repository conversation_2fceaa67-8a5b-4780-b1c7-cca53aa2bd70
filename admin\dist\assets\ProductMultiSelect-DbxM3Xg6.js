import{a5 as a,a2 as n,bf as B,a4 as O,bS as F}from"./index-ClX9RVH0.js";const H=({name:w,label:y,required:j,placeholder:C="Type and press Enter",className:N,options:p=[]})=>{const[d,m]=a.useState(""),[h,b]=a.useState(!1),[o,i]=a.useState(-1),v=a.useRef(null),x=a.useRef(new Map);a.useEffect(()=>{x.current.clear()},[p.length]),a.useEffect(()=>{i(-1)},[p.length,d]),a.useEffect(()=>{h||i(-1)},[h]);const T=a.useCallback(l=>c=>{c?x.current.set(l,c):x.current.delete(l)},[]);return a.useEffect(()=>{if(o>=0&&h&&v.current){const l=v.current,c=x.current.get(o);if(l&&c){l.getBoundingClientRect(),c.getBoundingClientRect();const s=l.scrollTop,u=s+l.clientHeight,f=c.offsetTop,r=f+c.offsetHeight;r>u?l.scrollTop=r-l.clientHeight+8:f<s&&(l.scrollTop=f-8)}}},[o,h]),n.jsx(B,{name:w,children:({field:l,form:c})=>{const s=l.value||[],u=e=>{c.setFieldValue(w,e)},f=s.includes(""),r=f?p.filter(e=>e.value===""):p.filter(e=>e.label.toLowerCase().includes(d.toLowerCase())&&!s.includes(e.value)),E=e=>{switch(e.key){case"Enter":if(e.preventDefault(),o>=0&&o<r.length){const t=r[o];s.includes(t.value)||(t.value===""?u([""]):u([...s,t.value]),m(""),i(-1))}else if(d.trim()&&!f){const t=d.trim();s.includes(t)||(u([...s,t]),m(""))}break;case"ArrowDown":e.preventDefault(),r.length>0&&i(t=>t<r.length-1?t+1:0);break;case"ArrowUp":e.preventDefault(),r.length>0&&i(t=>t>0?t-1:r.length-1);break;case"Escape":e.preventDefault(),b(!1),i(-1);break;case"Backspace":d===""&&s.length>0&&u(s.slice(0,-1));break}},D=e=>{u(s.filter(t=>t!==e))},R=e=>{s.includes(e.value)||(e.value===""?u([""]):f||u([...s,e.value]),m(""),i(-1))};return n.jsxs("div",{className:"relative",children:[y&&n.jsxs("label",{className:"flex text-gray-800 gap-1 ",children:[n.jsx(O,{variant:"grey-500",size:"body-sm-mid",className:"tracking-wide capitalize",children:y}),j&&n.jsx("span",{className:"text-red",children:"*"})]}),n.jsxs("div",{className:F("border rounded-sm px-2 py-[6px] text-sm",N),children:[n.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:s.map((e,t)=>{var k;const g=((k=p.find(S=>S.value===e))==null?void 0:k.label)||e;return n.jsxs("div",{className:"flex items-center bg-[#146c6e] text-white px-2 py-1 rounded-full text-sm",children:[g,n.jsx("button",{type:"button",className:"ml-2 text-blue-200 hover:text-red-400",onClick:()=>D(e),children:"×"})]},t)})}),n.jsx("input",{type:"text",className:"w-full outline-none bg-transparent text-sm",placeholder:C,value:d,onChange:e=>m(e.target.value),onKeyDown:E,onFocus:()=>b(!0),onBlur:()=>setTimeout(()=>b(!1),150)})]}),h&&r.length>0&&n.jsx("ul",{ref:v,className:"absolute z-10 w-full mt-1 bg-white border rounded shadow-md max-h-40 overflow-y-auto",onMouseDown:e=>e.preventDefault(),children:r.map((e,t)=>n.jsx("li",{ref:T(t),onClick:()=>R(e),onMouseEnter:()=>i(t),className:"px-4 py-2 cursor-pointer transition-colors",style:o===t?{backgroundColor:"#00717f",color:"#ffffff"}:{},onMouseOver:g=>{o!==t&&(g.currentTarget.style.backgroundColor="#f3f4f6")},onMouseOut:g=>{o!==t&&(g.currentTarget.style.backgroundColor="transparent")},children:e.label},t))})]})}})};export{H as P};
