// import { Icon } from "@iconify/react/dist/iconify.js";
// import React from "react";
// import MasterTable from "../../../../../../../layouts/Table/MasterTable";
// import { AllergyData } from "../../Consultations/pages/sampleMasterTableData";
// import { useFormik } from "formik";
// import * as Yup from "yup";

// const Allergies: React.FC = () => {
//   const allergyValidationSchema = Yup.object({
//     allergyType: Yup.string().required("Allergy type is required"),
//     allergen: Yup.string().required("Allergen is required"),
//     severity: Yup.string().required("Severity is required"),
//     lastSeen: Yup.string()
//       .matches(/^\d{2}\/\d{2}\/\d{4}$/, "Date must be in DD/MM/YYYY format")
//       .required("Last seen date is required"),
//     reaction: Yup.string().required("Reaction details are required"),
//   });

//   const formik = useFormik({
//     initialValues: {
//       allergyType: "",
//       allergen: "",
//       severity: "",
//       lastSeen: "",
//       reaction: "",
//     },
//     validationSchema: allergyValidationSchema,
//     onSubmit: (values, { resetForm }) => {
//       console.log("Form submitted", values);
//       resetForm();
//     },
//   });

//   return (
//     <div className='max-w-full p-4 mx-auto'>
//       <h2 className='mb-4 font-semibold text-center'>Allergies</h2>

//       {/* Allergies Form */}
//       <form
//         onSubmit={formik.handleSubmit}
//         className='w-full p-4 mb-8 space-y-4 border rounded-md'
//       >
//         {/* First Row */}
//         <div className='grid items-end grid-cols-12 gap-4'>
//           {/* Allergy Type */}
//           <div className='flex flex-col col-span-4 gap-1'>
//             <div className='flex items-center gap-2'>
//               <label className='whitespace-nowrap'>Allergy Type</label>
//               <select
//                 name='allergyType'
//                 className='w-full max-w-[200px] border border-gray-300 p-1 rounded focus:outline-none'
//                 onChange={formik.handleChange}
//                 // onBlur={formik.handleBlur}
//                 value={formik.values.allergyType}
//               >
//                 <option value=''>Select</option>
//                 <option value='Type1'>Type1</option>
//                 <option value='Type2'>Type2</option>
//               </select>
//             </div>
//             {formik.touched.allergyType && formik.errors.allergyType && (
//               <p className='text-xs text-red-500'>
//                 {formik.errors.allergyType}
//               </p>
//             )}
//           </div>

//           {/* Allergen */}
//           <div className='flex flex-col col-span-4 gap-1'>
//             <div className='flex items-center gap-2'>
//               <label className='whitespace-nowrap'>Allergen</label>
//               <select
//                 name='allergen'
//                 className='w-full max-w-[200px] border border-gray-300 p-1 rounded focus:outline-none'
//                 onChange={formik.handleChange}
//                 // onBlur={formik.handleBlur}
//                 value={formik.values.allergen}
//               >
//                 <option value=''>Select</option>
//                 <option value='Allergen1'>Allergen1</option>
//                 <option value='Allergen2'>Allergen2</option>
//               </select>
//             </div>
//             {formik.touched.allergen && formik.errors.allergen && (
//               <p className='text-xs text-red-500'>{formik.errors.allergen}</p>
//             )}
//           </div>

//           {/* Severity */}
//           <div className='flex flex-col col-span-4 gap-1'>
//             <div className='flex items-center gap-2'>
//               <label className='whitespace-nowrap'>Severity</label>
//               <select
//                 name='severity'
//                 className='w-full max-w-[200px] border border-gray-300 p-1 rounded focus:outline-none'
//                 onChange={formik.handleChange}
//                 // onBlur={formik.handleBlur}
//                 value={formik.values.severity}
//               >
//                 <option value=''>Select</option>
//                 <option value='Mild'>Mild</option>
//                 <option value='Moderate'>Moderate</option>
//                 <option value='Severe'>Severe</option>
//               </select>
//             </div>
//             {formik.touched.severity && formik.errors.severity && (
//               <p className='text-xs text-red-500'>{formik.errors.severity}</p>
//             )}
//           </div>
//         </div>

//         {/* Second Row */}
//         <div className='grid items-end grid-cols-12 gap-4'>
//           {/* Last Seen */}
//           <div className='flex flex-col col-span-4 gap-1'>
//             <div className='flex items-center gap-7'>
//               <label className='whitespace-nowrap'>Last Seen</label>
//               <input
//                 type='text'
//                 name='lastSeen'
//                 placeholder='DD/MM/YYYY'
//                 className='w-full max-w-[200px] border border-gray-300 p-2 rounded focus:outline-none'
//                 onChange={formik.handleChange}
//                 // onBlur={formik.handleBlur}
//                 value={formik.values.lastSeen}
//               />
//             </div>
//             {formik.touched.lastSeen && formik.errors.lastSeen && (
//               <p className='text-xs text-red-500'>{formik.errors.lastSeen}</p>
//             )}
//           </div>

//           {/* Reaction */}
//           <div className='flex flex-col col-span-5 gap-1'>
//             <div className='flex items-center gap-2'>
//               <label className='whitespace-nowrap'>Reaction</label>
//               <input
//                 type='text'
//                 name='reaction'
//                 placeholder='Details of reaction'
//                 className='w-full p-2 border border-gray-300 rounded focus:outline-none'
//                 onChange={formik.handleChange}
//                 // onBlur={formik.handleBlur}
//                 value={formik.values.reaction}
//               />
//             </div>
//             {formik.touched.reaction && formik.errors.reaction && (
//               <p className='text-xs text-red-500'>{formik.errors.reaction}</p>
//             )}
//           </div>

//           {/* Save Button */}
//           <div className='flex items-center justify-center col-span-3'>
//             <button
//               type='submit'
//               className='flex items-center justify-evenly px-6 py-2 gap-3 text-white text-xl bg-[#116aef] rounded-lg hover:bg-blue-700'
//             >
//               <Icon
//                 icon='fa6-solid:floppy-disk'
//                 width='18'
//                 height='18'
//                 color='white'
//               />
//               Save
//             </button>
//           </div>
//         </div>
//       </form>

//       {/* Allergies Table */}
//       <div className='overflow-x-auto'>
//         <MasterTable
//           color='bg-[#b3b3b3]'
//           textcolor='text-[#000000]/100'
//           columns={AllergyData.columns}
//           rows={AllergyData.rows}
//           loading={false}
//         />
//       </div>
//     </div>
//   );
// };

// export default Allergies;

import { Icon } from "@iconify/react/dist/iconify.js";
import React, { ChangeEvent } from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { useUpdatePatientHistory } from "../../../../../../../server-action/api/patienthistory.api";
import { Form, FormikProvider, useFormik } from "formik";
import { DropdownField, InputField } from "../../../../../../../components";
import { ActionButton } from "../../../../../../../components/ActionButton";
import { get } from "lodash";

interface VitalSignValues {
  allergyType?: string;
  allergen?: string;
  reaction?: string;
  lastSeen?: string;
  serverity?: string;
}
interface VitalSignsProps {
  data: {
    _id: string;
    data: {
      users: any[];
    };
  };
}

const Allergies: React.FC<VitalSignsProps> = ({ data }) => {
  const AllergyType = [
    { value: "FOOD", label: "Food" },
    { value: "DRUG", label: "Drug" },
    { value: "ENVIRONMENTAL", label: "Enviromental" },
    { value: "INSECT", label: "Insect" },
    { value: "LATEX", label: "Latex" },
    { value: "CHEMICAL", label: "Chemical" },
    { value: "OTHER", label: "Other" },
  ];

  const AllergyList = [
    { value: "PEANUTS", label: "Food" },
    { value: "SHELLFISH", label: "ShellFish" },
    { value: "MILK", label: "Milk" },
    { value: "EGGS", label: "Eggs" },
    { value: "WHEAT", label: "Wheat" },
    { value: "SOY", label: "Soy" },
    { value: "PENICILLIN", label: "Penicillin" },
    { value: "ASPIRIN", label: "Aspirin" },
    { value: "DUST MITES", label: "Dust Mites" },
    { value: "POLLEN", label: "Pollen" },
    { value: "MOLD", label: "Mold" },
    { value: "BEE STING", label: "Bee Sting" },
    { value: "LATEX", label: "Latex" },
    { value: "NICKEL", label: "Nickel" },
    { value: "OTHER", label: "Other" },
  ];
  const Severitylist = [
    { value: "HIGH", label: "High" },
    { value: "MEDIUM", label: "Medium" },
    { value: "LOW", label: "Low" },
  ];

  const { mutate } = useUpdatePatientHistory();
  const formik = useFormik<VitalSignValues>({
    initialValues: {
      allergyType: "",
      allergen: "",
      reaction: "",
      lastSeen: "",
      serverity: "",
    },
    onSubmit: async (values) => {
      const finalValue = {
        _id: data._id,
        allergies: [
          {
            ...values,
          },
        ],
      };
      mutate({
        _id: data._id,
        entityData: finalValue,
      });
    },
  });

  const { handleSubmit, values } = formik;
  console.log(formik, "formik");
  console.log(values, "values");

  const tableData = {
    columns: [
      { title: "S.N", key: "key" },
      { title: "Allergy Type", key: "allergyType" },
      { title: "Allergen", key: "allergen" },
      { title: "Severity", key: "serverity" },
      { title: "Last Seen", key: "lastSeen" },
      { title: "Reaction", key: "reaction" },
    ],
    rows: get(data, "allergies", []).map((item: any, index: number) => ({
      key: index,
      allergyType: item.allergyType,
      allergen: item.allergen,
      serverity: item.serverity,
      lastSeen: item.lastSeen,
      reaction: item.reaction,
      // action: <TableAction onShow={() => {}} />,
    })),
  };
  console.log(formik.values, "");

  return (
    <div className="max-w-full p-4 mx-auto">
      <h2 className="mb-6 text-xl font-semibold text-center">Allergies</h2>

      {/* Allergies Form */}
      {/* <form className='mb-8 space-y-4'>
        <div className='grid items-end grid-cols-12 gap-4'>
          <div className='flex items-center col-span-4 gap-2'>
            <label className='whitespace-nowrap'>Allergy Type</label>
            <select className='w-full max-w-[200px] border border-gray-300 p-1.5 rounded focus:outline-none'>
              <option>Select</option>
              <option>Select2</option>
            </select>
          </div>

          <div className='flex items-center col-span-4 gap-2'>
            <label className='whitespace-nowrap'>Allergen</label>
            <select className='w-full max-w-[200px] border border-gray-300 p-1.5 rounded focus:outline-none'>
              <option>Select</option>
            </select>
          </div>

          <div className='flex items-center col-span-4 gap-2'>
            <label className='whitespace-nowrap'>Severity</label>
            <select className='w-full max-w-[200px] border border-gray-300 p-1.5  rounded focus:outline-none'>
              <option>Select</option>
            </select>
          </div>
        </div>

        <div className='grid items-end grid-cols-12 gap-4'>
          <div className='flex items-center col-span-4 gap-7'>
            <label className='whitespace-nowrap'>Last Seen</label>
            <input
              type='text'
              placeholder='DD/MM/YYYY'
              className='w-full max-w-[200px] border border-gray-300 p-2 rounded focus:outline-none'
            />
          </div>

          <div className='flex items-center col-span-5 gap-2'>
            <label className='whitespace-nowrap'>Reaction</label>
            <input
              type='text'
              placeholder='Details of reaction'
              className='w-full p-2 border border-gray-300 rounded focus:outline-none'
            />
          </div>

          <div className='flex items-center justify-center col-span-3'>
            <button
              type='submit'
              className='flex items-center justify-evenly  px-6 py-2 gap-3 text-white text-xl bg-[#116aef] rounded-lg hover:bg-blue-700'
            >
              <Icon
                icon='fa6-solid:floppy-disk'
                width='18'
                height='18'
                color='white'
              />
              Save
            </button>
          </div>
        </div>
      </form> */}
      <FormikProvider value={formik}>
        <Form autoComplete="off" onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 p-2 sm:grid-cols-2 lg:grid-cols-3 gap-x-10 gap-y-4">
            <div>
              <DropdownField
                label="Allergy Type"
                firstInput="Select"
                options={AllergyType}
                name={values.allergyType}
                value={formik.values.allergyType}
                onChange={(event: ChangeEvent<HTMLSelectElement>) => {
                  formik.setFieldValue(`allergyType`, event.target.value);
                }}
              />
            </div>
            <div>
              <DropdownField
                label="Allergen List"
                firstInput="Select"
                options={AllergyList}
                name={values.allergen}
                value={formik.values.allergen}
                onChange={(event: ChangeEvent<HTMLSelectElement>) => {
                  formik.setFieldValue(`allergen`, event.target.value);
                }}
              />
            </div>{" "}
            <div>
              <DropdownField
                label="Severity List"
                firstInput="Select"
                options={Severitylist}
                name={values.serverity}
                value={formik.values.serverity}
                onChange={(event: ChangeEvent<HTMLSelectElement>) => {
                  formik.setFieldValue(`serverity`, event.target.value);
                }}
              />
            </div>
            {[
              {
                label: "Last Seen",
                name: "lastSeen",
                placeholder: "Enter Last Seen",
                type: "date",
              },
              {
                label: "Reaction",
                name: "reaction",
                placeholder: "Enter reaction",
                type: "text",
              },
            ].map(({ label, name, placeholder, type }) => (
              <div key={name} className="flex flex-col gap-0">
                <InputField
                  label={label}
                  name={name}
                  type={type}
                  placeholder={placeholder}
                />
              </div>
            ))}
            <div className="flex justify-end mt-2 mb-2">
              <ActionButton hideCancel="hidden" onSubmit={handleSubmit} />
            </div>
          </div>
        </Form>
      </FormikProvider>
      <div></div>

      {/* Allergies Table */}
      <div className="overflow-x-auto">
        <MasterTable
          columns={tableData.columns}
          rows={tableData.rows}
          loading={!data}
        />
      </div>
    </div>
  );
};

export default Allergies;
