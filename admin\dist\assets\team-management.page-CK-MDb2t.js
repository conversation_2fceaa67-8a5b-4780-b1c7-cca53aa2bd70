import{bR as y,bV as k,a1 as p,a2 as t,a9 as j,bx as w,aa as T,b3 as N,dt as c,ao as D,a5 as h,aM as v,ad as M,dg as L,aP as B,du as C,aR as S}from"./index-ClX9RVH0.js";import{G as A}from"./GenericCard.components-BAGnoJGb.js";const b=y(k.BOD,"Team Management"),E=b.useCreate,F=b.useGetAll,O=b.useUpdate,I=b.useDeleteWithQuery,W=({onCancel:a,teamProps:e})=>{var m,d,i;const{mutateAsync:n,isPending:l}=E(),{mutateAsync:s,isPending:o}=O(),u=p({initialValues:{category:(e==null?void 0:e.category)??"",designation:(e==null?void 0:e.designation)??"",fullName:(e==null?void 0:e.fullName)??"",images:(e==null?void 0:e.images)??"",facebook:((m=e==null?void 0:e.mediaLink)==null?void 0:m.facebook)??"",instagram:((d=e==null?void 0:e.mediaLink)==null?void 0:d.instagram)??"",twitter:((i=e==null?void 0:e.mediaLink)==null?void 0:i.twitter)??""},onSubmit:async g=>{const f={...g,mediaLink:{facebook:g.facebook,instagram:g.instagram,twitter:g.twitter}};e?(await s({_id:e._id??"",entityData:f}),a()):(await n(f),a())}}),{handleSubmit:r}=u,x=[{label:"Board of Directors",value:"BOD"},{label:"Senior Management",value:"SM"}];return t.jsxs(j,{value:u,children:[(l||o)&&t.jsx(w,{isLoading:l||o}),t.jsxs(T,{className:"grid grid-cols-2 gap-4 p-6",children:[t.jsxs("div",{className:"flex col-span-2 justify-between items-center border-b pb-4 mb-2",children:[t.jsxs("div",{children:[t.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:e?"Edit Team Member":"Create Team Member"}),t.jsx("p",{className:"text-sm text-gray-500",children:e?"Update the details of your Team member.":"Fill out the form to create a new Team member."})]}),t.jsx("button",{onClick:a,className:"text-gray-500 hover:text-gray-700",children:t.jsx(N,{icon:"mdi:close",className:"w-6 h-6"})})]}),t.jsx(c,{name:"category",label:"Cateogry",type:"select",options:x}),t.jsx(c,{name:"designation",label:"Designation",type:"text",placeholder:"Enter designation"}),t.jsx(c,{name:"fullName",label:"Full Name",type:"text",placeholder:"Enter full name"}),t.jsx(c,{name:"facebook",label:"Facebook link",type:"text",placeholder:"Enter facebook url"}),t.jsx(c,{name:"twitter",label:"Twitter link",type:"text",placeholder:"Enter twitter url"}),t.jsx(c,{name:"instagram",label:"Instagram link",type:"text",placeholder:"Enter instagram url"}),t.jsx("section",{className:"col-span-2",children:t.jsx(c,{name:"images",label:"Image",type:"file",accept:"image/*"})}),t.jsx("section",{className:"col-span-2",children:t.jsx(D,{onCancel:a,onSubmit:r})})]})]})},$=()=>({getTitle:a=>(a==null?void 0:a.fullName)||"Unknown Director",getDescription:a=>{const e=(a==null?void 0:a.category)||"",n=(a==null?void 0:a.designation)||"";return`${n}${e&&n?" - ":""}${e}`},getImage:a=>(a==null?void 0:a.images)||"/placeholder.svg?height=200&width=400",getMetadata:a=>{const e=[];a!=null&&a.designation&&e.push({icon:"mdi:account-tie",label:"Position",value:a.designation}),a!=null&&a.category&&e.push({icon:"mdi:tag",label:"Department",value:a.category});const n=(a==null?void 0:a.mediaLink)||{},l=Object.values(n).filter(s=>s).length;return l>0&&e.push({icon:"mdi:share-variant",label:"Social Links",value:`${l} platform${l>1?"s":""}`}),e},getKeyPoints:a=>{const e=(a==null?void 0:a.mediaLink)||{},n=[],l={facebook:"Facebook",messanger:"Messenger",instagram:"Instagram",whatsapp:"WhatsApp",viber:"Viber",tiktok:"TikTok",twitter:"Twitter",linkedin:"LinkedIn",snapchat:"Snapchat",youtube:"YouTube",telegram:"Telegram",pinterest:"Pinterest",reddit:"Reddit",tumblr:"Tumblr"};return Object.entries(e).forEach(([s,o])=>{o&&o.trim()&&s in l&&n.push(l[s])}),n},cardStyle:"default",maxDescriptionLength:100,maxKeyPoints:6}),G=({setCareerProps:a})=>{var r,x,m,d;const[e,n]=h.useState({limit:5,page:1}),l=v({page:e.page,limit:e.limit}),{data:s}=F(l),{mutateAsync:o}=I(),u=$();return M(),t.jsxs("div",{className:"-mx-4 flex flex-col gap-4",children:[t.jsx("section",{className:"grid grid-cols-3 gap-6",children:(x=(r=s==null?void 0:s.data)==null?void 0:r.boardofdirectors)==null?void 0:x.map(i=>t.jsx(A,{item:i,config:u,onEdit:g=>{a({modal:!0,selectedData:g})},onDelete:async()=>{await o({id:JSON.stringify([i==null?void 0:i._id])})}}))}),t.jsx(L,{currentPage:e.page,limit:e.limit,onClick:i=>{i.page&&n({...e,page:i.page}),i.limit&&n({...e,limit:i.limit})},totalPage:(d=(m=s==null?void 0:s.data)==null?void 0:m.pagination)==null?void 0:d.pages})]})},Q=()=>{const[a,e]=h.useState({modal:!1,selectedData:{}}),n=B(()=>e({...a,modal:!1}));return t.jsxs("div",{children:[t.jsx(C,{title:"Team Management",subtitle:"Manage your team",buttonText:"Add Team Members",onButtonClick:()=>{e({modal:!0,selectedData:void 0})}}),t.jsx("section",{className:"px-8 ",children:t.jsx(G,{setCareerProps:e})}),a.modal&&t.jsx(S,{ref:n,classname:"h-[650px] overflow-scroll w-[700px]",children:t.jsx(W,{teamProps:a.selectedData,onCancel:()=>e({...a,modal:!1})})})]})};export{Q as TeamManagementPage};
