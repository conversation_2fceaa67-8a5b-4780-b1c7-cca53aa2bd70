import{a5 as t,aP as w,bD as A,a2 as a,af as S,bE as k,aR as F,j as C,bF as T,b3 as r,aj as D}from"./index-ClX9RVH0.js";const M=()=>{var d;const[x,s]=t.useState(!1),[y,o]=t.useState(void 0),[h,f]=t.useState(""),[m,j]=t.useState("all"),v=w(()=>s(!1)),{data:n}=A(),l=(d=n==null?void 0:n.data)==null?void 0:d.advancePayment,c={columns:[{key:"paymentNo",title:"Payment No."},{key:"date",title:"Date"},{key:"paymentFor",title:"Payment For"},{key:"paidTo",title:"Paid To"},{key:"amount",title:"Amount"},{key:"status",title:"Status"},{key:"paymentMethod",title:"Payment Method"},{title:"Action",key:"action"}],rows:l==null?void 0:l.map(e=>{var i,u,p;return{paymentNo:e==null?void 0:e.paymentNo,date:e==null?void 0:e.date,paymentFor:e==null?void 0:e.paymentFor,paidTo:(p=(u=(i=e==null?void 0:e.paidAgainst)==null?void 0:i.commonInfo)==null?void 0:u.personalInfo)==null?void 0:p.fullName,amount:e==null?void 0:e.paymentAmount,status:e==null?void 0:e.paymentStatus,paymentMethod:e==null?void 0:e.paymentMethod,action:a.jsx(S,{onShow:()=>b(e),onEdit:()=>N(e),onDelete:()=>P(e)})}})},{mutateAsync:g}=k(),b=e=>{o(e),console.log("View payment:",e)},N=e=>{o(e),s(!0)},P=async e=>{await g({id:JSON.stringify([e==null?void 0:e._id])})};return x?a.jsx(F,{ref:v,classname:"h-[650px] overflow-scroll w-[850px]",children:a.jsx(C,{onClose:()=>{s(!1),o(void 0)},editData:y})}):a.jsx("div",{className:"flex flex-col",children:a.jsxs("div",{className:"p-4 ",children:[a.jsxs("div",{className:"",children:[a.jsxs("div",{className:"flex justify-between items-center mb-4",children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-800",children:"Advance Payments"}),a.jsx(T,{onClick:()=>s(!0),title:"Advance Payment"})]}),a.jsxs("div",{className:"flex gap-4 items-center",children:[a.jsxs("div",{className:"relative flex-1 max-w-md",children:[a.jsx(r,{icon:"material-symbols:search",className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),a.jsx("input",{type:"text",placeholder:"Search by payment number or beneficiary...",value:h,onChange:e=>f(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),a.jsxs("div",{className:"flex items-center gap-2",children:[a.jsx(r,{icon:"material-symbols:filter-list",className:"w-5 h-5 text-gray-400"}),a.jsxs("select",{value:m,onChange:e=>j(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",children:[a.jsx("option",{value:"all",children:"All Status"}),a.jsx("option",{value:"Pending Approval",children:"Pending Approval"}),a.jsx("option",{value:"Approved",children:"Approved"}),a.jsx("option",{value:"Paid",children:"Paid"}),a.jsx("option",{value:"Rejected",children:"Rejected"})]})]}),a.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",children:[a.jsx(r,{icon:"material-symbols:download",className:"w-5 h-5"}),"Export"]})]})]}),a.jsx("div",{className:"py-6",children:a.jsx(D,{rows:c.rows,loading:!1,columns:c.columns})})]})})};export{M as default};
