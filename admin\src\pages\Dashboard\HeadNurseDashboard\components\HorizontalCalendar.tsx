import { useState } from "react";

type WeekDate = Date;

const HorizontalCalender = ({
  selectedMonth,
  selectedYear,
  onDateSelect,
}: {
  selectedMonth: number;
  selectedYear: number;
  onDateSelect: (date: Date) => void;
}) => {
  const today = new Date();
  const getStartOfWeek = (date: Date): Date => {
    const startDate = new Date(date);
    const dayOfWeek = startDate.getDay();
    startDate.setDate(startDate.getDate() - dayOfWeek);
    return startDate;
  };

  const [currentDate, setCurrentDate] = useState<Date>(getStartOfWeek(today));

  if (
    currentDate.getMonth() !== selectedMonth ||
    currentDate.getFullYear() !== selectedYear
  ) {
    setCurrentDate(new Date(selectedYear, selectedMonth));
  }

  const generateWeek = (startDate: Date): WeekDate[] => {
    const week: WeekDate[] = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startDate);
      day.setDate(startDate.getDate() + i);
      week.push(day);
    }
    return week;
  };

  const weekDates = generateWeek(currentDate);

  const handlePrevWeek = (): void => {
    const newStartDate = new Date(currentDate);
    newStartDate.setDate(currentDate.getDate() - 7);
    setCurrentDate(newStartDate);
  };

  const handleNextWeek = (): void => {
    const newStartDate = new Date(currentDate);
    newStartDate.setDate(currentDate.getDate() + 7);
    setCurrentDate(newStartDate);
  };

  const isCurrentDate = (date: Date): boolean => {
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  return (
    <div className="mx-auto  ">
      <div className="flex items-center justify-between">
        <button
          className=" bg-blue-500 rounded-md hover:bg-blue-600 transition"
          onClick={handlePrevWeek}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="36"
            height="36"
            viewBox="0 0 16 16"
          >
            <path
              fill="none"
              stroke="currentColor"
              d="M9.5 4.5L6 8l3.5 3.5"
              strokeWidth="1"
            />
          </svg>
        </button>

        <div className="flex gap-2 overflow-x-auto overflow-y-hidden">
          {weekDates.map((date, index) => (
            <div
              key={index}
              onClick={() => onDateSelect(date)}
              className={`w-16 sm:w-24 p-2 text-center rounded-lg cursor-pointer ${
                isCurrentDate(date) ? "text-DashboardTitle " : " "
              }`}
            >
              <div className="font-semibold text-xs ">
                {date.toLocaleDateString("en-US", { weekday: "short" })}
              </div>
              <div className="text-sm  rounded-full">{date.getDate()}</div>
            </div>
          ))}
        </div>

        <button
          className="bg-blue-500 rounded-md hover:bg-blue-600 transition rotate-180"
          onClick={handleNextWeek}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="36"
            height="36"
            viewBox="0 0 16 16"
          >
            <path
              fill="none"
              stroke="currentColor"
              d="M9.5 4.5L6 8l3.5 3.5"
              strokeWidth="1"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default HorizontalCalender;
