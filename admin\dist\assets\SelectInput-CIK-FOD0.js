import{a5 as t,a2 as s,a4 as w,bf as g,c8 as v}from"./index-ClX9RVH0.js";const y=({options:r,placeholder:d="Type to search...",name:u,label:i,border:f=!0})=>{const[a,n]=t.useState(""),[c,m]=t.useState(r),[x,l]=t.useState(!1),o=t.useRef(null);t.useEffect(()=>{m(r.filter(e=>e.label.toLowerCase().includes(a.toLowerCase())))},[a,r]),t.useEffect(()=>{const e=p=>{o.current&&!o.current.contains(p.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);const h=e=>{n(e),l(!1)};return s.jsxs("div",{className:"relative ",ref:o,children:[s.jsxs("div",{className:"flex flex-col gap-2",children:[s.jsx("label",{className:"flex gap-1",children:s.jsx(w,{variant:"grey-600",size:"body-sm-lg",children:i})}),s.jsx(g,{type:"text",value:a,onChange:e=>{n(e.target.value),l(!0)},name:u,placeholder:d,className:v(`${f&&"border"} border-grey-200 rounded  text-black py-2 px-4 outline-none w-full `)})]}),x&&c.length>0&&s.jsx("ul",{className:"absolute w-full mt-1 bg-white border rounded-md shadow-md max-h-40 overflow-y-auto",children:c.map(e=>s.jsx("li",{onClick:()=>h(e.value),className:"px-3 py-1 cursor-pointer hover:bg-gray-200",children:e.label},e.value))})]})};export{y as S};
