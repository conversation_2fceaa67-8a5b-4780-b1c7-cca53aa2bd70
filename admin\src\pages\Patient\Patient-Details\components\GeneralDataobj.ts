// import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Bi<PERSON><PERSON> } from "react-icons/bi"; // Import React Icons

import { MdLocalPhone } from "react-icons/md";
import { MdOutlineFileDownload } from "react-icons/md";
import { AiOutlineMessage } from "react-icons/ai";
import { BsFillTelephonePlusFill } from "react-icons/bs";

export const leftItems = [
  { label: "Age", value: "32 Yrs" },
  { label: "Gender", value: "Female" },
  { label: "Blood Group", value: "O+" },
  { label: "Marital Status", value: "Married" },
];

export const rightItems = [
  { label: "Occupation", value: "Software Developer" },
  { label: "Insurance Provider", value: "Everguard Insurance" },
  { label: "Primary Physician", value: "Dr. <PERSON>" },
  { label: "Admission Status", value: "Admitted" },
];

export const secondCardData = [
  { label: "Phone Number", value: "+977-**********" },
  { label: "Email", value: "<EMAIL>" },
  { label: "Address", value: "Maitidevi-5, KTM" },
  { label: "Emergency Contact", value: "+977-**********" },
];

// Array of icon objects
export const iconArray = [
  { icon: MdLocalPhone },
  { icon: AiOutlineMessage },
  { icon: MdOutlineFileDownload },
  { icon: BsFillTelephonePlusFill },
];
