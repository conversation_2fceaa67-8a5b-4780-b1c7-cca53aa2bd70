import{aS as f,aT as A,aU as w,aV as S,aW as P,aX as c,a1 as q,a2 as e,a4 as k,a9 as F,aa as I,ac as _,ab as R,aY as B,ao as T}from"./index-ClX9RVH0.js";import{P as V}from"./ProductMultiSelect-DbxM3Xg6.js";const $=[{label:"Available",value:"true"},{label:"In Active",value:"false"}],O=({editData:s,onClose:r})=>{var m,o,u;const{mutate:x}=f(),{mutate:v}=A(),{data:n}=w(),d=[{label:"All",value:""},...((o=(m=n==null?void 0:n.data)==null?void 0:m.departments)==null?void 0:o.map(t=>({label:t==null?void 0:t.name,value:t==null?void 0:t._id})))||[]],h=d.filter(t=>t.value!=="").map(t=>t.value),g=S().shape({categoryName:c().required("Product category name is required"),isActive:c().required("Status is required"),description:c().required("Description is required"),department:P().of(c().min(1,"Department items must be non-empty strings")).min(1,"At least one department must be selected").required("Department field is required")}),l=q({initialValues:{categoryName:(s==null?void 0:s.categoryName)??"",isActive:s!=null&&s.isActive?"true":"false",description:(s==null?void 0:s.description)??"",department:((u=s==null?void 0:s.department)==null?void 0:u.map(t=>t._id))||[]},validationSchema:g,enableReinitialize:!0,onSubmit:t=>{const j=t.isActive==="true",N=t.department.includes("")?h:t.department,p={categoryName:t.categoryName,isActive:j,description:t.description,department:N};s&&s._id?v({entityData:p,_id:s._id}):x(p),r==null||r()}}),{handleSubmit:b,handleChange:y,errors:a,touched:i,values:z}=l;return e.jsxs("div",{className:" ",children:[e.jsx(k,{as:"h3",text:"Add Product Category",size:"body-lg-lg",variant:"primary-blue",className:"my-4 text-primary flex justify-center items-center"}),e.jsx(F,{value:l,children:e.jsxs(I,{children:[e.jsxs("div",{className:"my-3 mx-4 border-2 border-[#e6e6e6] rounded-xl",children:[e.jsx("div",{className:"absolute cursor-pointer hover:text-red top-5 right-10",onClick:()=>{r==null||r()},children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",children:e.jsx("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeWidth:2,d:"m8 8l4 4m0 0l4 4m-4-4l4-4m-4 4l-4 4"})})}),e.jsxs("div",{className:"flex px-2 pt-4 pb-1 mx-2 items-center gap-4",children:[e.jsxs("div",{className:"w-full",children:[e.jsx(_,{label:"Category Name",type:"text",name:"categoryName",placeholder:"Routine patient transfers"}),a.categoryName&&i.categoryName&&e.jsx("div",{className:"text-red text-xs ",children:a.categoryName})]}),e.jsxs("div",{className:"w-full ",children:[e.jsx(R,{firstInput:"Select",label:"Status",name:"isActive",value:l.values.isActive,onChange:y,options:$}),a.isActive&&i.isActive&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.isActive})]}),e.jsxs("div",{className:"w-full",children:[e.jsx(V,{name:"department",label:"Department",options:d}),a.department&&i.department&&e.jsx("div",{className:"text-red text-xs ",children:a.department})]})]}),e.jsx("div",{className:"flex px-4 items-center gap-6 py-4 ",children:e.jsxs("div",{className:"w-full",children:[e.jsx(B,{label:"Short Description",name:"description",placeholder:"Routine patient transfers"}),a.description&&i.description&&e.jsx("div",{className:"text-red text-xs ",children:a.description})]})})]}),e.jsx("div",{className:" mr-4 mb-4",children:e.jsx(T,{onCancel:()=>{r==null||r()},submitLabel:"Save",submitlabelButton:!0,onSubmit:b})})]})})]})};export{O as default};
