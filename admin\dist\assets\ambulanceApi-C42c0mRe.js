import{bR as a,bV as t}from"./index-ClX9RVH0.js";const s=a(t.AMBULANCELIST,"Ambulance"),l=s.useCreate,c=s.useGetAll,A=s.useUpdate,r=s.useDeleteWithQuery,u=a(t.AMBULANCETYP<PERSON>,"Ambulance Type"),y=u.useCreate,b=u.useGetAll,m=u.useUpdate,o=u.useDeleteWithQuery,e=a(t.AMBULANCEINQUIRY,"Ambulance Inquiry"),i=e.useCreate,p=e.useGetAll,I=e.useUpdate,C=e.useGetById,U=e.useDeleteWithQuery;export{c as a,b,i as c,C as d,I as e,m as f,o as g,p as h,U as i,r as j,A as k,l,y as u};
