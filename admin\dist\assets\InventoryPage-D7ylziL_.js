import{a5 as a,a2 as e,b3 as pe,ad as he,aP as ue,aD as ge,aM as je,a1 as Ne,co as ne,av as n,cp as fe,aK as be,af as we,ah as ke,aQ as Se,aJ as Pe,aj as Ce,aR as Ie,cq as Ae,a9 as Ee,aa as Te,ac as me,ao as Fe}from"./index-ClX9RVH0.js";function Re({inventoryData:s,onClose:o,setInventoryProps:p}){var E,T,F,R,M,g,x,O,i,w,j,H,U,k,B,d,S,L,K,P,$,N,c,C,r,m,q,Q,V,_,G,W,J,z,X,Z,v,y,D,ee,se,te,ce,le,de,re,ae,xe,ie;const[h,I]=a.useState("overview"),l=t=>{try{return new Date(t).toLocaleDateString("ne-NP",{year:"numeric",month:"short",day:"numeric"})}catch{return t}},f=t=>{if(!t)return 1/0;const Y=new Date,oe=new Date(t).getTime()-Y.getTime();return Math.ceil(oe/(1e3*60*60*24))},b=t=>{switch(t.toUpperCase()){case"IN-STOCK":return"bg-green text-darkishGreen";case"LOW-STOCK":return"bg-yellow text-white";case"OUT-OF-STOCK":return"bg-red text-white";default:return"bg-border text-darkish-black"}},A=t=>t===1/0?"text-gray-500":t<0?"text-red-600":t<30?"text-amber-600":t<90?"text-blue-600":"text-green-600",u=s!=null&&s.totalStock?Math.round((s==null?void 0:s.availableStock)/(s==null?void 0:s.totalStock)*100):0;return e.jsx("div",{className:"min-h-screen p-6",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:(E=s==null?void 0:s.product)==null?void 0:E.name}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsxs("span",{className:"text-sm text-gray-500 mr-3",children:["Product Code: ",(T=s==null?void 0:s.product)==null?void 0:T.productCode]}),e.jsx("span",{className:"px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800",children:(R=(F=s==null?void 0:s.product)==null?void 0:F.category)==null?void 0:R.categoryName})]})]}),o&&e.jsx("div",{className:"cursor-pointer hover:text-red-600",onClick:o,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",children:e.jsx("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeWidth:2,d:"m8 8l4 4m0 0l4 4m-4-4l4-4m-4 4l-4 4"})})})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Stock Status"}),e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:e.jsx("div",{className:`h-2.5 rounded-full ${u>50?"bg-green-500":u>20?"bg-yellow-500":"bg-red-500"}`,style:{width:`${u}%`}})}),e.jsxs("span",{className:"ml-2 text-sm font-medium text-gray-700",children:[u,"%"]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Available"}),e.jsx("p",{className:"text-xl font-semibold text-gray-900",children:s==null?void 0:s.availableStock})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Total"}),e.jsx("p",{className:"text-xl font-semibold text-gray-900",children:s==null?void 0:s.totalStock})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Latest Batch"}),((M=s==null?void 0:s.entries)==null?void 0:M.length)>0&&e.jsxs("div",{children:[e.jsx("div",{className:"flex items-center mb-2",children:e.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${b((x=(g=s==null?void 0:s.entries)==null?void 0:g[0])==null?void 0:x.status)}`,children:(i=(O=s==null?void 0:s.entries)==null?void 0:O[0])==null?void 0:i.status})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Batch No"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:(j=(w=s==null?void 0:s.entries)==null?void 0:w[0])==null?void 0:j.batchNo})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Expiry"}),e.jsx("p",{className:`text-sm font-medium ${A(f((U=(H=s==null?void 0:s.entries)==null?void 0:H[0])==null?void 0:U.expiryDate))}`,children:(B=(k=s==null?void 0:s.entries)==null?void 0:k[0])!=null&&B.expiryDate?l((S=(d=s==null?void 0:s.entries)==null?void 0:d[0])==null?void 0:S.expiryDate):"N/A"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Pricing"}),((L=s==null?void 0:s.entries)==null?void 0:L.length)>0&&e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Purchase Rate"}),e.jsxs("p",{className:"text-xl font-semibold text-gray-900",children:["Rs."," ",($=(P=(K=s==null?void 0:s.entries)==null?void 0:K[0])==null?void 0:P.purchaseRate)==null?void 0:$.toFixed(2)]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"MRP Rate"}),e.jsxs("p",{className:"text-xl font-semibold text-gray-900",children:["Rs. ",(C=(c=(N=s==null?void 0:s.entries)==null?void 0:N[0])==null?void 0:c.mrpRate)==null?void 0:C.toFixed(2)]})]})]})]})]})}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-200",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsx("button",{onClick:()=>I("overview"),className:`py-4 px-1 border-b-2 font-medium text-sm ${h==="overview"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Overview"}),e.jsx("button",{onClick:()=>I("batches"),className:`py-4 px-1 border-b-2 font-medium text-sm ${h==="batches"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Batches"})]})})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[h==="overview"&&e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"Product Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-md font-medium text-gray-900 mb-3",children:"Basic Information"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Product Name"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:(r=s==null?void 0:s.product)==null?void 0:r.name})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Product Code"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:(m=s==null?void 0:s.product)==null?void 0:m.productCode})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Category"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:(Q=(q=s==null?void 0:s.product)==null?void 0:q.category)==null?void 0:Q.categoryName})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Created On"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:l((V=s==null?void 0:s.product)==null?void 0:V.createdAt)})]}),e.jsxs("div",{className:"col-span-2",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Description"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:((_=s==null?void 0:s.product)==null?void 0:_.description)||"N/A"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-md font-medium text-gray-900 mb-3",children:"Category Details"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Category Name"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:(W=(G=s==null?void 0:s.product)==null?void 0:G.category)==null?void 0:W.categoryName})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Category Description"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:((z=(J=s==null?void 0:s.product)==null?void 0:J.category)==null?void 0:z.description)||"N/A"})]})]})]}),e.jsxs("div",{className:"md:col-span-2 mt-4",children:[e.jsx("h3",{className:"text-md font-medium text-gray-900 mb-3",children:"Supplier Information"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Supplier Name"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:((D=(y=(v=(Z=(X=s==null?void 0:s.entries)==null?void 0:X[0])==null?void 0:Z.vendor)==null?void 0:v.commonInfo)==null?void 0:y.personalInfo)==null?void 0:D.fullName)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:((te=(se=(ee=s==null?void 0:s.entries)==null?void 0:ee[0])==null?void 0:se.vendor)==null?void 0:te.email)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),e.jsx("p",{className:"text-sm font-medium text-gray-900",children:((xe=(ae=(re=(de=(le=(ce=s==null?void 0:s.entries)==null?void 0:ce[0])==null?void 0:le.vendor)==null?void 0:de.commonInfo)==null?void 0:re.contactInfo)==null?void 0:ae.phone)==null?void 0:xe.primaryPhone)||"N/A"})]})]})]})]})]}),h==="batches"&&e.jsxs("div",{children:[e.jsx("div",{className:"flex justify-between items-center mb-4",children:e.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Batch Information"})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Batch No"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Invoice"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Manufactured"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Expiry"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Stock"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Used Stock"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Available Stock"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:(ie=s==null?void 0:s.entries)==null?void 0:ie.map(t=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t==null?void 0:t.batchNo}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t==null?void 0:t.invoiceNo}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:l(t==null?void 0:t.manufacturedDate)}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.jsx("span",{className:`${A(f(t==null?void 0:t.expiryDate))}`,children:t.expiryDate?l(t==null?void 0:t.expiryDate):"N/A"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t==null?void 0:t.totalStock}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t==null?void 0:t.stockInUse}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t==null?void 0:t.availableStock}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${b(t==null?void 0:t.status)}`,children:t==null?void 0:t.status})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx(pe,{icon:"tabler:edit",onClick:()=>{p==null||p({modal:!0,selectedEntry:t}),o==null||o()}})})]},t==null?void 0:t._id))})]})})]})]})]})})}const Ue=()=>{he();const[s,o]=a.useState(""),[p,h]=a.useState(null),[I,l]=a.useState(!1);ue(()=>{l(!1)});const f=a.useRef(null),{contentRef:b,exportToPDF:A,exportToExcel:u,handlePrint:E}=ge(),T=()=>{u(N.rows,"Inventorylist.xlsx")},F=()=>{A(b.current,"Inventorylist.pdf",f)},R=()=>{l(!1)},M=c=>{h(c),l(!0)},[g,x]=a.useState({modal:!1,selectedEntry:void 0}),O=()=>p?e.jsx(Re,{inventoryData:p,onClose:R,setInventoryProps:x}):null,[i,w]=a.useState({search:"",selectedHospInvProductCategory:"",selectedHospInvStatus:""}),[j,H]=a.useState({page:1,limit:10}),U=s,k=({page:c,limit:C})=>{H(r=>({...r,page:c??1,limit:C??r.limit}))},B=je({search:i.search,category:i.selectedHospInvProductCategory,combinedData:1,...U?{}:{page:j.page,limit:j.limit}}),d=Ne({initialValues:{inUsedStock:0,remarks:""},onSubmit:()=>{}});a.useEffect(()=>{k({page:1})},[]);const{data:S,isLoading:L,isSuccess:K}=ne(B),P=n.get(S,"data.pagination",{}),{mutateAsync:$}=fe(),N={columns:[{title:"Item Name",key:"itemName"},{title:"Categories",key:"categories"},{title:"Current Stock",key:"currentStock"},{title:"Last Restocked",key:"lastRestocked"},{title:"Available Status",key:"status"},{title:"Action",key:"action"}],rows:n.get(S,"data.hospitalProductsInventory",[]).map((c,C)=>{const r=Number(n.get(c,"availableStock",0));let m;return r===0?m="Out of Stock":r>0&&r<20?m="Low Stock":m="On Stock",{itemName:n.get(c,"product.name","N/A"),categories:n.get(c,"product.category.categoryName","N/A"),currentStock:r||"N/A",lastRestocked:be(n.get(c,"product.updatedAt","N/A")).format("YYYY-MM-DD"),status:e.jsx(ke,{status:m}),action:e.jsx(we,{onShow:()=>M(c)})}})};return e.jsxs("div",{children:[e.jsx(Se,{headerTitle:"Inventory",onSearch:!0,toSearch:"Date, BatchNo, InvoiceNo, Expiry Date, Manufactured Date",onSearchFunc:c=>{w({...i,search:c})},onHospInvProductCategory:!0,onHospInvProductCategorySelectFunc:c=>w({...i,selectedHospInvProductCategory:c}),onExportFunctions:!0,onExportExcel:T,onExportPDF:F,onPrint:E}),e.jsxs("div",{className:"py-4",children:[e.jsx(Pe,{ref:f}),e.jsx(Ce,{columns:N.columns,rows:N.rows,ref:b,color:"bg-white",textcolor:"text-gray-400",loading:L,pagination:{totalPage:P.pages||1,currentPage:P.page||1,limit:j.limit||10,onClick:k}})]}),I&&e.jsx(Ie,{classname:"w-[950px] max-h-[80vh] overflow-y-auto",children:O()}),g.modal&&e.jsx(Ae,{onClose:()=>{l(!0),x({modal:!1,selectedEntry:void 0})},heading:"Total Used Stock",className:" min-w-[350px]  border bg-red border-primary",children:e.jsx(Ee,{value:d,children:e.jsxs(Te,{className:"flex flex-col gap-6",children:[e.jsx(me,{value:d.values.inUsedStock,label:"Total Used Stock",onChange:c=>d.setFieldValue("inUsedStock",c.target.value)}),e.jsx(me,{value:d.values.remarks,label:"Remarks",onChange:c=>d.setFieldValue("remarks",c.target.value)}),e.jsx(Fe,{onCancel:()=>{l(!0),x({modal:!1,selectedEntry:void 0})},onSubmit:async()=>{var c;await $({_id:((c=g.selectedEntry)==null?void 0:c._id)??"",entityData:{...g.selectedEntry,stockInUse:d.values.inUsedStock,remarks:d.values.remarks}}),l(!0),x({modal:!1,selectedEntry:void 0})}})]})})})]})};export{Ue as default};
