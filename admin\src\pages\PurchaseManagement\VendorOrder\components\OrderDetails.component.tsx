import { Icon } from '@iconify/react/dist/iconify.js';

interface OrderDetailsProps {
  orderId: string;
  onClose: () => void;
}

export const OrderDetails = ({ orderId, onClose }: OrderDetailsProps) => {
  // Mock data - replace with real API call
  const orderData = {
    orderId: 'MED-1234',
    orderDate: 'March 01, 2024',
    totalAmount: 2305.0,
    currentStatus: 'Shipped',
    deliveryAddress: {
      name: 'Hospital Central',
      address: 'Pharmacy, 2nd Floor',
      city: 'Ave. Main St. 123',
      zipCode: '20230, USA',
    },
    department: 'Internal Medicine',
    requestedBy: 'Dr. <PERSON>',
    statusHistory: [
      {
        status: 'Order Placed',
        date: '2024-02-28 10:30 AM',
        description: 'Order confirmed and sent to warehouse for processing.',
      },
      {
        status: 'Processing',
        date: '2024-03-01 09:15 AM',
        description: 'Items being picked and packed for shipment.',
      },
      {
        status: 'Shipped',
        date: '2024-03-01 02:00 PM',
        description:
          'Package with tracking number ABC123XYZ. Estimated delivery in 2 days.',
      },
      {
        status: 'In Transit',
        date: '2024-03-02 08:00 AM',
        description:
          'Package en route to delivery address. Scan at regional distribution center.',
      },
      {
        status: 'Out for Delivery',
        date: '2024-03-03 06:00 AM',
        description:
          'Package is out for delivery and expected to arrive today.',
      },
    ],
    orderedItems: [
      {
        id: 1,
        name: 'Sterile Surgical Gown',
        brand: 'MedPro',
        sku: 'MSG-001',
        quantity: 50,
        unitPrice: 15.0,
        total: 750.0,
      },
      {
        id: 2,
        name: 'Medical Syringes',
        brand: 'SafeInject',
        sku: 'SYR-10',
        quantity: 200,
        unitPrice: 0.75,
        total: 150.0,
      },
      {
        id: 3,
        name: 'Disposable Face Masks N95',
        brand: 'ProtectMax',
        sku: 'N95-MASK',
        quantity: 100,
        unitPrice: 1.2,
        total: 120.0,
      },
      {
        id: 4,
        name: 'Alcohol Wipes (Box of 200)',
        brand: 'CleanCare',
        sku: 'AW-200',
        quantity: 10,
        unitPrice: 8.5,
        total: 85.0,
      },
      {
        id: 5,
        name: 'IV Solution Dextrose 5%',
        brand: 'FluidTech',
        sku: 'IV-D5',
        quantity: 20,
        unitPrice: 25.0,
        total: 500.0,
      },
      {
        id: 6,
        name: 'Digital Thermometer',
        brand: 'TempCheck',
        sku: 'DT-PRO',
        quantity: 5,
        unitPrice: 35.0,
        total: 175.0,
      },
      {
        id: 7,
        name: 'Stethoscope Professional',
        brand: 'CardioSound',
        sku: 'STETH-PRO',
        quantity: 2,
        unitPrice: 150.0,
        total: 300.0,
      },
      {
        id: 8,
        name: 'Surgical Gloves (Disposable)',
        brand: 'SafeHands',
        sku: 'SG-DISP',
        quantity: 30,
        unitPrice: 7.5,
        total: 225.0,
      },
    ],
    relatedDocuments: [
      {
        name: 'Purchase Order',
        id: '#PO-7890',
        type: 'PDF',
      },
      {
        name: 'Delivery Receipt (Draft)',
        id: 'DRAFT',
        type: 'DOCX',
      },
      {
        name: 'Medical Supplies Catalog',
        id: 'PDF',
        type: 'PDF',
      },
      {
        name: 'Product Images for Item 1',
        id: 'LINK',
        type: 'LINK',
      },
    ],
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'shipped':
        return 'bg-blue text-white';
      case 'processing':
        return 'bg-yellow-100 text-yellow-800';
      case 'order placed':
        return 'bg-green-100 text-green-800';
      case 'in transit':
        return 'bg-purple-100 text-purple-800';
      case 'out for delivery':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'shipped':
        return 'material-symbols:local-shipping';
      case 'processing':
        return 'material-symbols:settings';
      case 'order placed':
        return 'material-symbols:check-circle';
      case 'in transit':
        return 'material-symbols:flight';
      case 'out for delivery':
        return 'material-symbols:delivery-truck';
      default:
        return 'material-symbols:info';
    }
  };

  return (
    <div className="bg-white rounded shadow-xl">
      {/* Header */}
      <div className="flex items-center justify-between py-1 px-6 border-b border-gray-200">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-bold text-gray-900">Order Details:</h2>
          <div className="flex items-center gap-2">
            <span className="text-base font-medium text-gray-700">
              {orderData.orderId}
            </span>
            <span
              className={`px-3 py-1 rounded text-sm font-medium ${getStatusColor(
                orderData.currentStatus
              )}`}
            >
              {orderData.currentStatus}
            </span>
          </div>
        </div>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <Icon
            icon="material-symbols:close"
            className="text-xl text-gray-500"
          />
        </button>
      </div>

      {/* Content */}
      <div className="flex h-[calc(90vh-120px)]">
        {/* Left Side - Order Summary and Items */}
        <div className=" flex flex-col gap-2 px-4 py-2 overflow-y-auto">
          {/* Order Summary */}
          <div className="border rounded px-1 py-2  mb-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Order Summary
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Icon
                    icon="material-symbols:receipt"
                    className="text-blue-600"
                  />
                  <div>
                    <p className="text-sm text-gray-600">Order ID</p>
                    <p className="font-medium">{orderData.orderId}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Icon
                    icon="material-symbols:attach-money"
                    className="text-green-600"
                  />
                  <div>
                    <p className="text-sm text-gray-600">Total Amount</p>
                    <p className="font-medium">
                      ${orderData.totalAmount.toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Icon
                    icon="material-symbols:calendar-today"
                    className="text-purple-600"
                  />
                  <div>
                    <p className="text-sm text-gray-600">Order Date</p>
                    <p className="font-medium">{orderData.orderDate}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Icon
                    icon="material-symbols:local-shipping"
                    className="text-blue-600"
                  />
                  <div>
                    <p className="text-sm text-gray-600">Current Status</p>
                    <p className="font-medium">{orderData.currentStatus}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Delivery Address */}
          <div className="bg-white border border-gray-200 rounded-lg py-2 px-4 ">
            <h4 className="font-semibold text-gray-900 mb-3">
              Delivery Address:
            </h4>
            <div className="text-gray-700">
              <p className="font-medium">{orderData.deliveryAddress.name}</p>
              <p>{orderData.deliveryAddress.address}</p>
              <p>{orderData.deliveryAddress.city}</p>
              <p>{orderData.deliveryAddress.zipCode}</p>
            </div>
          </div>

          {/* Department and Requested By */}
          <div className="grid grid-cols-2 gap-4 ">
            <div className="bg-white border border-gray-200 rounded-lg p-2">
              <p className="text-sm text-gray-600">Department:</p>
              <p className="font-medium text-gray-900">
                {orderData.department}
              </p>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-2">
              <p className="text-sm text-gray-600">Requested By:</p>
              <p className="font-medium text-gray-900">
                {orderData.requestedBy}
              </p>
            </div>
          </div>

          {/* Ordered Items */}
          <div className="bg-white border border-gray-200 rounded-lg">
            <div className="p-4 border-b border-gray-200">
              <h4 className="font-semibold text-gray-900">Ordered Items</h4>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Text
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Brand/Model
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      SKU
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Quantity
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Unit Price
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                      Total
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {orderData.orderedItems.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                            <Icon
                              icon="material-symbols:medical-services"
                              className="text-blue-600 text-sm"
                            />
                          </div>
                          <span className="font-medium text-gray-900">
                            {item.name}
                          </span>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-gray-700">{item.brand}</td>
                      <td className="px-4 py-3 text-gray-700">{item.sku}</td>
                      <td className="px-4 py-3 text-gray-700">
                        {item.quantity}
                      </td>
                      <td className="px-4 py-3 text-gray-700">
                        ${item.unitPrice.toFixed(2)}
                      </td>
                      <td className="px-4 py-3 font-medium text-gray-900">
                        ${item.total.toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Right Side - Order Status History and Documents */}
        <div className="w-80 border-l border-gray-200 p-6 overflow-y-auto bg-gray-50">
          {/* Order Status History */}
          <div className="mb-6">
            <h4 className="font-semibold text-gray-900 mb-4">
              Order Status History
            </h4>
            <div className="space-y-4">
              {orderData.statusHistory.map((status, index) => (
                <div key={index} className="relative">
                  {/* Timeline line */}
                  {index < orderData.statusHistory.length - 1 && (
                    <div className="absolute left-4 top-8 w-0.5 h-16 bg-gray-300"></div>
                  )}

                  <div className="flex items-start gap-3">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center ${getStatusColor(
                        status.status
                      )}`}
                    >
                      <Icon
                        icon={getStatusIcon(status.status)}
                        className="text-sm"
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h5 className="font-medium text-gray-900">
                          {status.status}
                        </h5>
                      </div>
                      <p className="text-xs text-gray-500 mb-2">
                        {status.date}
                      </p>
                      <p className="text-sm text-gray-600">
                        {status.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Related Documents */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-4">
              Related Documents
            </h4>
            <div className="space-y-3">
              {orderData.relatedDocuments.map((doc, index) => (
                <div
                  key={index}
                  className="flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200 hover:shadow-sm transition-shadow cursor-pointer"
                >
                  <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                    <Icon
                      icon={
                        doc.type === 'PDF'
                          ? 'material-symbols:picture-as-pdf'
                          : doc.type === 'DOCX'
                          ? 'material-symbols:description'
                          : 'material-symbols:link'
                      }
                      className="text-blue-600 text-sm"
                    />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-gray-900 text-sm">
                      {doc.name}
                    </p>
                    <p className="text-xs text-gray-500">({doc.id})</p>
                  </div>
                  <Icon
                    icon="material-symbols:download"
                    className="text-gray-400 text-sm"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Footer Actions */}
      <div className="border-t border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Expected delivery by <span className="font-medium">2024-03-04</span>
          </div>
          <div className="flex gap-3">
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
              <Icon icon="material-symbols:print" className="text-sm" />
              Print Invoice
            </button>
            <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2">
              <Icon
                icon="material-symbols:local-shipping"
                className="text-sm"
              />
              Track Shipment
            </button>
            <button className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2">
              <Icon icon="material-symbols:support-agent" className="text-sm" />
              Contact Support
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
