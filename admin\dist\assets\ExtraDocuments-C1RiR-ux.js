import{a2 as e,an as x,ac as d,b$ as u}from"./index-ClX9RVH0.js";const y=({values:t,setActiveStep:r,formik:i})=>e.jsx(x,{name:"identityInformation",children:({push:c,remove:m})=>{var o;return e.jsx("div",{children:(o=t==null?void 0:t.identityInformation)==null?void 0:o.map((p,n)=>{var s,a,l;return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[e.jsx("div",{children:e.jsx(d,{label:"Document Type",name:`identityInformation.${n}.documentType`,type:"text",placeholder:"EX: (Citizenship)",onFocus:()=>r(7),onBlur:()=>{i.setFieldTouched(`identityInformation.${n}.documentType`,!0)}})}),e.jsx("div",{children:e.jsx(d,{label:"Document Number",name:`identityInformation.${n}.documentNumber`,type:"text",placeholder:"Enter (i.e. 123572)",onFocus:()=>r(7),onBlur:()=>{i.setFieldTouched(`professionalDetails.${n}.yearsOfExperience`,!0)}})})]}),e.jsxs("div",{className:"",children:[e.jsx("label",{className:"block text-sm ",children:"Upload Documents"}),e.jsx(u,{name:`identityInformation.${n}.documentImages`,multiple:!0,onChange:h=>{i.setFieldValue(`identityInformation.${n}.documentImages`,h)},value:(a=(s=i==null?void 0:i.values)==null?void 0:s.identityInformation)==null?void 0:a[n].documentImages})]}),e.jsxs("div",{className:"flex gap-2 justify-center items-center w-full",children:[e.jsxs("button",{type:"button",className:"flex text-xl gap-2 border text-center bg-blue-500 py-2 px-4 rounded-md bg-primary text-white hover:text-primary duration-500 transition-all hover:bg-white hover:bg-blue-600",onClick:()=>c({documentType:"",documentNumber:"",documentImages:null}),children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",children:e.jsxs("g",{fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd",children:[e.jsx("path",{d:"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m10-8a8 8 0 1 0 0 16a8 8 0 0 0 0-16"}),e.jsx("path",{d:"M13 7a1 1 0 1 0-2 0v4H7a1 1 0 1 0 0 2h4v4a1 1 0 1 0 2 0v-4h4a1 1 0 1 0 0-2h-4z"})]})}),e.jsx("div",{className:"text-base",children:"Add Documents"})]}),(t==null?void 0:t.identityInformation)&&((l=t==null?void 0:t.identityInformation)==null?void 0:l.length)>1&&e.jsxs("button",{type:"button",className:"flex justify-center items-center text-xl gap-2 border text-center bg-gray-600 pl-2 pr-4 py-2 text-white hover:text-textGray duration-500 transition-all hover:bg-white rounded-md hover:bg-blue-600",onClick:()=>m(n),children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 32 32",children:e.jsx("path",{fill:"currentColor",d:"M8 15h16v2H8z"})}),e.jsx("div",{className:"text-base",children:"Remove"})]})]})]},n)})})}});export{y as E};
