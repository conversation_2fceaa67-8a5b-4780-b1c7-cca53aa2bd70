import{bH as te,a5 as o,aP as M,bM as s,c0 as g,aM as se,c1 as le,a_ as ne,c2 as E,a2 as a,bF as ie,c3 as re,ai as j,aj as ce,aR as k,c4 as oe,c5 as be,av as b,af as me}from"./index-ClX9RVH0.js";import{A as ue}from"./AddProduct-DEXONU3r.js";import{A as de}from"./AddProductListForm-OS2aOkDS.js";import"./ProductMultiSelect-DbxM3Xg6.js";const Ae=({product:l,onClose:e})=>{var T;const f=[{title:"Product Name",value:(l==null?void 0:l.name)||(l==null?void 0:l.productName)||"N/A"},{title:"Category",value:((T=l==null?void 0:l.category)==null?void 0:T.categoryName)||(l==null?void 0:l.category)||"N/A"},{title:"Description",value:(l==null?void 0:l.description)||"N/A"},{title:"Unit",value:(l==null?void 0:l.unit)||"N/A"},{title:"Department",value:(l==null?void 0:l.department)||"N/A"}];return a.jsxs("div",{className:"max-w-2xl mx-auto p-6 bg-white rounded-md",children:[a.jsxs("div",{className:"flex justify-between items-center mb-4",children:[a.jsx("h2",{className:"text-xl font-bold",children:"Product Details"}),a.jsx("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:"✕"})]}),a.jsx("div",{className:"space-y-3",children:f.map((x,h)=>a.jsxs("div",{className:"flex justify-between border-b pb-2",children:[a.jsxs("span",{className:"font-medium text-gray-600",children:[x.title,":"]}),a.jsx("span",{className:"text-gray-800",children:x.value})]},h))})]})},Pe=()=>{var Y,D;const{data:l}=te();(Y=l==null?void 0:l.user)==null||Y.role;const[e,f]=o.useState({currentTab:"all",availableTab:[]}),[T,x]=o.useState(""),[h,G]=o.useState(null),[B,O]=o.useState(!1),[y,u]=o.useState(!1),[r,m]=o.useState({page:1,limit:10}),[i,c]=o.useState(""),v=()=>[{label:"All",value:"all"},{label:"Medicine",value:g.DRUG},{label:"Device",value:g.DEVICES},{label:"Skin Product",value:g.BEAUTIESSKINCARE},{label:"Medical Supplies",value:g.MEDICALSUPPLIES},{label:"Lab Test Equipment",value:g.LABTEST},{label:"Other",value:g.OTHER}],p=()=>["All","Equipment","Chemical","Consumable","Glassware","Plasticware","Safety Supplies","Instruments","Kits","Others"],R=()=>["All","Equipment","Chemical","Consumable","Glassware","Plasticware","Safety Supplies","Instruments","Kits","Others"],L=()=>["All","Medical Equipment","Supplies","Furniture","Others"],q=M(()=>O(!1)),U=M(()=>u(!1)),V=[{label:"Department",value:"department"},{label:"Laboratory",value:`${s.LABORATORY}`},{label:"Hospital",value:`${s.HOSPITAL}`},{label:"Pharmacy",value:`${s.PHARMACY}`},{label:"Radiology",value:"RADIOLOGY"}];o.useEffect(()=>{var t;e.currentTab===s.PHARMACY?c(((t=v()[0])==null?void 0:t.value)||""):e.currentTab===s.LABORATORY?c(p()[0]||""):e.currentTab==="RADIOLOGY"?c(R()[0]||""):e.currentTab===s.HOSPITAL?c(L()[0]||""):c(""),m({page:1,limit:10})},[e.currentTab]);const P=se({search:T,page:r.page,limit:r.limit,...e.currentTab===s.PHARMACY&&i&&i!=="All"?{category:i}:{},...e.currentTab===s.LABORATORY&&i&&i!=="All"?{category:i}:{},...e.currentTab==="RADIOLOGY"&&i&&i!=="All"?{category:i}:{},...e.currentTab===s.HOSPITAL&&i&&i!=="All"?{category:i}:{}}),{data:C,isLoading:$}=le(e.currentTab===s.PHARMACY?P:void 0,{enabled:e.currentTab===s.PHARMACY}),{data:S,isLoading:F}=ne(e.currentTab===s.HOSPITAL?P:void 0,{enabled:e.currentTab===s.HOSPITAL}),{data:I,isLoading:K}=E(e.currentTab===s.LABORATORY?{...P,upperHirachy:"6811cb6808e7f503fbf1c40a"}:void 0,{enabled:e.currentTab===s.LABORATORY}),{data:w,isLoading:Q}=E(e.currentTab==="RADIOLOGY"?{...P,upperHirachy:"6823163b7abfaa19e7374883"}:void 0,{enabled:e.currentTab==="RADIOLOGY"}),_=o.useCallback(t=>{G(t),O(!0)},[]),z=()=>{let t=[],d=!1,A={};switch(e.currentTab){case s.PHARMACY:t=b.get(C,"data.medicalProducts",[]),d=$,A=b.get(C,"data.pagination",{});break;case s.HOSPITAL:t=b.get(S,"data.products",[]),d=F,A=b.get(S,"data.pagination",{});break;case s.LABORATORY:t=b.get(I,"data.equipments",[]),d=K,A=b.get(I,"data.pagination",{});break;case"RADIOLOGY":t=b.get(w,"data.equipments",[]),d=Q,A=b.get(w,"data.pagination",{});break;default:t=[],d=!1,A={}}const Z=[{title:"S.No",key:"sn"},{title:"Product Name",key:"productName"},{title:"Category",key:"category"},{title:"Description",key:"description"},{title:"Unit",key:"unit"},{title:"Action",key:"action"}],ee=t.map((n,ae)=>{var H;return{sn:ae+1,productName:(n==null?void 0:n.name)||(n==null?void 0:n.productName)||"N/A",category:((H=n==null?void 0:n.category)==null?void 0:H.categoryName)||(n==null?void 0:n.category)||"N/A",description:(n==null?void 0:n.description)||"N/A",unit:(n==null?void 0:n.unit)||"N/A",action:a.jsx(me,{onShow:()=>_(n)})}});return{columns:Z,rows:ee,loading:d,paginationData:A}},{columns:J,rows:W,loading:X,paginationData:N}=z();return a.jsxs("div",{children:[a.jsxs("section",{className:"flex justify-between p-4 bg-white shadow-sm rounded-md mb-2",children:[a.jsx("select",{className:"w-full px-2 py-[6px] text-sm rounded-sm md:w-64 border outline-none",value:e.currentTab||"",onChange:t=>f({currentTab:t.target.value,availableTab:[]}),children:V.map(t=>a.jsx("option",{value:t.value,children:t.label},t.value))}),a.jsxs("div",{className:"flex place-items-center gap-4",children:[a.jsx("input",{type:"text",className:"w-full px-2 py-[6px] text-sm rounded-sm md:w-64 border outline-none",placeholder:"Search by Name, Item Code",value:T,onChange:t=>x(t.target.value)}),e.currentTab!=="all"&&a.jsx(ie,{title:"Add Product",onClick:()=>u(!0)})]})]}),e.currentTab!=="all"&&a.jsxs("div",{className:"bg-white rounded-md",children:[e.currentTab===s.PHARMACY&&a.jsx("div",{className:"px-5 pt-4",children:a.jsx(re,{tabs:v(),defaultTab:i||((D=v()[0])==null?void 0:D.value),onTabChange:t=>{c(t),m({...r,page:1})}})}),e.currentTab===s.LABORATORY&&a.jsx("div",{className:"px-5 pt-4",children:a.jsx(j,{tabs:p(),defaultTab:i||p()[0],onTabChange:t=>{c(t),m({...r,page:1})}})}),e.currentTab==="RADIOLOGY"&&a.jsx("div",{className:"px-5 pt-4",children:a.jsx(j,{tabs:R(),defaultTab:i||R()[0],onTabChange:t=>{c(t),m({...r,page:1})}})}),e.currentTab===s.HOSPITAL&&a.jsx("div",{className:"px-5 pt-4",children:a.jsx(j,{tabs:L(),defaultTab:i||L()[0],onTabChange:t=>{c(t),m({...r,page:1})}})}),a.jsx(ce,{columns:J,rows:W,loading:X,pagination:{currentPage:r.page,totalPage:(N==null?void 0:N.pages)||1,limit:r.limit,onClick:t=>{t.page&&m({...r,page:t.page}),t.limit&&m({...r,limit:t.limit})}}})]}),B&&h&&a.jsx(k,{ref:q,classname:"max-w-2xl w-full",children:a.jsx(Ae,{product:h,onClose:()=>O(!1)})}),y&&(e.currentTab===s.PHARMACY||e.currentTab===s.HOSPITAL)&&a.jsxs(k,{ref:U,classname:"p-4 max-w-4xl w-full",children:[a.jsxs("h3",{className:"text-[#4188f2] mb-4 text-lg font-medium",children:["Add Product -"," ",e.currentTab.charAt(0).toUpperCase()+e.currentTab.slice(1)]}),e.currentTab===s.PHARMACY&&a.jsx(oe,{onClose:()=>u(!1),showRate:!0}),e.currentTab===s.HOSPITAL&&a.jsx(de,{onClose:()=>u(!1)})]}),y&&e.currentTab===s.LABORATORY&&a.jsx(be,{onClose:()=>u(!1),heading:"Add Product"}),y&&e.currentTab==="RADIOLOGY"&&a.jsx(ue,{onClose:()=>u(!1)})]})};export{Pe as VendorProductList};
