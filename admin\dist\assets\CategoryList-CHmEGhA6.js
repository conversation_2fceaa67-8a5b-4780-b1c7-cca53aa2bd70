import{ad as M,a5 as a,aM as D,aN as I,av as s,aO as O,aP as w,a2 as e,af as E,ah as L,aQ as Q,aj as T,aR as H}from"./index-ClX9RVH0.js";import R from"./AddCategory-DI-bCQrh.js";import"./ProductMultiSelect-DbxM3Xg6.js";const _=()=>{M();const[m,q]=a.useState(""),[n,y]=a.useState({search:""}),[i,h]=a.useState({page:1,limit:10}),P=m,x=D({search:n.search,...P?{}:{page:i.page,limit:i.limit}}),{data:c,isLoading:S}=I(x),l=s.get(c,"data.pagination",{}),A=({page:t,limit:r})=>{h(p=>({...p,page:t??1,limit:r??p.limit}))},{mutate:v}=O(),[B,d]=a.useState("create"),[C,u]=a.useState(null),[f,o]=a.useState(!1),j=w(()=>{o(!1)}),b=()=>{d("create"),u(null),o(!0)},N=t=>{d("edit"),u(t),o(!0)},k=()=>e.jsx(R,{editData:C,onClose:()=>o(!1)}),g={columns:[{title:"S.N",key:"sn"},{title:"Categories",key:"categories"},{title:"Description",key:"description"},{title:"Status",key:"isActive"},{title:"Action",key:"action"}],rows:s.get(c,"data.productscategory",[]).map((t,r)=>({sn:r+1,categories:s.get(t,"categoryName","N/A"),description:s.get(t,"description","N/A"),isActive:e.jsx(L,{status:s.get(t,"isActive",!0)?"Available":"Inactive"}),action:e.jsx(E,{onDelete:()=>{v({id:JSON.stringify([s.get(t,"_id","-")])})},onEdit:()=>N(t)})}))};return e.jsxs("div",{children:[e.jsx(Q,{headerTitle:"Product Category",onSearch:!0,toSearch:"Category Name, Description",onSearchFunc:t=>y({...n,search:t}),button:!0,buttonText:"Add Product Category",buttonAction:b}),e.jsx("div",{className:"py-4",children:e.jsx(T,{columns:g.columns,rows:g.rows,loading:S,color:"bg-white ",textcolor:"text-gray-400",pagination:{totalPage:l.pages||1,currentPage:l.page||1,limit:i.limit||10,onClick:A}})}),f&&e.jsx(H,{ref:j,children:k()})]})};export{_ as default};
