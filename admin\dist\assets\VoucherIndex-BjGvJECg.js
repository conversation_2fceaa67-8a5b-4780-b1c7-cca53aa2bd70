import{dh as Y,bo as J,av as c,a1 as K,b8 as X,a5 as T,a2 as e,b2 as $,a9 as z,aa as Q,cd as R,aH as V,bq as q,ac as H,ao as W,di as Z,c0 as p,bj as D,dj as ee,ct as se,aK as le,af as ne,a7 as te,aj as me,dk as ae}from"./index-ClX9RVH0.js";const xe=[{title:"S.No",key:"sno"},{title:"Date",key:"date"},{title:"Category",key:"category"},{title:"Amount",key:"amount"},{title:"Action",key:"action"}],de=({isOpen:s,onClose:N,data:j})=>{const x=JSON.parse(localStorage.getItem("user")||"{}"),{mutate:i,isSuccess:h,isPending:f}=Y(),{data:g}=J(),y=c.get(g,"data.banks",[]).map(a=>({label:c.get(a,"bankName",""),value:c.get(a,"_id","")})),m=K({initialValues:{paymentStatus:"",paymentMethod:"",bank:"",verifiedBy:c.get(x,"id","")},enableReinitialize:!0,onSubmit:a=>{const b=X(a);i({_id:c.get(j,"_id",""),entityData:b})}});return T.useEffect(()=>{h&&(m.resetForm(),N())},[h]),console.log("oslsdfkjsdf",m.values),s?e.jsx($,{onClose:N,classname:"w-full max-w-2xl p-6",children:e.jsx(z,{value:m,children:e.jsxs(Q,{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsx(R,{label:"Payment Method",required:!0,children:e.jsx(V,{placeholder:"Method",onChange:a=>m.setFieldValue("paymentMethod",a),options:[{label:"Cash",value:"Cash"},{label:"Bank",value:"Bank"}],value:m.values.paymentMethod})}),m.values.paymentMethod==="Bank"&&e.jsx(R,{label:"Bank",required:!0,children:e.jsx(V,{placeholder:"Bank",onChange:a=>m.setFieldValue("bank",a),options:y,value:m.values.bank})}),e.jsx(R,{label:"Payment Status",required:!0,children:e.jsx(V,{onChange:a=>m.setFieldValue("paymentStatus",a),placeholder:"Status",options:[{label:"Paid",value:q.PAID},{label:"Partially Paid",value:q.PARTPAID}],value:m.values.paymentStatus})}),e.jsx(H,{label:"Amount",disabled:!0,value:c.get(j,"payableAmount",0)}),e.jsx(H,{label:"Verified By",disabled:!0,value:c.get(x,"fullName","")})]}),e.jsx("div",{className:"flex justify-end gap-3 pt-4 border-t",children:e.jsx(W,{onCancel:N,onSubmit:m.handleSubmit,isPending:f})})]})})}):e.jsx(e.Fragment,{})},U=`
  @media print {
    body { margin: 0; padding: 0; }
    .print-container { 
      padding: 20px; 
      font-family: Arial, sans-serif;
      color: black;
      background: white;
    }
    .no-print { display: none !important; }
    .print-page-break { page-break-before: always; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; }
    th { background-color: #f5f5f5; }
  }
`;T.forwardRef(({prescriptionData:s},N)=>{var A,P,w,k,S,I,o,u,C,B,L;console.log(s,"pres");const j=l=>new Date(l).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),x=()=>{var l,n,t,d;return((l=s==null?void 0:s.walkInCustomer)==null?void 0:l.name)??(((d=(t=(n=s==null?void 0:s.billingAgainst)==null?void 0:n.commonInfo)==null?void 0:t.personalInfo)==null?void 0:d.fullName)||"N/A")},i=()=>{var l,n,t,d,r;return((l=s==null?void 0:s.walkInCustomer)==null?void 0:l.contact)??(((r=(d=(t=(n=s==null?void 0:s.billingAgainst)==null?void 0:n.commonInfo)==null?void 0:t.contactInfo)==null?void 0:d.phone)==null?void 0:r.primaryPhone)||"N/A")},h=()=>{var l,n,t;return((t=(n=(l=s==null?void 0:s.billingAgainst)==null?void 0:l.commonInfo)==null?void 0:n.personalInfo)==null?void 0:t.gender)||"N/A"},f=()=>{var l,n,t;return((t=(n=(l=s==null?void 0:s.supervisor)==null?void 0:l.commonInfo)==null?void 0:n.personalInfo)==null?void 0:t.fullName)||"N/A"},g=()=>{var l;return((l=s==null?void 0:s.supervisor)==null?void 0:l.email)||"N/A"},y=()=>{var l,n,t,d;return((d=(t=(n=(l=s==null?void 0:s.supervisor)==null?void 0:l.commonInfo)==null?void 0:n.contactInfo)==null?void 0:t.phone)==null?void 0:d.primaryPhone)||"N/A"},m=l=>{var n,t;return((n=l==null?void 0:l.pProduct)==null?void 0:n.name)||((t=l==null?void 0:l.hProduct)==null?void 0:t.name)||"N/A"},a=l=>{var t,d,r,v,F,E,M,G,O;let n="";return(t=l==null?void 0:l.pProduct)!=null&&t.strength?n=(d=l==null?void 0:l.pProduct)==null?void 0:d.strength:n=((r=l==null?void 0:l.pProduct)==null?void 0:r.productCategory)==="DRUG"&&((F=(v=l==null?void 0:l.pProduct)==null?void 0:v.drug)==null?void 0:F.strength)||((E=l==null?void 0:l.pProduct)==null?void 0:E.productCategory)===p.BEAUTIESSKINCARE&&((G=(M=l==null?void 0:l.pProduct)==null?void 0:M.beautySkinCare)==null?void 0:G.volume)||((O=l==null?void 0:l.hProduct)==null?void 0:O.strength)||"N/A",n},b=l=>{var t,d,r,v,F,E,M;let n="";return(t=l==null?void 0:l.pProduct)!=null&&t.form?n=(d=l==null?void 0:l.pProduct)==null?void 0:d.form:n=((r=l==null?void 0:l.pProduct)==null?void 0:r.productCategory)==="DRUG"&&((F=(v=l==null?void 0:l.pProduct)==null?void 0:v.drug)==null?void 0:F.form)||((E=l==null?void 0:l.pProduct)==null?void 0:E.form)||((M=l==null?void 0:l.hProduct)==null?void 0:M.form)||"N/A",n};return e.jsxs("div",{className:"min-h-screen bg-gray-100 p-4",children:[e.jsx("style",{children:U}),e.jsx("div",{className:"max-w-4xl mx-auto",ref:N,children:e.jsxs("div",{className:"bg-white shadow-lg rounded-sm print:shadow-none",children:[e.jsxs("div",{className:"p-6 pb-0",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"bg-blue w-[80px]",children:e.jsx("img",{src:"/medicinelogo.png",alt:""})}),e.jsxs("div",{className:"border border-gray-800 rounded-md p-2",children:[e.jsx("h1",{className:"text-lg font-bold",children:"Aarogya Niketan Hospital Pvt. Ltd."}),e.jsx("p",{className:"text-xs",children:"Janakpurdham-08, Dhanusha, Nepal"}),e.jsx("p",{className:"text-xs",children:"<EMAIL> | **********"})]})]}),e.jsx("div",{className:"border border-gray-300 rounded-md px-2 py-1 text-sm",children:e.jsx("span",{children:j(s==null?void 0:s.date)})})]}),e.jsx("h2",{className:"text-xl font-bold border-b-2 border-gray-300 pb-2 mb-4",children:"Medical Prescription"})]}),e.jsx("div",{className:"px-6",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold mb-2",children:"Patient Information"}),e.jsx("table",{className:"w-full",children:e.jsxs("tbody",{children:[e.jsxs("tr",{children:[e.jsx("td",{className:"py-1 text-sm font-semibold w-1/3",children:"Name:"}),e.jsx("td",{className:"py-1 text-sm",children:x()})]}),e.jsxs("tr",{children:[e.jsx("td",{className:"py-1 text-sm font-semibold",children:"Gender:"}),e.jsx("td",{className:"py-1 text-sm",children:h()})]}),e.jsxs("tr",{children:[e.jsx("td",{className:"py-1 text-sm font-semibold",children:"Phone:"}),e.jsx("td",{className:"py-1 text-sm",children:i()})]}),(s==null?void 0:s.billingAgainst)&&e.jsxs("tr",{children:[e.jsx("td",{className:"py-1 text-sm font-semibold",children:"Age:"}),e.jsx("td",{className:"py-1 text-sm",children:s!=null&&s.age?s==null?void 0:s.age:Z(((w=(P=(A=s==null?void 0:s.billingAgainst)==null?void 0:A.commonInfo)==null?void 0:P.personalInfo)==null?void 0:w.dob)??"")})]}),(s==null?void 0:s.billingAgainst)&&e.jsxs("tr",{children:[e.jsx("td",{className:"py-1 text-sm font-semibold",children:"PID:"}),e.jsx("td",{className:"py-1 text-sm",children:(S=(k=s==null?void 0:s.billingAgainst)==null?void 0:k.patientInfo)==null?void 0:S.patientId})]})]})})]}),(s==null?void 0:s.supervisor)&&e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold mb-2",children:"Doctor Information"}),e.jsx("table",{className:"w-full",children:e.jsxs("tbody",{children:[e.jsxs("tr",{children:[e.jsx("td",{className:"py-1 text-sm font-semibold w-1/3",children:"Name:"}),e.jsx("td",{className:"py-1 text-sm",children:f()})]}),e.jsxs("tr",{children:[e.jsx("td",{className:"py-1 text-sm font-semibold",children:"Email:"}),e.jsx("td",{className:"py-1 text-sm",children:g()})]}),e.jsxs("tr",{children:[e.jsx("td",{className:"py-1 text-sm font-semibold",children:"Contact:"}),e.jsx("td",{className:"py-1 text-sm",children:y()})]})]})})]})]})}),e.jsxs("div",{className:"px-6",children:[e.jsx("h3",{className:"font-bold border-b-2 border-gray-300 pb-2 mb-4",children:"Prescribed Medications"}),e.jsx("div",{className:"overflow-x-auto mb-6",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100",children:[e.jsx("th",{className:"text-left p-2 text-sm font-bold",children:"Medication"}),e.jsx("th",{className:"text-left p-2 text-sm font-bold",children:"Strength"}),e.jsx("th",{className:"text-left p-2 text-sm font-bold",children:"Dose"}),e.jsx("th",{className:"text-left p-2 text-sm font-bold",children:"Frequency"}),e.jsx("th",{className:"text-left p-2 text-sm font-bold",children:"Duration"}),e.jsx("th",{className:"text-left p-2 text-sm font-bold",children:"Form"}),e.jsx("th",{className:"text-left p-2 text-sm font-bold",children:"Quantity"}),e.jsx("th",{className:"text-left p-2 text-sm font-bold",children:"Discount %"}),e.jsx("th",{className:"text-left p-2 text-sm font-bold",children:"TAX %"}),e.jsx("th",{className:"text-left p-2 text-sm font-bold",children:"Total Amount (RS.)"})]})}),e.jsx("tbody",{children:(I=s==null?void 0:s.productList)==null?void 0:I.map((l,n)=>{var t,d,r,v;return e.jsxs("tr",{className:"border-b border-gray-200",children:[e.jsx("td",{className:"p-2 text-sm",children:m(l)}),e.jsx("td",{className:"p-2 text-sm",children:a(l)}),e.jsx("td",{className:"p-2 text-sm",children:(l==null?void 0:l.doses)??"-"}),e.jsx("td",{className:"p-2 text-sm",children:(l==null?void 0:l.frequency)??"-"}),e.jsx("td",{className:"p-2 text-sm",children:(l==null?void 0:l.duration)??"-"}),e.jsx("td",{className:"p-2 text-sm",children:b(l)}),e.jsx("td",{className:"p-2 text-sm",children:l.quantity}),e.jsx("td",{className:"p-2 text-sm",children:((t=l.discount)==null?void 0:t.toFixed(2))??0}),e.jsx("td",{className:"p-2 text-sm",children:((d=l.tax)==null?void 0:d.toFixed(2))??0}),e.jsx("td",{className:"p-2 text-sm",children:l!=null&&l.payableAmount?(r=l==null?void 0:l.payableAmount)==null?void 0:r.toFixed(2):(v=l.totalAmount)==null?void 0:v.toFixed(2)})]},l==null?void 0:l._id)})})]})})]}),e.jsx("section",{className:"flex flex-col gap-2 mt-4 justify-end place-items-end px-6",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("p",{className:"text-sm font-bold",children:"Total Amount:"}),e.jsx("p",{className:"text-sm font-bold",children:"Discount:"}),e.jsx("p",{className:"text-sm font-bold",children:"Paid Amount:"}),e.jsx("p",{className:"text-sm font-bold",children:"Due Amount:"})]}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("p",{className:"text-sm font-bold",children:((o=s==null?void 0:s.subTotal)==null?void 0:o.toFixed(2))??((u=s==null?void 0:s.totalAmount)==null?void 0:u.toFixed(2))}),e.jsx("p",{className:"text-sm font-bold",children:(C=s==null?void 0:s.discount)==null?void 0:C.toFixed(2)}),e.jsx("p",{className:"text-sm font-bold",children:(B=s==null?void 0:s.paidAmount)==null?void 0:B.toFixed(2)}),e.jsx("p",{className:"text-sm font-bold",children:(L=s==null?void 0:s.dueAmount)==null?void 0:L.toFixed(2)})]})]})})]})})]})});const _=D.forwardRef(({data:s},N)=>{var h,f,g,y,m,a,b,A,P,w,k,S,I;const j=o=>o?new Date(o).toLocaleDateString("en-GB",{day:"2-digit",month:"2-digit",year:"numeric"}):"",x=o=>`Rs. ${(o==null?void 0:o.toLocaleString())||"0"}`,i=()=>{const o=((s==null?void 0:s.totalAmount)||0)+((s==null?void 0:s.tax)||0),u=(s==null?void 0:s.paymentStatus)==="PAID"&&(s==null?void 0:s.payableAmount)||0;return o-u};return e.jsxs("div",{className:"min-h-screen bg-gray-100 p-4",children:[e.jsx("style",{children:U}),e.jsx("div",{className:"max-w-4xl mx-auto",ref:N,children:e.jsxs("div",{className:"bg-white shadow-lg rounded-sm print:shadow-none",children:[e.jsx("div",{className:"p-6 pb-0",children:e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"bg-blue w-[80px]",children:e.jsx("img",{src:"/medicinelogo.png",alt:"Hospital Logo"})}),e.jsxs("div",{className:"border border-gray-800 rounded-md p-2",children:[e.jsx("h1",{className:"text-lg font-bold",children:"Aarogya Niketan Hospital Pvt. Ltd."}),e.jsx("p",{className:"text-xs",children:"Janakpurdham-08, Dhanusha, Nepal"}),e.jsx("p",{className:"text-xs",children:"<EMAIL> | **********"})]})]}),e.jsx("div",{className:"border border-gray-300 rounded-md px-2 py-1 text-sm",children:e.jsx("span",{children:j(s==null?void 0:s.date)})})]})}),e.jsx("div",{className:"px-6 py-2",children:e.jsx("h2",{className:"text-xl font-bold text-center border-b pb-2",children:"EXPENSE VOUCHER"})}),e.jsxs("div",{className:"px-6 py-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-32",children:"Voucher No:"}),e.jsx("span",{children:(s==null?void 0:s.voucherNo)||"N/A"})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-32",children:"Department:"}),e.jsx("span",{children:((h=s==null?void 0:s.department)==null?void 0:h.name)||"N/A"})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-32",children:"Date:"}),e.jsx("span",{children:j(s==null?void 0:s.createdAt)})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-32",children:"Payment Status:"}),e.jsx("span",{className:`px-2 py-1 rounded text-sm ${(s==null?void 0:s.paymentStatus)==="PAID"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:(s==null?void 0:s.paymentStatus)||"N/A"})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-32",children:"Responsible Person:"}),e.jsx("span",{children:((y=(g=(f=s==null?void 0:s.responsiblePerson)==null?void 0:f.commonInfo)==null?void 0:g.personalInfo)==null?void 0:y.fullName)||"N/A"})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-32",children:"Contact:"}),e.jsx("span",{children:((A=(b=(a=(m=s==null?void 0:s.responsiblePerson)==null?void 0:m.commonInfo)==null?void 0:a.contactInfo)==null?void 0:b.phone)==null?void 0:A.primaryPhone)||"N/A"})]}),e.jsxs("div",{className:"flex",children:[e.jsx("span",{className:"font-semibold w-32 flex-shrink-0",children:"Address:"}),e.jsx("span",{className:"break-words",children:((S=(k=(w=(P=s==null?void 0:s.responsiblePerson)==null?void 0:P.commonInfo)==null?void 0:w.contactInfo)==null?void 0:k.address)==null?void 0:S.currentAddress)||"N/A"})]})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Expense Details"}),e.jsxs("table",{className:"w-full border-collapse border border-gray-300",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"bg-gray-100",children:[e.jsx("th",{className:"border border-gray-300 px-4 py-2 text-left",children:"S.N."}),e.jsx("th",{className:"border border-gray-300 px-4 py-2 text-left",children:"Category"}),e.jsx("th",{className:"border border-gray-300 px-4 py-2 text-left",children:"Details"}),e.jsx("th",{className:"border border-gray-300 px-4 py-2 text-right",children:"Amount"})]})}),e.jsx("tbody",{children:(I=s==null?void 0:s.expensesList)==null?void 0:I.map((o,u)=>{var C;return e.jsxs("tr",{children:[e.jsx("td",{className:"border border-gray-300 px-4 py-2",children:u+1}),e.jsx("td",{className:"border border-gray-300 px-4 py-2",children:((C=o.categoryName)==null?void 0:C.categoryName)||"N/A"}),e.jsx("td",{className:"border border-gray-300 px-4 py-2",children:o.details||"N/A"}),e.jsx("td",{className:"border border-gray-300 px-4 py-2 text-right",children:x(o.amount)})]},o._id||u)})})]})]}),(s==null?void 0:s.remark)&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Remarks"}),e.jsx("p",{className:"border border-gray-300 rounded p-3 bg-gray-50",children:s.remark})]})]}),e.jsx("section",{className:"flex flex-col gap-2 mt-4 justify-end place-items-end px-6 pb-6",children:e.jsxs("div",{className:"border border-gray-300 rounded-lg p-4 min-w-[300px]",children:[e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm font-bold",children:"Total Amount:"}),e.jsx("span",{className:"text-sm",children:x(s==null?void 0:s.totalAmount)})]}),e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm font-bold",children:"Discount:"}),e.jsx("span",{className:"text-sm",children:x(s==null?void 0:s.discount)})]}),e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm font-bold",children:"Tax (13%):"}),e.jsx("span",{className:"text-sm",children:x(s==null?void 0:s.tax)})]}),e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm font-bold",children:"Paid Amount:"}),e.jsx("span",{className:"text-sm",children:x(s==null?void 0:s.payableAmount)})]}),e.jsxs("div",{className:"flex justify-between items-center mb-2",children:[e.jsx("span",{className:"text-sm font-bold",children:"Due Amount:"}),e.jsx("span",{className:"text-sm text-red-600",children:x(i())})]}),e.jsx("hr",{className:"my-2"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm font-bold",children:"Total Amount (After Tax):"}),e.jsx("span",{className:"text-sm font-bold",children:x(((s==null?void 0:s.totalAmount)||0)+((s==null?void 0:s.tax)||0))})]})]})}),e.jsx("div",{className:"px-6 py-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-center text-xs text-gray-600",children:[e.jsxs("span",{children:["Generated on: ",new Date().toLocaleString()]}),e.jsxs("span",{children:["Voucher ID: ",s==null?void 0:s._id]})]})})]})})]})});_.displayName="VoucherPrintOut";const ce=()=>{const[s,N]=T.useState({page:1,limit:10}),j=T.useRef(null),[x,i]=T.useState({state:"",data:null}),{data:h,isLoading:f}=ee({...s,paymentStatus:"PENDING"}),g=se.useReactToPrint({contentRef:j}),y=c.get(h,"data.expenses",[]).map((m,a)=>({sno:(s.page-1)*s.limit+a+1,date:le(c.get(m,"date")).format("MMM-DD-YYYY"),category:c.get(m,"department.name","-"),amount:c.get(m,"payableAmount",0),action:e.jsx(ne,{onPrint:()=>{i({state:"print",data:m}),setTimeout(()=>{g()},100)},onPay:()=>i({state:"pay",data:m}),onShow:()=>i({state:"view",data:m})})}));return e.jsxs("div",{children:[e.jsx(te,{listTitle:"Voucher",hideHeader:!0}),e.jsx(me,{pagination:{currentPage:c.get(h,"data.pagination.page",1),totalPage:c.get(h,"data.pagination.pages",1),limit:10,onClick:({page:m,limit:a})=>N(b=>({...b,page:m??(a?1:b.page),limit:a??b.limit}))},loading:f,columns:xe,rows:y}),x.state==="view"&&e.jsx(ae,{title:"Voucher Overview",data:x.data,onClose:()=>i({state:"",data:null})}),e.jsx(de,{onClose:()=>i({state:"",data:null}),isOpen:x.state==="pay",data:x.data}),e.jsx("section",{className:"hidden",children:x.data!==null&&e.jsx(_,{data:x.data,ref:j})})]})};export{ce as default};
