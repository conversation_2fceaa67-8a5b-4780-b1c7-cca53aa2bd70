import { useFormik } from "formik";
import * as Yup from "yup";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { Icon } from "@iconify/react/dist/iconify.js";

const SampleTableData = {
  columns: [
    { title: "S.N", key: "serialNumber" },
    { title: "Date", key: "date" },
    { title: "Attendance", key: "attendance" },
    { title: "Type", key: "type" },
    { title: "Category", key: "category" },
    { title: "Diagnosis", key: "diagnosis" },
    { title: "ICD-10", key: "icd10" },
    { title: "State", key: "state" },
    { title: "Adverse Effect", key: "adverseEffect" },
  ],
  rows: [
    {
      serialNumber: "1",
      date: "11/11/2024",
      attendance: "OPD",
      type: "Principal",
      category: "Primary",
      diagnosis: "Malaria",
      icd10: "B54",
      state: "New",
      adverseEffect: "Severe",
    },
    {
      serialNumber: "2",
      date: "11/11/2024",
      attendance: "OPD",
      type: "Principal",
      category: "Primary",
      diagnosis: "Malaria",
      icd10: "B54",
      state: "New",
      adverseEffect: "Severe",
    },
    {
      serialNumber: "3",
      date: "11/11/2024",
      attendance: "OPD",
      type: "Principal",
      category: "Primary",
      diagnosis: "Malaria",
      icd10: "B54",
      state: "New",
      adverseEffect: "Severe",
    },
    {
      serialNumber: "4",
      date: "11/11/2024",
      attendance: "OPD",
      type: "Principal",
      category: "Primary",
      diagnosis: "Malaria",
      icd10: "B54",
      state: "New",
      adverseEffect: "Severe",
    },
    {
      serialNumber: "5",
      date: "11/11/2024",
      attendance: "OPD",
      type: "Principal",
      category: "Primary",
      diagnosis: "Malaria",
      icd10: "B54",
      state: "New",
      adverseEffect: "Severe",
    },
  ],
};

const diagnosisFormValidationSchema = Yup.object({
  attendance: Yup.string().required("Attendance is required"),
  date: Yup.string()
    .matches(/^\d{2}\/\d{2}\/\d{4}$/, "Invalid date format")
    .required("Date is required"),
  type: Yup.string().required("Type is required"),
  category: Yup.string().required("Category is required"),
  diagnosis: Yup.string().required("Diagnosis is required"),
  icd10: Yup.string().required("ICD-10 code is required"),
  state: Yup.string().required("State of diagnosis is required"),
  adverseEffect: Yup.string().required("Adverse effect is required"),
});

const Diagnosis = () => {
  const formik = useFormik({
    initialValues: {
      attendance: "",
      date: "",
      type: "",
      category: "",
      diagnosis: "",
      icd10: "",
      state: "",
      adverseEffect: "",
    },
    validationSchema: diagnosisFormValidationSchema,
    onSubmit: (values) => {
      console.log("Form submitted", values);
    },
  });

  return (
    <div className='w-full p-4 mx-auto'>
      <h2 className='mb-4 font-semibold text-center'>Diagnosis</h2>

      <form
        onSubmit={formik.handleSubmit}
        className='grid grid-cols-2 p-4 mt-6 border rounded-md gap-x-6 gap-y-4'
      >
        {/* Attendance */}
        <div className='flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <label className='text-sm font-medium w-28'>Attendance</label>
            <select
              name='attendance'
              className='flex-1 px-3 py-1 border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.attendance}
            >
              <option value=''>Select</option>
              <option value='OPD'>OPD</option>
              <option value='IPD'>IPD</option>
            </select>
          </div>
          {formik.touched.attendance && formik.errors.attendance && (
            <p className='text-xs text-red-500'>{formik.errors.attendance}</p>
          )}
        </div>

        {/* Date */}
        <div className='flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <label className='w-20 text-sm font-medium'>Date</label>
            <input
              name='date'
              placeholder='DD/MM/YYYY ( Auto Generated )'
              className='flex-1 px-3 py-1 border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.date}
            />
          </div>
          {formik.touched.date && formik.errors.date && (
            <p className='text-xs text-red-500'>{formik.errors.date}</p>
          )}
        </div>

        {/* Type */}
        <div className='flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <label className='text-sm font-medium w-28'>Type</label>
            <select
              name='type'
              className='flex-1 px-3 py-1 border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.type}
            >
              <option value=''>Select</option>
              <option value='Principal'>Principal</option>
              <option value='Secondary'>Secondary</option>
            </select>
          </div>
          {formik.touched.type && formik.errors.type && (
            <p className='text-xs text-red-500'>{formik.errors.type}</p>
          )}
        </div>

        {/* Category */}
        <div className='flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <label className='w-20 text-sm font-medium'>Category</label>
            <select
              name='category'
              className='flex-1 px-3 py-1 border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.category}
            >
              <option value=''>Select</option>
              <option value='Primary'>Primary</option>
              <option value='Secondary'>Secondary</option>
            </select>
          </div>
          {formik.touched.category && formik.errors.category && (
            <p className='text-xs text-red-500'>{formik.errors.category}</p>
          )}
        </div>

        {/* Diagnosis */}
        <div className='flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <label className='text-sm font-medium w-28'>Diagnosis</label>
            <input
              name='diagnosis'
              placeholder='Diagnosis ( e.g., MALARIA )'
              className='flex-1 px-3 py-1 border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.diagnosis}
            />
          </div>
          {formik.touched.diagnosis && formik.errors.diagnosis && (
            <p className='text-xs text-red-500'>{formik.errors.diagnosis}</p>
          )}
        </div>

        {/* ICD-10 */}
        <div className='flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <label className='w-20 text-sm font-medium'>ICD - 10</label>
            <input
              name='icd10'
              placeholder='B54'
              className='flex-1 px-3 py-1 border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.icd10}
            />
          </div>
          {formik.touched.icd10 && formik.errors.icd10 && (
            <p className='text-xs text-red-500'>{formik.errors.icd10}</p>
          )}
        </div>

        {/* State */}
        <div className='flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <label className='text-sm font-medium w-28'>
              State of Diagnosis
            </label>
            <select
              name='state'
              className='flex-1 px-3 py-1 border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.state}
            >
              <option value=''>Select</option>
              <option value='New'>New</option>
              <option value='Follow-up'>Follow-up</option>
            </select>
          </div>
          {formik.touched.state && formik.errors.state && (
            <p className='text-xs text-red-500'>{formik.errors.state}</p>
          )}
        </div>

        {/* Adverse Effect */}
        <div className='flex flex-col gap-1'>
          <div className='flex items-center gap-2'>
            <label className='w-20 text-sm font-medium'>Adverse Effect</label>
            <select
              name='adverseEffect'
              className='flex-1 px-3 py-1 border rounded-md'
              onChange={formik.handleChange}
              value={formik.values.adverseEffect}
            >
              <option value=''>Select</option>
              <option value='None'>None</option>
              <option value='Mild'>Mild</option>
              <option value='Moderate'>Moderate</option>
              <option value='Severe'>Severe</option>
            </select>
          </div>
          {formik.touched.adverseEffect && formik.errors.adverseEffect && (
            <p className='text-xs text-red-500'>
              {formik.errors.adverseEffect}
            </p>
          )}
        </div>

        {/* Save Button */}
        <div className='flex justify-end col-span-2 pt-2'>
          <button
            type='submit'
            className='bg-blue-600 hover:bg-blue-700 text-white bg-[#116aef] px-4 py-1.5 rounded-md flex items-center gap-1'
          >
            <Icon
              icon='fa6-solid:floppy-disk'
              width='18'
              height='18'
              color='white'
            />
            Save
          </button>
        </div>
      </form>

      {/* Diagnosis Data Table */}
      <div className='mt-6'>
        <MasterTable
          color='bg-[#b3b3b3]'
          textcolor='text-[#000000]/100'
          columns={SampleTableData.columns}
          rows={SampleTableData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default Diagnosis;
