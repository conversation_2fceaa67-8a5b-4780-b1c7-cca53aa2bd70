import{a5 as l,av as t,a2 as e,ah as A,b8 as S,b2 as f,b3 as P,aj as v,ad as R,bH as I,bB as F,aK as C,af as M,a7 as T,ab as y,ca as O,d1 as w,d2 as D}from"./index-ClX9RVH0.js";import{P as L}from"./PurchaseReturnForm-DnNRJFB9.js";const Y=l.memo(({onClose:c,details:s={}})=>{const i=l.useMemo(()=>({LABORATORY:"lProduct",HOSPITAL:"hProduct",CANTEEN:"cProduct",PHARMACY:"pProduct"}),[]),u=l.useMemo(()=>[{title:"S.N",key:"sn"},{title:"Product",key:"product"},...t.get(s,"inventoryFor")==="PHARMACY"?[{title:"Batches",key:"batches"}]:[],{title:"Quantity",key:"quantity"},{title:"Rate (Rs)",key:"rate"},{title:"Amount (Rs)",key:"amount"}],[]),m=l.useMemo(()=>t.get(s,"productList",[]).map((a,o)=>({sn:o+1,product:t.get(t.get(a,i[t.get(s,"inventoryFor","")],""),"name","N/A"),quantity:t.get(a,"quantity",0),batches:t.get(a,"batchNo",""),rate:t.get(a,"purchaseRate",0),amount:t.get(a,"quantity",0)*t.get(a,"purchaseRate",0)})),[s]),p={firstSection:{Vendor:t.get(s,"vendor.commonInfo.personalInfo.fullName",""),Category:t.get(s,"category",""),"Total Amount":Number(t.get(s,"payableAmount","0")).toFixed(0),...t.get(s,"paymentStatus")!=="PAID"&&{"Paid Amount":Number(t.get(s,"paidAmount",0)).toFixed(0)},...t.get(s,"paymentStatus")!=="PAID"&&{"Due Amount":(Number(t.get(s,"payableAmount",0))-Number(t.get(s,"paidAmount",0))).toFixed(0)}},secondSection:{...t.get(s,"invoiceNo")&&{"Invoice No":t.get(s,"invoiceNo","-")},"Payment Method":t.get(s,"paymentMethod","-"),"Payment Status":e.jsx(A,{status:t.get(s,"paymentStatus","PENDING")})}},d=S(p.firstSection);return e.jsxs(f,{classname:"max-w-3xl w-full",onClose:c,children:[e.jsxs("div",{className:"relative py-4 border-b mx-2",children:[e.jsx("h4",{className:"w-full text-lg text-center font-bold",children:"Details"}),e.jsx("button",{onClick:c,className:"p-4 absolute top-2 right-6 rounded-full hover:bg-gray-50",children:e.jsx(P,{icon:"akar-icons:cross",className:"size-46 cursor-pointer",onClick:c})})]}),e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"w-full flex items-center py-4 px-8 justify-between text-sm sm:text-base",children:[e.jsx("div",{children:Object.entries(d).map(([a,o])=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("h1",{className:"font-semibold",children:[a,":"]}),e.jsx("h1",{children:o})]},a))}),e.jsx("div",{children:Object.entries(p.secondSection).map(([a,o])=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("h1",{className:"font-semibold",children:[a,":"]}),e.jsx("h1",{children:o})]},a))})]}),e.jsx(v,{loading:!1,columns:u,rows:m})]})]})}),H=()=>{var x;const c=R(),[s,i]=l.useState({state:"",data:null}),{data:u}=I(),m=(x=u==null?void 0:u.user)==null?void 0:x.role,d={ADMIN:"all",PHARMACIST:"PHARMACY",LAB_TECHNICIAN:"LABORATORY",RADIOLOGSITS:"LABORATORY",STAFF:"HOSPITAL"}[m],[a,o]=l.useState({page:1,limit:10,search:"",inventoryFor:d==="all"?"":d,paymentStatus:""}),{data:g,isLoading:b}=F({page:a.page,limit:a.limit,category:"PURCHASE",...a.search!==""&&{search:a.search},...a.inventoryFor!==""&&{inventoryFor:a.inventoryFor},...a.paymentStatus!==""&&{paymentStatus:a.paymentStatus}}),j=t.get(g,"data.invoices",[]).map((n,r)=>({sn:(a.page-1)*a.limit+r+1,invoice:t.get(n,"invoiceNo","-"),supplier:t.get(n,"vendor.commonInfo.personalInfo.fullName","-"),date:C(t.get(n,"date","-")).format("MMM-DD-YYYY"),department:t.get(n,"inventoryFor","-"),status:e.jsx(A,{status:t.get(n,"paymentStatus",""),className:"min-w-32 justify-center"}),totalCost:t.get(n,"totalAmount",0).toFixed(0),action:e.jsx(M,{onReturn:()=>i({state:"return",data:n}),onShow:()=>i({state:"view",data:n}),onEdit:()=>{c(`/purchase-management/purchase-list/edit-${t.get(n,"_id","")}`)}})})),N=l.useCallback(t.debounce(n=>o(r=>({...r,search:n})),500),[]);return e.jsxs("div",{children:[e.jsx(T,{title:"Purchased List",onSearch:N,onAddClick:()=>{c("/purchase-management/purchase-list/add")},listTitle:"Purchase List",FilterSection:()=>e.jsxs("div",{className:"flex gap-5",children:[d==="all"&&e.jsx(y,{label:"",value:a.inventoryFor,firstInput:"Department",onChange:n=>o(r=>({...r,inventoryFor:n.target.value})),options:[{label:"All",value:""},...O]}),e.jsx(y,{label:"",value:a.paymentStatus,firstInput:"Status",onChange:n=>o(r=>({...r,paymentStatus:n.target.value})),options:[{value:"",label:"All"},...w]})]})}),e.jsxs("div",{className:"py-4",children:[e.jsx(v,{columns:D,rows:j,loading:b,pagination:{currentPage:t.get(g,"data.pagination.page",1),totalPage:t.get(g,"data.pagination.pages",1),limit:a.limit,onClick:({page:n,limit:r})=>o(h=>({...h,page:n??(r?1:h.page),limit:r??h.limit}))}}),s.state==="view"&&e.jsx(Y,{onClose:()=>i({state:"",data:null}),details:s.data}),s.state==="return"&&e.jsx(L,{onClose:()=>i({state:"",data:null}),editData:s.data})]})]})};export{H as default};
