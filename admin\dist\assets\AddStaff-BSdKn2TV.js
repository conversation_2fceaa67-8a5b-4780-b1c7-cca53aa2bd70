import{a2 as e,an as ce,ac as d,ab as V,br as Fe,aU as Se,bw as y,dy as re,b$ as De,aV as de,bm as Te,aX as M,ba as Ee,a5 as G,b4 as qe,at as Oe,b1 as Ce,ad as we,a1 as Re,a7 as Pe,am as Ae,bx as Be,a9 as Ue,aa as $e,ao as He}from"./index-ClX9RVH0.js";import{E as Me}from"./ExtraDocuments-C1RiR-ux.js";const Ve=({formik:a,setActiveStep:n})=>e.jsx(ce,{name:"employmentDetails",children:()=>{var o,I,N,t,i,s,r,h,b,m,g,c;return e.jsx("div",{children:e.jsx("div",{className:"flex flex-col gap-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-5",children:[e.jsxs("div",{children:[e.jsx(d,{label:"Hire Date",name:"employmentDetails.hireDate",type:"date",placeholder:"Select",onFocus:()=>{n(2),a.validateField("employmentDetails.hireDate")},onBlur:()=>{a.setFieldTouched("employmentDetails.hireDate",!0)}}),((I=(o=a==null?void 0:a.touched)==null?void 0:o.employmentDetails)==null?void 0:I.hireDate)&&((t=(N=a==null?void 0:a.errors)==null?void 0:N.employmentDetails)==null?void 0:t.hireDate)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(s=(i=a==null?void 0:a.errors)==null?void 0:i.employmentDetails)==null?void 0:s.hireDate})]}),e.jsxs("div",{children:[e.jsx(V,{label:"Employment Type",firstInput:"Select",options:[{value:"Full Time",label:"Full Time"},{value:"Part Time",label:"Part Time"},{value:"Contract",label:"Contract"},{value:"Temporary",label:"Temporary"}],name:"employmentDetails.employmentType",onChange:v=>{a.setFieldValue("employmentDetails.employmentType",v.target.value)},value:a.values.employmentDetails.employmentType,onFocus:()=>{n(2),a.validateField("employmentDetails.employmentType")},onBlur:()=>{a.setFieldTouched("employmentDetails.employmentType",!0)}}),((h=(r=a==null?void 0:a.touched)==null?void 0:r.employmentDetails)==null?void 0:h.employmentType)&&((m=(b=a==null?void 0:a.errors)==null?void 0:b.employmentDetails)==null?void 0:m.employmentType)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(c=(g=a==null?void 0:a.errors)==null?void 0:g.employmentDetails)==null?void 0:c.employmentType})]})]})})})}}),_e=({formik:a,setActiveStep:n})=>e.jsx(ce,{name:"paymentDetails",children:()=>{var o,I,N,t,i,s,r,h,b,m,g,c,v,B,u,U,j,f,$,H,F,S,T,E,q,O,C,w,R,P;return e.jsx("div",{children:e.jsx("div",{className:"flex flex-col gap-8",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-5",children:[e.jsxs("div",{children:[e.jsx(d,{label:"Bank Name",name:"paymentDetails.bankName",type:"text",placeholder:"Bank Name",onFocus:()=>n(4),onBlur:()=>{a.setFieldTouched("paymentDetails.bankName",!0)}}),((I=(o=a==null?void 0:a.touched)==null?void 0:o.paymentDetails)==null?void 0:I.bankName)&&((t=(N=a==null?void 0:a.errors)==null?void 0:N.paymentDetails)==null?void 0:t.bankName)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(s=(i=a==null?void 0:a.errors)==null?void 0:i.paymentDetails)==null?void 0:s.bankName})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Account No",name:"paymentDetails.accountNo",type:"text",placeholder:"Account No",onFocus:()=>n(4),onBlur:()=>{a.setFieldTouched("paymentDetails.accountNo",!0)}}),((h=(r=a==null?void 0:a.touched)==null?void 0:r.paymentDetails)==null?void 0:h.accountNo)&&((m=(b=a==null?void 0:a.errors)==null?void 0:b.paymentDetails)==null?void 0:m.accountNo)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(c=(g=a==null?void 0:a.errors)==null?void 0:g.paymentDetails)==null?void 0:c.accountNo})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Account Holder Name",name:"paymentDetails.accountHolderName",type:"text",placeholder:"Account holder name",onFocus:()=>n(4),onBlur:()=>{a.setFieldTouched("paymentDetails.accountHolderName",!0)}}),((B=(v=a==null?void 0:a.touched)==null?void 0:v.paymentDetails)==null?void 0:B.accountHolderName)&&((U=(u=a==null?void 0:a.errors)==null?void 0:u.paymentDetails)==null?void 0:U.accountHolderName)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(f=(j=a==null?void 0:a.errors)==null?void 0:j.paymentDetails)==null?void 0:f.accountHolderName})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Pan No",name:"paymentDetails.panNo",type:"text",placeholder:"Pan No",onFocus:()=>n(4),onBlur:()=>{a.setFieldTouched("paymentDetails.panNo",!0)}}),((H=($=a==null?void 0:a.touched)==null?void 0:$.paymentDetails)==null?void 0:H.panNo)&&((S=(F=a==null?void 0:a.errors)==null?void 0:F.paymentDetails)==null?void 0:S.panNo)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(E=(T=a==null?void 0:a.errors)==null?void 0:T.paymentDetails)==null?void 0:E.panNo})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Basic Salary",name:"paymentDetails.basicSalary",type:"text",placeholder:"Basic Salary",onFocus:()=>n(4),onBlur:()=>{a.setFieldTouched("paymentDetails.basicSalary",!0)}}),((O=(q=a==null?void 0:a.touched)==null?void 0:q.paymentDetails)==null?void 0:O.basicSalary)&&((w=(C=a==null?void 0:a.errors)==null?void 0:C.paymentDetails)==null?void 0:w.basicSalary)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(P=(R=a==null?void 0:a.errors)==null?void 0:R.paymentDetails)==null?void 0:P.basicSalary})]})]})})})}}),Le=({values:a,formik:n,setActiveStep:o})=>{var h,b,m,g;const{data:I}=Fe(),N=(b=(h=I==null?void 0:I.data)==null?void 0:h.departmentCategory)==null?void 0:b.map(c=>({value:c._id,label:c.name})),{data:t}=Se({upperHirachy:a.personalInformation.department}),i=(g=(m=t==null?void 0:t.data)==null?void 0:m.departments)==null?void 0:g.map(c=>({value:c._id,label:c.name})),s=[{value:y.RECEPTIONIST,label:"Receptionist"},{value:y.SOCIAL_WORKER,label:"Social Worker"},{value:y.HOSPITAL_MANAGER,label:"Hospital Manager"},{value:y.FINANCE_MANAGER,label:"Finance Manager"},{value:y.ASSISTANT_ACCOUNTANT,label:"Assistant Accountant"},{value:y.CLEANER,label:"Cleaner"},{value:y.IT_OFFICER,label:"IT Officer"},{value:y.SECURITY_GUARD,label:"Security Guard"},{value:y.OFFICE_BOY,label:"Office Boy"},{value:y.ELECTRICIAN,label:"Electrician"},{value:y.PLUMBER,label:"Plumber"},{value:y.STAFF,label:"Staff"},{value:y.DRIVER,label:"Driver"},{value:y.OTHER,label:"Other"}],r=[{value:re.SENIOR,label:"Senior"},{value:re.INTERMEDIATE,label:"Intermediate"},{value:re.JUNIOR,label:"Junior"},{value:re.INTERN,label:"Intern"}];return e.jsx(ce,{name:"personalInformation",children:()=>{var c,v,B,u,U,j,f,$,H,F,S,T,E,q,O,C,w,R,P,z,Q,J,K,W,Y,X,Z,k,ee,ae,te,ne,le;return e.jsx("div",{children:e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-5",children:[e.jsxs("div",{children:[e.jsx(d,{label:"Full Name",name:"personalInformation.fullName",type:"text",placeholder:"Enter Name",onFocus:()=>o(1)}),typeof n.errors.personalInformation=="object"&&((c=n.errors.personalInformation)==null?void 0:c.fullName)&&e.jsx("div",{className:"text-red text-sm mt-2",children:(v=n.errors.personalInformation)==null?void 0:v.fullName})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Address",name:"personalInformation.address",type:"text",placeholder:"Enter Address",onFocus:()=>o(2)}),((u=(B=n.errors)==null?void 0:B.personalInformation)==null?void 0:u.address)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String((j=(U=n.errors)==null?void 0:U.personalInformation)==null?void 0:j.address)})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Phone Number",name:"personalInformation.contactNumber",type:"text",placeholder:"Enter Phone",onFocus:()=>o(1)}),(($=(f=n.errors)==null?void 0:f.personalInformation)==null?void 0:$.contactNumber)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String((H=n.errors.personalInformation)==null?void 0:H.contactNumber)})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Alternate Phone Number",name:"personalInformation.alternativeNumber",type:"text",placeholder:"Enter Phone",onFocus:()=>o(1)}),((S=(F=n.touched)==null?void 0:F.personalInformation)==null?void 0:S.alternativeNumber)&&((E=(T=n.errors)==null?void 0:T.personalInformation)==null?void 0:E.alternativeNumber)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String((O=(q=n==null?void 0:n.errors)==null?void 0:q.personalInformation)==null?void 0:O.alternativeNumber)})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Email Address",name:"personalInformation.email",type:"email",placeholder:"Enter Email",onFocus:()=>o(1)}),((w=(C=n==null?void 0:n.errors)==null?void 0:C.personalInformation)==null?void 0:w.email)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String((P=(R=n==null?void 0:n.errors)==null?void 0:R.personalInformation)==null?void 0:P.email)})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Date of Birth",name:"personalInformation.dateOfBirth",type:"date",placeholder:"Select a date",onFocus:()=>o(1)}),((Q=(z=n==null?void 0:n.errors)==null?void 0:z.personalInformation)==null?void 0:Q.dateOfBirth)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String((J=n==null?void 0:n.errors.personalInformation)==null?void 0:J.dateOfBirth)})]}),e.jsxs("div",{children:[e.jsx(V,{label:"Gender",firstInput:"Select",options:[{value:"MALE",label:"Male"},{value:"FEMALE",label:"Female"},{value:"OTHER",label:"Other"}],name:"personalInformation.gender",value:a.personalInformation.gender,onChange:p=>{n.setFieldValue("personalInformation.gender",p.target.value)},onFocus:()=>o(1)}),((W=(K=n==null?void 0:n.errors)==null?void 0:K.personalInformation)==null?void 0:W.gender)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(X=(Y=n==null?void 0:n.errors)==null?void 0:Y.personalInformation)==null?void 0:X.gender})]}),e.jsxs("div",{children:[e.jsx(V,{label:"Department",firstInput:"Select",options:N,name:"personalInformation.department",value:a.personalInformation.department,onChange:p=>{n.setFieldValue("personalInformation.department",p.target.value)},onFocus:()=>o(1)}),((k=(Z=n==null?void 0:n.touched)==null?void 0:Z.personalInformation)==null?void 0:k.department)&&((ae=(ee=n==null?void 0:n.errors)==null?void 0:ee.personalInformation)==null?void 0:ae.department)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:(ne=(te=n==null?void 0:n.errors)==null?void 0:te.personalInformation)==null?void 0:ne.department})]}),e.jsx("div",{children:e.jsx(V,{label:"Sub-Department",firstInput:"Select",options:i,name:"personalInformation.subDepartment",value:(le=a==null?void 0:a.personalInformation)==null?void 0:le.subDepartment,onChange:p=>{n.setFieldValue("personalInformation.subDepartment",p.target.value)},onFocus:()=>o(1)})}),e.jsx("div",{children:e.jsx(V,{label:"Marital Status",firstInput:"Select",options:[{value:"Married",label:"Married"},{value:"Unmarried",label:"Unmarried"},{value:"Others",label:"Others"}],value:a.personalInformation.maritalStatus,name:"personalInformation.maritalStatus",onChange:p=>{n.setFieldValue("personalInformation.maritalStatus",p.target.value)},onFocus:()=>o(1)})}),e.jsx("div",{children:e.jsx(V,{label:"Role",firstInput:"Select",options:s,value:a.personalInformation.role,name:"personalInformation.role",onChange:p=>{n.setFieldValue("personalInformation.role",p.target.value)},onFocus:()=>o(1)})}),e.jsx("div",{children:e.jsx(V,{label:"Designation",firstInput:"Select",options:r,value:a.personalInformation.designation,name:"personalInformation.designation",onChange:p=>{n.setFieldValue("personalInformation.designation",p.target.value)},onFocus:()=>o(1)})}),e.jsxs("div",{className:"mt-6 col-span-3  ",children:[e.jsx("label",{className:"block mb-1 font-medium",children:"Upload Profile Image"}),e.jsx(De,{name:"personalInformation.profileImage",onChange:p=>{n.setFieldValue("personalInformation.profileImage",p)},value:a.personalInformation.profileImage})]})]})})})}})},Ge=({values:a,formik:n,setActiveStep:o})=>e.jsx(ce,{name:"qualification",children:({push:I,remove:N})=>e.jsx("div",{children:a.qualification.length>0&&a.qualification.map((t,i)=>{var h,b,m,g;const s=typeof((h=n.errors.qualification)==null?void 0:h[i])=="object"?(b=n.errors.qualification)==null?void 0:b[i]:void 0,r=typeof((m=n.touched.qualification)==null?void 0:m[i])=="object"?(g=n.touched.qualification)==null?void 0:g[i]:void 0;return e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-5",children:[e.jsxs("div",{children:[e.jsx(d,{label:"Qualification Level",name:`qualification.${i}.qualification`,type:"text",placeholder:"Enter (i.e. Doctor of medicine)",onFocus:()=>o(3),onBlur:()=>{n.setFieldTouched(`qualification.${i}.qualification`,!0)}}),(r==null?void 0:r.qualification)&&(s==null?void 0:s.qualification)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(s==null?void 0:s.qualification)})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Institution",name:`qualification.${i}.qualificationInstitution`,type:"text",placeholder:"Enter (i.e. Tribhuvan University)",onFocus:()=>o(3),onBlur:()=>{n.setFieldTouched(`qualification.${i}.qualificationInstitution`,!0)}}),(r==null?void 0:r.qualificationInstitution)&&(s==null?void 0:s.qualificationInstitution)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(s==null?void 0:s.qualificationInstitution)})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Field of Study",name:`qualification.${i}.fieldOfStudy`,type:"text",placeholder:"Enter field of study",onFocus:()=>o(3),onBlur:()=>{n.setFieldTouched(`qualification.${i}.fieldOfStudy`,!0)}}),(r==null?void 0:r.fieldOfStudy)&&(s==null?void 0:s.fieldOfStudy)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(s.fieldOfStudy)})]}),e.jsxs("div",{children:[e.jsx(d,{label:"Start Date",name:`qualification.${i}.startDate`,type:"date",placeholder:"Select",onFocus:()=>{o(2),n.validateField(`qualification.${i}.startDate`)},onBlur:()=>{n.setFieldTouched(`qualification.${i}.startDate`,!0)}}),(r==null?void 0:r.startDate)&&(s==null?void 0:s.startDate)&&e.jsxs("div",{className:"text-red mt-2 text-sm",children:[" ",String(s.startDate)]})]}),e.jsxs("div",{children:[e.jsx(d,{label:"end Date",name:`qualification.${i}.qualifiedDate`,type:"date",placeholder:"Select",onFocus:()=>{o(2),n.validateField(`qualification.${i}.qualifiedDate`)},onBlur:()=>{n.setFieldTouched(`qualification.${i}.qualifiedDate`,!0)}}),(r==null?void 0:r.qualifiedDate)&&(s==null?void 0:s.qualifiedDate)&&e.jsxs("div",{className:"text-red mt-2 text-sm",children:[" ",String(s.qualifiedDate)]})]})]}),e.jsxs("div",{className:"mt-4 ",children:[e.jsx("label",{className:"block mb-1 font-medium ",children:"Upload Photo"}),e.jsx(De,{name:`qualification.${i}.documentImages`,multiple:!0,onChange:c=>n.setFieldValue(`qualification.${i}.documentImages`,c),value:a.qualification[i].documentImages}),(r==null?void 0:r.documentImages)&&(s==null?void 0:s.documentImages)&&e.jsx("div",{className:"text-red mt-2 text-sm",children:String(s.documentImages)})]}),e.jsxs("div",{className:"flex gap-2 justify-center items-center w-full my-5",children:[e.jsxs("button",{type:"button",className:"flex text-base font-medium gap-1 border text-center bg-blue-500   p-2.5 rounded-md bg-primary text-white hover:text-primary duration-500 transition-all hover:bg-white hover:bg-blue-600",onClick:()=>I({qualification:"",qualificationInstitution:"",fieldOfStudy:"",documentImages:null,startDate:"",qualifiedDate:""}),children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:e.jsxs("g",{fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd",children:[e.jsx("path",{d:"M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12m10-8a8 8 0 1 0 0 16a8 8 0 0 0 0-16"}),e.jsx("path",{d:"M13 7a1 1 0 1 0-2 0v4H7a1 1 0 1 0 0 2h4v4a1 1 0 1 0 2 0v-4h4a1 1 0 1 0 0-2h-4z"})]})}),e.jsx("div",{children:"Add Qualification"})]}),a.qualification.length>1&&e.jsxs("button",{type:"button",className:"flex justify-center items-center text-xl gap-2 border text-center bg-gray-600 pl-2 pr-4 py-2 text-white hover:text-textGray duration-500 transition-all hover:bg-white rounded-md hover:bg-blue-600",onClick:()=>N(i),children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 32 32",children:e.jsx("path",{fill:"currentColor",d:"M8 15h16v2H8z"})}),e.jsx("div",{children:"Remove"})]})]})]},i)})})}),ze=()=>de({personalInformation:de({fullName:M(),address:M(),contactNumber:M().matches(/^\d{10}$/,"Phone number must be 10 digits"),alternativeNumber:M().matches(/^\d{10}$/,"Phone number must be 10 digits"),email:M().email("Invalid email"),dateOfBirth:M(),gender:M()}),paymentDetails:de({basicSalary:Te().typeError("Salary  must be a number").min(0,"Salary must be a positive number")})}),Ke=()=>{var S,T,E,q,O,C,w,R,P,z,Q,J,K,W,Y,X,Z,k,ee,ae,te,ne,le,p,me,ue,pe,he,xe,ye,Ie,be,ge,Ne,je;const{id:a}=Ee(),[n,o]=G.useState(1),{mutateAsync:I,isPending:N}=qe(),{data:t}=Oe(a??""),{mutateAsync:i,isPending:s}=Ce(),[r,h]=G.useState({personalInformation:!1,paymentDetails:!1}),[b,m]=G.useState(!1),g=G.useRef(null),c=G.useRef(!1),v=[{step:1,title:"Personal Information",isActive:n===1,hasError:r.personalInformation},{step:2,title:"Employment Details",isActive:n===2},{step:3,title:"Qualification Details",isActive:n===3},{step:4,title:"Payment Details",isActive:n===4,hasError:r.paymentDetails},{step:5,title:"Identity Information",isActive:n===5}],B=we(),u=Re({initialValues:{personalInformation:{fullName:((T=(S=t==null?void 0:t.commonInfo)==null?void 0:S.personalInfo)==null?void 0:T.fullName)??"",address:((O=(q=(E=t==null?void 0:t.commonInfo)==null?void 0:E.contactInfo)==null?void 0:q.address)==null?void 0:O.currentAddress)??"",contactNumber:((R=(w=(C=t==null?void 0:t.commonInfo)==null?void 0:C.contactInfo)==null?void 0:w.phone)==null?void 0:R.primaryPhone)??"",alternativeNumber:((Q=(z=(P=t==null?void 0:t.commonInfo)==null?void 0:P.contactInfo)==null?void 0:z.phone)==null?void 0:Q.secondaryPhone)??"",email:(t==null?void 0:t.email)??"",dateOfBirth:((K=(J=t==null?void 0:t.commonInfo)==null?void 0:J.personalInfo)==null?void 0:K.dob)??"",gender:((Y=(W=t==null?void 0:t.commonInfo)==null?void 0:W.personalInfo)==null?void 0:Y.gender)??"",department:((Z=(X=t==null?void 0:t.departmentDetails)==null?void 0:X.hirachyFirst)==null?void 0:Z._id)??"",subDepartment:((ee=(k=t==null?void 0:t.departmentDetails)==null?void 0:k.department)==null?void 0:ee._id)??"",maritalStatus:((te=(ae=t==null?void 0:t.commonInfo)==null?void 0:ae.personalInfo)==null?void 0:te.maritalStatus)??"",profileImage:((ne=t==null?void 0:t.identityInformation)==null?void 0:ne.profileImage)??"",designation:((le=t==null?void 0:t.professionalDetails)==null?void 0:le.designation)??"JUNIOR",role:(t==null?void 0:t.role)??""},employmentDetails:{hireDate:((p=t==null?void 0:t.experienceDetails)==null?void 0:p.joinedDate)??"",employmentType:((me=t==null?void 0:t.professionalDetails)==null?void 0:me.employeeType)??""},qualification:((ue=t==null?void 0:t.professionalDetails)==null?void 0:ue.education)??[{qualification:"",qualificationInstitution:"",fieldOfStudy:"",startDate:"",qualifiedDate:"",documentImages:""}],paymentDetails:{tdsApplicable:!1,bankName:((he=(pe=t==null?void 0:t.financialInfo)==null?void 0:pe.accountDetails)==null?void 0:he.bankName)??"",accountNo:((ye=(xe=t==null?void 0:t.financialInfo)==null?void 0:xe.accountDetails)==null?void 0:ye.accountNumber)??"",panNo:((Ie=t==null?void 0:t.financialInfo)==null?void 0:Ie.pan)??"",basicSalary:((be=t==null?void 0:t.financialInfo)==null?void 0:be.salary)??"",accountHolderName:((Ne=(ge=t==null?void 0:t.financialInfo)==null?void 0:ge.accountDetails)==null?void 0:Ne.accountHolderName)??""},identityInformation:((je=t==null?void 0:t.identityInformation)==null?void 0:je.identityDocuments)??[{documentType:"",documentNumber:"",documentImages:""}]},validationSchema:()=>ze(),enableReinitialize:!0,onSubmit:async l=>{var L,A,oe;const D=(L=l.qualification)==null?void 0:L.map(x=>({qualification:x.qualification,qualificationInstitution:x.qualificationInstitution,startDate:x.startDate,qualifiedDate:x.qualifiedDate,fieldOfStudy:x.fieldOfStudy,documentImages:x.documentImages})),se=(A=l.identityInformation)==null?void 0:A.map(x=>({documentType:x.documentType,documentNumber:x.documentNumber,documentImages:x.documentImages})),_={email:l.personalInformation.email,role:l.personalInformation.role,commonInfo:{personalInfo:{fullName:l.personalInformation.fullName,dob:l.personalInformation.dateOfBirth,gender:l.personalInformation.gender,maritalStatus:l.personalInformation.maritalStatus},contactInfo:{phone:{primaryPhone:l.personalInformation.contactNumber,secondaryPhone:l.personalInformation.alternativeNumber},address:{currentAddress:l.personalInformation.address,permanentAddress:l.personalInformation.address}}},identityInformation:{profileImage:l.personalInformation.profileImage,identityDocuments:se},professionalDetails:{employeeType:l.employmentDetails.employmentType,education:D,designation:l.personalInformation.designation},experienceDetails:{joinedDate:(oe=l.employmentDetails)==null?void 0:oe.hireDate},departmentDetails:{...l.personalInformation.department?{hirachyFirst:l.personalInformation.department}:{},...l.personalInformation.subDepartment?{department:l.personalInformation.subDepartment}:{}},financialInfo:{salary:Number(l.paymentDetails.basicSalary),pan:l.paymentDetails.panNo,accountDetails:{bankName:l.paymentDetails.bankName,accountNumber:l.paymentDetails.accountNo}}};a?await i({_id:a,entityData:_}):await I(_),B(-1)}}),{handleSubmit:U,values:j,errors:f,isValid:$}=u;G.useEffect(()=>{if(f){const l={...r};let D=!1;f.personalInformation?(l.personalInformation=!0,D=!0):l.personalInformation=!1,f.paymentDetails?(l.paymentDetails=!0,D=!0):l.paymentDetails=!1,D&&h(l)}},[f]);const H=()=>{if($)return;const l={personalInformation:1,employmentDetails:2,qualification:3,paymentDetails:4,identityInformation:5},D=Object.keys(f);if(D.length===0)return;D.sort((A,oe)=>l[A]-l[oe]);const se=D[0],_=l[se];o(_);const L={...r};se==="personalInformation"&&(L.personalInformation=!0),se==="paymentDetails"&&(L.paymentDetails=!0),h(L),setTimeout(()=>{const A=document.querySelectorAll(".bg-white.rounded-sm");if(A&&A.length>=_){const x=A[_-1].querySelectorAll(".text-red");if(x&&x.length>0){const ie=x[0];ie.scrollIntoView({behavior:"smooth",block:"center"}),ie.style.transition="background-color 0.5s ease",ie.style.backgroundColor="rgba(255, 0, 0, 0.1)",setTimeout(()=>{ie.style.backgroundColor="transparent"},1500);const fe=ie.parentElement;if(fe){const ve=fe.querySelector("input, select, textarea");ve&&setTimeout(()=>{ve.focus()},300)}}}},100)},F=()=>{b||c.current||(m(!0),u.validateForm().then(l=>{Object.keys(l).length>0?(u.setErrors(l),setTimeout(()=>{H(),m(!1)},100)):(c.current=!0,h({personalInformation:!1,paymentDetails:!1}),U(),setTimeout(()=>{m(!1),c.current=!1},1e3))}).catch(l=>{console.error("Form validation error:",l),m(!1),c.current=!1}))};return e.jsxs("div",{className:"fixed",children:[e.jsx("div",{className:"fixed top-20 left-72 right-16 z-50 ",children:e.jsx(Pe,{listTitle:a?"Edit Staff":"Add Staff",hideHeader:!0})}),e.jsxs("div",{className:"relative flex w-full gap-6 mt-24",children:[e.jsx("div",{className:"w-auto h-full fixed",children:e.jsx(Ae,{steps:v})}),e.jsx(Be,{isLoading:N||s}),e.jsx("div",{className:"w-full ml-96 h-[500px] overflow-y-scroll",ref:g,children:e.jsx(Ue,{value:u,children:e.jsx($e,{onSubmit:l=>{l.preventDefault(),F()},children:e.jsxs("div",{className:"flex flex-col w-full gap-5 pb-4",children:[e.jsx("div",{className:"p-8 bg-white rounded-sm",onClick:()=>o(1),children:e.jsx(Le,{values:j,formik:u,setActiveStep:o})}),e.jsx("div",{className:"p-8 bg-white rounded-sm",onClick:()=>o(2),children:e.jsx(Ve,{values:j,formik:u,setActiveStep:o})}),e.jsx("div",{className:"px-8 pb-2 pt-8 bg-white rounded-sm",onClick:()=>o(3),children:e.jsx(Ge,{values:j,formik:u,setActiveStep:o})}),e.jsx("div",{className:"p-8 bg-white rounded-sm",onClick:()=>o(4),children:e.jsx(_e,{values:j,formik:u,setActiveStep:o})}),e.jsx("div",{className:"p-8 bg-white rounded-sm",onClick:()=>o(5),children:e.jsx(Me,{values:j,formik:u,setActiveStep:o})}),e.jsx(He,{onCancel:()=>u.resetForm(),onSubmit:F})]})})})})]})]})};export{Ke as AddStaff};
