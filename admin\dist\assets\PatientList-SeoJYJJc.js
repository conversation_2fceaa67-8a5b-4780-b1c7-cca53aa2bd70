import{ad as X,bG as Z,aD as ee,bH as te,a5 as r,aK as y,aM as k,aU as ae,bI as se,aw as ne,av as l,a2 as t,af as le,ag as w,a7 as oe,b3 as v,aH as L,aI as re,aj as ie,bJ as ce}from"./index-ClX9RVH0.js";import{u as de}from"./analytics.api-DOA8_WvB.js";const R={search:"",department:"ALL",patientType:"all",fromDate:"",toDate:"",dateFilter:"all"},_={limit:5,page:1},me=[{label:"All Time",value:"all"},{label:"Today",value:"today"},{label:"Custom Range",value:"custom"}],pe=[{label:"All Types",value:"all"},{value:"IPD",label:"IPD"},{value:"OPD",label:"OPD"},{value:"EMERGENCY",label:"EMERGENCY"},{value:"OT",label:"OT"}],ue={EMERGENCY:"border-[#FF3B30] text-[#FF3B30]",OPD:"border-[#34C759] text-[#34C759]",IPD:"border-[#3A86FF] text-[#3A86FF]"},T=({title:m,value:g,icon:D,color:x="blue",loading:I=!1})=>t.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-100",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("p",{className:"text-sm text-gray-500",children:m}),t.jsx("p",{className:`text-2xl font-semibold text-${x}-600`,children:I?t.jsx("span",{className:"animate-pulse bg-gray-200 h-8 w-16 block rounded"}):g})]}),t.jsx("div",{className:`p-3 bg-${x}-50 rounded-full`,children:t.jsx(v,{icon:D,className:`text-${x}-500 text-xl`})})]})}),ge=({label:m,onRemove:g})=>t.jsxs("div",{className:"bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm flex items-center gap-1",children:[t.jsx("span",{children:m}),t.jsx("button",{onClick:g,children:t.jsx(v,{icon:"mdi:close-circle",className:"text-blue-700"})})]}),ye=()=>{const m=X(),[g]=Z(),{contentRef:D,exportToPDF:x,exportToExcel:I,handlePrint:$}=ee(),{data:G}=te();console.log(G,"userdata");const u=g.get("from")==="inpatients",[a,f]=r.useState({...R,patientType:u?"IPD":"all"}),[B,A]=r.useState(""),[p,b]=r.useState(_),[Q,h]=r.useState(!1),H=r.useMemo(()=>{const e={},s=y().format("YYYY-MM-DD");return a.dateFilter==="today"?(e.startDate=s,e.endDate=s):a.dateFilter==="custom"&&(a.fromDate&&(e.startDate=a.fromDate),e.endDate=a.toDate||s),k(e)},[a]),{data:F,isLoading:P}=de(H),{data:O}=ae(),{mutateAsync:S}=se(),U=r.useMemo(()=>{const e={role:"PATIENT",search:a.search,limit:p.limit,page:p.page};return a.department!=="ALL"&&a.department!=="NONE"&&(e["departmentDetails.department"]=a.department),a.patientType.toLowerCase()!=="all"&&(e["commonInfo.ipdOpd"]=a.patientType),a.fromDate&&(e.startDate=a.fromDate),a.toDate&&(e.endDate=a.toDate),console.log("=== API Query Debug ==="),console.log("Query Params:",e),console.log("Current Filters:",a),console.log("Built Query String:",k(e)),console.log("======================"),k(e)},[a,p]),{data:N,isLoading:J}=ne(U);console.log(N,"here is data");const d=r.useMemo(()=>{const e=l.get(F,"data.coreServices.patient.totalPatient",0),s=l.get(F,"data.coreServices.patient.patientByDepartment",[]),n=o=>{const c=s.find(C=>l.get(C,"_id")===o);return c?l.get(c,"count"):0};return{totalPatients:e,ipdPatients:n("IPD"),opdPatients:n("OPD"),emergencyPatients:n("EMERGENCY")}},[F]),E=r.useMemo(()=>{const e=l.get(O,"data.departments",[]);console.log("All Department Data:",e),e.forEach((o,c)=>{console.log(`Department ${c}:`,{name:l.get(o,"name"),id:l.get(o,"_id"),ipdOpd:l.get(o,"ipdOpd",[]),isActive:l.get(o,"isActive")})});let s=e;a.patientType&&a.patientType.toLowerCase()!=="all"&&(console.log(`Filtering departments for patient type: ${a.patientType}`),s=e.filter(o=>{const c=l.get(o,"ipdOpd",[]),C=l.get(o,"name"),Y=c.includes(a.patientType)||c.includes("BOTH");return console.log(`Department "${C}":`,{ipdOpd:c,patientTypeFilter:a.patientType,shouldInclude:Y}),Y}),console.log(`Filtered departments count: ${s.length}`));let n;return a.patientType&&a.patientType.toLowerCase()!=="all"&&s.length===0?n=[{label:"No departments available for this patient type",value:"NONE"}]:n=[{label:"All Departments",value:"ALL"},...s.map(o=>({label:l.get(o,"name"),value:l.get(o,"_id")}))],console.log("Filtered Department Options:",n),console.log("Current Patient Type Filter:",a.patientType),n},[O,a.patientType]),i=r.useCallback((e,s)=>{f(n=>{const o={...n,[e]:s};return e==="patientType"&&(o.department="ALL"),e==="department"&&s==="NONE"?n:o}),b(n=>({...n,page:1}))},[]),V=r.useCallback(l.debounce(e=>i("search",e),500),[i]),q=r.useCallback(e=>{const s=y().format("YYYY-MM-DD");e==="today"?(f(n=>({...n,fromDate:s,toDate:s,dateFilter:e})),h(!1)):e==="all"?(f(n=>({...n,fromDate:"",toDate:"",dateFilter:e})),h(!1)):e==="custom"&&(f(n=>({...n,fromDate:n.fromDate||s,toDate:n.toDate||s,dateFilter:e})),h(!0)),b(n=>({...n,page:1}))},[]),K=r.useCallback(e=>{b(s=>({page:e.page||s.page,limit:e.limit||s.limit}))},[]),W=r.useCallback(()=>{f({...R,patientType:u?"IPD":"all"}),A(""),b(_),h(!1)},[u]),M=r.useMemo(()=>{var s;const e=[];if(a.search&&e.push({label:`Search: ${a.search}`,onRemove:()=>{i("search",""),A("")}}),a.department!=="ALL"){const n=(s=E.find(o=>o.value===a.department))==null?void 0:s.label;e.push({label:`Department: ${n}`,onRemove:()=>i("department","ALL")})}return a.fromDate&&e.push({label:`From: ${y(a.fromDate).format("MMM DD, YYYY")}`,onRemove:()=>i("fromDate","")}),a.toDate&&e.push({label:`To: ${y(a.toDate).format("MMM DD, YYYY")}`,onRemove:()=>i("toDate","")}),e},[a,E,i]),j=r.useMemo(()=>({columns:[{title:"S.N.",key:"serialNumber"},{title:"Date",key:"date"},{title:"ID",key:"tokenid"},{title:"Name",key:"patientName"},{title:"Contact Number",key:"contactNumber"},{title:"Address",key:"address"},{title:"Department",key:"department"},{title:"Patient Type",key:"patientType"},{title:"Action",key:"action"}],rows:l.get(N,"data.users",[]).map((e,s)=>{const n=l.get(e,"commonInfo.ipdOpd","-"),o=l.get(e,"departmentDetails.department.name","-"),c=l.get(e,"departmentDetails.department._id","-");return s===0&&console.log("Sample Patient Data:",{patientType:n,departmentName:o,departmentId:c,fullPatientData:e}),{key:s,serialNumber:(p.page-1)*p.limit+s+1,patientName:l.get(e,"commonInfo.personalInfo.fullName","-"),tokenid:l.get(e,"patientInfo.patientId","-"),contactNumber:l.get(e,"commonInfo.contactInfo.phone.primaryPhone",l.get(e,"commonInfo.contactInfo.phone.secondaryPhone","-")),address:l.get(e,"commonInfo.contactInfo.address.currentAddress",l.get(e,"commonInfo.contactInfo.address.permanentAddress","-")),department:t.jsx("span",{className:"px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full",children:o}),patientType:t.jsx("span",{className:`px-2 py-1 w-fit flex items-center justify-center text-xs rounded-full border ${ue[n]||"border-gray-500 text-gray-500"}`,children:n}),date:y(l.get(e,"createdAt","-")).format("MMM-DD-YYYY"),action:t.jsx(le,{...u&&String(n)==="IPD"?{onPay:()=>{m(`${w.IPDPATIENTLIST}/${l.get(e,"_id","")}`)}}:{onEdit:()=>m(`/general-ward/patient/edit-${l.get(e,"_id","")}`),onShow:()=>m(`${w.PATIENTDETAIL}/${l.get(e,"_id","")}`),onDelete:()=>S({id:JSON.stringify([l.get(e,"_id","")])})}})}})}),[N,p,S,m,u]),z=r.useMemo(()=>({"Total Patients":d.totalPatients,"IPD Patients":d.ipdPatients,"OPD Patients":d.opdPatients,"Emergency Patients":d.emergencyPatients}),[d]);return t.jsxs("div",{className:"flex flex-col pb-8",children:[t.jsx("div",{className:"flex justify-between items-center",children:t.jsx(oe,{listTitle:"Patient List",hideHeader:!0})}),t.jsxs("div",{className:"flex flex-col gap-4",children:[t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[t.jsx(T,{title:"Total Patients",value:d.totalPatients,icon:"mdi:account-group",color:"gray",loading:P}),t.jsx(T,{title:"IPD Patients",value:d.ipdPatients,icon:"mdi:bed",color:"blue",loading:P}),t.jsx(T,{title:"OPD Patients",value:d.opdPatients,icon:"mdi:clipboard-pulse",color:"green",loading:P}),t.jsx(T,{title:"Emergency Patients",value:d.emergencyPatients,icon:"mdi:ambulance",color:"red",loading:P})]}),t.jsxs("div",{className:"bg-white p-4 rounded-lg shadow-sm",children:[t.jsx("div",{className:"mb-2 flex items-center justify-end",children:t.jsxs("button",{onClick:W,className:"text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1",children:[t.jsx(v,{icon:"mdi:refresh"}),"Reset Filters"]})}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[t.jsxs("div",{className:"relative",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search"}),t.jsxs("div",{className:"relative",children:[t.jsx("input",{type:"text",placeholder:"Name, ID, or Contact",value:B,onChange:e=>{A(e.target.value),V(e.target.value)},className:"w-full py-[6px] ps-8 px-4 border border-gray-300 rounded-sm text-sm focus:outline-none"}),t.jsx(v,{icon:"mdi:magnify",className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"})]})]}),t.jsxs("div",{children:[t.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Patient Type",u&&t.jsx("span",{className:"text-xs text-gray-500 ml-1"})]}),t.jsx(L,{options:pe,value:a.patientType,onChange:e=>i("patientType",e),placeholder:"Select Patient Type",className:"text-sm rounded-sm",disabled:u})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Department"}),t.jsx(L,{options:E,value:a.department,onChange:e=>i("department",e),placeholder:"Select Department",className:"text-sm rounded-sm"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter By Date"}),t.jsx(L,{options:me,value:a.dateFilter,onChange:q,placeholder:"Select Date Filter",className:"text-sm rounded-sm"})]}),Q&&t.jsxs(t.Fragment,{children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"From Date"}),t.jsx("input",{type:"date",value:a.fromDate,onChange:e=>i("fromDate",e.target.value),max:a.toDate||void 0,className:"w-full py-[6px] px-4 border border-gray-300 rounded-sm text-sm focus:outline-none"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"To Date"}),t.jsx("input",{type:"date",value:a.toDate,onChange:e=>i("toDate",e.target.value),min:a.fromDate||void 0,className:"w-full py-[6px] px-4 border border-gray-300 rounded-sm text-sm focus:outline-none"})]})]}),t.jsx(re,{onExportExcel:()=>I(j.rows,"report.xlsx"),onExportPDF:()=>x(D.current,"report.pdf"),onPrint:$})]}),M.length>0&&t.jsx("div",{className:"mt-4 flex flex-wrap gap-2",children:M.map((e,s)=>t.jsx(ge,{label:e.label,onRemove:e.onRemove},s))})]}),t.jsx(ie,{columns:j.columns,rows:j.rows,loading:J,pagination:{currentPage:p.page,totalPage:l.get(N,"data.pagination.pages",1),limit:p.limit,onClick:K}}),t.jsx(ce,{ref:D,tableData:j,cardData:z})]})]})};export{ye as default};
