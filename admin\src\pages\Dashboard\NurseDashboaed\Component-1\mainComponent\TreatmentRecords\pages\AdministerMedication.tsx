import { Icon } from "@iconify/react/dist/iconify.js";
import React from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { useFormik } from "formik";
import * as Yup from "yup";

// Data for the "Recently Taken Medicine" table
const medicationTableData = {
  columns: [
    { title: "Medicine Name", key: "medicineName" },
    { title: "Dose", key: "dose" },
    { title: "Route", key: "route" },
    { title: "Scheduled Time", key: "scheduledTime" },
    { title: "Given Time", key: "givenTime" },
    { title: "Nurse", key: "nurse" },
    { title: "Status", key: "status" },
    { title: "Reason", key: "reason" },
  ],
  rows: [
    {
      medicineName: "Paracetamol",
      dose: "500 mg",
      route: "Oral",
      scheduledTime: "12:00 PM",
      givenTime: "12:10 PM",
      nurse: "<PERSON> Rai",
      status: "Given",
      reason: "---",
    },
    {
      medicineName: "Amoxicillin",
      dose: "250 mg",
      route: "Oral",
      scheduledTime: "9:00 AM",
      givenTime: "9:05 AM",
      nurse: "<PERSON><PERSON>",
      status: "Given",
      reason: "---",
    },
    {
      medicineName: "Pantoprazole",
      dose: "40 mg",
      route: "IV",
      scheduledTime: "3:00 PM",
      givenTime: "---",
      nurse: "Kiran Thapa",
      status: "Not Given",
      reason: "Patient Refused",
    },
    {
      medicineName: "Insulin",
      dose: "10 units",
      route: "Subcutaneous",
      scheduledTime: "8:00 AM",
      givenTime: "8:05 AM",
      nurse: "Bikash Rai",
      status: "Given",
      reason: "---",
    },
    {
      medicineName: "Ondansetron",
      dose: "4 mg",
      route: "Oral",
      scheduledTime: "11:30 AM",
      givenTime: "---",
      nurse: "Sita Gurung",
      status: "Missed",
      reason: "Patient NPO",
    },
  ],
};

const AdministerMedication: React.FC = () => {
  const initialValues = {
    medicineName: "",
    scheduledTime: "",
    givenTime: "",
    dose: "",
    route: "",
    status: "",
    reason: "",
  };

  const medicationValidationSchema = Yup.object({
    medicineName: Yup.string().required("Medicine Name is required"),
    scheduledTime: Yup.string().required("Scheduled Time is required"),
    givenTime: Yup.string().required("Given Time is required"),
    dose: Yup.string().required("Dose is required"),
    route: Yup.string().required("Route is required"),
    status: Yup.string().required("Status is required"),
    reason: Yup.string().when("status", {
      is: "Not Given",
      then: (schema) =>
        schema.required("Reason is required if medication is omitted"),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: medicationValidationSchema,
    onSubmit: (values, { resetForm }) => {
      console.log("Medication Administration Form submitted", values);
      resetForm();
    },
  });

  return (
    <div className='max-w-full p-4 mx-auto'>
      <h2 className='mb-4 text-lg font-semibold text-center'>
        Administer Medication
      </h2>

      <form
        onSubmit={formik.handleSubmit}
        className='w-full p-4 mb-8 space-y-4 border rounded-md'
      >
        {/* First Row: Medicine Name, Scheduled Time, Given Time */}
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Medicine Name */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='medicineName' className='whitespace-nowrap'>
                Medicine Name
              </label>
              <input
                type='text'
                id='medicineName'
                name='medicineName'
                placeholder='Medicine Name'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.medicineName}
              />
            </div>
            {formik.touched.medicineName && formik.errors.medicineName && (
              <p className='text-xs text-red-500'>
                {formik.errors.medicineName}
              </p>
            )}
          </div>

          {/* Scheduled Time */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='scheduledTime' className='whitespace-nowrap'>
                Scheduled Time
              </label>
              <input
                type='time'
                id='scheduledTime'
                name='scheduledTime'
                placeholder='Scheduled Time'
                className='w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-200'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.scheduledTime}
              />
            </div>
            {formik.touched.scheduledTime && formik.errors.scheduledTime && (
              <p className='text-xs text-red-500'>
                {formik.errors.scheduledTime}
              </p>
            )}
          </div>

          {/* Given Time */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='givenTime' className='whitespace-nowrap'>
                Given Time
              </label>
              <input
                type='time'
                id='givenTime'
                name='givenTime'
                placeholder='Given time'
                className='w-full p-1.5 border text-[#3a3a3a] border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.givenTime}
              />
            </div>
            {formik.touched.givenTime && formik.errors.givenTime && (
              <p className='text-xs text-red-500'>{formik.errors.givenTime}</p>
            )}
          </div>
        </div>

        {/* Second Row: Dose, Route, Status */}
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Dose */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='dose' className='whitespace-nowrap'>
                Dose
              </label>
              <input
                type='text'
                id='dose'
                name='dose'
                placeholder='Dose'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.dose}
              />
            </div>
            {formik.touched.dose && formik.errors.dose && (
              <p className='text-xs text-red-500'>{formik.errors.dose}</p>
            )}
          </div>

          {/* Route */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='route' className='whitespace-nowrap'>
                Route
              </label>
              <select
                id='route'
                name='route'
                className='w-full p-1.5 border text-[#3a3a3a] border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.route}
              >
                <option value='' className='text-[#3a3a3a]'>
                  Status
                </option>
                <option value='Oral'>Oral</option>
                <option value='Injection'>Injection</option>
                <option value='IV'>IV</option>
                <option value='Topical'>Topical</option>
                <option value='Other'>Other</option>
              </select>
            </div>
            {formik.touched.route && formik.errors.route && (
              <p className='text-xs text-red-500'>{formik.errors.route}</p>
            )}
          </div>

          {/* Status */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='status' className='whitespace-nowrap'>
                Status
              </label>
              <select
                id='status'
                name='status'
                className='w-full p-1.5  text-[#3a3a3a] border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.status}
              >
                <option value='' className='text-[#3a3a3a]'>
                  Status
                </option>
                <option value='Given'>Given</option>
                <option value='Not Given'>Not Given</option>
              </select>
            </div>
            {formik.touched.status && formik.errors.status && (
              <p className='text-xs text-red-500'>{formik.errors.status}</p>
            )}
          </div>
        </div>

        {/* Third Row: Reason, Save Button */}
        <div className='grid items-end grid-cols-12 gap-4'>
          {/* Reason */}
          <div className='col-span-9 flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='reason' className='whitespace-nowrap'>
                Reason
              </label>

              <div className='flex flex-col w-full'>
                <input
                  id='reason'
                  name='reason'
                  placeholder='Write Reason if Omitted'
                  className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.reason}
                />

                {formik.touched.reason && formik.errors.reason && (
                  <p className='text-xs text-red-500 mt-1'>
                    {formik.errors.reason}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className='flex items-end justify-end h-full col-span-3'>
            <button
              type='submit'
              className='flex items-center justify-center px-6 py-2 gap-3 text-white text-md bg-[#116aef] rounded-lg hover:bg-blue-700 h-fit'
            >
              <Icon
                icon='fa6-solid:floppy-disk'
                width='18'
                height='18'
                color='white'
              />
              Save
            </button>
          </div>
        </div>
      </form>

      {/* Recently Taken Medicine Table */}
      <h2 className='mb-4 text-lg font-semibold text-center'>
        Recently Taken Medicine
      </h2>
      <div className='overflow-x-auto'>
        <MasterTable
          columns={medicationTableData.columns}
          rows={medicationTableData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default AdministerMedication;
