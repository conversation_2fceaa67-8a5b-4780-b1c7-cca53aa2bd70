import{a5 as l,bB as v,av as e,a2 as s,aK as h,af as x,a7 as j,ab as f,ca as y,aj as F,cb as b,cc as C}from"./index-ClX9RVH0.js";import{P}from"./PurchaseReturnForm-DnNRJFB9.js";const S=()=>{const[n,i]=l.useState({state:"",data:null}),[t,c]=l.useState({page:1,limit:10,search:"",inventoryFor:""}),{data:r,isLoading:u}=v({page:t.page,limit:t.limit,category:"PURCHASERETURN",...t.search!==""&&{search:t.search},...t.inventoryFor!==""&&{inventoryFor:t.inventoryFor}});console.log("data",e.get(r,"data.invoices",[]));const p=e.get(r,"data.invoices",[]).map((a,o)=>({sn:(t.page-1)*t.limit+o+1,supplierName:e.get(a,"vendor.commonInfo.personalInfo.fullName","-"),invoice:e.get(a,"invoiceNo","-"),date:h(e.get(a,"date")).format("MMM-DD-YYYY"),category:e.get(a,"inventoryFor","-"),remarks:e.get(a,"remarks","-"),totalCost:e.get(a,"totalAmount",0),action:s.jsx(x,{onEdit:()=>i({state:"edit",data:a}),onShow:()=>i({state:"view",data:a})})})),g=l.useCallback(()=>i({state:"",data:null}),[]),m=l.useCallback(e.debounce(a=>c(o=>({...o,search:a})),500),[]);return s.jsxs("div",{children:[s.jsx(j,{onSearch:m,listTitle:"Purchase Return",FilterSection:()=>s.jsx("div",{className:"flex gap-5",children:s.jsx(f,{label:"",firstInput:"Department",value:t.inventoryFor,onChange:a=>c(o=>({...o,inventoryFor:a.target.value})),options:[{label:"All",value:""},...y]})})}),s.jsxs("div",{className:"py-4",children:[s.jsx(F,{columns:b,rows:p,loading:u,pagination:{currentPage:e.get(r,"data.pagination.page",1),totalPage:e.get(r,"data.pagination.pages",1),limit:t.limit,onClick:({page:a,limit:o})=>c(d=>({...d,page:a??(o?1:d.page),limit:o??d.limit}))}}),n.state==="view"&&s.jsx(C,{onClose:g,details:n.data}),n.state==="edit"&&s.jsx(P,{editData:n.data,onClose:g})]})]})};export{S as default};
