import{a2 as e,a7 as c,a8 as o,a1 as n,al as d,a9 as p,aa as u,ac as a,ab as l,b3 as m,ao as x}from"./index-ClX9RVH0.js";import{S as b}from"./SelectInput-CIK-FOD0.js";const h=[{label:"12345",value:"12345"}],t=[{label:"General",value:"General"},{label:"Cardiology",value:"Cardiology"},{label:"Neurology",value:"Neurology"},{label:"Orthopedics",value:"Orthopedics"},{label:"Gynecology",value:"Gynecology"}],y=[{label:"General",value:"General"},{label:"Cardiology",value:"Cardiology"},{label:"Neurology",value:"Neurology"},{label:"Orthopedics",value:"Orthopedics"},{label:"Gynecology",value:"Gynecology"}],f=()=>e.jsxs("div",{children:[e.jsx(c,{title:"Add Purchase",hideHeader:!0,listTitle:"Add Purchase Return"}),e.jsxs("div",{className:"flex w-full gap-10",children:[e.jsxs("div",{className:"h-auto",children:[e.jsxs("div",{className:"flex flex-col gap-4 bg-white mt-5 px-4 py-2",children:[e.jsx(o,{step:1,title:"Basic Information",isActive:!0}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(o,{step:2,title:"Product Details",isActive:!1})]}),e.jsx("div",{className:"flex flex-col gap-3 col-span-2"})]}),e.jsx("div",{className:"w-full h-full",children:e.jsx(j,{})})]})]}),j=()=>{const s=n({initialValues:{name:"",contactNumber:"",paitentId:"",department:"",date:"",doctor:""},enableReinitialize:!0,onSubmit:i=>{d.success("Form submitted successfully!"),history.back(),console.log(i)}}),{handleSubmit:r}=s;return e.jsx(e.Fragment,{children:e.jsx(p,{value:s,children:e.jsx(u,{onSubmit:r,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsx(b,{options:h,placeholder:"454912",name:"purchaseid",label:"Purchase Id"}),e.jsx(a,{type:"date",label:"Purchase Date",name:"select"}),e.jsx(l,{label:"Department",options:y,name:"ABCDEFG Suppliers PVT LTD"})]}),e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsx(a,{type:"text",label:"Product Name",placeholder:"Paracetamol",name:"paraacetamol"}),e.jsx(l,{label:"Product",options:t,name:"Medication"}),e.jsx(l,{label:"Sub-Category",options:t,name:"Medication"}),e.jsx(a,{type:"number",label:"Batch ID",placeholder:"Auto Generated",name:"batchid"}),e.jsx(a,{type:"date",label:"Mfg Date",placeholder:"Select",name:"mfgdate"}),e.jsx(a,{type:"date",label:"Expired Date",placeholder:"Select",name:"expireddate"}),e.jsx(a,{type:"number",label:"MRP",placeholder:"Enter",name:"mrp"}),e.jsx(a,{type:"number",label:"Total Price",placeholder:"Enter",name:"totalprice"}),e.jsx(a,{type:"number",label:"Discount",placeholder:"Enter",name:"discount"}),e.jsx(l,{label:"Discount Type",options:t,name:"Flat"}),e.jsx(a,{type:"number",label:"Total Price",placeholder:"Select",name:"tatalpricess"}),e.jsx(a,{type:"number",label:"Price per piece",placeholder:"Enter",name:"enter"}),e.jsx("div",{className:"col-span-3 flex justify-center",children:e.jsxs("button",{className:"bg-primary text-white px-6 py-2 rounded-md flex items-center gap-2",children:[e.jsx(m,{icon:"icons8:plus",className:"text-2xl text-white"}),"Add Product"]})})]}),e.jsx(x,{onCancel:()=>{history.back()},onSubmit:r})]})})})})};export{j as BasicInformation,f as default};
