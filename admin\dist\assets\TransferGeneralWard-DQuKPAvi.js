import{ad as C,ap as L,aq as R,a5 as c,ar as k,as as W,at as V,a1 as U,au as z,av as a,aw as H,ax as O,ay as q,a2 as t,az as $,a7 as J,am as K,a9 as M,aa as Q,aA as X,aB as Y,ao as Z,aC as ee}from"./index-ClX9RVH0.js";const te=()=>{var p;const v=C(),{mutate:N,isSuccess:l,isPending:A}=L(),{mutate:h,isSuccess:m,isPending:x}=R(),[u,I]=c.useState(1),{patientId:r}=k(ee,e=>e),{data:o}=W({patient:r},{enabled:r!==""&&r!==void 0}),{data:i}=V(r),s=U({initialValues:{room:"",patientName:"",patientId:"",contactNumber:"",isTransfer:!0,bedNumber:"",transferDate:"",receivedRoom:"",receivedWard:"",transferringDoctor:"",remarks:""},enableReinitialize:!0,validationSchema:z,onSubmit:async e=>{const b={transferInfo:[...a.get(o,"data.bedAssign[0].transferInfo",[]).map(d=>({categoryName:a.get(d,"categoryName._id"),room:a.get(d,"_id",""),bedNumber:a.get(d,"bedNumber"),status:"AVAILABLE"})),{categoryName:e.receivedWard,room:e.receivedRoom,bedNumber:e.bedNumber,joiningDate:e.transferDate,transferedBy:e.transferringDoctor,status:"ASSIGNED"}]};a.get(o,"data.bedAssign[0]._id","")?N({_id:a.get(o,"data.bedAssign[0]._id",""),entityData:b}):h({patient:r,admissionDate:e.transferDate,...b})}});c.useEffect(()=>{i&&s.setValues({...s.values,patientName:a.get(i,"commonInfo.personalInfo.fullName",""),patientId:a.get(i,"patientInfo.patientId",""),contactNumber:a.get(i,"commonInfo.contactInfo.phone.primaryPhone","")})},[i]);const{data:D}=H({role:"DOCTOR"}),{data:y}=O(),{data:g}=q({categoryName:s.values.receivedWard},{enabled:!!s.values.receivedWard}),S=a.map(a.get(D,"data.users",[]),e=>({label:a.get(e,"commonInfo.personalInfo.fullName",""),value:a.get(e,"_id","")})),j=a.map(a.get(y,"data.wardCategory",[]),e=>({label:a.get(e,"categoryName",""),value:a.get(e,"_id","")})),B=a.map(a.get(g,"data.rooms",[]),e=>({label:a.get(e,"roomNo",""),value:a.get(e,"_id","")})),F=((p=a.get(g,"data.rooms",[]).find(e=>e._id===s.values.receivedRoom))==null?void 0:p.beds.filter(e=>e.status==="AVAILABLE").map(e=>({label:a.get(e,"bedNo",""),value:a.get(e,"bedNo","")})))||[],_=[{step:1,title:"Patient Information",isActive:u===1},{step:2,title:"Transfer Details",isActive:u===2}];c.useEffect(()=>{(l||m)&&(s.resetForm(),v("/bed-allocation-list"))},[l,m]);const{handleSubmit:f,getFieldProps:w,setFieldValue:E,touched:G,errors:P,values:T}=s;return r?t.jsx("div",{className:"space-y-4",children:t.jsxs("div",{children:[t.jsx(J,{listTitle:"Transfer Patient",hideHeader:!0}),t.jsxs("div",{className:"relative flex w-full gap-6",children:[t.jsx("div",{className:"w-auto h-full",children:t.jsx(K,{steps:_})}),t.jsx("div",{className:"w-full",children:t.jsx(M,{value:s,children:t.jsxs(Q,{onSubmit:f,className:"space-y-4",children:[X({doctors:S,wardsCategory:j,roomsList:B,bedsList:F}).map(({fields:e},n)=>t.jsx("div",{className:"p-4 bg-white rounded-sm grid grid-cols-1 sm:grid-cols-2 gap-4 md:grid-cols-3",onClick:()=>I(n+1),children:t.jsx(Y,{formDatails:e,errors:P,setFieldValue:E,values:T,touched:G,getFieldProps:w})},`field-group-${n}`)),t.jsx(Z,{onCancel:()=>history.back(),onSubmit:f,isPending:A||x})]})})})]})]})}):t.jsx($,{to:"/general-ward",replace:!0})};export{te as TransferGeneralWard};
