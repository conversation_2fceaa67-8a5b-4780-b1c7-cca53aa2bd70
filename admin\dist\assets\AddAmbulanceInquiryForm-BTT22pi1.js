import{ad as B,aw as I,a5 as m,aV as u,aX as s,aW as K,bm as R,a2 as e,bn as H,aa as W,ac as t,ab as g,bl as L,a4 as X,aY as Y}from"./index-ClX9RVH0.js";import{a as z,b as J,c as Q}from"./ambulanceApi-C42c0mRe.js";const ie=()=>{const{data:l}=z(),{data:c}=J(),j=B(),{data:o}=I({role:"NURSE"}),{mutate:k,isPending:Z}=Q(),x=(o==null?void 0:o.data.users)??[],C=m.useMemo(()=>x.map(a=>({value:a._id||"",label:a.commonInfo.personalInfo.fullName||""}))||[],[x]),{data:p}=I({role:"STAFF"},{enabled:x.length>0}),r=(p==null?void 0:p.data.users)??[],P=m.useMemo(()=>(r==null?void 0:r.map(a=>({value:a._id||"",label:a.commonInfo.personalInfo.fullName})))||[],[r]),F=[{value:"PENDING",label:"Pending"},{value:"ASSIGNED",label:"Assigned"},{value:"COMPLETED",label:"Completed"},{value:"CANCELLED",label:"Cancelled"}],w=m.useMemo(()=>(c==null?void 0:c.data.ambulanceConfiguration.map(a=>({value:a._id||"",label:a.ambulanceType||""})))||[],[c]),O=(a,{resetForm:i})=>{k(a,{onSuccess:()=>{i(),j("/ambulance-management/ambulance-inquiry")}})},_=u().shape({patientName:s().required("Patient Name is required"),contactNumber:s().required("Phone No is required"),date:s().required("The request date is required"),inquiryTime:s().required("inquiryTime is required"),assignedAmbulance:s().required("Ambulance is required"),ambulanceType:s(),nurseAssigned:K().of(s().required("Nurse name is required")).min(1,"At least one nurse must be assigned").required("The assigned nurses are required"),staffAssigned:K().of(s().required("Staff name is required")).min(1,"At least one staff must be assigned").required("The assigned staffs are required"),pickupLocation:u().shape({address:s().required("Pickup location is required")}),dropLocation:u().shape({address:s().required("Drop location is required")}),notes:s(),status:s().required("Inquiry Status is required"),financialDetails:u().shape({initialReadingKM:R(),finalReadingKM:R()})}),$=()=>new Date().toISOString().split("T")[0],G=()=>{const a=new Date,i=String(a.getHours()).padStart(2,"0"),n=String(a.getMinutes()).padStart(2,"0");return`${i}:${n}`};return e.jsx(e.Fragment,{children:e.jsx("div",{children:e.jsx("div",{className:"bg-white rounded",children:e.jsx(H,{initialValues:{patientName:"",contactNumber:"",date:$(),inquiryTime:G(),assignedAmbulance:"",ambulanceType:"",nurseAssigned:[],staffAssigned:[],pickupLocation:{address:"",coordinates:[]},dropLocation:{address:"",coordinates:[]},notes:"",status:"",financialDetails:{initialReadingKM:0,finalReadingKM:0}},validationSchema:_,onSubmit:O,children:({errors:a,touched:i,values:n,handleChange:b,setFieldValue:V})=>{var h,v,N,y,f,A,q,T,M,S,D;const U=m.useMemo(()=>(l==null?void 0:l.data.ambulances.filter(d=>{var E;return((E=d.ambulanceType)==null?void 0:E._id)===n.ambulanceType&&d.availabilityStatus==="AVAILABLE"}).map(d=>({value:d._id||"",label:d.vehicleNo||""})))||[],[l,n.ambulanceType]);return e.jsx(W,{children:e.jsxs("div",{className:"p-6 relative",children:[e.jsxs("div",{className:"border-2 border-[#e8e6e7] px-4 py-3 rounded-xl mb-3",children:[e.jsx("div",{children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[e.jsxs("div",{children:[e.jsx(t,{label:"Patient Name",type:"text",name:"patientName",placeholder:"Enter patient Name"}),a.patientName&&i.patientName&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.patientName})]}),e.jsxs("div",{children:[e.jsx(t,{label:"Phone No.",type:"text",name:"contactNumber",placeholder:"Enter phone number"}),a.contactNumber&&i.contactNumber&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.contactNumber})]})]})}),e.jsx("div",{children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[e.jsxs("div",{children:[e.jsx(t,{label:"Request Date",type:"date",name:"date",placeholder:"2025-01-12"}),a.date&&i.date&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.date})]}),e.jsxs("div",{children:[e.jsx(t,{label:"inquiryTime",type:"time",name:"inquiryTime",placeholder:"9:00 AM"}),a.inquiryTime&&i.inquiryTime&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.inquiryTime})]})]})}),e.jsx("div",{children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[e.jsxs("div",{children:[e.jsx(g,{firstInput:"Select",label:"Ambulance Type",name:"ambulanceType",value:n.ambulanceType,onChange:d=>{b(d),V("assignedAmbulance","")},options:w}),a.ambulanceType&&i.ambulanceType&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.ambulanceType})]}),e.jsxs("div",{children:[e.jsx(g,{firstInput:"Select",label:"Ambulance",name:"assignedAmbulance",value:n.assignedAmbulance,onChange:b,options:U,disabled:!n.ambulanceType}),a.assignedAmbulance&&i.assignedAmbulance&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.assignedAmbulance})]})]})}),e.jsx("div",{children:e.jsx("div",{className:"grid grid-cols-2 gap-4 my-2",children:e.jsxs("div",{children:[e.jsx(g,{firstInput:"Select",label:"Status",name:"status",value:n.status,onChange:b,options:F}),a.status&&i.status&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.status})]})})}),e.jsx("div",{children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[e.jsxs("div",{children:[e.jsx(t,{label:"Pickup Location",type:"text",name:"pickupLocation.address",placeholder:"Enter pickup location"}),((h=a.pickupLocation)==null?void 0:h.address)&&((v=i.pickupLocation)==null?void 0:v.address)&&e.jsx("div",{className:"text-red text-xs mb-2",children:(N=a.pickupLocation)==null?void 0:N.address})]}),e.jsxs("div",{children:[e.jsx(t,{label:"Drop Location",type:"text",name:"dropLocation.address",placeholder:"Enter drop location"}),((y=a.dropLocation)==null?void 0:y.address)&&((f=i.dropLocation)==null?void 0:f.address)&&e.jsx("div",{className:"text-red text-xs mb-2",children:(A=a.dropLocation)==null?void 0:A.address})]})]})}),e.jsx("div",{children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[e.jsxs("div",{children:[e.jsx(L,{label:"Nurse Assigned",name:"nurseAssigned",options:C}),a.nurseAssigned&&i.nurseAssigned&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.nurseAssigned})]}),e.jsxs("div",{children:[e.jsx(L,{label:"Staff Assigned",name:"staffAssigned",options:P}),a.staffAssigned&&i.staffAssigned&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.staffAssigned})]})]})}),e.jsxs("div",{children:[e.jsx(X,{text:"Reading in (Km)",className:"text-base"}),e.jsx("div",{className:"border-b-2 border-gray-200"})]}),e.jsx("div",{children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[e.jsxs("div",{children:[e.jsx(t,{label:"Trip Start (KM)",type:"number",min:"0",name:"financialDetails.initialReadingKM",placeholder:"Enter the initial reading of ambulance in (Km)"}),((q=a.financialDetails)==null?void 0:q.initialReadingKM)&&((T=i.financialDetails)==null?void 0:T.initialReadingKM)&&e.jsx("div",{className:"text-red text-xs mb-2",children:(M=a.financialDetails)==null?void 0:M.initialReadingKM})]}),e.jsxs("div",{children:[e.jsx(t,{label:"Trip End (KM)",type:"number",name:"financialDetails.finalReadingKM",placeholder:"Enter the final reading of ambulance in (Km)",min:"0"}),((S=a.financialDetails)==null?void 0:S.finalReadingKM)&&((D=i.financialDetails)==null?void 0:D.finalReadingKM)&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.financialDetails.finalReadingKM})]})]})}),e.jsxs("div",{children:[e.jsx(Y,{label:"Notes",name:"notes"}),a.notes&&i.notes&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.notes})]})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>j("/ambulance-management/ambulance-inquiry"),className:"px-4 py-2 bg-gray-300 text-black rounded",children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary hover:bg-light_primary text-white rounded",children:"Create"})]})]})})}})})})})};export{ie as default};
