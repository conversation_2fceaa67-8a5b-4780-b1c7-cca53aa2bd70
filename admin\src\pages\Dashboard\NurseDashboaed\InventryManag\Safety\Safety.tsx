import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../../../layouts/Table/MasterTable";
import classNames from "classnames";

const Safety = () => {
  const tableData = {
    columns: [
      { title: "Item Name", key: "itemName" },
      { title: "Unit", key: "unit" },
      { title: "Quantity", key: "quantity" },
      { title: "Minimum Stock", key: "minimumStock" },
      { title: "Expiration Date", key: "expirationDate" },
      { title: "Batch", key: "batch" },
      { title: "Supplier", key: "supplier" },
      { title: "Order", key: "orderQuantity" },
    ],
    rows: [
      {
        itemName: "Face Shields",
        unit: "Pieces",
        quantity: 25,
        minimumStock: 40,
        expirationDate: "Dec 20, 2025",
        batch: "FS-2025-010",
        supplier: "SafeGuard Medicals",
        orderQuantity: 20,
      },
      {
        itemName: "Protective Goggles",
        unit: "Pairs",
        quantity: 15,
        minimumStock: 25,
        expirationDate: "N/A",
        batch: "PG-2024-003",
        supplier: "VisionCare Safety",
        orderQuantity: 10,
      },
      {
        itemName: "Hazmat Suits",
        unit: "Suits",
        quantity: 10,
        minimumStock: 20,
        expirationDate: "Jul 30, 2025",
        batch: "HZ-2025-012",
        supplier: "ProtectWear Inc.",
        orderQuantity: 10,
      },
      {
        itemName: "Safety Gloves",
        unit: "Pairs",
        quantity: 30,
        minimumStock: 50,
        expirationDate: "Oct 15, 2025",
        batch: "SGC-2025-006",
        supplier: "ShieldPro Medical",
        orderQuantity: 25,
      },
      {
        itemName: "Ear Plugs",
        unit: "Boxes",
        quantity: 12,
        minimumStock: 20,
        expirationDate: "N/A",
        batch: "EP-2024-005",
        supplier: "SafeSound Equipments",
        orderQuantity: 10,
      },
      {
        itemName: "Emergency Eye Wash Station",
        unit: "Units",
        quantity: 2,
        minimumStock: 3,
        expirationDate: "Jan 5, 2026",
        batch: "EWS-2026-001",
        supplier: "FirstAid Systems",
        orderQuantity: 1,
      },
      {
        itemName: "High-Visibility Vests",
        unit: "Pieces",
        quantity: 8,
        minimumStock: 15,
        expirationDate: "N/A",
        batch: "HVV-2024-009",
        supplier: "BrightWear Corp.",
        orderQuantity: 7,
      },
      {
        itemName: "Biohazard Waste Bags",
        unit: "Packs",
        quantity: 20,
        minimumStock: 35,
        expirationDate: "N/A",
        batch: "BWB-2024-008",
        supplier: "CleanDispo Ltd.",
        orderQuantity: 15,
      },
      {
        itemName: "Sharps Containers",
        unit: "Units",
        quantity: 18,
        minimumStock: 30,
        expirationDate: "Sep 12, 2025",
        batch: "SC-2025-007",
        supplier: "SafeDisposal Inc.",
        orderQuantity: 20,
      },
    ],
  };

  const statCards = [
    {
      title: "Total Items",
      count: 240,
      subtitle: "Active inventory items",
      icon: "pajamas:work-item-epic",
      iconBg: "text-gray-9000",
      textColor: "text-gray-900",
      bgColor: "bg-[#9DF2DB]",
    },
    {
      title: "Low Stock",
      count: 56,
      subtitle: "Items below minimum",
      icon: "tdesign:error-triangle",
      iconBg: "text-rose-600",
      textColor: "text-rose-600",
      bgColor: "bg-[#FAECEC]",
    },
    {
      title: "Expiring Soon",
      count: 20,
      subtitle: "within 10 days",
      icon: "lets-icons:date-fill",
      iconBg: "text-amber-500",
      textColor: "text-amber-500",
      bgColor: "bg-[#CEF9F6]",
    },
    {
      title: "Today's Reorders",
      count: 9,
      subtitle: "Active inventory items",
      icon: "solar:restart-circle-bold",
      iconBg: "text-lime-600",
      textColor: "text-lime-600",
      bgColor: "bg-[#D8F7DE]",
    },
  ];

  return (
    <div className='p-2 mt-4'>
      {/* Metrics Cards */}
      <div className='grid grid-cols-4 gap-4 mb-6'>
        {statCards.map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgColor} rounded-lg shadow-sm border border-gray-200 p-4`}
          >
            <div className='flex items-center justify-between gap-2'>
              <div className='text-sm font-semibold text-gray-800'>
                {stat.title}
              </div>
              <div>
                <Icon
                  icon={stat.icon}
                  width='24'
                  height='24'
                  className={`${stat.iconBg}`}
                />
              </div>
            </div>
            <div className={`text-2xl font-bold mt-1 ${stat.textColor}`}>
              {stat.count}
            </div>
            <div className='text-xs text-gray-500'>{stat.subtitle}</div>
          </div>
        ))}
      </div>

      <div className='bg-white p-6'>
        <div className='flex items-center justify-between mb-4'>
          <h2 className='text-lg font-semibold text-gray-900'>
            Stock Inventory
          </h2>
          <div className='flex items-center gap-3'>
            <div className='relative'>
              <svg
                className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
                />
              </svg>
              <input
                placeholder='Search items'
                className='pl-10 w-64 border border-gray-300 rounded-md px-3 py-2'
              />
            </div>
            <button className='flex items-center gap-2 bg-transparent border border-gray-300 rounded-md px-4 py-2 hover:bg-gray-50'>
              Sort
              <svg
                className='w-4 h-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M19 9l-7 7-7-7'
                />
              </svg>
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Add New Item
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Reorder Selected
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Download Report
            </button>
          </div>
        </div>
        <div>
          <MasterTable
            columns={tableData.columns}
            rows={tableData.rows}
            loading={false}
          />
        </div>
      </div>
    </div>
  );
};

export default Safety;
