import{a5 as r,av as a,c6 as F,a1 as k,aK as R,bo as L,a2 as e,b2 as M,b3 as w,a9 as B,aa as S,c7 as E,ao as I}from"./index-ClX9RVH0.js";const C=({editData:o,onClose:d})=>{var m,p;const h=r.useMemo(()=>({LABORATORY:"lProduct",HOSPITAL:"hProduct",CANTEEN:"cProduct",PHARMACY:"pProduct"}),[]),N=r.useMemo(()=>({LABORATORY:"lBatch",HOSPITAL:"hBatch",CANTEEN:"cBatch",PHARMACY:"pBatch"}),[]),u=h[a.get(o,"inventoryFor")],[b,v]=r.useState(0),{mutate:j}=F(),s=k({initialValues:{productList:a.get(o,"productList",[]).map(t=>({quantity:a.get(t,"quantity",0),item:t})),returnAmount:0,paymentMethod:"",remarks:"",bank:"",accountNo:""},onSubmit:async t=>{const c={inventoryFor:a.get(o,"inventoryFor",""),category:"PURCHASERETURN",date:R().format("YYYY-MM-DD"),remarks:t.remarks,paymentMethod:t.paymentMethod,vendor:o.vendor._id,invoiceNo:o.invoiceNo,productList:t.productList.map(n=>{var i,x;return{...n.item,totalAmount:Number(a.get(n,"item.purchaseRate",0))*Number(a.get(n,"quantity",0)),quantity:n.quantity,[u]:n.item[u]._id,...a.get(o,"inventoryFor")&&{[N[a.get(o,"inventoryFor")]]:(x=(i=n==null?void 0:n.item)==null?void 0:i.batches[0])==null?void 0:x._id}}}),paymentStatus:"PAID"};t.paymentMethod==="BANK"&&(c.bank=t.bank),j(c),d()}}),{data:f}=L(),l=a.get(f,"data.banks",[]).map(t=>({value:a.get(t,"_id"),label:a.get(t,"bankName"),accountNumber:a.get(t,"accountNumber")}));r.useEffect(()=>{if(s.values.productList){const t=s.values.productList.reduce((c,n)=>c+a.get(n,"item.purchaseRate",0)*n.quantity,0);v(t),s.setFieldValue("returnAmount",t)}},[s.values.productList]);const g={"Invoice no :":a.get(o,"invoiceNo","-"),"Date :":a.get(o,"date","-")},y={"Vendor :":a.get(o,"vendor.commonInfo.personalInfo.fullName","-"),"Phone :":a.get(o,"vendor.commonInfo.contactInfo.phone.primaryPhone","-")},A=[{label:"Cash",value:"CASH"},{label:"Bank",value:"BANK"}];r.useEffect(()=>{if(s.values.bank){const t=l==null?void 0:l.find(c=>c.value===s.values.bank);s.setFieldValue("accountNo",t==null?void 0:t.accountNumber)}},[s.values.bank]);const P=[{type:"select",field:"paymentMethod",label:"Payment Method",options:A},{type:"select",field:"bank",label:"Bank",options:l,isVisible:s.values.paymentMethod==="BANK"},{type:"text",field:"accountNo",label:"Account Number",isVisible:s.values.paymentMethod==="BANK"},{type:"text",field:"remarks",label:"Remarks"}];return e.jsx(M,{onClose:d,classname:"max-w-xl w-full p-6",children:e.jsxs("div",{className:"flex flex-col gap-10",children:[e.jsxs("section",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex flex-col gap-1",children:Object.entries(g).map(([t,c])=>e.jsxs("div",{className:"flex gap-2",children:[e.jsx("span",{className:"font-semibold",children:t}),e.jsx("span",{children:c})]},t))}),e.jsx("div",{children:Object.entries(y).map(([t,c])=>e.jsxs("div",{className:"flex gap-2",children:[e.jsx("span",{className:"font-semibold",children:t}),e.jsx("span",{children:c})]},t))})]}),e.jsxs("section",{children:[e.jsxs("div",{className:"flex border-b pb-2 text-sm text-gray-500",children:[e.jsx("div",{className:"w-1/3",children:"Product"}),e.jsx("div",{className:"w-1/4 text-center",children:"Price"}),e.jsx("div",{className:"w-1/6 text-center",children:"QTY"}),e.jsx("div",{className:"w-1/6 text-center",children:"Sub Total"}),e.jsx("div",{className:"w-10"})]}),((p=(m=s.values)==null?void 0:m.productList)==null?void 0:p.length)>0?s.values.productList.map((t,c)=>e.jsxs("div",{className:"flex items-center py-4 border-b",children:[e.jsx("div",{className:"w-1/3 text-xs",children:a.get(t,`item.${u}.name`,"N/A")}),e.jsx("div",{className:"w-1/4 text-center",children:a.get(t,"item.purchaseRate",0)}),e.jsx("div",{className:"w-1/6 text-center",children:e.jsx("input",{type:"number",min:"1",value:t.quantity,onChange:n=>{const i=Number.parseInt(n.target.value)||0;s.setFieldValue(`productList[${c}].quantity`,i)},className:"w-16 p-1 text-center border rounded"})}),e.jsx("div",{className:"w-1/6 text-center",children:a.get(t,"item.purchaseRate",0)*t.quantity||0}),e.jsx("div",{className:"w-10 flex justify-center",children:e.jsx("button",{onClick:()=>{const n=[...s.values.productList];n.splice(c,1),s.setFieldValue("productList",n)},className:"text-red-400 hover:text-red-600",children:e.jsx(w,{icon:"fluent:delete-24-regular"})})})]},a.get(t,"item._id",c))):e.jsx("div",{className:"text-center mt-2",children:"No Product"})]}),e.jsx("section",{className:"flex flex-col gap-3",children:e.jsxs("div",{className:"flex justify-end gap-40",children:[e.jsx("p",{className:"",children:"Return Amount"}),e.jsx("p",{className:"",children:b.toFixed(2)})]})}),e.jsx("section",{className:"-mt-2",children:e.jsx(B,{value:s,children:e.jsxs(S,{onSubmit:s.handleSubmit,className:"grid grid-cols-2 gap-5",children:[e.jsx(E,{formDatails:P,getFieldProps:s.getFieldProps,touched:s.touched,errors:s.errors}),e.jsx("div",{className:"col-span-2 mt-4",children:e.jsx(I,{onCancel:d,onSubmit:s.submitForm})})]})})})]})})};export{C as P};
