import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { PrescriptionMedicineData } from "../../Consultations/pages/sampleMasterTableData";
import { useFormik } from "formik";
import * as Yup from "yup";

const Prescription = () => {
  const medicineFormValidationSchema = Yup.object({
    medicine: Yup.string().required("Medicine is required"),
    date: Yup.string()
      .matches(/^\d{2}\/\d{2}\/\d{4}$/, "Date must be in DD/MM/YYYY format")
      .required("Date is required"),
    dosage: Yup.number()
      .typeError("Dosage must be a number")
      .positive("Dosage must be greater than zero")
      .required("Dosage is required"),
    dosageUnit: Yup.string().required("Dosage unit is required"),
    frequency: Yup.string().required("Frequency is required"),
    days: Yup.number()
      .typeError("Number of days must be a number")
      .positive("Days must be greater than zero")
      .required("Number of days is required"),
    daysUnit: Yup.string().required("Day unit is required"),
    foodRelation: Yup.string().required("Food relation is required"),
    instruction: Yup.string().required("Instruction is required"),
    route: Yup.string().required("Route is required"),
  });

  const formik = useFormik({
    initialValues: {
      medicine: "",
      date: "",
      dosage: "",
      dosageUnit: "",
      frequency: "",
      days: "",
      daysUnit: "",
      foodRelation: "",
      route: "",
      instruction: "",
    },
    validationSchema: medicineFormValidationSchema,
    onSubmit: (values) => {
      console.log(values);
    },
  });

  return (
    <div className='flex flex-col w-full gap-4 p-4 mx-auto'>
      <h2 className='mb-4 font-semibold text-center'>Prescription Medicine</h2>
      <form
        onSubmit={formik.handleSubmit}
        className='w-full p-6 mx-auto space-y-4 border rounded-md shadow-sm '
      >
        {/* Row 1: Medicine + Date */}
        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <label className='w-20 text-sm font-medium'>Medicine</label>
              <input
                name='medicine'
                placeholder='Search medicine'
                className='flex-1 px-3 py-1 border rounded-md'
                onChange={formik.handleChange}
                value={formik.values.medicine}
              />
            </div>
            {formik.touched.medicine && formik.errors.medicine && (
              <p className='text-xs text-red-500'>{formik.errors.medicine}</p>
            )}
          </div>
          <div className='flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <label className='w-20 text-sm font-medium'>Date</label>
              <input
                name='date'
                placeholder='DD/MM/YYYY (Auto gen)'
                className='flex-1 px-3 py-1 border rounded-md'
                onChange={formik.handleChange}
                value={formik.values.date}
              />
            </div>
            {formik.touched.date && formik.errors.date && (
              <p className='text-xs text-red-500'>{formik.errors.date}</p>
            )}
          </div>
        </div>

        {/* Row 2: Dosage + Frequency */}
        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <label className='w-20 text-sm font-medium'>Dosage</label>
              <input
                name='dosage'
                placeholder='dosage'
                className='w-1/2 px-3 py-1 border rounded-md'
                onChange={formik.handleChange}
                value={formik.values.dosage}
              />
              <select
                name='dosageUnit'
                className='w-1/3 px-2 py-1 border rounded-md'
                onChange={formik.handleChange}
                value={formik.values.dosageUnit}
              >
                <option value=''>Select</option>
                <option value='mg'>mg</option>
                <option value='ml'>ml</option>
              </select>
            </div>
            {formik.touched.dosage && formik.errors.dosage && (
              <p className='text-xs text-red-500'>{formik.errors.dosage}</p>
            )}
            {formik.touched.dosageUnit && formik.errors.dosageUnit && (
              <p className='text-xs text-red-500'>{formik.errors.dosageUnit}</p>
            )}
          </div>
          <div className='flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <label className='w-20 text-sm font-medium'>Frequency</label>
              <select
                name='frequency'
                className='flex-1 px-3 py-1 border rounded-md'
                onChange={formik.handleChange}
                value={formik.values.frequency}
              >
                <option value=''>Select</option>
                <option value='once'>Once Daily</option>
                <option value='twice'>Twice Daily</option>
              </select>
            </div>
            {formik.touched.frequency && formik.errors.frequency && (
              <p className='text-xs text-red-500'>{formik.errors.frequency}</p>
            )}
          </div>
        </div>

        {/* Row 3: No. of Days + Food Relation */}
        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <label className='w-20 text-sm font-medium'>No. of Days</label>
              <input
                name='days'
                placeholder='number of days'
                className='w-1/2 px-3 py-1 border rounded-md'
                onChange={formik.handleChange}
                value={formik.values.days}
              />
              <select
                name='daysUnit'
                className='w-1/3 px-2 py-1 border rounded-md'
                onChange={formik.handleChange}
                value={formik.values.daysUnit}
              >
                <option value=''>Select</option>
                <option value='days'>days</option>
                <option value='weeks'>weeks</option>
              </select>
            </div>
            {formik.touched.days && formik.errors.days && (
              <p className='text-xs text-red-500'>{formik.errors.days}</p>
            )}
            {formik.touched.daysUnit && formik.errors.daysUnit && (
              <p className='text-xs text-red-500'>{formik.errors.daysUnit}</p>
            )}
          </div>
          <div className='flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <label className='w-20 text-sm font-medium'>Food Relation</label>
              <select
                name='foodRelation'
                className='flex-1 px-3 py-1 border rounded-md'
                onChange={formik.handleChange}
                value={formik.values.foodRelation}
              >
                <option value=''>Select</option>
                <option value='before'>Before Food</option>
                <option value='after'>After Food</option>
              </select>
            </div>
            {formik.touched.foodRelation && formik.errors.foodRelation && (
              <p className='text-xs text-red-500'>
                {formik.errors.foodRelation}
              </p>
            )}
          </div>
        </div>

        {/* Row 4: Instruction + Route */}
        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <label className='w-20 text-sm font-medium'>Instruction</label>
              <input
                name='instruction'
                placeholder='instructions'
                className='flex-1 px-3 py-1 border rounded-md'
                onChange={formik.handleChange}
                value={formik.values.instruction}
              />
            </div>
            {formik.touched.instruction && formik.errors.instruction && (
              <p className='text-xs text-red-500'>
                {formik.errors.instruction}
              </p>
            )}
          </div>
          <div className='flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <label className='w-20 text-sm font-medium'>Route</label>
              <select
                name='route'
                className='flex-1 px-3 py-1 border rounded-md'
                onChange={formik.handleChange}
                value={formik.values.route}
              >
                <option value=''>Select</option>
                <option value='oral'>Oral</option>
                <option value='iv'>IV</option>
                <option value='injection'>Injection</option>
              </select>
            </div>
            {formik.touched.route && formik.errors.route && (
              <p className='text-xs text-red-500'>{formik.errors.route}</p>
            )}
          </div>
        </div>

        {/* Save Button */}
        <div className='flex justify-end'>
          <button
            type='submit'
            className='bg-blue-600 hover:bg-blue-700 text-white bg-[#116aef] px-4 py-1.5 rounded-md flex items-center gap-1'
          >
            <Icon
              icon='fa6-solid:floppy-disk'
              width='18'
              height='18'
              color='white'
            />
            Save
          </button>
        </div>
      </form>

      <div>
        <MasterTable
          color='bg-[#b3b3b3]'
          textcolor='text-[#000000]/100'
          columns={PrescriptionMedicineData.columns}
          rows={PrescriptionMedicineData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default Prescription;
