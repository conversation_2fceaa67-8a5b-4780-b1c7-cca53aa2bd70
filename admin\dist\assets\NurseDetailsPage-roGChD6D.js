import{ad as _,ba as F,a2 as s,b3 as P,ag as C,at as G,cB as L,a5 as g,aK as O,cM as U,cN as H,cO as Q,cP as Y,cQ as q,cR as z,cS as V,cT as W}from"./index-ClX9RVH0.js";const K=({staff:c,contactInfo:e,staffType:v})=>{const x=_(),{id:j}=F(),w=()=>{v==="Doctor"?x(`${C.ADDDOCTOR}/${j}`):v==="Nurse"&&x(`${C.ADDNURSE}/${j}`)},S=()=>{if(e!=null&&e.primaryPhone&&e.primaryPhone!=="N/A"){const D=`https://wa.me/${e.primaryPhone.replace(/[^0-9]/g,"")}`;window.open(D,"_blank")}else alert("Phone number not available")},M=()=>{if(e!=null&&e.email&&e.email!=="N/A"){const D=`https://mail.google.com/mail/?view=cm&to=${e.email}`;window.open(D,"_blank")}else alert("Email address not available")};if(!c)return null;const T=()=>v==="Doctor"?c.doctorId:v==="Nurse"?c.nurseId:c.id,I=c.status==="Available"||c.status==="Active";return s.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border",children:s.jsxs("div",{className:"flex items-start justify-between",children:[s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx("div",{className:"relative",children:c.profileImage?s.jsx("img",{src:c.profileImage,alt:c.name,className:"w-16 h-16 rounded-md object-cover"}):s.jsx("div",{className:"w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center",children:s.jsx(P,{icon:"mdi:account",className:"text-gray-400 text-2xl"})})}),s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx("h1",{className:"text-base font-semibold text-gray-900",children:c.name}),I&&s.jsxs("div",{className:"flex items-center gap-1 bg-green/10 text-green border px-2 py-0.5 rounded text-xs",children:[s.jsx("div",{className:"w-1.5 h-1.5 bg-green rounded-full"}),"Available"]})]}),s.jsx("p",{className:"text-gray-600 text-xs",children:c.specialization}),s.jsxs("p",{className:"text-gray-500 text-xs",children:[v," ID: ",T()]})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("button",{onClick:S,className:"p-2 text-gray-600 hover:bg-gray-200 bg-gray-100 rounded",title:"Call via WhatsApp",children:s.jsx(P,{icon:"material-symbols:call",className:"w-4 h-4"})}),s.jsx("button",{onClick:M,className:"p-2 text-gray-600 hover:bg-gray-200 bg-gray-100 rounded",title:"Send Email via Gmail",children:s.jsx(P,{icon:"material-symbols:mail-outline",className:"w-4 h-4"})}),s.jsx("button",{onClick:w,className:"bg-blue text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-blue-700",children:"Edit Profile"})]})]})})},X=()=>{const{id:c}=F(),{data:e,isLoading:v}=G(c),{data:x}=L(c?{user:c}:void 0,{enabled:!!c,refetchOnMount:!0,refetchOnWindowFocus:!0}),j=g.useMemo(()=>{var i;const a=O().format("YYYY-MM-DD"),r=(((i=x==null?void 0:x.data)==null?void 0:i.shiftassigned)||[]).flatMap(t=>{var n;return((n=t.shiftAssignment)==null?void 0:n.filter(o=>{var l;return o.date===a&&((l=o.shifts)==null?void 0:l.length)>0}))||[]}).flatMap(t=>t.shifts.map(n=>({id:n.shift._id||`shift-${Date.now()}`,shiftName:n.shift.shiftName||"N/A",startTime:n.shift.startTime||"N/A",endTime:n.shift.endTime||"N/A",department:t.department||"N/A"}))).filter((t,n,o)=>n===o.findIndex(l=>l.shiftName===t.shiftName&&l.startTime===t.startTime&&l.endTime===t.endTime&&l.department===t.department));return r.length>0?r:[{id:"no-shifts",shiftName:"No shifts scheduled",startTime:"N/A",endTime:"N/A",department:"N/A"}]},[x]),w=g.useMemo(()=>j.length===1&&j[0].shiftName==="No shifts scheduled"?[{id:"no-shifts",time:"No shifts scheduled",appointmentCount:0}]:j.map(a=>({id:a.id,time:`${a.startTime} - ${a.endTime}`,appointmentCount:0,shiftName:a.shiftName,department:a.department})),[j]),S=g.useMemo(()=>{var a,d,r,i,t,n,o;return e?{id:e==null?void 0:e._id,name:((d=(a=e.commonInfo)==null?void 0:a.personalInfo)==null?void 0:d.fullName)||"N/A",role:"Nurse",specialization:((r=e.professionalDetails)==null?void 0:r.fieldOfStudy)||"General Nursing",profileImage:((i=e.identityInformation)==null?void 0:i.profileImage)||"",status:e.isActive?"Available":"Unavailable",nurseId:`${(t=e==null?void 0:e.nurseInformations)==null?void 0:t.nurseId}`,designation:((n=e.nurseInformations)==null?void 0:n.designation)||((o=e.professionalDetails)==null?void 0:o.designation)||"Staff Nurse"}:null},[e]),M=g.useMemo(()=>{var a,d,r,i,t,n,o,l,f,p,N,h,u,y,b,A;return e?{fullName:((d=(a=e.commonInfo)==null?void 0:a.personalInfo)==null?void 0:d.fullName)||"N/A",gender:((i=(r=e.commonInfo)==null?void 0:r.personalInfo)==null?void 0:i.gender)||"N/A",dateOfBirth:((n=(t=e.commonInfo)==null?void 0:t.personalInfo)==null?void 0:n.dob)||"N/A",age:(l=(o=e.commonInfo)==null?void 0:o.personalInfo)!=null&&l.dob?O().diff(O(e.commonInfo.personalInfo.dob),"year"):"N/A",maritalStatus:((p=(f=e.commonInfo)==null?void 0:f.personalInfo)==null?void 0:p.maritalStatus)||"N/A",bloodGroup:((h=(N=e.commonInfo)==null?void 0:N.personalInfo)==null?void 0:h.bloodGroup)||"N/A",religion:((y=(u=e.commonInfo)==null?void 0:u.personalInfo)==null?void 0:y.religion)||"N/A",language:((A=(b=e.commonInfo)==null?void 0:b.personalInfo)==null?void 0:A.language)||"N/A"}:null},[e]),T=g.useMemo(()=>{var a,d,r,i,t,n,o,l,f,p;return e?{nurseId:`N-${(a=e._id)==null?void 0:a.slice(-5)}`,department:((d=e.nurseInformations)==null?void 0:d.nurseDepartment)||"N/A",designation:((r=e.nurseInformations)==null?void 0:r.designation)||((i=e.professionalDetails)==null?void 0:i.designation)||"Staff Nurse",yearsofExperience:((t=e.experienceDetails)==null?void 0:t.yearOfExperience)||"N/A",licenseNumber:((n=e.professionalDetails)==null?void 0:n.medicalLicenseNumber)||"N/A",joiningDate:((o=e.experienceDetails)==null?void 0:o.joinedDate)||"N/A",shiftTiming:((l=e.employmentDetails)==null?void 0:l.shiftTiming)||"N/A",qualification:(()=>{var y,b,A;const N=((b=(y=e.professionalDetails)==null?void 0:y.education)==null?void 0:b.map(m=>m.qualification).filter(m=>m&&m.trim()!=="").filter((m,$,E)=>E.indexOf(m)===$))||[],h=(A=e.professionalDetails)==null?void 0:A.qualificationLevel,u=[];return h&&h.trim()!==""&&u.push(h),N.forEach(m=>{u.includes(m)||u.push(m)}),u.length>0?u.join(", "):"N/A"})(),institution:((f=e.professionalDetails)==null?void 0:f.institution)||"N/A",fieldOfStudy:(()=>{var y,b,A;const N=(y=e.professionalDetails)==null?void 0:y.fieldOfStudy,h=((A=(b=e.professionalDetails)==null?void 0:b.education)==null?void 0:A.map(m=>m.fieldOfStudy).filter(m=>m&&m.trim()!=="").filter((m,$,E)=>E.indexOf(m)===$))||[],u=[];return N&&N.trim()!==""&&u.push(N),h.forEach(m=>{u.includes(m)||u.push(m)}),u.length>0?u.join(", "):"N/A"})(),employmentType:((p=e.professionalDetails)==null?void 0:p.employeeType)||"N/A"}:null},[e]),I=g.useMemo(()=>{var a,d,r,i,t,n,o,l,f,p,N,h;return e?{primaryPhone:((r=(d=(a=e.commonInfo)==null?void 0:a.contactInfo)==null?void 0:d.phone)==null?void 0:r.primaryPhone)||"N/A",secondaryPhone:((n=(t=(i=e.commonInfo)==null?void 0:i.contactInfo)==null?void 0:t.phone)==null?void 0:n.secondaryPhone)||"N/A",email:e.email||"N/A",currentAddress:((f=(l=(o=e.commonInfo)==null?void 0:o.contactInfo)==null?void 0:l.address)==null?void 0:f.currentAddress)||"N/A",permanentAddress:((h=(N=(p=e.commonInfo)==null?void 0:p.contactInfo)==null?void 0:N.address)==null?void 0:h.permanentAddress)||"N/A"}:null},[e]),D=g.useMemo(()=>{var a;return e?{description:((a=e.commonInfo)==null?void 0:a.generalDescription)||"No description available"}:null},[e]),k=g.useMemo(()=>{var a,d,r,i,t,n,o,l,f,p;return e?{accountHolderName:((d=(a=e==null?void 0:e.professionalDetails)==null?void 0:a.accountDetails)==null?void 0:d.accountHolderName)||((i=(r=e==null?void 0:e.commonInfo)==null?void 0:r.personalInfo)==null?void 0:i.fullName)||"N/A",bankName:((n=(t=e.professionalDetails)==null?void 0:t.accountDetails)==null?void 0:n.bankName)||"N/A",accountNumber:((l=(o=e.professionalDetails)==null?void 0:o.accountDetails)==null?void 0:l.accountNumber)||"N/A",panNumber:((f=e.professionalDetails)==null?void 0:f.pan)||"N/A",basicSalary:((p=e.professionalDetails)==null?void 0:p.salary)||"N/A"}:null},[e]),B=g.useMemo(()=>{var d,r;if(!e)return[];const a=[];return(d=e.identityInformation)!=null&&d.identityDocuments&&e.identityInformation.identityDocuments.forEach((i,t)=>{i.documentImages&&i.documentImages.length>0&&i.documentImages.forEach((n,o)=>{n&&n.trim()!==""&&a.push({name:`${i.documentType||"Identity Document"}${i.documentImages.length>1?` (${o+1})`:""}`,type:"identity",hasView:!!n,hasDownload:!!n,url:n,id:`${i._id||i.documentNumber}-${t}-${o}`})})}),(r=e.professionalDetails)!=null&&r.education&&e.professionalDetails.education.forEach((i,t)=>{const n=i.certificate;n&&n.trim()!==""&&a.push({name:`${i.qualificationLevel||"Academic Document"}`,type:"academic",hasView:!!n,hasDownload:!!n,url:n,id:`edu-${t}`})}),a},[e]),R=g.useMemo(()=>!(e!=null&&e.reviews)||e.reviews.length===0?[]:e.reviews.map(a=>({id:a._id,patientName:a.reviewerName,rating:a.reviewerRating,comment:a.reviewerComment,isRecommended:a.reviewerRating>3})),[e==null?void 0:e.reviews]);return v||!e?s.jsx(U,{}):s.jsx("div",{className:"min-h-screen bg-[#EFF7F9] mb-2",style:{fontFamily:"roboto"},children:s.jsx("div",{className:"mx-auto space-y-2",children:s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-2",children:[s.jsxs("div",{className:"lg:col-span-3 flex flex-col gap-2",children:[s.jsx(K,{staff:S,contactInfo:I,staffType:"Nurse"}),s.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[s.jsx("div",{className:"col-span-2",children:s.jsx(H,{personalDetails:M,professionalDetails:T,staffType:"Nurse"})}),s.jsx(Q,{contactInfo:I})]}),s.jsx(Y,{about:D}),s.jsxs("div",{className:"grid gap-2 grid-cols-2",children:[s.jsx(q,{documents:B}),s.jsx(z,{bankDetails:k})]})]}),s.jsxs("div",{className:"flex flex-col gap-2",children:[s.jsx(V,{todaySchedule:w,shiftData:x,staffId:c,staffData:e,staffType:"Nurse"}),s.jsx(W,{reviews:R,staffType:"Nurse",showEmptyMessage:!0})]})]})})})};export{X as default};
