import{a5 as p,ad as g,ae as y,a2 as e,ag as x,ah as h,ai as b,aj as j}from"./index-ClX9RVH0.js";import{c as f,D as k}from"./Svg-BMTGOzwv.js";import{D as C}from"./DepartmentHeader-Aj6XBXn4.js";const v=()=>{const[n,s]=p.useState("Patient"),o=g(),a={columns:[{title:"Paitent Id",key:"tokenid"},{title:"Patient Name",key:"patientName"},{title:"Date Assigned",key:"date"},{title:"Contact Number",key:"treatment"},{title:"Appointment Date",key:"doctorName"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:y.map(({tokenId:t,patientName:i,date:c,doctorName:l,status:m,treatment:d},u)=>({key:u,tokenid:t,patientName:i,date:c,doctorName:l,status:e.jsx(h,{status:m}),treatment:d,action:e.jsx("button",{onClick:()=>o(x.GYNECOLOGY),className:"px-4 py-2 text-white rounded bg-primary",children:"Checkup"})}))},r=t=>{console.log(t,"onSearch")};return e.jsxs(e.Fragment,{children:[e.jsx(C,{title:"Gynecology",headerTitle:"ODP",doctorName:"Dr. John Smith",services:["Eye Checkup","Cataract & Glaucoma","Retina & Cornea Care","LASIK Surgery","Pediatric Eye Care","Emergency Eye Care"],patientServed:1020,doctorImage:e.jsx(k,{}),followUpPatient:1120,newPatient:220,revenueGenerated:402300,icon:e.jsx(f,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pb-2 mt-5",children:[e.jsx(b,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:n,onTabChange:t=>s(t)}),e.jsx("div",{children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name id",onChange:t=>r(t.target.value),className:"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(j,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{v as GynecologyDepartmentPage};
