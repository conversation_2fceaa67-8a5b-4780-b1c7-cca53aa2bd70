import{a2 as e,a5 as w,cG as _,aP as q,aM as z,as as K,cH as V,af as X,ah as Y,a7 as Q,aj as Z,aR as B}from"./index-ClX9RVH0.js";import{B as ee,A as se,e as le}from"./Svg-BMTGOzwv.js";function ie({data:s}){var g,b,f,a,x,n,d,r,v,p,j;const t=s==null?void 0:s.transferInfo[s.transferInfo.length-1],c=i=>new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),h=i=>new Date(i).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),N=i=>{switch(i.toLowerCase()){case"available":return"bg-green text-white";case"occupied":return"bg-red text-white";case"maintenance":return"bg-yellow text-black";default:return"bg-gray-100 text-gray-800"}},y=i=>{switch(i.toLowerCase()){case"paid":return"bg-green text-white";case"pending":return"bg-yellow text-black";case"overdue":return"bg-red text-white";default:return"bg-gray-100 text-gray-800"}};return e.jsxs("div",{className:"max-w-4xl mx-auto p-6 space-y-6",children:[e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6 border-l-4 border-indigo-300",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-bold text-gray-900 mb-2",children:"Bed Occupancy Details"}),e.jsxs("p",{className:"text-gray-600",children:["Name: ",(f=(b=(g=s==null?void 0:s.patient)==null?void 0:g.commonInfo)==null?void 0:b.personalInfo)==null?void 0:f.fullName]}),e.jsxs("p",{className:"text-gray-600",children:["Email: ",(a=s==null?void 0:s.patient)==null?void 0:a.email]})]}),e.jsx("div",{className:"text-right",children:e.jsx("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${s!=null&&s.isActive?"bg-green text-white":"bg-gray-100 text-gray-800"}`,children:s!=null&&s.isActive?"Active":"Inactive"})})]})}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Current Bed Assignment"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 bg-indigo-100 rounded-lg",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Bed Number"}),e.jsx("p",{className:"text-2xl font-bold ",children:t==null?void 0:t.bedNumber})]}),e.jsx("div",{className:"text-right",children:e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${N((t==null?void 0:t.status)??"")}`,children:t==null?void 0:t.status})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Ward Category"}),e.jsx("p",{className:"font-medium text-gray-900",children:(x=t==null?void 0:t.categoryName)==null?void 0:x.categoryName})]}),(t==null?void 0:t.joiningDate)&&e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Joining Date"}),e.jsx("p",{className:"font-medium text-gray-900",children:c(t==null?void 0:t.joiningDate)})]})]})]}),(t==null?void 0:t.room)&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"p-4 bg-gray-50 rounded-lg",children:[e.jsx("h3",{className:"font-semibold text-gray-900 mb-3",children:"Room Information"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Room Number:"}),e.jsx("span",{className:"font-medium",children:(n=t==null?void 0:t.room)==null?void 0:n.roomNo})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Floor:"}),e.jsx("span",{className:"font-medium",children:(d=t==null?void 0:t.room)==null?void 0:d.floor})]})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Facilities"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:(v=(r=t==null?void 0:t.room)==null?void 0:r.facilities)==null?void 0:v.map((i,m)=>e.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-indigo-100 text-indigo-800",children:i==null?void 0:i.toUpperCase()},m))})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Admission Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Admission Date"}),e.jsx("p",{className:"text-base text-gray-900",children:c((s==null?void 0:s.admissionDate)??"")})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Created At"}),e.jsx("p",{className:"text-base text-gray-900",children:h((s==null?void 0:s.createdAt)??"")})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Last Updated"}),e.jsx("p",{className:" text-gray-900",children:h((s==null?void 0:s.updatedAt)??"")})]})]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Payment Information"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Payment Method"}),e.jsx("p",{className:"font-medium text-gray-900",children:s==null?void 0:s.paymentMethod})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Payment Status"}),e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${y((s==null?void 0:s.paymentStatus)??"")}`,children:s==null?void 0:s.paymentStatus})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total Charges"}),e.jsxs("p",{className:"text-xl font-bold text-green-600",children:["Rs. ",s==null?void 0:s.totalCharges]})]})]})]})]}),(s==null?void 0:s.transferInfo)&&((p=s==null?void 0:s.transferInfo)==null?void 0:p.length)>1&&e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Transfer History"}),e.jsx("div",{className:"space-y-3",children:(j=s==null?void 0:s.transferInfo)==null?void 0:j.map((i,m)=>{var l,o,u;return e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-sm font-medium text-blue-600",children:m+1})})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium text-gray-900",children:["Bed ",i==null?void 0:i.bedNumber," -"," ",(l=i==null?void 0:i.categoryName)==null?void 0:l.categoryName]}),(i==null?void 0:i.room)&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Room ",(o=i==null?void 0:i.room)==null?void 0:o.roomNo,", Floor"," ",(u=i==null?void 0:i.room)==null?void 0:u.floor]}),(i==null?void 0:i.joiningDate)&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Joined: ",c(i==null?void 0:i.joiningDate)]})]})]}),e.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${N(i==null?void 0:i.status)}`,children:i==null?void 0:i.status})]},i==null?void 0:i._id)})})]})]})}const ce=()=>{var p,j,i,m;const[s,t]=w.useState(""),[c,h]=w.useState({limit:5,page:1}),{mutateAsync:N}=_(),[y,g]=w.useState({selectedItem:{},showModal:!1}),b=q(()=>{g({selectedItem:{},showModal:!1})}),f=z({search:s,...s?{}:{page:c.page,limit:c.limit}}),{data:a}=K(f),{data:x}=V(),n=(p=x==null?void 0:x.data)==null?void 0:p.beds,d=((j=a==null?void 0:a.data)==null?void 0:j.bedAssign)||[],r={columns:[{title:"Room No",key:"roomId"},{title:"Patient Name",key:"patientName"},{title:"Ward",key:"department"},{title:"Bed No",key:"bedNo"},{title:"Admission Date",key:"admissionDate"},{title:"Mobile No",key:"mobileNo"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:d==null?void 0:d.map((l,o)=>{var u,I,D,A,S,k,P,C,M,T,R,O,F,L,H,U,$,E,G,J,W;return{key:o+1,roomId:(A=(D=(I=l==null?void 0:l.transferInfo)==null?void 0:I[((u=l==null?void 0:l.transferInfo)==null?void 0:u.length)-1])==null?void 0:D.room)==null?void 0:A.roomNo,patientName:(P=(k=(S=l==null?void 0:l.patient)==null?void 0:S.commonInfo)==null?void 0:k.personalInfo)==null?void 0:P.fullName,department:(R=(T=(M=l==null?void 0:l.transferInfo)==null?void 0:M[((C=l==null?void 0:l.transferInfo)==null?void 0:C.length)-1])==null?void 0:T.categoryName)==null?void 0:R.categoryName,bedNo:(L=(F=l==null?void 0:l.transferInfo)==null?void 0:F[((O=l==null?void 0:l.transferInfo)==null?void 0:O.length)-1])==null?void 0:L.bedNumber,admissionDate:l.admissionDate,mobileNo:(E=($=(U=(H=l==null?void 0:l.patient)==null?void 0:H.commonInfo)==null?void 0:U.contactInfo)==null?void 0:$.phone)==null?void 0:E.primaryPhone,status:e.jsx(Y,{status:(W=(J=l==null?void 0:l.transferInfo)==null?void 0:J[((G=l==null?void 0:l.transferInfo)==null?void 0:G.length)-1])==null?void 0:W.status}),action:e.jsx(X,{onShow:()=>{g({selectedItem:l,showModal:!0})},onDelete:()=>{N({id:JSON.stringify([l==null?void 0:l._id])})}})}})},v=[{id:1,title:"Total Beds",bed:n==null?void 0:n.totalBeds,icon:e.jsx(ee,{})},{id:2,title:"Available Beds",bed:n==null?void 0:n.totalAvailableBeds,icon:e.jsx(se,{})},{id:3,title:"Occupied Beds",bed:n==null?void 0:n.totalOccupiedBeds,icon:e.jsx(le,{})}];return e.jsxs("div",{className:"flex flex-col w-full h-full gap-4",children:[e.jsx(Q,{listTitle:"Bed Allocation",hideHeader:!0}),e.jsx("div",{className:"p-6 rounded-lg bg-white w-full h-full",children:e.jsx("div",{className:"w-full justify-between   grid grid-cols-3 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-3 gap-4",children:v.map((l,o)=>e.jsxs("div",{className:"w-[16rem] h-fit rounded-xl flex justify-between gap-1 p-4 bg-[#F0F2F4]",children:[e.jsxs("div",{className:"w-full flex flex-col gap-2  h-full",children:[e.jsx("h1",{className:"font-semibold text-[#606060]",children:l.title}),e.jsx("h1",{className:"text-xl text-black font-bold",children:l.bed})]}),e.jsx("div",{className:"w-fit h-fit",children:l.icon})]},o))})}),e.jsxs("div",{className:"w-full h-full bg-white",children:[e.jsx("div",{className:"w-full h-full  px-4",children:e.jsx(Q,{onSearch:l=>{t(l)}})}),e.jsx("div",{children:e.jsx(Z,{columns:r.columns,rows:r.rows,loading:!1,pagination:{currentPage:c.page,totalPage:(m=(i=a==null?void 0:a.data)==null?void 0:i.pagination)==null?void 0:m.pages,limit:c.limit,onClick:l=>{l.page&&h({...c,page:l.page}),l.limit&&h({...c,limit:l.limit})}}})}),y.showModal&&e.jsx(B,{ref:b,classname:"h-[550px] overflow-scroll  p-4",children:e.jsx(ie,{data:y.selectedItem})})]})]})};export{ce as default};
