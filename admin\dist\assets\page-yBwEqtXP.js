import{a2 as t,af as n,ad as o,aQ as s,aj as r}from"./index-ClX9RVH0.js";const d=[{id:1,patient:{name:"<PERSON> <PERSON>",_id:"Ram-11"},room:{_id:"abc",roomNo:"101",bedNo:"B01"},dietType:"Regular Diet",MealTiming:["BreakFast","Lunch","Dinner"],status:"active"},{id:2,patient:{name:"<PERSON>hya<PERSON> Hari",_id:"Shy-112"},room:{_id:"abcde",roomNo:"102",bedNo:"B02"},dietType:"Diabetic Diet",MealTiming:["BreakFast","Lunch","Dinner"],status:"active"}],m=()=>{const i={columns:[{key:"id",title:"Patient  ID"},{key:"name",title:"Patient Name"},{key:"room",title:"Room no"},{key:"bed",title:"Bed no"},{key:"type",title:"Diet Type"},{key:"servingTime",title:"Meal Time"},{key:"status",title:"Status"},{key:"action",title:"Action"}],rows:d.map(e=>({id:e.patient._id,name:e.patient.name,room:e.room.roomNo,bed:e.room.bedNo,type:e.dietType,servingTime:e.MealTiming,status:t.jsx("div",{className:`${e.status==="active"?"bg-[#F3FCF5] border border-[#34C759] rounded-md text-[#34C759]":""} capitalize p-1`,children:e.status}),action:t.jsx(n,{onEdit:()=>{}})}))},a=o();return t.jsxs("div",{children:[t.jsx(s,{headerTitle:"Patients Diet Plan",button:!0,buttonText:"Assign Diet Plan",onSearch:!0,buttonAction:()=>{a("/assign-diet-plan/add-diet-plan")}}),t.jsx(r,{columns:i.columns,rows:i.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})};export{m as AssignDietPlan};
