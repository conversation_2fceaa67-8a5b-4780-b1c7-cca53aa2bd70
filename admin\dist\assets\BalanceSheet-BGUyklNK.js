import{a5 as a,cr as W,cs as q,av as y,a2 as e,ct as z,aM as K,ah as Q,a7 as U,b3 as J,aj as O}from"./index-ClX9RVH0.js";import{u as V}from"./balanceSheetApi-qQfxc5M2.js";const X=a.forwardRef(({data:s,type:f,status:o},u)=>{var N,g,w,v,S,d,l,C,k,i;const c=(N=s==null?void 0:s.data)==null?void 0:N.totalTransaction,h=W(Math.abs(c??0),{currency:"NRS",locale:"IN"}),{data:m,isSuccess:j}=q();console.log(m,"hospitalInfo");const n=y.get(m,"data.hospital");return console.log(o,"status"),console.log((g=s==null?void 0:s.data)==null?void 0:g.transactions,"data"),e.jsxs("div",{className:" p-2 bg-white",ref:u,children:[e.jsx("div",{className:"flex items-center justify-between mb-6",children:e.jsxs("div",{className:"flex  place-items-center justify-center w-full gap-4",children:[e.jsx("div",{className:"flex   ",children:e.jsx("img",{src:(n==null?void 0:n.logo)||"/ReportLogo.png",className:"h-20 w-20 rounded-full",alt:"hospital logo"})}),e.jsx("div",{children:e.jsx("h1",{className:"text-xl font-bold text-gray-800",children:(n==null?void 0:n.hospitalName)||"Aarogya Niketan Hospital Pvt. Ltd."})})]})}),e.jsx("div",{className:"text-center mb-6",children:e.jsxs("h2",{className:"text-lg font-semibold text-gray-800",children:[f," (",o,")"]})}),e.jsx("div",{className:"overflow-x-auto border border-gray-800 mb-4 rounded-md",children:e.jsxs("table",{className:"min-w-full text-sm text-left text-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 border-b border-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Date"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Transaction ID"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Payment Method"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Amount"}),e.jsx("th",{className:"px-4 py-2 text-center font-medium",children:"Balance"})]})}),e.jsx("tbody",{children:y.get(s,"data.transactions",[]).map((r,A)=>{var D,p,T;return e.jsxs("tr",{className:"border-b border-gray-200 hover:bg-gray-100 break-inside-avoid-page",children:[e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center whitespace-nowrap w-[100px]",children:(r==null?void 0:r.accCode)||"-"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center max-w-[200px]",children:(r==null?void 0:r.accDescription)||"-"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center w-fit whitespace-nowrap",children:(r==null?void 0:r.txdCategory)==="DEBIT"?(D=r.txdAmount)==null?void 0:D.toFixed(2):"0.00"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center w-fit whitespace-nowrap",children:(r==null?void 0:r.txdCategory)==="CREDIT"?(p=r.txdAmount)==null?void 0:p.toFixed(2):"0.00"}),e.jsx("td",{className:"px-4 py-2 text-right w-[120px]",children:((T=r==null?void 0:r.txdAmount)==null?void 0:T.toFixed(2))??"0.00"})]},A)})})]})}),e.jsxs("div",{className:`flex justify-around items-center my-4 rounded-md border ${((v=(w=s==null?void 0:s.data)==null?void 0:w.transactions)==null?void 0:v.length)>17?"mt-24":""} border-gray-800 bg-gray-50 text-sm font-semibold`,children:[e.jsx("div",{className:"text-right",children:"Total"}),e.jsxs("div",{className:"px-4 py-2 text-right border-l border-gray-800",children:[e.jsxs("div",{className:"flex",children:[e.jsx("p",{children:"Total Debit"}),e.jsxs("p",{children:[": Rs.",((d=(S=s==null?void 0:s.data)==null?void 0:S.totalDebit)==null?void 0:d.toFixed(2))??"0.00"]})," "]}),e.jsxs("div",{className:"flex",children:[e.jsx("p",{children:"Total Credit"}),e.jsxs("p",{children:[": Rs.",((C=(l=s==null?void 0:s.data)==null?void 0:l.totalCredit)==null?void 0:C.toFixed(2))??"0.00"]})," "]}),e.jsxs("div",{className:"flex",children:[e.jsx("p",{children:"Total Transaction"}),e.jsxs("p",{children:[": Rs.",((i=(k=s==null?void 0:s.data)==null?void 0:k.totalTransaction)==null?void 0:i.toFixed(2))??"0.00"," "]})]})]})]}),e.jsx("div",{className:"border border-gray-800 mb-4",children:e.jsxs("div",{className:"p-2 text-sm flex gap-4",children:[e.jsx("span",{className:"font-medium",children:"Amount in Words: "}),e.jsx("span",{className:"border-b border-gray-300 inline-block ",children:h})]})})]})}),ee=()=>{const[s,f]=a.useState(""),[o,u]=a.useState(""),[c,h]=a.useState(""),[m,j]=a.useState(""),[n,N]=a.useState(""),[g,w]=a.useState(""),[v,S]=a.useState(!1),[d,l]=a.useState([]),C=a.useRef(null),k=z.useReactToPrint({contentRef:C,pageStyle:`
        @page {
          size: A4;
          margin: 0.5in;
        }
        @media print {
          body {
            -webkit-print-color-adjust: exact;
          }
        }
      `}),i=o||c||s,r=i?K({startDate:o||void 0,endDate:c||void 0,page:1,limit:1e3,txdCategory:s&&s!=="ALL"?s:void 0}):null,{data:A,isLoading:D}=V(r),p=i?y.get(A,"data",{}):{};console.log(A,"dataraja"),a.useEffect(()=>{l([])},[]),a.useEffect(()=>{l([])},[o,c,s]);const T=()=>{const t=m||(n||g?"ALL":"");u(n),h(g),f(t),j(t),l([]),S(!0)},$=()=>{u(""),h(""),f(""),N(""),w(""),j(""),l([]),S(!0)},H=(t,x)=>{l(t?x:[])},_=(t,x)=>{l(x?b=>[...b,t]:b=>b.filter(E=>E!==t))},F=()=>{const t=y.get(p,"balanceSheet",[]);d.length>0&&t.filter(x=>d.includes(x._id)),setTimeout(()=>{k()},100)},I={columns:[{title:"Date",key:"date"},{title:"Name",key:"fullName"},{title:"Catrgory",key:"txdCategory"},{title:"Payment Method",key:"paymentMethod"},{title:"Amount",key:"amount"},{title:"Total Balance",key:"balance"}],rows:y.get(p,"balanceSheet",[]).map(t=>{var x,b,E,R,B,P,M;return{_id:t._id,txdCategory:t==null?void 0:t.txdCategory,date:new Date(t.date).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),paymentMethod:t.paymentMethod,fullName:((E=(b=(x=t==null?void 0:t.createdBy)==null?void 0:x.commonInfo)==null?void 0:b.personalInfo)==null?void 0:E.fullName)||((P=(B=(R=t==null?void 0:t.user)==null?void 0:R.commonInfo)==null?void 0:B.personalInfo)==null?void 0:P.fullName)||"N/A",amount:`Rs. ${t.txdAmount.toLocaleString()}`,balance:`Rs. ${t.totalBalance.toLocaleString()}`,bank:((M=t.bank)==null?void 0:M.bankName)||"N/A",category:e.jsx(Q,{status:t.txdCategory,className:`px-2 py-1 rounded-full text-xs font-medium ${t.txdCategory==="DEBIT"?"bg-green-100 text-green-800":t.txdCategory==="CREDIT"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`})}})},G=i?y.get(p,"balanceSheet",[]).length:0,L=o||c||s;return e.jsxs("div",{className:"h-screen overflow-y-auto",children:[e.jsx(U,{listTitle:"General Ledger Statement",hideHeader:!0}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-end md:space-x-4 space-y-4 md:space-y-0",children:[e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:n,onChange:t=>N(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:g,onChange:t=>w(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category"}),e.jsxs("select",{value:m,onChange:t=>j(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[e.jsx("option",{value:"",disabled:!0,children:"Select Category"}),e.jsx("option",{value:"ALL",children:"All"}),e.jsx("option",{value:"DEBIT",children:"Debit"}),e.jsx("option",{value:"CREDIT",children:"Credit"})]})]}),e.jsxs("div",{className:"w-full md:w-auto flex gap-4 mt-2 md:mt-0",children:[e.jsx("button",{onClick:T,className:"w-full md:w-auto px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 transition-colors font-medium",children:"Apply"}),e.jsx("button",{onClick:$,className:"w-full md:w-auto px-4 py-2 bg-rose-500 text-white rounded-md hover:bg-rose-600 transition-colors font-medium",children:"Clear"}),e.jsxs("button",{onClick:F,disabled:!v||!m,className:`w-full md:w-auto px-4 py-2  text-white rounded-md  transition-colors font-medium
                ${v&&m?"bg-sky-500 text-white hover:bg-sky-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:[e.jsx(J,{icon:"material-symbols-light:print-outline",width:"24",height:"24"})," "]})]})]}),L&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[e.jsx("span",{className:"text-sm text-gray-600 font-medium",children:"Active filters:"}),o&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["From: ",o,e.jsx("button",{onClick:()=>{u(""),u("")},className:"ml-1 text-green-600 hover:text-green-800",children:"×"})]}),c&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["To: ",c,e.jsx("button",{onClick:()=>{h(""),h("")},className:"ml-1 text-green-600 hover:text-green-800",children:"×"})]}),s&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:["Category: ",s==="ALL"?"All":s,e.jsx("button",{onClick:()=>{j(""),f("")},className:"ml-1 text-purple-600 hover:text-purple-800",children:"×"})]})]})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm",children:[e.jsx("div",{className:"p-6 border-b border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Transaction Details"}),e.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:[i?D?"Loading...":`Showing ${G} transactions`:"Apply filters to view transactions",L&&i&&" (filtered)"]})]}),d.length>0&&e.jsxs("div",{className:"text-sm text-gray-600",children:[d.length," transaction(s) selected"]})]})}),e.jsx(O,{columns:I.columns,rows:I.rows,loading:D,color:"bg-gray-50",textcolor:"text-gray-600",selectedIds:d,onSelectAll:H,onSelectRow:_,primaryKey:"_id",onBulkAction:F,bulkActionLabel:"Print Selected",showBulkActions:!0})]}),e.jsx("section",{className:"hidden",children:e.jsx(X,{ref:C,data:A,type:"Balance Sheet"})})]})};export{ee as default};
