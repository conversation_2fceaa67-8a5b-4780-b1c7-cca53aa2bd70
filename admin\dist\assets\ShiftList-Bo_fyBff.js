import{a1 as F,a2 as t,cq as L,a9 as P,aa as _,d6 as f,bR as M,a5 as a,af as R,a7 as H,bF as I,aj as O,bA as B}from"./index-ClX9RVH0.js";import{a as q,b as z,u as G}from"./shiftList-piIuxMpn.js";const J=({onClose:c,heading:u,EditData:s})=>{const{mutate:o}=q(),{mutate:p}=z(),i=F({initialValues:{shiftName:(s==null?void 0:s.shiftName)||"",startTime:(s==null?void 0:s.startTime)||"",endTime:(s==null?void 0:s.endTime)||""},onSubmit:async r=>{console.log("Submitted values:",r),s?await p({entityData:r,_id:s._id}):await o(r),d()}}),d=()=>{c==null||c()};return t.jsx(L,{onClose:d,heading:u||"Add Shift",className:"max-w-screen-md w-full",children:t.jsx(P,{value:i,children:t.jsxs(_,{onSubmit:i.handleSubmit,className:"space-y-4",children:[t.jsxs("div",{className:"grid grid-cols-3 gap-x-4 gap-y-2",children:[t.jsx(f,{formik:i,name:"shiftName",type:"text",label:"Shift Name",placeholder:"Enter Shift Name"}),t.jsx(f,{formik:i,name:"startTime",type:"time",label:"Start Time",placeholder:"Enter Start Time"}),t.jsx(f,{formik:i,name:"endTime",type:"time",label:"End Time",placeholder:"Enter End Time"})]}),t.jsxs("div",{className:"py-5 flex justify-end space-x-3",children:[t.jsx("button",{type:"button",onClick:d,className:"px-4 py-2 bg-gray-400 text-white rounded",children:"Cancel"}),t.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary text-white rounded",children:"Save"})]})]})})})},Q=M("shift","shift"),U=Q.useDeleteByQuery,W=()=>{var y,b,j,N;const[c,u]=a.useState("Doctor"),[s,o]=a.useState(""),[p,i]=a.useState();a.useCallback(()=>o(""),[]);const[d,r]=a.useState(!1),[w,h]=a.useState(!1),[m,S]=a.useState(null),{mutateAsync:k}=U({id:JSON.stringify([m])}),[n,x]=a.useState({limit:5,page:1}),{data:l,refetch:v}=G({page:n.page,limit:n.limit});console.log(l,"data");const A=e=>{i(e),r(!0)},C=async()=>{if(m)try{await k(),h(!1),S(null),await v()}catch(e){console.error("Failed to delete shift:",e)}},g={columns:[{title:"S.N.",key:"serialNumber"},{title:"Shift Name",key:"shiftName"},{title:"Start Time",key:"startTime"},{title:"End Time",key:"endTime"},{title:"Action",key:"action"}],rows:(b=(y=l==null?void 0:l.data)==null?void 0:y.shiftList)==null?void 0:b.map((e,T)=>({...e,key:T,serialNumber:T+1,shiftName:e==null?void 0:e.shiftName,startTime:e==null?void 0:e.startTime,endTime:e==null?void 0:e.endTime,action:t.jsx(R,{onEdit:()=>A({...e,_id:(e==null?void 0:e._id)||(e==null?void 0:e.id)}),onDelete:()=>{S((e==null?void 0:e._id)||(e==null?void 0:e.id)),h(!0)}})}))};return t.jsxs("div",{className:"flex flex-col gap-y-2",children:[t.jsx(H,{listTitle:"Shift Config",title:"Shift Management",hideHeader:!0}),t.jsxs("div",{className:"bg-white rounded-md  px-3",children:[t.jsx("div",{className:"flex justify-end",children:t.jsx(I,{variant:"primary",title:"Add Shift",size:"md",className:"w-32 h-10 mt-3",onClick:()=>o("add")})}),t.jsx("div",{children:t.jsx(O,{className:"px-0 py-4",columns:g.columns,rows:g.rows,loading:!1,pagination:{currentPage:n.page,totalPage:((N=(j=l==null?void 0:l.data)==null?void 0:j.pagination)==null?void 0:N.pages)||1,limit:n.limit,onClick:e=>{e.page&&x({...n,page:e.page}),e.limit&&x({...n,limit:e.limit})}}})})]}),s==="add"&&t.jsx(J,{onClose:()=>o("")}),t.jsx(B,{confirmAction:w,title:"Do you want to delete this record?",des:"This action cannot be undone.",onClose:()=>h(!1),onConfirm:C})]})};export{W as default};
