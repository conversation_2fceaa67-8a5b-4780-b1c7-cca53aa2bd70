import clsx from "clsx";

export function StatusDoctorCard({
  status,
  className,
}: {
  status: string;
  className?: string;
}) {
  const statusColors: { [key: string]: string } = {
    Available: "border-[#34C759]  text-[#34C759]",
    Busy: "border-gray-500  text-gray-500",
    "In-surgery": "border-red  text-red",
    "On-leave": "border-[#FFCC00]  text-[#FFCC00]",
    "On-duty": "border-purple-500  text-purple-500",
  };

  return (
    <div
      className={clsx(
        "px-2 py-1 flex justify-start w-fit rounded-full text-xs font-medium border",
        statusColors[status] || "bg-gray-100 text-gray-700",
        className
      )}
    >
      <span className="mr-1">●</span>
      {status}
    </div>
  );
}
