import{b4 as X,cx as ee,cy as te,a5 as m,br as ae,aw as W,a1 as re,cz as le,cA as ne,bw as se,cB as Y,cC as oe,a2 as e,a9 as de,aa as ie,bx as ce,ab as h,ac as u,aZ as ue,cD as q,ao as me,ba as pe,cE as he,a7 as xe,a8 as I,ai as be,cF as ve}from"./index-ClX9RVH0.js";const ge=[{label:"Male",value:"MALE"},{label:"Female",value:"FEMALE"},{label:"Other",value:"OTHER"}],Ne=[{label:"Single",value:"Single"},{label:"Married",value:"Married"}],je=[{label:"A+",value:"A+"},{label:"A-",value:"A-"},{label:"B+",value:"B+"},{label:"B-",value:"B-"},{label:"AB+",value:"AB+"},{label:"AB-",value:"AB-"},{label:"O+",value:"O+"},{label:"O-",value:"O-"},{label:"Don't Know",value:"unknown"}],Z=()=>{const i=new Date,P=i.getFullYear(),A=String(i.getMonth()+1).padStart(2,"0"),T=String(i.getDate()).padStart(2,"0");return`${P}-${A}-${T}`};function fe({setsStep:i}){var H,_,$,U;const{mutateAsync:P,isPending:A}=X(),{mutateAsync:T}=ee(),F=te(),{tokenId:x,tokenNumber:N}=F.state||{},[R,V]=m.useState({fullName:"",gender:"",age:"",bloodGroup:"",maritalStatus:"",citizenNumber:"",language:"",currentAddress:"",permanentAddress:"",contactNumber:"",alternatePhoneNumber:"",role:"PATIENT",email:"",password:"patient@123",remark:"",token:x,department:"",appointmentDate:Z(),doctor:"",availableTimeSlot:"",doctorDutyTime:"",availablestatus:"",totalCharge:"",resellerName:"none",status:"PENDING",totalAmount:"",paymentMethod:"CASH",transactionId:"",paymentStatus:"PAID",bankName:""});m.useEffect(()=>{x&&V(t=>({...t,token:x}))},[x,N]);const{data:k}=ae(),j=(H=k==null?void 0:k.data)==null?void 0:H.departmentCategory,y=j==null?void 0:j.map(t=>({label:t==null?void 0:t.name,value:t==null?void 0:t._id})),{data:v}=W({role:"RESELLER"}),E=[...((_=v==null?void 0:v.data)==null?void 0:_.users.map(t=>{var s,l,n,o,d;return{label:`${(l=(s=t==null?void 0:t.commonInfo)==null?void 0:s.personalInfo)==null?void 0:l.fullName} | ${(d=(o=(n=t==null?void 0:t.commonInfo)==null?void 0:n.contactInfo)==null?void 0:o.phone)==null?void 0:d.primaryPhone}`,value:t==null?void 0:t._id}}))||[],{label:"None",value:"none"}],a=re({initialValues:R,enableReinitialize:!0,validationSchema:le,onSubmit:async t=>{try{await P({role:t.role,email:t.email,password:t.password,isActive:!0,FCMToken:"",commonInfo:{generalDescription:"",personalInfo:{fullName:t.fullName,gender:t.gender,dob:t.age,language:t.language,bloodGroup:t.bloodGroup,maritalStatus:t.maritalStatus},contactInfo:{phone:{primaryPhone:t.contactNumber,secondaryPhone:t.alternatePhoneNumber},address:{currentAddress:t.currentAddress,permanentAddress:t.permanentAddress}}},identityInformation:{primaryIdNo:t.citizenNumber,identityDocuments:[{documentType:"citizenship",documentNumber:"",documentImages:[""]}],profileImage:""},appointInfo:{date:t.appointmentDate,token:t.token||void 0,timeSlot:t.doctorDutyTime,doctor:t.doctor,status:t.status,department:t.department,remark:t.remark,paymentMethod:t.paymentMethod,bank:t.bankName||void 0,transactionNo:t.transactionId||void 0,paymentStatus:t.paymentStatus,...t.resellerName!=="OTHER"&&t.resellerName!=="none"&&{reseller:t.resellerName}}}),x&&await T({_id:x,entityData:{tokenStatus:ne.completed}}),window.location.reload(),history.back()}catch(s){console.error("Error in form submission:",s)}}}),{data:p}=W({role:se.DOCTOR},{enabled:!0,refetchOnMount:!0,refetchOnWindowFocus:!0}),{data:S}=Y({},{enabled:!!a.values.appointmentDate,refetchOnMount:!0,refetchOnWindowFocus:!0}),w=(($=S==null?void 0:S.data)==null?void 0:$.shiftassigned)??[],[O,G]=m.useState([]),M=()=>{var t;return Array.isArray(p)?p:(t=p==null?void 0:p.data)!=null&&t.users&&Array.isArray(p.data.users)?p.data.users:[]};m.useEffect(()=>{const t=M();if(!a.values.appointmentDate||!t.length){G([]);return}const s=w.filter(n=>n.shiftAssignment.some(o=>o.date===a.values.appointmentDate)).map(n=>{var o;return(o=n.user)==null?void 0:o._id}),l=t.filter(n=>s.includes(n._id)).map(n=>{var o,d;return{value:n._id,label:(d=(o=n==null?void 0:n.commonInfo)==null?void 0:o.personalInfo)==null?void 0:d.fullName}});G(l)},[a.values.appointmentDate,p,w]),m.useEffect(()=>{const t=M();if(a.values.doctor&&t.length){const s=t.find(l=>(l==null?void 0:l._id)===a.values.doctor);if(s!=null&&s.departmentDetails){const{hirachyFirst:l}=s.departmentDetails;l&&!a.values.department&&a.setFieldValue("department",l)}}},[a.values.doctor,p]),m.useEffect(()=>{var l,n;const t=M();if(!a.values.doctor||!t.length){a.setFieldValue("availablestatus",""),a.setFieldValue("totalCharge",0);return}const s=t.find(o=>o._id===a.values.doctor);if(s){const o=((l=s.professionalDetails)==null?void 0:l.checkInStatus)===!0,d=((n=s.professionalDetails)==null?void 0:n.consultationFee)||0;a.setFieldValue("availablestatus",o?"Available":"Not-Available"),a.setFieldValue("totalCharge",d)}},[a.values.doctor,p]);const{data:g}=Y(a.values.doctor?{user:a.values.doctor}:void 0,{enabled:!!a.values.doctor,refetchOnMount:!0,refetchOnWindowFocus:!0}),D=m.useMemo(()=>{var t;return(t=g==null?void 0:g.data)!=null&&t.shiftassigned?g.data.shiftassigned.flatMap(s=>{var l;return((l=s==null?void 0:s.shiftAssignment)==null?void 0:l.flatMap(n=>{var o;return((o=n==null?void 0:n.shifts)==null?void 0:o.map(d=>{var K;return{userId:(K=s.user)==null?void 0:K._id,date:n==null?void 0:n.date,shiftId:d==null?void 0:d.shift,startTime:d==null?void 0:d.startTime,endTime:d==null?void 0:d.endTime,slots:d==null?void 0:d.slots}}))||[]}))||[]}):[]},[g]),B=m.useMemo(()=>{if(!a.values.appointmentDate||!D.length)return[];const t=l=>{if(!l)return"";const[n,o,d]=l.split("-");return`${n}-${o.padStart(2,"0")}-${d.padStart(2,"0")}`},s=t(a.values.appointmentDate.toString());return D.filter(l=>(l==null?void 0:l.date)&&t(l.date)===s).flatMap(l=>{var n;return((n=l==null?void 0:l.slots)==null?void 0:n.filter(o=>o==null?void 0:o.isAvailiable))||[]})},[a.values.appointmentDate,D]),J=(B==null?void 0:B.map(t=>({label:(t==null?void 0:t.timeRange)||"No time specified",value:(t==null?void 0:t.timeRange)||""})))||[],{data:L}=oe(),Q=((U=L==null?void 0:L.data)==null?void 0:U.banks.map(t=>({value:t._id,label:t.bankName})))||[],{handleSubmit:z,errors:r,touched:c,getFieldProps:C,values:f,setFieldValue:b}=a;return e.jsx("div",{children:e.jsx(de,{value:a,children:e.jsx(ie,{onSubmit:z,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsx(ce,{isLoading:A,subText:"Finalizing Request",text:"Almost Done..."}),e.jsxs("div",{className:"text-textCounter font-semibold text-sm ",children:["Token Number :"," ",e.jsx("h1",{className:"text-black font-semibold text-sm  inline",children:N||"No Token"})]}),e.jsxs("div",{"data-step":"1",className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5 ",children:[e.jsx("div",{children:e.jsx(h,{required:!0,onClick:()=>i==null?void 0:i(1),label:"Department",options:y||[],firstInput:"Select Department",name:"department",value:a.values.department,onChange:t=>a.setFieldValue("department",t.target.value)})}),e.jsxs("div",{children:[e.jsx(h,{required:!0,label:"Choose Doctor",options:O,firstInput:"Select Doctor",name:"doctor",value:a.values.doctor,onChange:t=>a.setFieldValue("doctor",t.target.value),disabled:!a.values.appointmentDate}),r.doctor&&c.doctor&&e.jsx("p",{className:"text-red text-sm",children:r.doctor})]}),e.jsxs("div",{children:[e.jsx(u,{type:"date",required:!0,label:"Appointment Date",placeholder:"yy/mm/dd",name:"appointmentDate",min:Z(),onChange:t=>{a.setFieldValue("appointmentDate",t.target.value),a.setFieldValue("doctor","")}}),r.appointmentDate&&c.appointmentDate&&e.jsx("p",{className:"text-red text-sm",children:r.appointmentDate})]}),e.jsx("div",{children:e.jsx(u,{type:"text",label:"Available Status",placeholder:"None",value:a.values.availablestatus,disabled:!0})}),e.jsx("div",{children:e.jsx(u,{type:"totalCharge",label:"Total Charge",placeholder:"Auto",value:a.values.totalCharge,disabled:!0})}),e.jsxs("div",{children:[e.jsx(h,{required:!0,label:"Available Time Slot",options:J,firstInput:"Select Time Slot",name:"availableTimeSlot",value:a.values.availableTimeSlot,onChange:t=>{const s=t.target.value;if(s.includes("-")){const[l]=s.split("-");a.setFieldValue("availableTimeSlot",s),a.setFieldValue("doctorDutyTime",l.trim())}else a.setFieldValue("availableTimeSlot",s),a.setFieldValue("doctorDutyTime","")},disabled:!a.values.doctor}),r.availableTimeSlot&&c.availableTimeSlot&&e.jsx("p",{className:"text-red text-sm",children:r.availableTimeSlot})]}),e.jsxs("div",{children:[e.jsx(u,{required:!0,type:"time",label:"Doctor Duty Time",placeholder:"Time",name:"doctorDutyTime",value:a.values.doctorDutyTime||"",onChange:a.handleChange,onBlur:a.handleBlur,disabled:!0}),r.doctorDutyTime&&c.doctorDutyTime&&e.jsx("p",{className:"text-red text-sm",children:r.doctorDutyTime})]}),e.jsxs("div",{children:[e.jsx(h,{label:"Referral Name",options:E,name:"resellerName",value:f.resellerName,onChange:t=>b("resellerName",t.target.value)}),r.resellerName&&c.resellerName&&e.jsx("p",{className:"text-red text-sm",children:r.resellerName})]}),e.jsx("div",{className:"h-1 ",children:e.jsx(ue,{name:"remark",label:"Remark",placeholder:"Remarks",value:a.values.remark,className:"h-12 border rounded-md p-2 block ",onChange:t=>b("remark",t.target.value)})}),e.jsx("div",{children:e.jsx(h,{label:"Status",options:[{value:"PENDING",label:"PENDING"},{value:"CONFIRMED",label:"CONFIRMED"},{value:"IN-PROGRESS",label:"IN-PROGRESS"},{value:"CANCELLED",label:"CANCELLED"},{value:"RE-SCHEDULED",label:"RE-SCHEDULED"}],firstInput:"Select Status",name:"status",value:a.values.status,onChange:t=>b("status",t.target.value)})})]}),e.jsxs("div",{"data-step":"2",className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsxs("div",{children:[e.jsx(u,{required:!0,onClick:()=>i==null?void 0:i(2),type:"text",label:"Full Name",placeholder:"Full Name",...C("fullName")}),r.fullName&&c.fullName&&e.jsx("p",{className:"text-red text-sm",children:r.fullName})]}),e.jsxs("div",{children:[e.jsx(h,{label:"Gender",options:ge,name:"gender",required:!0,value:f.gender,onChange:t=>b("gender",t.target.value)}),r.gender&&c.gender&&e.jsx("p",{className:"text-red text-sm",children:r.gender})]}),e.jsxs("div",{children:[e.jsx(h,{label:"Blood Group",name:"bloodGroup",required:!0,options:je,value:f.bloodGroup,onChange:t=>b("bloodGroup",t.target.value)}),r.bloodGroup&&c.bloodGroup&&e.jsx("p",{className:"text-red text-sm mt-1",children:r.bloodGroup})]}),e.jsxs("div",{children:[e.jsx(u,{type:"date",label:"DOB",placeholder:"auto",required:!0,...C("age")}),r.age&&c.age&&e.jsx("p",{className:"text-red text-sm",children:r.age})]}),e.jsxs("div",{children:[e.jsx(h,{label:"Marital Status",options:Ne,name:"maritalStatus",required:!0,value:f.maritalStatus,onChange:t=>b("maritalStatus",t.target.value)}),r.maritalStatus&&c.maritalStatus&&e.jsx("p",{className:"text-red text-sm",children:r.maritalStatus})]}),e.jsxs("div",{children:[e.jsx(u,{type:"text",label:"language",placeholder:"language",value:f.language,onChange:t=>b("language",t.target.value)}),r.language&&c.language&&e.jsx("p",{className:"text-red text-sm",children:r.language})]}),e.jsxs("div",{children:[e.jsx(u,{type:"text",label:"Current Address",placeholder:"Current Address",value:f.currentAddress,onChange:t=>b("currentAddress",t.target.value),required:!0}),r.currentAddress&&c.currentAddress&&e.jsx("p",{className:"text-red text-sm",children:r.currentAddress})]}),e.jsxs("div",{children:[e.jsx(u,{type:"text",label:"Permanent Address",placeholder:"PermanentAddress",value:f.permanentAddress,onChange:t=>b("permanentAddress",t.target.value)}),r.permanentAddress&&c.permanentAddress&&e.jsx("p",{className:"text-red text-sm",children:r.permanentAddress})]}),e.jsxs("div",{children:[e.jsx(u,{type:"text",label:"Phone Number",placeholder:"Phone Number",...C("contactNumber"),required:!0}),r.contactNumber&&c.contactNumber&&e.jsx("p",{className:"text-red text-sm",children:r.contactNumber})]}),e.jsxs("div",{children:[e.jsx(u,{type:"text",label:"Alternate Phone Number",placeholder:"Alternate Phone Number",...C("alternatePhoneNumber")}),r.alternatePhoneNumber&&c.alternatePhoneNumber&&e.jsx("p",{className:"text-red text-sm",children:r.alternatePhoneNumber})]}),e.jsxs("div",{children:[e.jsx(u,{required:!0,type:"email",label:"Email Address",placeholder:"Email Address",...C("email")}),r.email&&c.email&&e.jsx("p",{className:"text-red text-sm",children:r.email})]}),e.jsxs("div",{children:[e.jsx(u,{type:"text",label:"Citizenship No",placeholder:"Enter Citizenship number",...C("citizenNumber")}),r.citizenNumber&&c.citizenNumber&&e.jsx("p",{className:"text-red text-sm",children:r.citizenNumber})]})]}),e.jsxs("div",{"data-step":"3",className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsxs("div",{children:[e.jsx(h,{onClick:()=>i==null?void 0:i(3),label:"Choose Payment Method",name:"paymentMethod",options:[{value:"CASH",label:"Cash"},{value:"BANK",label:"Bank"}],value:a.values.paymentMethod,onChange:t=>{t.target.value!=="BANK"&&(a.setFieldValue("bankName",""),a.setFieldValue("transactionId","")),a.setFieldValue("paymentMethod",t.target.value)}}),a.errors.paymentMethod&&a.touched.paymentMethod&&e.jsx("p",{className:"text-red text-sm",children:a.errors.paymentMethod})]}),a.values.paymentMethod==="BANK"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx(h,{label:"Choose Bank Name",name:"bankName",options:Q,value:a.values.bankName,onChange:t=>a.setFieldValue("bankName",t.target.value)}),a.errors.bankName&&a.touched.bankName&&e.jsx("p",{className:"text-red text-sm",children:a.errors.bankName})]}),e.jsxs("div",{children:[e.jsx(u,{type:"text",name:"transactionId",label:"Transaction ID",placeholder:"Enter",value:a.values.transactionId,onChange:a.handleChange}),a.errors.transactionId&&a.touched.transactionId&&e.jsx("p",{className:"text-red text-sm",children:a.errors.transactionId})]})]}),e.jsxs("div",{children:[e.jsx(h,{label:"Payment Status",name:"paymentStatus",options:[{value:"PENDING",label:q.pending},{value:"PAID",label:q.paid},{value:"PARTIALLY-PAID",label:q.PARTIALLYPAID}],value:a.values.paymentStatus,onChange:t=>a.setFieldValue("paymentStatus",t.target.value)}),a.errors.paymentStatus&&a.touched.paymentStatus&&e.jsx("p",{className:"text-red text-sm",children:a.errors.paymentStatus})]})]}),e.jsx(me,{onCancel:()=>{a.resetForm()},onSubmit:z})]})})})})}function ye(){const[i,P]=m.useState("Old Patient"),{id:A}=pe(),T=!!A,[F,x]=m.useState(1),[N,R]=m.useState(1),V=T?"Edit Appointment":"Add Appointment",{data:k}=he(A),j=i==="Old Patient",y=m.useRef(null);return m.useEffect(()=>{const v=()=>{if(!y.current)return;const a=y.current,p=a.querySelectorAll("[data-step]"),S=a.scrollTop,w=a.clientHeight;let O=1;p.forEach((G,M)=>{const g=G,D=g.offsetTop,B=g.offsetHeight;S>=D-w/3&&S<D+B-w/3&&(O=M+1)}),j?x(O):R(O)},E=y.current;if(E)return E.addEventListener("scroll",v),()=>E.removeEventListener("scroll",v)},[j]),e.jsxs("div",{children:[e.jsx(xe,{listTitle:V,hideHeader:!0}),e.jsxs("div",{className:"flex w-full gap-6",children:[e.jsx("div",{className:"w-auto",children:e.jsx("div",{className:"sticky top-4 flex flex-col gap-4 px-4 py-5 bg-white rounded-lg shadow-sm min-w-[280px]",children:j?e.jsxs(e.Fragment,{children:[e.jsx(I,{step:1,title:"Patient Information",isActive:F===1}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(I,{step:2,title:"Appointment Information",isActive:F===2}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(I,{step:3,title:"Payment Information",isActive:F===3})]}):e.jsxs(e.Fragment,{children:[e.jsx(I,{step:1,title:"Appointment Information",isActive:N===1}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(I,{step:2,title:"Patient Information",isActive:N===2}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(I,{step:3,title:"Payment Information",isActive:N===3})]})})}),e.jsxs("div",{ref:y,className:"flex flex-col gap-3 flex-1 min-w-0 h-[calc(100vh-120px)] overflow-y-auto",children:[e.jsx(be,{tabs:["Old Patient","New Patient"],defaultTab:i,onTabChange:v=>P(v)}),i==="Old Patient"&&e.jsx(ve,{getAppointmentById:k,setStep:x}),i==="New Patient"&&e.jsx(fe,{setsStep:R})]})]})]})}export{ye as AddAppointment};
