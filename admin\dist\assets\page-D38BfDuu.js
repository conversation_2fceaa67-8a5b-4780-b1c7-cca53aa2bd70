import{a5 as s,bQ as r,aw as N,a2 as e,ab as h,bF as i}from"./index-ClX9RVH0.js";import{u as b}from"./shiftList-piIuxMpn.js";const E=()=>{var x;const[o,l]=s.useState("Week"),[u,w]=s.useState(r.DayPilot.Date.today()),[d,C]=s.useState([]),[v,j]=s.useState(""),[a,D]=s.useState("Doctor"),m=[{label:"Doctor",value:"Doctor"},{label:"Nurse",value:"Nurse"}],S=a==null?void 0:a.toUpperCase(),{data:n}=N({role:S}),c=(x=n==null?void 0:n.data)==null?void 0:x.users,y=(c==null?void 0:c.map(t=>{var p,f;return{label:(f=(p=t==null?void 0:t.commonInfo)==null?void 0:p.personalInfo)==null?void 0:f.fullName,value:t==null?void 0:t._id}}))||[],{data:g}=b();return console.log(g),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between mb-5",children:[e.jsxs("div",{className:"flex gap-10",children:[e.jsx("div",{className:"w-full",children:e.jsx(h,{options:m,firstInput:"Select",name:"name",onChange:t=>D(t.target.value),value:a})}),e.jsx("div",{className:"w-full",children:e.jsx(h,{options:y,firstInput:"Select",name:"name",onChange:t=>j(t.target.value),value:v})})]}),e.jsxs("div",{className:"flex gap-5",children:[e.jsx(i,{onClick:()=>l("Day"),title:"Day"}),e.jsx(i,{onClick:()=>l("Week"),title:"Week"}),e.jsx(i,{onClick:()=>l("Month"),title:"Month"})]})]}),o!=="Month"&&e.jsx(r.DayPilotCalendar,{viewType:o,startDate:u,events:d}),o==="Month"&&e.jsx(r.DayPilotMonth,{startDate:u,events:d})]})};export{E as DailyShiftPage};
