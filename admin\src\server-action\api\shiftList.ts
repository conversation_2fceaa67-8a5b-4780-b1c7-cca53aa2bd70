// services/serviceApi.ts

import { createApiConfig } from "../config/Api-config";

const ShiftList = createApiConfig("shift-list", "shift-list");

//----------------------------shift----------------------
export const useGetShiftList = ShiftList.useGetAll;
export const useGetShiftListById = ShiftList.useGetById;
export const useCreateShiftList = ShiftList.useCreate;
export const useUpdateShiftList = ShiftList.useUpdate;
export const useDeleteShiftList = ShiftList.useDeleteByQuery;
