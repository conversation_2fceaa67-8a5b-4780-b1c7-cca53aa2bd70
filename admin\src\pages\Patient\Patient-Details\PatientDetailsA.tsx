// import React from "react";
// import Header from "./components/Header";
// import GeneralInfoCard from "./components/GeneralInfoCard";
// import {
//   leftItems,
//   rightItems,
//   secondCardData,
// } from "./components/GeneralDataobj";
// import HospitalCard from "./components/HospitalCard";
// const PatientDetailsA = () => {
//   return (
//     <div className="mx-4">
//       <div className="flex items-center justify-between space-x-4">
//         <div>
//           <Header />

//           <div className="flex flex-col md:flex-row items-center gap-4 py-4">
//             <GeneralInfoCard
//               variant="double"
//               title="General Info"
//               leftItems={leftItems}
//               rightItems={rightItems}
//             />
//             <GeneralInfoCard
//               variant="single"
//               title="Contact Info"
//               leftItems={secondCardData}
//             />
//           </div>
//         </div>
//         <HospitalCard
//           hospitalName="Global Hospital"
//           membershipType="Blue Membership"
//           name="Priya Joshi"
//           id="241"
//           validTill="11/2023"
//         />
//       </div>
//     </div>
//   );
// };

// export default PatientDetailsA;

// import React from "react";
// import Header from "./components/Header";
// import GeneralInfoCard from "./components/GeneralInfoCard";
// import {
//   leftItems,
//   rightItems,
//   secondCardData,
// } from "./components/GeneralDataobj";
// import HospitalCard from "./components/HospitalCard";

// const PatientDetailsA = () => {
//   return (
//     <div className="mx-4">
//       <div className="flex items-center gap-4">
//         <div>
//           <Header />

//           <div className="flex flex-col md:flex-row items-center gap-4 py-4">
//             <GeneralInfoCard
//               variant="double"
//               title="General Info"
//               leftItems={leftItems}
//               rightItems={rightItems}
//             />
//             <GeneralInfoCard
//               variant="single"
//               title="Contact Info"
//               leftItems={secondCardData}
//             />
//           </div>
//         </div>
//         <div className="flex justify-start items-start">
//           <HospitalCard
//             hospitalName="Global Hospital"
//             membershipType="Blue Membership"
//             name="Priya Joshi"
//             id="241"
//             validTill="11/2023"
//           />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default PatientDetailsA;

// import React from "react";
// import Header from "./components/Header";
// import GeneralInfoCard from "./components/GeneralInfoCard";
// import {
//   iconArray,
//   leftItems,
//   rightItems,
//   secondCardData,
// } from "./components/GeneralDataobj";
// import HospitalCard from "./components/HospitalCard";
// import ButtonList from "./components/ButtonList";

// const PatientDetailsA = () => {
//   return (
//     <div className="mx-2">
//       <div className="flex flex-col md:flex-row gap-4">
//         <div>
//           <Header />

//           <div className="flex flex-col md:flex-row items-start gap-4 py-4">
//             <GeneralInfoCard
//               variant="double"
//               title="General Info"
//               leftItems={leftItems}
//               rightItems={rightItems}
//             />
//             <GeneralInfoCard
//               variant="single"
//               title="Contact Info"
//               leftItems={secondCardData}
//             />
//           </div>
//         </div>
//         <div className="self-start">
//           <HospitalCard
//             hospitalName="Global Hospital"
//             membershipType="Blue Membership"
//             name="Priya Joshi"
//             id="241"
//             validTill="11/2023"
//           />
//           <ButtonList icon={iconArray} />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default PatientDetailsA;

// import Header from "./components/Header";
// import GeneralInfoCard from "./components/GeneralInfoCard";
// import {
//   iconArray,
//   leftItems,
//   rightItems,
//   secondCardData,
// } from "../Patient-Details/components/GeneralDataobj";
// import HospitalCard from "./components/HospitalCard";
// import ButtonList from "./components/ButtonList";

// const PatientDetailsA = () => {
//   return (
//     <div className="mx-2">
//       <div className="flex flex-col md:flex-row gap-4">
//         <div className="flex-1">
//           <Header />
//           <div className="flex flex-col md:flex-row items-stretch gap-4 py-4">
//             <GeneralInfoCard
//               variant="double"
//               title="General Info"
//               leftItems={leftItems}
//               rightItems={rightItems}
//             />
//             <GeneralInfoCard
//               variant="single"
//               title="Contact Info"
//               leftItems={secondCardData}
//             />
//           </div>
//         </div>
//         <div className="flex flex-col gap-4 w-[350px]">
//           {/* <div className="flex-1"> */}
//           <HospitalCard
//             hospitalName="Global Hospital"
//             membershipType="Blue Membership"
//             name="Priya Joshi"
//             id="241"
//             validTill="11/2023"
//           />
//           {/* </div> */}
//           <ButtonList icon={iconArray} />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default PatientDetailsA;

// import React from "react";
// import Header from "./components/Header";
// import GeneralInfoCard from "./components/GeneralInfoCard";
// import {
//   iconArray,
//   leftItems,
//   rightItems,
//   secondCardData,
// } from "./components/GeneralDataobj";
// import HospitalCard from "./components/HospitalCard";
// import ButtonList from "./components/ButtonList";
// import Tabs from "./components/CustomTabs";

// const PatientDetailsA = () => {
//   return (
//     <div className="mx-2">
//       <div className="flex flex-col lg:flex-row gap-4">
//         <div className="flex-1 min-w-0">
//           <Header />
//           <div className="flex flex-col lg:flex-row items-stretch gap-4 py-4">
//             <GeneralInfoCard
//               variant="double"
//               title="General Info"
//               leftItems={leftItems}
//               rightItems={rightItems}
//             />
//             <GeneralInfoCard
//               variant="single"
//               title="Contact Info"
//               leftItems={secondCardData}
//             />
//           </div>
//         </div>
//         <div className="flex flex-col gap-4 w-[350px] flex-shrink-0">
//           {/* <div className="flex-1"> */}
//           <HospitalCard
//             hospitalName="Global Hospital"
//             membershipType="Blue Membership"
//             name="Priya Joshi"
//             id="241"
//             validTill="11/2023"
//           />
//           {/* </div> */}
//           <ButtonList icon={iconArray} />
//         </div>
//       </div>

//       <div className="bg-white">
//         <Tabs/>

//       </div>
//     </div>
//   );
// };

// export default PatientDetailsA;

// Second

// import React, { useMemo } from "react";
// import Header from "./components/Header";
// import GeneralInfoCard from "./components/GeneralInfoCard";
// import {
//   iconArray,
//   leftItems,
//   rightItems,
//   secondCardData,
// } from "./components/GeneralDataobj";
// import HospitalCard from "./components/HospitalCard";
// import ButtonList from "./components/ButtonList";
// import Tabs from "./components/CustomTabs";
// import { AppointmentCard, MedicationCard } from "./PatientDetails";
// import { get } from "lodash";
// import { useGetUserById } from "../../../server-action/api/user";
// import { useParams } from "react-router-dom";
// import BillingInformation from "./components/BillingInformation";
// import MedicalRecordsApp from "./components/VerticalTabs";

// // Define placeholder components for tab content
// const MedicationTab: React.FC = () => (
//   <div className="p-4">
//     <h2 className="text-lg font-semibold">Medication</h2>
//     <p>List of prescribed medications will be displayed here.</p>
//   </div>
// );

// const AppointmentsTab: React.FC = () => (
//   <div className="p-4">
//     <h2 className="text-lg font-semibold">Appointments</h2>
//     <p>Upcoming and past appointments will be listed here.</p>
//   </div>
// );

// const PaymentDetailsTab: React.FC = () => (
//   <div className="p-4">
//     <h2 className="text-lg font-semibold">Payment Details</h2>
//     <p>Billing information and payment history will be shown here.</p>
//   </div>
// );

// const MedicalRecordsTab: React.FC = () => (
//   <div className="p-4">
//     <h2 className="text-lg font-semibold">Medical Records</h2>
//     <p>Patient medical records and history will be displayed here.</p>
//   </div>
// );

// const PatientDetailsA: React.FC = () => {
//   const { id } = useParams();

//   const { data: userData } = useGetUserById(id as string);

//   const generalInfo = useMemo(
//     () => ({
//       ...get(userData, "commonInfo.personalInfo", {}),
//       ...get(userData, "commonInfo.contactInfo.address", {}),
//     }),
//     [userData]
//   );
//   // Define tab items
//   const tabItems = [
//     {
//       id: "medication",
//       label: "Medication",
//       content: <MedicationCard user_id={get(userData, "_id", "")} />,
//     },
//     {
//       id: "appointments",
//       label: "Appointments",
//       content: <AppointmentCard user_id={get(userData, "_id", "")} />,
//     },
//     {
//       id: "payment",
//       label: "Payment Details",
//       content: (
//         <BillingInformation
//           user_id={get(userData, "_id", "")}
//           generalInfo={generalInfo}
//         />
//       ),
//     },
//     { id: "records", label: "Medical Records", content: <MedicalRecordsApp /> },
//   ];

//   return (
//     <div className="mx-2">
//       <div className="flex flex-col lg:flex-row gap-4">
//         <div className="flex-1 min-w-0">
//           <Header />
//           {/* <div className="flex flex-col lg:flex-row items-stretch gap-4 py-4">
//             <GeneralInfoCard
//               variant="double"
//               title="General Info"
//               leftItems={leftItems}
//               rightItems={rightItems}
//             />
//             <GeneralInfoCard
//               variant="single"
//               title="Contact Info"
//               leftItems={secondCardData}
//             />
//           </div> */}

//           <div className="grid grid-cols-3 gap-4 py-4">
//             <div className="col-span-3 lg:col-span-2">
//               <GeneralInfoCard
//                 variant="double"
//                 title="General Info"
//                 leftItems={leftItems}
//                 rightItems={rightItems}
//               />
//             </div>
//             <div className="col-span-3 lg:col-span-1">
//               <GeneralInfoCard
//                 variant="single"
//                 title="Contact Info"
//                 leftItems={secondCardData}
//               />
//             </div>
//           </div>
//         </div>
//         <div className="flex flex-col gap-4 w-[350px] flex-shrink-0">
//           <HospitalCard
//             hospitalName="Global Hospital"
//             membershipType="Blue Membership"
//             name="Priya Joshi"
//             id="241"
//             validTill="11/2023"
//           />
//           <ButtonList icon={iconArray} />
//         </div>
//       </div>

//       <div className="bg-white mt-4 rounded-md">
//         <Tabs tabs={tabItems} defaultActiveTab="medication" />
//       </div>
//     </div>
//   );
// };

// export default PatientDetailsA;

// Third

import React, { useMemo } from "react";
import Header from "./components/Header";
import GeneralInfoCard from "./components/GeneralInfoCard";
import {
  iconArray,
  leftItems,
  rightItems,
  secondCardData,
} from "./components/GeneralDataobj";
import HospitalCard from "./components/HospitalCard";
import ButtonList from "./components/ButtonList";
import Tabs from "./components/CustomTabs";
import { AppointmentCard, MedicationCard } from "./PatientDetails";
import { get } from "lodash";
import { useGetUserById } from "../../../server-action/api/user";
import { useParams } from "react-router-dom";
import BillingInformation from "./components/BillingInformation";
import MedicalRecordsApp from "./components/VerticalTabs";

// Define placeholder components for tab content
const MedicationTab: React.FC = () => (
  <div className="p-4">
    <h2 className="text-lg font-semibold">Medication</h2>
    <p>List of prescribed medications will be displayed here.</p>
  </div>
);

const AppointmentsTab: React.FC = () => (
  <div className="p-4">
    <h2 className="text-lg font-semibold">Appointments</h2>
    <p>Upcoming and past appointments will be listed here.</p>
  </div>
);

const PaymentDetailsTab: React.FC = () => (
  <div className="p-4">
    <h2 className="text-lg font-semibold">Payment Details</h2>
    <p>Billing information and payment history will be shown here.</p>
  </div>
);

const MedicalRecordsTab: React.FC = () => (
  <div className="p-4">
    <h2 className="text-lg font-semibold">Medical Records</h2>
    <p>Patient medical records and history will be displayed here.</p>
  </div>
);

const PatientDetailsA: React.FC = () => {
  const { id } = useParams();

  const { data: userData } = useGetUserById(id as string);

  const generalInfo = useMemo(
    () => ({
      ...get(userData, "commonInfo.personalInfo", {}),
      ...get(userData, "commonInfo.contactInfo.address", {}),
    }),
    [userData]
  );

  const tabItems = [
    {
      id: "medication",
      label: "Medication",
      content: <MedicationCard user_id={get(userData, "_id", "")} />,
    },
    {
      id: "appointments",
      label: "Appointments",
      content: <AppointmentCard user_id={get(userData, "_id", "")} />,
    },
    {
      id: "payment",
      label: "Payment Details",
      content: (
        <BillingInformation
          user_id={get(userData, "_id", "")}
          generalInfo={generalInfo}
        />
      ),
    },
    { id: "records", label: "Medical Records", content: <MedicalRecordsApp /> },
  ];

  return (
    <div className="mx-2">
      <div className="grid lg:grid-cols-[1fr_350px] gap-4">
        <div className="flex flex-col min-w-0">
          <Header />

          <div className="grid grid-cols-3 gap-4 py-4 h-full">
            <div className="col-span-3 lg:col-span-2">
              <GeneralInfoCard
                variant="double"
                title="General Info"
                leftItems={leftItems}
                rightItems={rightItems}
              />
            </div>
            <div className="col-span-3 lg:col-span-1">
              <GeneralInfoCard
                variant="single"
                title="Contact Info"
                leftItems={secondCardData}
              />
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-4 h-full">
          <HospitalCard
            hospitalName="Global Hospital"
            membershipType="Blue Membership"
            name="Priya Joshi"
            id="241"
            validTill="11/2023"
          />
          <ButtonList icon={iconArray} />
        </div>
      </div>

      <div className="bg-white mt-4 rounded-md">
        <Tabs tabs={tabItems} defaultActiveTab="medication" />
      </div>
    </div>
  );
};

export default PatientDetailsA;
