import{ad as o,a2 as t,af as n,ah as c,a7 as r,ag as l,aj as u}from"./index-ClX9RVH0.js";const d=[{itemName:"Paracetamol",subcategories:"Medications",description:"Medications",status:"ACTIVE"}],p=()=>{const e=o(),a={columns:[{title:"S.N",key:"sn"},{title:"Sub Categories",key:"subcategories"},{title:"Description",key:"description"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:d.map((s,i)=>({sn:i,description:s.description,subcategories:s.subcategories,status:t.jsx(c,{status:s.status}),action:t.jsx(n,{onDelete:()=>{},onEdit:()=>{},onSwitch:()=>{}})}))};return t.jsxs("div",{children:[t.jsx(r,{title:"Product Category",onSearch:()=>{},onAddClick:()=>{e(l.ADDSUBCATEGORY)},listTitle:"Sub-Category List"}),t.jsx("div",{className:"py-4",children:t.jsx(u,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})})]})};export{p as default};
