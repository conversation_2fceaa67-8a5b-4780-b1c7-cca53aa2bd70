import{a2 as e,a7 as se,a8 as C,ba as re,bu as oe,aq as de,ap as ie,a5 as j,a1 as le,aV as me,aX as f,bv as ce,aw as Z,bw as J,aU as ue,ax as be,ay as pe,bx as xe,a9 as fe,aa as ge,by as he,ab as g,ac as ve,aZ as je,ao as Ne}from"./index-ClX9RVH0.js";const we=[{label:"Male",value:"male"},{label:"Female",value:"female"}],Ie=()=>e.jsxs("div",{className:"mb-6 rounded-lg",children:[e.jsx(se,{title:"Assign Bed",hideHeader:!0,listTitle:"Assign Bed"}),e.jsxs("div",{className:"flex w-full gap-10",children:[e.jsx("div",{className:"h-auto w-[30%]",children:e.jsxs("div",{className:"flex flex-col gap-4 bg-white px-4 py-2",children:[e.jsx(C,{step:1,title:"Patient Details",isActive:!0}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(C,{step:2,title:"Bed Information",isActive:!1}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(C,{step:3,title:"Additional Details",isActive:!1})]})}),e.jsx("div",{className:"w-full h-full",children:e.jsx(Be,{})})]})]}),Be=()=>{var O,R,_,G,P,$,L,W,M;const{id:B}=re(),o=!!B,{data:h}=oe(B??""),A=de(),v=ie(),[K,V]=j.useState([]),[Q,I]=j.useState([]),t=le({initialValues:{patientname:"",gender:"",admissiondate:"",ward:"",department:"",floor:"",roomnumber:"",bednumber:"",currentbedstatus:"ASSIGNED",admissionreason:"",attendingdoctor:"",currentdoctor:""},validationSchema:me({patientname:f().required("Patient name is required"),gender:f().required("Gender is required"),admissiondate:ce().required("Admission date is required"),ward:f().required("Ward is required"),department:f().required("Department is required"),floor:f().required("Floor is required"),roomnumber:f().required("Room number is required"),bednumber:f().required("Bed number is required"),attendingdoctor:f().required("Attending doctor is required")}),onSubmit:async a=>{var s,n,l;try{const d={patient:o?(l=(n=(s=h.patient)==null?void 0:s.commonInfo)==null?void 0:n.personalInfo)==null?void 0:l._id:a.patientname,admissionDate:a.admissiondate,transferInfo:[{categoryName:a.ward,room:a.roomnumber,bedNumber:Number(a.bednumber),transferedBy:a.currentdoctor,attendingDoctor:a.attendingdoctor,note:a.admissionreason,status:a.currentbedstatus||"ASSIGNED"}]};o?await v.mutateAsync({_id:B??"",entityData:d}):await A.mutateAsync(d)}catch{}},enableReinitialize:!0}),{data:N}=Z({role:J.PATIENT},{refetchOnMount:!0,refetchOnWindowFocus:!0}),{data:F}=ue(),Y=(R=(O=F==null?void 0:F.data)==null?void 0:O.departments)==null?void 0:R.map(a=>({value:a._id,label:a.name})),ee=(G=(_=N==null?void 0:N.data)==null?void 0:_.users)==null?void 0:G.map(a=>{var s,n,l,d;return{value:a._id,label:(n=(s=a.commonInfo)==null?void 0:s.personalInfo)==null?void 0:n.fullName,phone:(d=(l=a.commonInfo)==null?void 0:l.personalInfo)==null?void 0:d.phoneNumber}}),{data:y}=be({department:t.values.department}),ae=($=(P=y==null?void 0:y.data)==null?void 0:P.wardCategory)==null?void 0:$.map(a=>({value:a._id,label:a.categoryName})),{data:S}=pe({categoryName:t.values.ward}),m=(L=S==null?void 0:S.data)==null?void 0:L.rooms,te=m?[...new Set(m.map(a=>a.floor))].map(a=>({value:a,label:`Floor ${a}`})):[];j.useEffect(()=>{var a,s,n,l,d,w,x,T,k,H,U,z,X;if(o&&h){const p=((a=h.transferInfo)==null?void 0:a[0])||{};t.setValues({patientname:((l=(n=(s=h.patient)==null?void 0:s.commonInfo)==null?void 0:n.personalInfo)==null?void 0:l.fullName)||"",gender:((x=(w=(d=h.patient)==null?void 0:d.commonInfo)==null?void 0:w.personalInfo)==null?void 0:x.gender)||"",admissiondate:h.admissionDate||"",ward:((T=p.categoryName)==null?void 0:T._id)||"",department:((k=p.categoryName)==null?void 0:k.department)||"",floor:((U=(H=p.room)==null?void 0:H.floor)==null?void 0:U.toString())||"",roomnumber:((z=p.room)==null?void 0:z._id)||"",bednumber:((X=p.bedNumber)==null?void 0:X.toString())||"",currentbedstatus:p.status||"ASSIGNED",admissionreason:p.note||"",attendingdoctor:p.attendingDoctor||"",currentdoctor:p.transferedBy||""})}},[h,o]),j.useEffect(()=>{if(o&&t.values.roomnumber&&m){const a=m.find(s=>s._id===t.values.roomnumber);if(a&&a.beds){const s=a.beds.filter(n=>n.status==="AVAILABLE"||n.bedNo.toString()===t.values.bednumber).map(n=>({value:n.bedNo.toString(),label:`Bed ${n.bedNo}`}));I(s)}}else if(!o&&t.values.roomnumber&&m){const a=m.find(s=>s._id===t.values.roomnumber);if(a&&a.beds){const s=a.beds.filter(n=>n.status==="AVAILABLE").map(n=>({value:n.bedNo.toString(),label:`Bed ${n.bedNo}`}));I(s)}else I([])}},[t.values.roomnumber,m,o,t.values.bednumber]),j.useEffect(()=>{t.values.department&&!o&&(t.setFieldValue("ward",""),t.setFieldValue("floor",""),t.setFieldValue("roomnumber",""))},[t.values.department,o]),j.useEffect(()=>{t.values.ward&&!o&&(t.setFieldValue("floor",""),t.setFieldValue("roomnumber",""))},[t.values.ward,o]),j.useEffect(()=>{if(t.values.floor&&m){const a=m.filter(s=>s.floor===parseInt(t.values.floor)).map(s=>({value:s._id,label:`Room ${s.roomNo}`}));V(a),o||(t.setFieldValue("roomnumber",""),t.setFieldValue("bednumber",""))}else V([])},[t.values.floor,m,o]);const{data:q}=Z({role:J.DOCTOR},{enabled:!0,refetchOnMount:!0,refetchOnWindowFocus:!0}),D=(M=(W=q==null?void 0:q.data)==null?void 0:W.users)==null?void 0:M.map(a=>{var s,n;return{value:a==null?void 0:a._id,label:(n=(s=a==null?void 0:a.commonInfo)==null?void 0:s.personalInfo)==null?void 0:n.fullName}}),{handleSubmit:E,values:c,errors:r,touched:i,handleChange:u,handleBlur:b,getFieldProps:ne}=t;return e.jsxs(e.Fragment,{children:[(A.isPending||(v==null?void 0:v.isPending))&&e.jsx(xe,{isLoading:A.isPending||(v==null?void 0:v.isPending)}),e.jsx(fe,{value:t,children:e.jsx(ge,{onSubmit:E,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5 rounded-lg",children:[e.jsxs("div",{children:[e.jsx(he,{label:"Name",field:"patientname",getFieldProps:ne,options:ee??[],isEditMode:o,value:t.values.patientname,disableInput:!!o,onValueChange:(a,s)=>{var l,d,w;t.setFieldValue("patientname",s);const n=(d=(l=N==null?void 0:N.data)==null?void 0:l.users)==null?void 0:d.find(x=>x._id===s);if(n){const x=(w=n==null?void 0:n.commonInfo)==null?void 0:w.personalInfo;t.setFieldValue("gender",x==null?void 0:x.gender),n.patientStatus&&t.setFieldValue("patientstatus",n.patientStatus),n.department&&t.setFieldValue("department",n.department)}}}),r.patientname&&i.patientname&&e.jsx("p",{className:"text-red text-sm",children:r.patientname})]}),e.jsxs("div",{children:[e.jsx(g,{label:"Gender",options:we,name:"gender",onChange:u,onBlur:b,value:c.gender,disabled:o}),i.gender&&r.gender&&e.jsx("div",{className:"text-red text-xs",children:r.gender})]}),e.jsxs("div",{children:[e.jsx(ve,{type:"date",label:"Admission Date",placeholder:"Enter Date",name:"admissiondate",onChange:u,onBlur:b,value:c.admissiondate}),i.admissiondate&&r.admissiondate&&e.jsx("div",{className:"text-red text-xs",children:r.admissiondate})]})]}),e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5 rounded-lg",children:[e.jsxs("div",{children:[e.jsx(g,{label:"Department",options:Y,name:"department",onChange:u,onBlur:b,value:c.department}),i.department&&r.department&&e.jsx("div",{className:"text-red text-xs",children:r.department})]}),e.jsxs("div",{children:[e.jsx(g,{label:"Ward",options:ae,name:"ward",onChange:u,onBlur:b,value:c.ward}),i.ward&&r.ward&&e.jsx("div",{className:"text-red text-xs",children:r.ward})]}),e.jsxs("div",{children:[e.jsx(g,{label:"Floor",options:te,name:"floor",onChange:u,onBlur:b,value:c.floor}),i.floor&&r.floor&&e.jsx("div",{className:"text-red text-xs",children:r.floor})]}),e.jsxs("div",{children:[e.jsx(g,{label:"Room Number",options:K,name:"roomnumber",onChange:u,onBlur:b,value:c.roomnumber}),i.roomnumber&&r.roomnumber&&e.jsx("div",{className:"text-red text-xs",children:r.roomnumber})]}),e.jsxs("div",{children:[e.jsx(g,{label:"Bed Number",options:Q,name:"bednumber",onChange:u,onBlur:b,value:c.bednumber}),i.bednumber&&r.bednumber&&e.jsx("div",{className:"text-red text-xs",children:r.bednumber})]})]}),e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5 rounded-lg",children:[e.jsxs("div",{className:"col-span-3",children:[e.jsx(je,{label:"Reason for Admission",placeholder:"Enter Reason",name:"admissionreason",onChange:u,onBlur:b,value:c.admissionreason}),i.admissionreason&&r.admissionreason&&e.jsx("div",{className:"text-red text-xs",children:r.admissionreason})]}),e.jsxs("div",{className:"",children:[e.jsx(g,{label:"Current Doctor",options:D,name:"currentdoctor",onChange:u,onBlur:b,value:c.currentdoctor}),i.currentdoctor&&r.currentdoctor&&e.jsx("div",{className:"text-red text-xs",children:r.currentdoctor})]}),e.jsxs("div",{className:"",children:[e.jsx(g,{label:"Attending Doctor",options:D,name:"attendingdoctor",onChange:u,onBlur:b,value:c.attendingdoctor}),i.attendingdoctor&&r.attendingdoctor&&e.jsx("div",{className:"text-red text-xs",children:r.attendingdoctor})]})]}),e.jsx(Ne,{onCancel:()=>t.resetForm(),onSubmit:E})]})})})]})};export{Be as BasicInformation,Ie as default};
