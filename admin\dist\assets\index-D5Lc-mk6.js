import{_ as P,V as c,c as y,e as A,i as D,a as L,A as C,j as h,a0 as R,B as b,f as v,d as T,C as I,Y as S,D as E,n as G,E as F,h as O,g as B,s as N,I as k,q as W,o as q,p as U,k as w,r as M,l as V,L as H,N as _,X as j,O as z,w as J,P as K,v as Q,u as X,x as Y,R as Z,J as $,G as ee,H as oe,z as ae,y as re,F as te,Z as se,Q as me,S as fe,K as ie,M as pe,m as de,t as ne,$ as le,b as xe,T as ge,U as ue,W as Pe}from"./index-ClX9RVH0.js";import{C as ye,F as Ae}from"./ForgotPage-8dWxuDBw.js";import{VerificationPage as Le}from"./VerificationPage-DzZto6UQ.js";import{default as he}from"./EmergencyRoomPage-GHtbdfG_.js";import{default as be}from"./AddPatient-CrkVS14E.js";import{default as Te}from"./Add_Events-CkrAjKjy.js";import{DayCareUnit as Se}from"./DayCareUnit-Db4Zjp4s.js";import{GeneralWardPage as Ge}from"./GeneralWardPage-o_KMi1T6.js";import{GyneObservationWard as Oe}from"./GyneObservation-DnKRVfTY.js";import{HighDependencyUnit as Ne}from"./HighDependency-OD4YOjIa.js";import{ICU as We}from"./ICU-B3-x9n_y.js";import{NICU as Ue}from"./NICU-CBpkr5t_.js";import{PostOperativeWard as Me}from"./PostOperativeWard-BA1vDIfj.js";import{DischargeGeneralWard as He}from"./DischargeGeneralWard-C5rI_0vv.js";import{SurgeryGeneralWard as je}from"./SurgeryGeneralWard-DKZlae1w.js";import{TransferGeneralWard as Je}from"./TransferGeneralWard-DQuKPAvi.js";import{default as Qe}from"./CategoryList-CHmEGhA6.js";import{default as Ye}from"./AddCategory-DI-bCQrh.js";import{default as $e}from"./InventoryPage-D7ylziL_.js";import{default as oo}from"./SubCategory-BWrcZPEP.js";import{default as ro}from"./AddSubCategories-IMHKN4HJ.js";import{default as so}from"./ProductList-DH9goJED.js";import{AppointmentPage as fo}from"./index-Bto2ygRx.js";import{AddAppointment as po}from"./AddAppointmentPage-DGtYqV0Z.js";import{CardiologyDepartmentPage as lo}from"./CardiologyPage-Dok4FuN3.js";import{AddCardiology as go}from"./AddCardiologyPage-DLLoc9Hx.js";import{DentalCareDepartmentPage as Po}from"./DentalCarePage-DAuvBd-H.js";import{AddDentalCarePage as yo}from"./AddDentalCarePage-DyIvTm2J.js";import{DermatologyDepartmentPage as Do}from"./DermatologyPage-D_YGfd9J.js";import{AddDermatology as Co}from"./AddDermotologyPage-CGl0JwpK.js";import{ENTDepartmentPage as Ro}from"./EntDepartmentPage-DQaFG1dH.js";import{AddENT as vo}from"./AddENTPage-BNwx9o__.js";import{G as Io}from"./GeneralCheckupPage-_HRWelc0.js";import{AddGeneralPreCheckupForm as Eo}from"./AddGeneralPreCheckupForm-CdrXvRJq.js";import{GeneralPhysicianPage as Fo}from"./GeneralPhysicianPage-Cp4nL3HN.js";import{AddPhysicsian as Bo}from"./AddGeneralPhysicsPage-Cw5kOmrr.js";import{GynecologyDepartmentPage as ko}from"./GynecologyPage-CHG0vzAY.js";import{AddGynecologyPage as qo}from"./AddGynecologyPage-CHuvYwlQ.js";import{NeurologyDepartmentPage as wo}from"./NeurologyPage-_5iQnKMs.js";import{AddNeuroLogy as Vo}from"./AddNeuroLogyPage-DUGSkLaq.js";import{OrthomologyDepartmentPage as _o}from"./OrthomologyPage-CTugP4OO.js";import{OrthopedicsDepartmentPage as zo}from"./OrthopedicsPage-IRZUjgkG.js";import{AddOrthopedics as Ko}from"./AddOrthopedicsPage-DFPQwwaP.js";import{PediatricDepartmentPage as Xo}from"./PediatricsPage-0XGJ0lp8.js";import{default as Zo}from"./PatientViewsDetails-BhBLXP1F.js";import{AmbulanceList as ea}from"./AmbulanceList-6mNumZJq.js";import{AmbulanceType as aa}from"./AmbulanceType-CgDCmj7I.js";import{AddAmbulanceType as ta}from"./AddAmbulanceType-BbSqyQe4.js";import{AmbulanceInquiry as ma}from"./AmbulanceInquiry-CHn9Oer7.js";import{default as ia}from"./AddAmbulanceInquiryForm-BTT22pi1.js";import{default as da}from"./EditAmbulanceInquiryForm-XSasy5X3.js";import{default as la}from"./PriceConfig-B036NeU5.js";import{AttendanceList as ga}from"./AttendanceList-DzcKNKcR.js";import{AddAttendance as Pa}from"./AddAttendance-C_ut6_r5.js";import{ViewAttendance as ya}from"./ViewAttendance-9oJDzkAZ.js";import{default as Da}from"./BedAllocation-BDJuQy0r.js";import{default as Ca}from"./AssignBed-DJp7c0pw.js";import{default as Ra}from"./EditBedAllocation-Ck8nJ92G.js";import{default as va}from"./RoomDetails-D0UXkj0R.js";import{default as Ia}from"./AddRoom-DfSlIOIO.js";import{default as Ea}from"./BedCategory-Baq5ph9j.js";import{default as Fa}from"./BedList-CbTdTQoB.js";import{default as Ba}from"./CreateInvoice-DX7kltdg.js";import{default as ka}from"./InvoicePage-CUv-PGLR.js";import{default as qa}from"./PaymentPage-CeSZ4pUG.js";import{default as wa}from"./GeneralOT-D48F2n65.js";import{default as Va}from"./POF-DqfUwTMT.js";import{default as _a}from"./PatientLayout-CHlP-N5X.js";import{default as za}from"./Otconfiguration-Ct822zqz.js";import{default as Ka}from"./EditPage-O2uDKjLc.js";import{default as Xa}from"./PatientList-SeoJYJJc.js";import{default as Za}from"./DoctorList-CrN-veYi.js";import{AddDoctor as er}from"./AddDoctor-BjFG274G.js";import{default as ar}from"./DoctorReviews-DgSDSHJB.js";import{DoctorAvailability as tr}from"./index-BBzxooqu.js";import{default as mr}from"./NurseList-cZRH5l6B.js";import{default as ir}from"./NurseDetailsPage-roGChD6D.js";import{PharmarcistsListPage as dr}from"./Pharamacists-list.page-BDH4XSpD.js";import{LabTechnicianListPage as lr}from"./LabTechnicianList.page-DYc28UTV.js";import{MedicineRequestPage as gr}from"./MedicineRequest.page-DF1HhWnP.js";import{MedicineRequestDetailsPage as Pr}from"./MedicineRequestDetails.page-D_J-xEJk.js";import{PharmacyInventoryPage as yr}from"./PharmacyInventory.page-DwKCDc6A.js";import{PharmacyProductListPage as Dr}from"./PharmacyProductList.page-DmIeQno_.js";import{PharmacyExpense as Cr}from"./expense.page-BxHpCQfO.js";import{PharmacyFinanceReportsPage as Rr}from"./reports.page-Be337VZC.js";import{SalesPage as vr}from"./sales.page-DGYF3N48.js";import{RadiologyInventoryProductDetails as Ir,RadiologyInvoice as Sr,RadiologyProductList as Er,RadiologyReport as Gr}from"./index-CW277MiK.js";import{default as Or}from"./DepartmentConfigPage-DVQ70nS_.js";import{DailyShiftPage as Nr}from"./page-D38BfDuu.js";import{default as Wr}from"./ShiftAssign-DlyovY49.js";import{default as Ur}from"./ShiftList-Bo_fyBff.js";import{default as Mr}from"./StaffList-BJuRVUYt.js";import{default as Hr}from"./StaffDetails-CgKuKyeJ.js";import{AddStaff as jr}from"./AddStaff-BSdKn2TV.js";import"./TransferPatient-qcC_AtDM.js";import"./Svg-BMTGOzwv.js";import"./DepartmentHeader-Aj6XBXn4.js";import"./operation.api-BRnS95Te.js";import"./ProductMultiSelect-DbxM3Xg6.js";import"./AddProductListForm-OS2aOkDS.js";import"./ambulanceApi-C42c0mRe.js";import"./attendance.api-CVfqyNC8.js";import"./OTAssignment-BHmGwNc9.js";import"./SurgerySubDepatment-D7s6By8Z.js";import"./analytics.api-DOA8_WvB.js";import"./ExtraDocuments-C1RiR-ux.js";import"./AddProduct-DEXONU3r.js";import"./shiftList-piIuxMpn.js";export{P as AccountTokenList,c as AddAllDepartment,ia as AddAmbulanceInquiryForm,ta as AddAmbulanceType,po as AddAppointment,Pa as AddAttendance,go as AddCardiology,Ye as AddCategory,y as AddCertificate,yo as AddDentalCarePage,Co as AddDermatology,er as AddDoctor,A as AddDonor,vo as AddENT,be as AddEmergencyPatient,D as AddExpense,L as AddGeneralCheckupForm,Eo as AddGeneralPreCheckupForm,qo as AddGynecologyPage,Vo as AddNeuroLogy,Ko as AddOrthopedics,Bo as AddPhysicsian,Ia as AddRooms,jr as AddStaff,ro as AddSubCategory,Te as Add_Events,C as AdminDashboard,h as AdvancePaymentForm,ma as AmbulanceInquiry,ea as AmbulanceList,aa as AmbulanceType,fo as AppointmentPage,R as AppointmentTokenList,Ca as AssignBedAllocation,ga as AttendanceList,b as Bank,Da as BedAllocationPage,Ea as BedCategory,Fa as BedList,v as BloodBank,T as BloodDonorList,lo as CardiologyDepartmentPage,Qe as CategoryList,I as CertificatePage,ye as ChangePasswordPage,S as CommissionConfig,Ba as CreateInvoicePage,Nr as DailyShiftPage,Se as DayCareUnit,Po as DentalCareDepartmentPage,Or as DepartmentConfig,Do as DermatologyDepartmentPage,He as DischargeGeneralWard,tr as DoctorAvailability,E as DoctorDashboard,Za as DoctorList,ar as DoctorReviews,Ro as ENTDepartmentPage,da as EditAmbulanceInquiryForm,Ra as EditBedAllocation,Ka as EditPage,G as EditReport,he as EmergencyRoomPage,F as Events,O as ExpenseCategory,B as ExpensesList,Ae as ForgotPage,Io as GeneralCheckupPage,wa as GeneralOT,Fo as GeneralPhysicianPage,Ge as GeneralWardPage,Oe as GyneObservationWard,ko as GynecologyDepartmentPage,Ne as HighDependencyUnit,We as ICU,$e as InventoryPage,N as InvoiceList,ka as InvoicePage,k as IpdPatientForm,W as LabDepartment,q as LabInventory,U as LabProductList,w as LabPurchaseList,M as LabSubDepartment,lr as LabTechnicianListPage,V as LabTestResult,H as LoginPage,Pr as MedicineRequestDetailsPage,gr as MedicineRequestPage,Ue as NICU,wo as NeurologyDepartmentPage,_ as NotFoundPage,j as NotificationSetup,ir as NurseDetailsPage,mr as NurseList,z as OrganDonorList,_o as OrthomologyDepartmentPage,zo as OrthopedicsDepartmentPage,za as Otconfiguration,J as OverAllStaffDetails,Va as POF,K as PatientDetails,_a as PatientLayout,Xa as PatientList,Zo as PatientViewsDetails,qa as PaymentPage,Q as PayrollConfig,X as PayrollList,Xo as PediatricDepartmentPage,Cr as PharmacyExpense,Rr as PharmacyFinanceReportsPage,yr as PharmacyInventoryPage,Dr as PharmacyProductListPage,dr as PharmarcistsListPage,Y as PosPage,Me as PostOperativeWard,la as PriceConfig,so as ProductList,Z as RadiologyDashboard,$ as RadiologyDueList,ee as RadiologyInventory,oe as RadiologyInventoryDetails,Ir as RadiologyInventoryProductDetails,Sr as RadiologyInvoice,ae as RadiologyInvoiceDetails,Er as RadiologyProductList,re as RadiologyPurchase,Gr as RadiologyReport,te as RadiologyTestRequest,se as Report,me as RoleManagement,va as RoomDetailsPage,vr as SalesPage,fe as SampleCollection,ie as ServiceType,Wr as ShiftAssign,Ur as ShiftList,Hr as StaffDetails,Mr as StaffList,oo as SubCategory,je as SurgeryGeneralWard,pe as TestListConfig,de as TestRequestList,ne as TestTypeConfig,le as TokenConfig,xe as TransactionDetails,Je as TransferGeneralWard,ge as TreatmentGeneralWard,ue as UserManagement,Le as VerificationPage,ya as ViewAttendance,Pe as WardConfigPage};
