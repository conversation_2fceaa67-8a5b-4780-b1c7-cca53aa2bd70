import{a5 as t,aM as x,aP as k,av as q,aV as v,bm as r,aX as D,a2 as o,aQ as C,aj as M}from"./index-ClX9RVH0.js";import{u as b,f as w,b as j,a as E}from"./ambulanceApi-C42c0mRe.js";const Y=()=>{var g;const[I,O]=t.useState("create"),[Q,$]=t.useState(null),[m,G]=t.useState(""),[a,l]=t.useState({page:1,limit:10}),[p,P]=t.useState({search:"",selectedAvailabilityStatus:""}),A=m,N=x({search:p.search,...A?{}:{page:a.page,limit:a.limit}}),{mutate:K,isPending:L}=b(),{mutate:V,isPending:F}=w(),[H,S]=t.useState(!1);k(()=>{S(!1)});const[U,X]=t.useState(""),{data:T}=j({page:a.page,limit:a.limit}),{data:n,isLoading:f}=E(N),[z,B]=t.useState(!1),c=q.get(T,"data.pagination",{});console.log("pagination",c);const R=({page:e,limit:i})=>{l(s=>({...s,page:e??1,limit:i??s.limit}))};v().shape({ambulanceType:D().required("Ambulance Type is required"),pricePerKM:r().required("Price per km is required"),pricePerDay:r().required("Price per day is required"),pricePerNightCharge:r().required("Price per night charge is required"),minCharge:r().required("Minimum charge is required ")});const d={columns:[{title:"Ambulance Type",key:"ambulanceType"},{title:"Vehicle No",key:"vehicleNo"},{title:"Per KM (NRs)",key:"kilometerRate"},{title:"Per Day (NRs)",key:"PerDayRate"},{title:"Night Charge (NRs)",key:"NightRate"}],rows:((g=n==null?void 0:n.data.ambulances)==null?void 0:g.map((e,i)=>{var s,u,h,y;return{key:i,ambulanceType:((s=e==null?void 0:e.ambulanceType)==null?void 0:s.ambulanceType)||"N/A",vehicleNo:(e==null?void 0:e.vehicleNo)||"N/A",kilometerRate:((u=e==null?void 0:e.financialDetails)==null?void 0:u.kmRate)||"N/A",PerDayRate:((h=e==null?void 0:e.financialDetails)==null?void 0:h.perDayRate)||"N/A",NightRate:((y=e==null?void 0:e.financialDetails)==null?void 0:y.nightRate)||"N/A"}}))??[]};return o.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[o.jsx(C,{headerTitle:"Ambulance Cost Config",onSearch:!0,toSearch:"Search by vehicle no",onSearchFunc:e=>{P({...p,search:e}),l(i=>({...i,page:1}))},button:!1}),o.jsx("div",{className:"bg-white rounded-md",children:o.jsx(M,{columns:d.columns,rows:d.rows,loading:f,color:"bg-white ",textcolor:"text-gray-400",pagination:{totalPage:c.pages||1,currentPage:c.page||1,limit:a.limit||2,onClick:R}})})]})};export{Y as default};
