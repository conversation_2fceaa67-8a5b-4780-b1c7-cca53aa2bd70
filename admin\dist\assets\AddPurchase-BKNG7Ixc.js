import{a2 as e,a7 as d,a8 as r,a1 as c,al as u,a9 as m,aa as p,ab as a,ac as l,b3 as x,ao as b}from"./index-ClX9RVH0.js";import{S as h}from"./SelectInput-CIK-FOD0.js";const g=[{label:"12345",value:"12345"}],n=[{label:"General",value:"General"},{label:"Cardiology",value:"Cardiology"},{label:"Neurology",value:"Neurology"},{label:"Orthopedics",value:"Orthopedics"},{label:"Gynecology",value:"Gynecology"}],t=[{label:"General",value:"General"},{label:"Cardiology",value:"Cardiology"},{label:"Neurology",value:"Neurology"},{label:"Orthopedics",value:"Orthopedics"},{label:"Gynecology",value:"Gynecology"}],f=()=>e.jsxs("div",{children:[e.jsx(d,{title:"Add Purchase",hideHeader:!0,listTitle:"Add Purchase Return"}),e.jsxs("div",{className:"flex w-full gap-10",children:[e.jsxs("div",{className:"h-auto",children:[e.jsxs("div",{className:"flex flex-col gap-4 bg-white mt-5 px-4 py-2",children:[e.jsx(r,{step:1,title:"Basic Information",isActive:!0}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(r,{step:2,title:"Product Details",isActive:!1})]}),e.jsx("div",{className:"flex flex-col gap-3 col-span-2"})]}),e.jsx("div",{className:"w-full h-full",children:e.jsx(y,{})})]})]}),y=()=>{const s=c({initialValues:{name:"",contactNumber:"",paitentId:"",department:"",date:"",doctor:""},enableReinitialize:!0,onSubmit:i=>{u.success("Form submitted successfully!"),history.back(),console.log(i)}}),{handleSubmit:o}=s;return e.jsx(e.Fragment,{children:e.jsx(m,{value:s,children:e.jsx(p,{onSubmit:o,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsx(h,{options:g,placeholder:"454912",name:"returnId",label:"Return Id"}),e.jsx(a,{required:!0,label:"Category",options:n,name:"department"}),e.jsx(l,{type:"date",label:"Return Date",placeholder:"Select Date",name:"selectDate"})]}),e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsx(a,{label:"Department",options:t,name:"department"}),e.jsx(a,{label:"Category",options:t,name:"category"}),e.jsx(a,{label:"Product",options:t,name:"productname"}),e.jsx(l,{type:"number",label:"Quantity",placeholder:"Enter",name:"quantity"}),e.jsx(a,{label:"Unit",options:n,name:"unit"}),e.jsx(l,{type:"number",label:"Refund Amount",placeholder:"Enter",name:"refundAmount"}),e.jsx(l,{type:"text",label:"Return Reason",placeholder:"Return Reason",name:"returnReason"}),e.jsx("div",{className:"col-span-3 flex justify-center",children:e.jsxs("button",{className:"bg-primary text-white px-6 py-2 rounded-md flex items-center gap-2",children:[e.jsx(x,{icon:"icons8:plus",className:"text-2xl text-white"}),"Add Product"]})})]}),e.jsx(b,{onCancel:()=>{history.back()},onSubmit:o})]})})})})};export{y as BasicInformation,f as default};
