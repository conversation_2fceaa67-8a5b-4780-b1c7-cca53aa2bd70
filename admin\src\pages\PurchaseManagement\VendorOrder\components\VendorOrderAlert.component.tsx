import { useState } from 'react';
import { Icon } from '@iconify/react/dist/iconify.js';

export const PurchaseOrderAlerts = () => {
  // const { data: userData } = useAuth();
  const [alertFilters, setAlertFilters] = useState({
    page: 1,
    limit: 20,
    priority: '',
    category: 'purchase_order',
  });

  // For future use when integrating with real notification API
  // const { data: notificationData } = useGetAllNotification({
  //   user: userData?.user?.id,
  //   ...alertFilters,
  // });
  // const alerts = get(notificationData, 'data.notifications', []);

  // Mock data for demonstration - replace with real API data
  const mockAlerts = [
    {
      id: 'PO-2024-001',
      title: 'Urgent: Medical Supplies Order',
      message:
        'New purchase order for medical supplies requires immediate attention',
      hospital: 'City General Hospital',
      priority: 'high',
      category: 'Medical Supplies',
      amount: 15000,
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      isRead: false,
      orderItems: ['Surgical Masks', 'Gloves', 'Syringes'],
    },
    {
      id: 'PO-2024-002',
      title: 'Equipment Purchase Request',
      message: 'Laboratory equipment order pending vendor confirmation',
      hospital: 'Metro Medical Center',
      priority: 'medium',
      category: 'Laboratory Equipment',
      amount: 45000,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      isRead: false,
      orderItems: ['Centrifuge', 'Microscope', 'Test Tubes'],
    },
    {
      id: 'PO-2024-003',
      title: 'Pharmacy Stock Replenishment',
      message: 'Regular pharmacy inventory restock order',
      hospital: 'Regional Health Center',
      priority: 'low',
      category: 'Pharmaceuticals',
      amount: 8500,
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      isRead: true,
      orderItems: ['Antibiotics', 'Pain Relievers', 'Vitamins'],
    },
    {
      id: 'PO-2024-004',
      title: 'Emergency Equipment Order',
      message: 'Critical equipment needed for emergency department',
      hospital: 'Emergency Medical Center',
      priority: 'high',
      category: 'Emergency Equipment',
      amount: 25000,
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      isRead: false,
      orderItems: ['Defibrillator', 'Ventilator Parts', 'Monitors'],
    },
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-blue text-white border-blue';
      case 'medium':
        return 'bg-[#83BDF7] text-whites-100 border-[#83BDF7]';
      case 'low':
        return 'bg-[#7CD7F7] text-grey border-[#7CD7F7]';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return (
          <Icon icon="material-symbols:priority-high" className="text-blue" />
        );
      case 'medium':
        return (
          <Icon icon="material-symbols:remove" className="text-[#83BDF7]" />
        );
      case 'low':
        return (
          <Icon
            icon="material-symbols:keyboard-arrow-down"
            className="text-[#7CD7F7]"
          />
        );
      default:
        return <Icon icon="material-symbols:info" className="text-gray-600" />;
    }
  };

  const getTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - timestamp.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 60) {
      return `${diffInMinutes} minutes ago`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  };

  const filteredAlerts = mockAlerts.filter((alert) => {
    if (alertFilters.priority && alert.priority !== alertFilters.priority) {
      return false;
    }
    return true;
  });

  return (
    <div className="space-y-4">
      {/* Alert Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">High Priority</p>
              <p className="text-xl font-bold text-blue">
                {
                  mockAlerts.filter((a) => a.priority === 'high' && !a.isRead)
                    .length
                }
              </p>
            </div>
            <Icon
              icon="material-symbols:priority-high"
              className="text-blue text-xl"
            />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#83BDF7]">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Medium Priority
              </p>
              <p className="text-xl font-bold text-[#83BDF7]">
                {
                  mockAlerts.filter((a) => a.priority === 'medium' && !a.isRead)
                    .length
                }
              </p>
            </div>
            <Icon
              icon="material-symbols:remove"
              className="text-[#83BDF7] text-2xl"
            />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#7CD7F7]">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Low Priority</p>
              <p className="text-xl font-bold text-[#7CD7F7]">
                {
                  mockAlerts.filter((a) => a.priority === 'low' && !a.isRead)
                    .length
                }
              </p>
            </div>
            <Icon
              icon="material-symbols:keyboard-arrow-down"
              className="text-[#7CD7F7] text-2xl"
            />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border-l-4 border-[#A5E49D]">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Value</p>
              <p className="text-xl font-bold text-[#A5E49D]">
                Rs.
                {mockAlerts
                  .reduce((sum, alert) => sum + alert.amount, 0)
                  .toLocaleString()}
              </p>
            </div>
            <Icon
              icon="material-symbols:attach-money"
              className="text-[#A5E49D] text-2xl"
            />
          </div>
        </div>
      </div>

      {/* Filter Section */}
      <div className="bg-white py-1 px-5  rounded shadow-sm">
        <div className="flex flex-col md:flex-row gap-4  w-full items-center justify-between">
          <h3 className="text-sm font-semibold text-gray-800">
            Purchase Order Alerts
          </h3>

          <div className="flex gap-3 place-items-center">
            <select
              className="px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
              value={alertFilters.priority}
              onChange={(e) =>
                setAlertFilters({ ...alertFilters, priority: e.target.value })
              }
            >
              <option value="">All Priorities</option>
              <option value="high">High Priority</option>
              <option value="medium">Medium Priority</option>
              <option value="low">Low Priority</option>
            </select>

            <button className="px-4 py-[4.5px] bg-blue text-white rounded hover:bg-blue-700 transition-colors">
              <Icon icon="material-symbols:refresh" className="inline mr-2" />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Alerts List */}
      <div className="grid grid-cols-1 gap-3">
        {filteredAlerts.map((alert) => (
          <div
            key={alert.id}
            className={`bg-white rounded shadow-sm border-l-4 p-[10.5px] transition-all hover:shadow-md ${
              alert.isRead ? 'opacity-75' : ''
            } ${
              alert.priority === 'high'
                ? 'border-blue'
                : alert.priority === 'medium'
                ? 'border-[#83bdf7]'
                : 'border-[#7cd7f7]'
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  {getPriorityIcon(alert.priority)}
                  <h4
                    className={`font-semibold ${
                      alert.isRead ? 'text-gray-600' : 'text-gray-900'
                    }`}
                  >
                    {alert.title}
                  </h4>
                  <span
                    className={`px-2 py-1 rounded text-xs font-medium border ${getPriorityColor(
                      alert.priority
                    )}`}
                  >
                    {alert.priority.toUpperCase()}
                  </span>
                  {!alert.isRead && (
                    <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                  )}
                </div>

                <p className="text-gray-600 mb-3">{alert.message}</p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Hospital
                    </p>
                    <p className="text-sm text-gray-900">{alert.hospital}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Category
                    </p>
                    <p className="text-sm text-gray-900">{alert.category}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Order Value
                    </p>
                    <p className="text-sm font-semibold text-green-600">
                      Rs. {alert.amount.toLocaleString()}
                    </p>
                  </div>
                </div>

                <div className="mb-3">
                  <p className="text-sm font-medium text-gray-500 mb-1">
                    Order Items
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {alert.orderItems.map((item, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs"
                      >
                        {item}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <p className="text-xs text-gray-500">
                    {getTimeAgo(alert.timestamp)}
                  </p>

                  <div className="flex gap-2">
                    <button className="px-3 py-1 bg-blue text-white rounded text-sm hover:bg-blue-700 transition-colors">
                      View Details
                    </button>
                    <button className="px-3 py-1 bg-green text-white rounded text-sm hover:bg-green transition-colors">
                      Accept Order
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        {filteredAlerts.length === 0 && (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <Icon
              icon="material-symbols:notifications-off"
              className="text-gray-400 text-4xl mb-4"
            />
            <h3 className="text-lg font-medium text-gray-600 mb-2">
              No alerts found
            </h3>
            <p className="text-gray-500">
              There are no purchase order alerts matching your current filters.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
