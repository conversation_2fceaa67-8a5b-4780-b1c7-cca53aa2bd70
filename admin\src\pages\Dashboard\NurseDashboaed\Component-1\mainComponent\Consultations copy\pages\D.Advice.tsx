import { useFormik } from "formik";
import * as Yup from "yup";
import { Icon } from "@iconify/react";

const adviceValidationSchema = Yup.object({
  followupDate: Yup.string().required("Follow up date is required"),
  department: Yup.string().required("Department is required"),
  doctor: Yup.string().required("Doctor is required"),
  advice: Yup.string().required("Advice is required"),
});

const Advice = () => {
  const formik = useFormik({
    initialValues: {
      followupDate: "",
      department: "",
      doctor: "",
      advice: "",
    },
    validationSchema: adviceValidationSchema,
    onSubmit: (values) => {
      console.log(values);
    },
  });

  return (
    <div className='w-full p-4 mx-auto '>
      <h2 className='mb-4 font-semibold text-center'>Advice</h2>
      <form
        onSubmit={formik.handleSubmit}
        className='flex flex-col gap-4 p-4 border rounded-md shadow-sm'
      >
        <div className='flex flex-wrap gap-4'>
          <div className='flex flex-col flex-1 min-w-[150px]'>
            <label className='mb-1 text-sm'>Follow up date</label>
            <input
              type='date'
              name='followupDate'
              value={formik.values.followupDate}
              onChange={formik.handleChange}
              className='px-2 py-1 border rounded'
            />
            {formik.touched.followupDate && formik.errors.followupDate && (
              <span className='text-xs text-red-500'>
                {formik.errors.followupDate}
              </span>
            )}
          </div>

          <div className='flex flex-col flex-1 min-w-[150px]'>
            <label className='mb-1 text-sm'>Refer to</label>
            <div className='flex gap-2'>
              <select
                name='department'
                value={formik.values.department}
                onChange={formik.handleChange}
                className='flex-1 px-2 py-1 border rounded'
              >
                <option value=''>Department</option>
                <option value='ENT'>ENT</option>
                <option value='Cardiology'>Cardiology</option>
                <option value='Neurology'>Neurology</option>
              </select>

              <select
                name='doctor'
                value={formik.values.doctor}
                onChange={formik.handleChange}
                className='flex-1 px-2 py-1 border rounded'
              >
                <option value=''>Doctor</option>
                <option value='Dr. Ram Prasad'>Dr. Ram Prasad</option>
                <option value='Dr. Sarah Johnson'>Dr. Sarah Johnson</option>
                <option value='Dr. Michael Chen'>Dr. Michael Chen</option>
              </select>
            </div>
            {(formik.touched.department && formik.errors.department) ||
            (formik.touched.doctor && formik.errors.doctor) ? (
              <span className='text-xs text-red-500'>
                Department and Doctor are required
              </span>
            ) : null}
          </div>
        </div>

        <div>
          <textarea
            name='advice'
            placeholder='Enter advice'
            value={formik.values.advice}
            onChange={formik.handleChange}
            className='w-full h-24 p-2 border rounded'
          />
          {formik.touched.advice && formik.errors.advice && (
            <span className='text-xs text-red-500'>{formik.errors.advice}</span>
          )}
        </div>

        <div className='flex justify-end'>
          <button
            type='submit'
            className='flex items-center gap-2 px-4 py-1 text-white bg-[#116AEF] rounded hover:bg-blue-700'
          >
            <Icon icon='material-symbols:save' className='text-lg' /> Save
          </button>
        </div>
      </form>
    </div>
  );
};

export default Advice;
