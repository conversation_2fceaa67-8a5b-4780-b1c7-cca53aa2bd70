import{a2 as t,af as a,ad as s,aQ as o,ag as r,aj as l}from"./index-ClX9RVH0.js";const c=[{id:"DP-001",type:"Regular Diet",targetedPatient:"General Patients",menuOptions:["Porridge","Boiled"],desc:"Balanced morning meal",servingTime:["BreakFast"],price:129},{id:"DP-002",type:"Diabetic Diet",targetedPatient:"Diabetes Patients",menuOptions:["Diabetic-friendly lunch"],desc:"lunch",servingTime:["lunch"],price:80}],u=()=>{const i={columns:[{key:"id",title:"Diet Plan ID"},{key:"type",title:"Diet Type"},{key:"patient",title:"Target Patients"},{key:"option",title:"Menu Option"},{key:"description",title:"Description"},{key:"servingTime",title:"Serving Time"},{key:"price",title:"Price"},{key:"action",title:"Action"}],rows:c.map(e=>({id:e.id,type:e.type,patient:e.targetedPatient,option:e.menuOptions.join(", "),description:e.desc,servingTime:e.servingTime.join(", "),price:e.price,action:t.jsx(a,{onEdit:()=>{},onDelete:()=>{}})}))},n=s();return t.jsxs("div",{children:[t.jsx(o,{headerTitle:"Diet Plan List",onSearch:!0,onFilter:!0,subButtonText:"Add Diet Plan",subButton:!0,subButtonAction:()=>{n(r.ADDDIETPLAN)}}),t.jsx(l,{columns:i.columns,rows:i.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})};export{u as DietPlanPage};
