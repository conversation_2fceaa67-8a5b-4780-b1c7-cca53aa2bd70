import React from "react";

interface IconItem {
  icon: React.ComponentType<any>; // Type for a React component
}

interface ButtonProps {
  icon: IconItem[]; // Array of objects with an icon property
}

const ButtonList: React.FC<ButtonProps> = ({ icon }) => {
  return (
    <div className="bg-white shadow-md rounded-xl p-4 flex items-center justify-center gap-4 mb-4">
      {icon &&
        icon.map((item, index) => (
          <button key={index} className="bg-primary rounded-xl px-4 py-2">
            <item.icon className="fill-white w-7 h-7" />{" "}
            {/* Render the React Icon component */}
          </button>
        ))}
    </div>
  );
};

export default ButtonList;
