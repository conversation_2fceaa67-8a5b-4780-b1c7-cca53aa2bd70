import{a5 as x,ad as P,a1 as y,d7 as S,al as b,ag as C,a2 as e,a9 as j,aa as v,d8 as N,bx as F,d9 as D,a3 as L,a4 as E}from"./index-ClX9RVH0.js";const O=({forgetDetails:m,clearPersistedData:n})=>{const[i,l]=x.useState(!1),u=P(),p=y({initialValues:{confirmPassword:"",password:""},onSubmit:async h=>{var g,t;if(h.confirmPassword===h.password){const s=await S({token:m.token,password:h.password});(g=s==null?void 0:s.data)!=null&&g.success&&(b.success((t=s==null?void 0:s.data)==null?void 0:t.message),u(C.LOGIN),n())}else b.error("Please enter the same password")}});return e.jsx(j,{value:p,children:e.jsxs(v,{className:"w-1/3 space-y-6 bg-opacity-80 px-8 mt-4 bg-primary ",children:[e.jsx("h1",{className:"my-1 font-medium text-white text-[27px] leading-[1.2] text-center font-[Hind]",children:"Change"}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-white",children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{id:"password",...p.getFieldProps("password"),type:i?"text":"password",placeholder:"Enter new password",className:"mt-1 p-2 w-full border rounded text-black pr-10"}),e.jsx("button",{type:"button",onClick:()=>l(!i),className:"absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:[e.jsx("path",{d:"M0.666504 8.00033C0.666504 8.00033 3.33317 2.66699 7.99984 2.66699C12.6665 2.66699 15.3332 8.00033 15.3332 8.00033C15.3332 8.00033 12.6665 13.3337 7.99984 13.3337C3.33317 13.3337 0.666504 8.00033 0.666504 8.00033Z",stroke:"#939393",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.99984 10.0003C9.10441 10.0003 9.99984 9.10489 9.99984 8.00033C9.99984 6.89576 9.10441 6.00033 7.99984 6.00033C6.89527 6.00033 5.99984 6.89576 5.99984 8.00033C5.99984 9.10489 6.89527 10.0003 7.99984 10.0003Z",stroke:"#939393",strokeLinecap:"round",strokeLinejoin:"round"})]})})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-white",children:"Confirm Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{id:"confirmPassword",...p.getFieldProps("confirmPassword"),type:i?"text":"password",placeholder:"Re-enter password",className:"mt-1 p-2 w-full border rounded text-black pr-10"}),e.jsx("button",{type:"button",onClick:()=>l(!i),className:"absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",children:[e.jsx("path",{d:"M0.666504 8.00033C0.666504 8.00033 3.33317 2.66699 7.99984 2.66699C12.6665 2.66699 15.3332 8.00033 15.3332 8.00033C15.3332 8.00033 12.6665 13.3337 7.99984 13.3337C3.33317 13.3337 0.666504 8.00033 0.666504 8.00033Z",stroke:"#939393",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.99984 10.0003C9.10441 10.0003 9.99984 9.10489 9.99984 8.00033C9.99984 6.89576 9.10441 6.00033 7.99984 6.00033C6.89527 6.00033 5.99984 6.89576 5.99984 8.00033C5.99984 9.10489 6.89527 10.0003 7.99984 10.0003Z",stroke:"#939393",strokeLinecap:"round",strokeLinejoin:"round"})]})})]})]}),e.jsx("button",{type:"submit",className:"h-[38px] w-full rounded bg-[#2FC0A1] shadow-md text-white font-semibold hover:bg-[#28ae93] transition",children:"Save"})]})})},R=({setStep:m})=>{const{mutateAsync:n,isPending:i}=N(),l=y({initialValues:{email:""},onSubmit:async u=>{const p=await n(u);localStorage.setItem("forgotPasswordEmail",JSON.stringify(u.email)),p.status===200?m({token:"",step:1}):b.error("Something went wrong. Please try again later.")}});return e.jsxs(j,{value:l,children:[i&&e.jsx(F,{isLoading:i}),e.jsxs(v,{className:"w-1/3 bg-opacity-80  px-8 mt-4 bg-primary ",children:[e.jsx("h1",{className:"my-1 font-medium text-white text-[27px] leading-[1.2] text-center font-[Hind]",children:"Forgot Password?"}),e.jsx("p",{className:"text-white text-center font-[Hind]",children:"Enter your email address"}),e.jsxs("div",{className:"space-y-6 my-6",children:[e.jsx("label",{htmlFor:"email",className:"block text-white",children:"Email address"}),e.jsx("input",{id:"email",type:"email",placeholder:"<EMAIL>",...l.getFieldProps("email"),className:"mt-1 p-2 w-full border rounded text-black"})]}),e.jsx("button",{type:"submit",className:"h-[38px] w-full rounded bg-[#2FC0A1] shadow-md text-white font-semibold hover:bg-[#28ae93] transition",children:"SEND"})]})]})},A=({onSubmit:m})=>{const[n,i]=x.useState(new Array(6).fill("")),l=x.useRef([]),u=localStorage.getItem("forgotPasswordEmail"),p=JSON.parse(u||""),{mutateAsync:h}=N(),g=(d,a)=>{var c;if(isNaN(Number(d.value)))return;const o=[...n];o[a]=d.value,i(o),d.value&&a<5&&((c=l.current[a+1])==null||c.focus())},t=(d,a)=>{var o;if(d.key==="Backspace"){const c=[...n];n[a]?(c[a]="",i(c)):a>0&&(c[a-1]="",i(c),(o=l.current[a-1])==null||o.focus())}},s=async d=>{var o,c;d.preventDefault();const a=n.join("");if(a.length===6){const w=await D({otp:a});console.log((o=w==null?void 0:w.data)==null?void 0:o.data,"response from otp"),m((c=w==null?void 0:w.data)==null?void 0:c.data)}},r=d=>{var c;d.preventDefault();const o=d.clipboardData.getData("text").replace(/\D/g,"").slice(0,6);if(o.length>0){const w=new Array(6).fill("");for(let f=0;f<o.length;f++)w[f]=o[f];i(w);const k=Math.min(o.length,5);(c=l.current[k])==null||c.focus()}};return e.jsxs("form",{onSubmit:s,className:"w-1/3 bg-opacity-80 px-8 mt-4 bg-primary",children:[e.jsx("h1",{className:"my-1 font-medium text-white text-[27px] leading-[1.2] text-center font-[Hind]",children:"Enter Verification Code"}),e.jsx("p",{className:"text-white text-center font-[Hind]",children:"We've sent a 6-digit code to your email"}),e.jsxs("div",{className:"space-y-6 my-6",children:[e.jsx("label",{className:"block text-white",children:"Verification Code"}),e.jsx("div",{className:"flex gap-2 justify-center",children:n.map((d,a)=>e.jsx("input",{ref:o=>l.current[a]=o,type:"text",maxLength:1,value:d,onChange:o=>g(o.target,a),onKeyDown:o=>t(o,a),onPaste:r,className:"w-12 h-12 text-center text-xl font-semibold border rounded text-black focus:outline-none focus:ring-2 focus:ring-[#2FC0A1] focus:border-transparent",autoComplete:"off"},a))})]}),e.jsx("button",{type:"submit",disabled:n.join("").length!==6,className:"h-[38px] w-full rounded bg-[#2FC0A1] shadow-md text-white font-semibold hover:bg-[#28ae93] transition disabled:opacity-50 disabled:cursor-not-allowed",children:"VERIFY"}),e.jsx("div",{className:"text-center mt-4 pb-4",children:e.jsx("button",{type:"button",className:"text-white text-sm underline hover:no-underline font-[Hind]",onClick:async()=>{console.log(typeof p),await h({email:p})},children:"Didn't receive code? Resend"})})]})},I=()=>{const[m,n]=x.useState({token:"",step:0}),i=()=>{const t=new URLSearchParams(window.location.search);return{step:parseInt(t.get("step")??"")||0,token:t.get("token")||""}},l=(t,s)=>{const r=new URL(window.location);t>0?r.searchParams.set("step",t.toString()):r.searchParams.delete("step"),s?r.searchParams.set("token",s):r.searchParams.delete("token"),window.history.replaceState({},"",r)},u=(t,s)=>{try{if(typeof window<"u"&&window.localStorage){const r=window.localStorage.getItem(t);return r?JSON.parse(r):s}}catch{console.warn(`Could not access localStorage for key: ${t}`)}return s},p=(t,s)=>{try{typeof window<"u"&&window.localStorage&&window.localStorage.setItem(t,JSON.stringify(s))}catch{console.warn(`Could not set localStorage for key: ${t}`)}},h=()=>{n({step:0,token:""}),l(0,"");try{typeof window<"u"&&window.localStorage&&(window.localStorage.removeItem("forgotPasswordState"),localStorage.removeItem("forgotPasswordEmail"))}catch{console.warn("Could not clear localStorage")}};x.useEffect(()=>{const t=i(),s=u("forgotPasswordState",{step:0,token:""}),r={step:t.step||s.step||0,token:t.token||s.token||""};n(r),l(r.step,r.token)},[]);const g=t=>{const s=typeof t=="function"?t(m):t;n(s),l(s.step,s.token),p("forgotPasswordState",s)};return e.jsxs("div",{className:"w-screen h-screen bg-white relative overflow-hidden flex items-center justify-center ",children:[e.jsx("div",{className:"absolute right-0 top-0 w-[955px] h-full z-0",style:{backgroundImage:"url('data:image/svg+xml;utf8,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%22955%22 height=%221024%22 viewBox=%220 0 955 1024%22 fill=%22none%22><path d=%22M320.5 0H955V1024H0L320.5 0Z%22 fill=%22%2300717E%22/></svg>')",backgroundRepeat:"no-repeat",backgroundSize:"cover"}}),e.jsxs("div",{className:"relative z-10 w-full max-w-7xl flex justify-between items-center  pl-12 pr-36  py-24 shadow-2xl",children:[e.jsxs("div",{className:"text-center space-y-3 w-80  mb-48 ml-24",children:[e.jsx("img",{src:L,alt:"logo",className:"w-28 h-24 mx-auto"}),e.jsx(E,{text:"AROGAY Niketan",className:"text-[#F6014F] text-[38px] leading-[57px] font-bold font-[Roboto]"}),e.jsx("h1",{className:"text-[#0865AB] text-[24px] font-semibold font-[Roboto]",children:"Dashboard"})]}),m.step===0&&e.jsx(R,{setStep:g}),m.step===1&&e.jsx(A,{onSubmit:t=>g({token:t,step:2})}),m.step===2&&e.jsx(O,{forgetDetails:m,clearPersistedData:h})]})]})},M=Object.freeze(Object.defineProperty({__proto__:null,ForgotPage:I},Symbol.toStringTag,{value:"Module"}));export{O as C,I as F,M as a};
