import{a2 as e,a7 as i,a8 as o,a1 as r,al as n,a9 as c,aa as d,ac as t,ab as m,aZ as x,ao as p}from"./index-ClX9RVH0.js";const u=[{label:"active",value:"active"}],j=()=>e.jsxs("div",{children:[e.jsx(i,{title:"Add Sub-Category",hideHeader:!0,listTitle:"Add Sub-Category"}),e.jsxs("div",{className:"flex w-full gap-10",children:[e.jsxs("div",{className:"h-auto",children:[e.jsx("div",{className:"flex flex-col gap-4 bg-white mt-5 px-4 py-2",children:e.jsx(o,{step:1,title:"General Information",isActive:!0})}),e.jsx("div",{className:"flex flex-col gap-3 col-span-2"})]}),e.jsx("div",{className:"w-full h-full",children:e.jsx(h,{})})]})]}),h=()=>{const a=r({initialValues:{name:"",contactNumber:"",paitentId:"",department:"",date:"",doctor:""},enableReinitialize:!0,onSubmit:l=>{n.success("Form submitted successfully!"),history.back(),console.log(l)}}),{handleSubmit:s}=a;return e.jsx(e.Fragment,{children:e.jsx(c,{value:a,children:e.jsx(d,{onSubmit:s,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsx(t,{type:"text",label:"Sub-Category Name",placeholder:"Medicine",name:"categoryname"}),e.jsx(t,{type:"text",label:"Category Name",placeholder:"Enter Short Description (optional)",name:"description"}),e.jsx(m,{label:"Status",options:u,name:"selectStatus"}),e.jsx("div",{className:"col-span-3",children:e.jsx(x,{label:"Description",placeholder:"Enter short description (optional)",name:"description"})})]}),e.jsx(p,{onCancel:()=>{history.back()},onSubmit:s})]})})})})};export{h as EditVendor,j as default};
