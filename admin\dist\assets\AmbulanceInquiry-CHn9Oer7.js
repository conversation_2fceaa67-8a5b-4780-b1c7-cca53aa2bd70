import{a2 as e,c8 as G,bS as U,ad as $,a5 as a,aD as z,aM as K,av as i,af as W,ah as X,aQ as Y,aj as Z,bJ as ee,aR as te}from"./index-ClX9RVH0.js";import{h as se,i as ne}from"./ambulanceApi-C42c0mRe.js";const ae=({viewData:s,className:j})=>e.jsxs("div",{className:G("mx-6 my-6 ",j),children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("h1",{className:"text-xl font-semibold",children:s.patientName}),e.jsxs("p",{className:"text-xl font-semibold",children:["(",s.contactNumber,")"]})]}),e.jsx("h3",{children:s.department})]}),e.jsx("div",{className:"border-b-2 border-gray-300 my-2 "}),e.jsxs("div",{className:"flex items-center gap-8 justify-between ",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"font-semibold",children:"Pick up Location"}),e.jsx("p",{children:s.pickupLocation})]}),e.jsxs("div",{children:[e.jsx("h1",{className:"font-semibold",children:"Drop Location"}),e.jsx("p",{children:s.dropLocation})]})]}),e.jsx("h3",{className:"font-semibold mt-6",children:"Short Note"}),e.jsx("p",{children:s.notes}),e.jsx("div",{className:"border-b-2 border-gray-300 my-2 "}),e.jsx("div",{className:"w-full flex items-center justify-between",children:e.jsxs("div",{className:"w-full space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{children:"Ambulance Type"}),e.jsx("p",{className:"text-[#656565]",children:s.ambulanceType})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{children:"Status"}),e.jsx("p",{className:U("text-[#fbcf4a]",{"text-green":s.status==="Confirmed","text-red":s.status==="Cancelled"}),children:s.status})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{children:"Request Date"}),e.jsx("p",{className:"text-[#656565]",children:s.date})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{children:"Request inquiryTime"}),e.jsx("p",{className:"text-[#656565]",children:s.inquiryTime})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{children:"Nurse Assigned"}),e.jsx("p",{className:"text-[#656565]",children:s.nurseAssigned})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{children:"Staff Assigned"}),e.jsx("p",{className:"text-[#656565]",children:s.staffAssigned})]})]})})]}),xe=()=>{const s=$(),[j,q]=a.useState("create"),[P,ie]=a.useState(""),[n,m]=a.useState(null),[T,v]=a.useState(!1),[ce,le]=a.useState(null),L=a.useRef(null),{contentRef:p,exportToPDF:k,exportToExcel:w,handlePrint:I}=z(),E=()=>{w(u.rows,"AmbulanceInquiry.xlsx")},R=()=>{k(p.current,"AmbulanceInquiry.pdf",L)},[x,M]=a.useState({page:1,limit:10}),[f,D]=a.useState({search:""}),B=P,F=K({search:f.search,...B?{}:{page:x.page,limit:x.limit}}),[oe,re]=a.useState(""),{data:d,isLoading:C}=se(F),{mutateAsync:O}=ne(),[de,ue]=a.useState(!1),Q=()=>{m(null),s("/ambulance-inquiry/add-ambulanceinquiry")},H=t=>{m(t),s(`/ambulance-inquiry/edit-ambulanceinquiry/${t._id}`)},J=t=>{q("view"),m(t),v(!0)},N=i.get(d,"data.pagination",{}),V=({page:t,limit:c})=>{M(l=>({...l,page:t??1,limit:c??l.limit}))},_=()=>{var c,l,y,g,b,A,S;if(!n)return null;const t={patientName:n.patientName||"N/A",contactNumber:n.contactNumber||"N/A",department:((c=n==null?void 0:n.inHospitalPatient)==null?void 0:c.commonInfo.ipdOpd)||"N/A",pickupLocation:((l=n.pickupLocation)==null?void 0:l.address)||"Pickup Location",dropLocation:((y=n==null?void 0:n.dropLocation)==null?void 0:y.address)||"Drop Location",notes:n.notes||"No notes provided",ambulanceType:((b=(g=n.assignedAmbulance)==null?void 0:g.ambulanceType)==null?void 0:b.ambulanceType)||"Basic Life Support (BLS)",status:n.status||"Pending",date:n.date||"2025-01-01",inquiryTime:n.inquiryTime||"09:00 AM",nurseAssigned:((A=n.nurseAssigned)==null?void 0:A.map(h=>{var o,r;return(r=(o=h.commonInfo)==null?void 0:o.personalInfo)==null?void 0:r.fullName}).join(", "))||"N/A",staffAssigned:((S=n.staffAssigned)==null?void 0:S.map(h=>{var o,r;return(r=(o=h.commonInfo)==null?void 0:o.personalInfo)==null?void 0:r.fullName}).join(", "))||"N/A"};return e.jsx(ae,{viewData:t})},u={columns:[{title:"Requested At",key:"requestedAt"},{title:"Ambulance Type",key:"ambulanceType"},{title:"Pickup",key:"pickupLocation"},{title:"Destination",key:"dropLocation"},{title:"Patient Name",key:"patientName"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:(d==null?void 0:d.data.ambulanceInquiries.map((t,c)=>({key:c,requestedAt:e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("h1",{children:i.get(t,"inquiryTime","")}),e.jsx("p",{children:i.get(t,"date","")})]}),ambulanceType:i.get(t,"assignedAmbulance.ambulanceType.ambulanceType","N/A"),pickupLocation:e.jsx("div",{className:"line-clamp-2",children:i.get(t,"pickupLocation.address","N/A")}),dropLocation:e.jsx("div",{className:"line-clamp-2",children:i.get(t,"dropLocation.address","N/A")}),patientName:e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("h1",{children:i.get(t,"patientName","N/A")}),e.jsx("p",{className:"text-sm",children:i.get(t,"contactNumber","N/A")})]}),status:e.jsx(X,{status:i.get(t,"status","")}),action:e.jsx(W,{onEdit:()=>H(t),onDelete:()=>{O({id:JSON.stringify([i.get(t,"_id","-")])})},onShow:()=>J(t)})})))??[]};return e.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[e.jsx(Y,{headerTitle:"Ambulance Inquiry",toSearch:"Search by patient name, contact no",onSearch:!0,onSearchFunc:t=>D({...f,search:t}),button:!0,buttonText:"Add Ambulance Inquiry",buttonAction:Q,onExportFunctions:!0,onExportExcel:E,onExportPDF:R,onPrint:I}),e.jsx("div",{className:"bg-white rounded-md",children:e.jsx(Z,{columns:u.columns,rows:u.rows,ref:p,loading:C,color:"bg-white",textcolor:"text-gray-400",pagination:{totalPage:N.pages||1,currentPage:N.page||1,limit:x.limit||2,onClick:V}})}),e.jsx(ee,{ref:p,tableData:u}),T&&e.jsx(te,{classname:"w-[35rem]",children:_()})]})};export{xe as AmbulanceInquiry};
