import{ad as p,a5 as u,bB as g,av as e,a2 as a,aK as E,af as N,ag as r,ah as h,a7 as D,ab as l,aj as k,c9 as x}from"./index-ClX9RVH0.js";const C=[{tokenId:"Tk1",patientName:"<PERSON>",date:"2025-02-14",treatment:"General Checkup",doctorName:"<PERSON>",status:"PENDING"},{tokenId:"Tk2",patientName:"<PERSON>",date:"2025-02-15",treatment:"Dental Cleaning",doctorName:"<PERSON>",status:"COMPLETED"},{tokenId:"Tk3",patientName:"<PERSON>",date:"2025-02-16",treatment:"Eye Examination",doctorName:"<PERSON>",status:"CANCELLED"},{tokenId:"Tk4",patientName:"<PERSON>",date:"2025-02-17",treatment:"Blood Test",doctorName:"<PERSON>",status:"COMPLETED"},{tokenId:"Tk5",patientName:"<PERSON>",date:"2025-02-18",treatment:"<PERSON>-<PERSON>",doctorName:"<PERSON>",status:"PENDING"},{tokenId:"Tk6",patientName:"Emily Green",date:"2025-02-19",treatment:"MRI Scan",doctorName:"James Hall",status:"COMPLETED"}],P=()=>{const i=p(),[s,d]=u.useState({page:1,limit:10}),{data:n,isLoading:m}=g({page:s.page,limit:s.limit,category:"SAMPLE"}),c=e.get(n,"data.invoices",[]).map((t,o)=>({key:o,serialNo:(s.page-1)*s.limit+o+1,orderno:e.get(t,"predefinedBillNo","-"),supplierName:e.get(t,"vendor.commonInfo.personalInfo.fullName","-"),orderdate:E(e.get(t,"date","")).format("MMM-DD-YYYY"),status:a.jsx(h,{status:e.get(t,"status","")}),action:a.jsx(N,{onEdit:()=>{i(r.EDITPURCHASEORDER)},onSwitch:()=>{}})}));return a.jsxs("div",{children:[a.jsx(D,{title:"Purchase Order",onSearch:()=>{},onAddClick:()=>{i(r.NEWPURCHASEORDER)},listTitle:"Purchase Order",FilterSection:()=>a.jsxs("div",{className:"flex gap-5",children:[a.jsx(l,{label:"",options:[{value:"All",label:"Categories"}]}),a.jsx(l,{label:"",options:[{value:"All",label:"Status"}]})]})}),a.jsx("div",{className:"py-4",children:a.jsx(k,{columns:x,rows:c,loading:m,pagination:{currentPage:e.get(n,"data.pagination.page",1),totalPage:e.get(n,"data.pagination.pages",1),limit:10,onClick:({page:t})=>d(o=>({...o,page:t||1}))}})})]})};export{C as SampleTableData,P as default};
