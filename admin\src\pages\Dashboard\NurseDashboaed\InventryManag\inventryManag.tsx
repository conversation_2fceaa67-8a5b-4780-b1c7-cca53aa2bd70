import { useState } from "react";
import ModifyCustomTab from "../../../../components/modifyCustomTab";
import stethoscopeIcon from "/Nurseicon/flu-shot_114694211.png";
import file from "/Nurseicon/consumables.png";
import folder from "/Nurseicon/equipment.png";
import treatmentRecord from "/Nurseicon/safety.png";
import Consumables from "./Consumables/Consumables";
import Medications from "./Medications/Medications";
import Equipments from "./Equipments/Equipments";
import Safety from "./Safety/Safety";

const InventryManag = () => {
  const [activeTab, setActiveTab] = useState("consumables");
  const medicalTabs = [
    {
      label: "Consumables",
      image: file,
      value: "consumables",
    },
    {
      label: "Medications",
      image: stethoscopeIcon,
      value: "medications",
    },
    {
      label: "Equipments ",
      image: folder,
      value: "equipments",
    },
    {
      label: "Safety",
      image: treatmentRecord,
      value: "safety",
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "consumables":
        return (
          <div>
            <Consumables />
          </div>
        );
      case "medications":
        return (
          <div>
            <Medications />
          </div>
        );
      case "equipments":
        return (
          <div>
            <Equipments />
          </div>
        );
      case "safety":
        return (
          <div>
            <Safety />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className='mb-4'>
      <div className=' px-4 mt-2 flex items-center gap-x-6 mb-2'>
        <h1 className='text-2xl font-semibold text-gray-900'>Inventory</h1>
        <select className='w-32 bg-white border border-gray-300 rounded-md px-3 py-2'>
          <option value='locations'>Locations</option>
          <option value='warehouse'>Warehouse</option>
          <option value='clinic'>Clinic</option>
        </select>
      </div>
      <div className=' shadow-md p-0.5 border rounded-xl border-[#b3b3b3] bg-white'>
        <ModifyCustomTab
          tabs={medicalTabs}
          defaultTab='consumables'
          onTabChange={setActiveTab}
          showButton={true}
          buttonTitle='Add New'
          buttonAction={() => console.log("Add New clicked")}
        />

        {/* Content */}
        <div>{renderTabContent()}</div>
      </div>
    </div>
  );
};

export default InventryManag;
