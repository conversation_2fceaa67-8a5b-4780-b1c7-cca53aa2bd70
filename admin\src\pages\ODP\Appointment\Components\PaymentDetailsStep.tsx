import React from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { InputField } from "../../../../components/Input-Field";
import { DropdownField } from "../../../../components/DropDownField";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

const paymentMethodOptions = [
  { value: "CASH", label: "Cash" },
  { value: "CARD", label: "Credit/Debit Card" },
  { value: "BANK_TRANSFER", label: "Bank Transfer" },
  { value: "INSURANCE", label: "Insurance" },
  { value: "ONLINE", label: "Online Payment" },
];

const bankOptions = [
  { value: "sbi", label: "State Bank of India" },
  { value: "hdfc", label: "HDFC Bank" },
  { value: "icici", label: "ICICI Bank" },
  { value: "axis", label: "Axis Bank" },
  { value: "pnb", label: "Punjab National Bank" },
];

const paymentValidationSchema = yup.object().shape({});

interface PaymentDetailsStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const PaymentDetailsStep: React.FC<PaymentDetailsStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const formik = useFormik({
    initialValues: {
      paymentMethod: initialData?.paymentMethod || "",
      amount: initialData?.amount || "500",
      paymentStatus: initialData?.paymentStatus || "Pending",
      bankName: initialData?.bankName || "",
      transactionId: initialData?.transactionId || "",
      insuranceProvider: initialData?.insuranceProvider || "",
      insuranceNumber: initialData?.insuranceNumber || "",
      discount: initialData?.discount || "",
      finalAmount: initialData?.finalAmount || "500",
    },
    enableReinitialize: true,
    validationSchema: paymentValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue } = formik;

  return (
    <FormikProvider value={formik}>
      <div>
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:credit-card" className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Payment Details
            </h2>
            <p className="text-sm text-gray-600">
              Billing & Insurance information
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5">
            <div>
              <DropdownField
                required
                label="Payment Method"
                options={paymentMethodOptions}
                firstInput="Select payment method"
                name="paymentMethod"
                value={values.paymentMethod}
                onChange={(e: any) =>
                  setFieldValue("paymentMethod", e.target.value)
                }
              />
            </div>

            <div>
              <InputField
                type="number"
                required
                label="Consultation Fee"
                placeholder="500"
                value={values.amount}
                onChange={(e: any) => setFieldValue("amount", e.target.value)}
              />
            </div>

            <div>
              <InputField
                type="text"
                label="Payment Status"
                placeholder="Pending"
                value={values.paymentStatus}
                disabled
              />
            </div>

            <div>
              <DropdownField
                label="Bank Name"
                options={bankOptions}
                firstInput="Select Bank"
                name="bankName"
                value={values.bankName}
                onChange={(e: any) => setFieldValue("bankName", e.target.value)}
              />
            </div>

            <div>
              <InputField
                type="text"
                label="Transaction ID"
                placeholder="Enter transaction ID"
                value={values.transactionId}
                onChange={(e: any) =>
                  setFieldValue("transactionId", e.target.value)
                }
              />
            </div>

            <div>
              <InputField
                type="text"
                label="Insurance Provider"
                placeholder="Enter insurance provider"
                value={values.insuranceProvider}
                onChange={(e: any) =>
                  setFieldValue("insuranceProvider", e.target.value)
                }
              />
            </div>

            <div>
              <InputField
                type="text"
                label="Insurance Number"
                placeholder="Enter insurance number"
                value={values.insuranceNumber}
                onChange={(e: any) =>
                  setFieldValue("insuranceNumber", e.target.value)
                }
              />
            </div>

            <div>
              <InputField
                type="number"
                label="Discount"
                placeholder="0"
                value={values.discount}
                onChange={(e: any) => {
                  const discountValue = e.target.value;
                  setFieldValue("discount", discountValue);
                  const baseAmount = parseFloat(values.amount) || 0;
                  const discountAmount = parseFloat(discountValue) || 0;
                  setFieldValue(
                    "finalAmount",
                    (baseAmount - discountAmount).toString()
                  );
                }}
              />
            </div>

            <div>
              <InputField
                type="text"
                label="Final Amount"
                placeholder="500"
                value={values.finalAmount}
                disabled
              />
            </div>
          </div>

          <div className="flex justify-between pt-6">
            {showBack && (
              <Button
                type="button"
                onClick={onBack}
                className="bg-gray-500 hover:bg-gray-600 flex items-center gap-2"
              >
                <Icon icon="mdi:arrow-left" className="w-4 h-4" />
                <span>Back</span>
              </Button>
            )}
            <Button
              type="submit"
              className="bg-green-600 hover:bg-green-700 flex items-center gap-2 px-6 py-3"
            >
              <Icon icon="mdi:check" className="w-4 h-4" />
              <span>Submit</span>
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default PaymentDetailsStep;
