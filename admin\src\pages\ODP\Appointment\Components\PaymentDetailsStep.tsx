import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";

interface PaymentDetailsStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const PaymentDetailsStep: React.FC<PaymentDetailsStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const [formData, setFormData] = useState({
    paymentMethod: "",
    amount: "500",
    paymentStatus: "Pending",
    bankName: "",
    transactionId: "",
    insuranceProvider: "",
    insuranceNumber: "",
    discount: "",
    finalAmount: "500",
  });

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
  }, [initialData]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => {
      const updated = {
        ...prev,
        [field]: value,
      };

      // Calculate final amount when discount changes
      if (field === "discount") {
        const baseAmount = parseFloat(updated.amount) || 0;
        const discountAmount = parseFloat(value) || 0;
        updated.finalAmount = (baseAmount - discountAmount).toString();
      }

      return updated;
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext(formData);
  };

  const isFormValid = () => {
    return formData.paymentMethod && formData.amount;
  };

  const paymentMethods = [
    "Cash",
    "Credit Card",
    "Debit Card",
    "Bank Transfer",
    "Digital Wallet",
    "Insurance",
    "Cheque",
  ];

  const paymentStatuses = [
    "Pending",
    "Paid",
    "Partially Paid",
    "Failed",
    "Refunded",
  ];

  const banks = [
    "Nepal Bank Limited",
    "Rastriya Banijya Bank",
    "Agricultural Development Bank",
    "Nepal Investment Bank",
    "Standard Chartered Bank",
    "Himalayan Bank",
    "Nepal SBI Bank",
    "Nepal Bangladesh Bank",
    "Everest Bank",
    "Bank of Kathmandu",
  ];

  return (
    <div>
      {/* Step Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
          <Icon icon="mdi:credit-card" className="w-5 h-5 text-purple-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-800">
            Payment Details
          </h2>
          <p className="text-sm text-gray-600">
            Billing & Insurance information
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Payment Method */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payment Method *
            </label>
            <select
              value={formData.paymentMethod}
              onChange={(e) =>
                handleInputChange("paymentMethod", e.target.value)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="">Select payment method</option>
              {paymentMethods.map((method) => (
                <option key={method} value={method}>
                  {method}
                </option>
              ))}
            </select>
          </div>

          {/* Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Consultation Fee *
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">Rs.</span>
              <input
                type="number"
                value={formData.amount}
                onChange={(e) => handleInputChange("amount", e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                min="0"
              />
            </div>
          </div>

          {/* Payment Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payment Status
            </label>
            <select
              value={formData.paymentStatus}
              onChange={(e) =>
                handleInputChange("paymentStatus", e.target.value)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              {paymentStatuses.map((status) => (
                <option key={status} value={status}>
                  {status}
                </option>
              ))}
            </select>
          </div>

          {/* Bank Name */}
          {(formData.paymentMethod === "Bank Transfer" ||
            formData.paymentMethod === "Cheque") && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bank Name
              </label>
              <select
                value={formData.bankName}
                onChange={(e) => handleInputChange("bankName", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="">Select bank</option>
                {banks.map((bank) => (
                  <option key={bank} value={bank}>
                    {bank}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Transaction ID */}
          {formData.paymentMethod !== "Cash" &&
            formData.paymentMethod !== "Insurance" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Transaction ID
                </label>
                <input
                  type="text"
                  value={formData.transactionId}
                  onChange={(e) =>
                    handleInputChange("transactionId", e.target.value)
                  }
                  placeholder="Enter transaction ID"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
            )}

          {/* Insurance Provider */}
          {formData.paymentMethod === "Insurance" && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Insurance Provider
                </label>
                <input
                  type="text"
                  value={formData.insuranceProvider}
                  onChange={(e) =>
                    handleInputChange("insuranceProvider", e.target.value)
                  }
                  placeholder="Enter insurance provider name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Insurance Number
                </label>
                <input
                  type="text"
                  value={formData.insuranceNumber}
                  onChange={(e) =>
                    handleInputChange("insuranceNumber", e.target.value)
                  }
                  placeholder="Enter insurance policy number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
            </>
          )}

          {/* Discount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Discount Amount
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">Rs.</span>
              <input
                type="number"
                value={formData.discount}
                onChange={(e) => handleInputChange("discount", e.target.value)}
                placeholder="0"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                min="0"
              />
            </div>
          </div>

          {/* Final Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Final Amount
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">Rs.</span>
              <input
                type="number"
                value={formData.finalAmount}
                readOnly
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700"
              />
            </div>
          </div>
        </div>

        {/* Payment Summary */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-800 mb-3">
            Payment Summary
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Consultation Fee:</span>
              <span className="font-medium">Rs. {formData.amount}</span>
            </div>
            {formData.discount && (
              <div className="flex justify-between">
                <span className="text-gray-600">Discount:</span>
                <span className="font-medium text-green-600">
                  - Rs. {formData.discount}
                </span>
              </div>
            )}
            <div className="flex justify-between border-t pt-2">
              <span className="font-medium text-gray-800">Total Amount:</span>
              <span className="font-bold text-lg">
                Rs. {formData.finalAmount}
              </span>
            </div>
          </div>
        </div>

        {/* Final Submission Note */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <Icon icon="mdi:information" className="w-5 h-5 text-blue-600" />
            <p className="text-sm text-blue-800">
              <strong>Ready to submit:</strong> Clicking "Complete Booking" will
              finalize your appointment and submit all information.
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            onClick={onBack}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Icon icon="mdi:arrow-left" className="w-4 h-4" />
            <span>Back</span>
          </Button>

          <Button
            type="submit"
            className="bg-green-600 hover:bg-green-700 flex items-center gap-2 px-6 py-3"
          >
            <Icon icon="mdi:check-circle" className="w-5 h-5" />
            <span>Complete Booking</span>
          </Button>
        </div>
      </form>
    </div>
  );
};

export default PaymentDetailsStep;
