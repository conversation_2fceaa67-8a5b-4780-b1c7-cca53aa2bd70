import React from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { DropdownField } from "../../../../components/DropDownField";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

const paymentMethodOptions = [
  { value: "CASH", label: "Cash" },
  { value: "ONLINE", label: "Online Payment" },
];

// Only Payment Method and Payment Status fields needed

const paymentValidationSchema = yup.object().shape({});

interface PaymentDetailsStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const PaymentDetailsStep: React.FC<PaymentDetailsStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const formik = useFormik({
    initialValues: {
      paymentMethod: initialData?.paymentMethod || "",
      amount: initialData?.amount || "500",
      paymentStatus: initialData?.paymentStatus || "PAID",
      bankName: initialData?.bankName || "",
      transactionId: initialData?.transactionId || "",
      insuranceProvider: initialData?.insuranceProvider || "",
      insuranceNumber: initialData?.insuranceNumber || "",
      discount: initialData?.discount || "",
      finalAmount: initialData?.finalAmount || "500",
    },
    enableReinitialize: true,
    validationSchema: paymentValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue } = formik;

  return (
    <FormikProvider value={formik}>
      <div>
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-10 h-10 bg-blue/20 rounded-full flex items-center justify-center">
            <Icon icon="mdi:credit-card" className="w-5 h-5 text-black" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Payment Details
            </h2>
            <p className="text-sm text-gray-600">
              Billing & Insurance information
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-2">
            <div>
              <DropdownField
                required
                label="Payment Method"
                options={paymentMethodOptions}
                firstInput="Select payment method"
                name="paymentMethod"
                value={values.paymentMethod}
                onChange={(e: any) =>
                  setFieldValue("paymentMethod", e.target.value)
                }
              />
            </div>

            <div>
              <DropdownField
                required
                label="Payment Status"
                options={[
                  { value: "PAID", label: "Paid" },
                  { value: "PENDING", label: "PENDING" },
                  { value: "PARTIALLY-PAID", label: "PARTIALLY-PAID" },
                ]}
                firstInput="Select Payment Status"
                name="paymentStatus"
                value={values.paymentStatus}
                onChange={(e: any) =>
                  setFieldValue("paymentStatus", e.target.value)
                }
              />
            </div>
          </div>

          <div className="flex justify-between pt-6">
            {showBack && (
              <Button
                type="button"
                onClick={onBack}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Icon icon="mdi:arrow-left" className="w-4 h-4" />
                <span>Back</span>
              </Button>
            )}
            <Button
              type="submit"
              variant="outline"
              className=" flex items-center gap-2 px-6 py-2"
            >
              <Icon icon="mdi:check" className="w-4 h-4" />
              <span>Submit</span>
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default PaymentDetailsStep;
