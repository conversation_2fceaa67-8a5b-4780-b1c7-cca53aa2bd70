import React, { useState, useEffect } from "react";
import { Icon } from "@iconify/react";
import Button from "../../../../components/Button";
import { DropdownField } from "../../../../components/DropDownField";
import { InputField } from "../../../../components/Input-Field";
import { useFormik, FormikProvider } from "formik";
import * as yup from "yup";

// Static data for payment options
const paymentMethodOptions = [
  { value: "CASH", label: "Cash" },
  { value: "CARD", label: "Credit/Debit Card" },
  { value: "BANK_TRANSFER", label: "Bank Transfer" },
  { value: "INSURANCE", label: "Insurance" },
  { value: "ONLINE", label: "Online Payment" },
];

const bankOptions = [
  { value: "sbi", label: "State Bank of India" },
  { value: "hdfc", label: "HDFC Bank" },
  { value: "icici", label: "ICICI Bank" },
  { value: "axis", label: "Axis Bank" },
  { value: "pnb", label: "Punjab National Bank" },
];

// No validation for now
const paymentValidationSchema = yup.object().shape({});

interface PaymentDetailsStepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
  showBack?: boolean;
}

const PaymentDetailsStep: React.FC<PaymentDetailsStepProps> = ({
  onNext,
  onBack,
  initialData,
  showBack = true,
}) => {
  const formik = useFormik({
    initialValues: {
      paymentMethod: initialData?.paymentMethod || "",
      amount: initialData?.amount || "500",
      paymentStatus: initialData?.paymentStatus || "Pending",
      bankName: initialData?.bankName || "",
      transactionId: initialData?.transactionId || "",
      insuranceProvider: initialData?.insuranceProvider || "",
      insuranceNumber: initialData?.insuranceNumber || "",
      discount: initialData?.discount || "",
      finalAmount: initialData?.finalAmount || "500",
    },
    enableReinitialize: true,
    validationSchema: paymentValidationSchema,
    onSubmit: (values) => {
      onNext(values);
    },
  });

  const { values, setFieldValue, getFieldProps } = formik;

  const paymentMethods = [
    "Cash",
    "Credit Card",
    "Debit Card",
    "Bank Transfer",
    "Digital Wallet",
    "Insurance",
    "Cheque",
  ];

  const paymentStatuses = [
    "Pending",
    "Paid",
    "Partially Paid",
    "Failed",
    "Refunded",
  ];

  const banks = [
    "Nepal Bank Limited",
    "Rastriya Banijya Bank",
    "Agricultural Development Bank",
    "Nepal Investment Bank",
    "Standard Chartered Bank",
    "Himalayan Bank",
    "Nepal SBI Bank",
    "Nepal Bangladesh Bank",
    "Everest Bank",
    "Bank of Kathmandu",
  ];

  return (
    <FormikProvider value={formik}>
      <div>
        {/* Step Header */}
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <Icon icon="mdi:credit-card" className="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">
              Payment Details
            </h2>
            <p className="text-sm text-gray-600">
              Billing & Insurance information
            </p>
          </div>
        </div>

        <form onSubmit={formik.handleSubmit}>
          <div className="grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5">
            {/* Payment Method */}
            <div>
              <DropdownField
                required
                label="Payment Method"
                options={paymentMethodOptions}
                firstInput="Select payment method"
                name="paymentMethod"
                value={values.paymentMethod}
                onChange={(e: any) =>
                  setFieldValue("paymentMethod", e.target.value)
                }
              />
            </div>

            {/* Amount */}
            <div>
              <InputField
                type="number"
                required
                label="Consultation Fee"
                placeholder="500"
                value={values.amount}
                onChange={(e: any) => setFieldValue("amount", e.target.value)}
              />
            </div>

            {/* Payment Status */}
            <div>
              <InputField
                type="text"
                label="Payment Status"
                placeholder="Pending"
                value={values.paymentStatus}
                disabled
              />
            </div>

            {/* Bank Name */}
            <div>
              <DropdownField
                label="Bank Name"
                options={bankOptions}
                firstInput="Select Bank"
                name="bankName"
                value={values.bankName}
                onChange={(e: any) => setFieldValue("bankName", e.target.value)}
              />
            </div>

            {/* Transaction ID */}
            <div>
              <InputField
                type="text"
                label="Transaction ID"
                placeholder="Enter transaction ID"
                value={values.transactionId}
                onChange={(e: any) => setFieldValue("transactionId", e.target.value)}
              />
            </div>

            {/* Insurance Provider */}
            <div>
              <InputField
                type="text"
                label="Insurance Provider"
                placeholder="Enter insurance provider"
                value={values.insuranceProvider}
                onChange={(e: any) => setFieldValue("insuranceProvider", e.target.value)}
              />
            </div>

            {/* Insurance Number */}
            <div>
              <InputField
                type="text"
                label="Insurance Number"
                placeholder="Enter insurance number"
                value={values.insuranceNumber}
                onChange={(e: any) => setFieldValue("insuranceNumber", e.target.value)}
              />
            </div>

            {/* Discount */}
            <div>
              <InputField
                type="number"
                label="Discount"
                placeholder="0"
                value={values.discount}
                onChange={(e: any) => {
                  const discountValue = e.target.value;
                  setFieldValue("discount", discountValue);
                  // Calculate final amount
                  const baseAmount = parseFloat(values.amount) || 0;
                  const discountAmount = parseFloat(discountValue) || 0;
                  setFieldValue("finalAmount", (baseAmount - discountAmount).toString());
                }}
              />
            </div>

            {/* Final Amount */}
            <div>
              <InputField
                type="text"
                label="Final Amount"
                placeholder="500"
                value={values.finalAmount}
                disabled
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between pt-6">
            {showBack && (
              <Button
                type="button"
                onClick={onBack}
                className="bg-gray-500 hover:bg-gray-600 flex items-center gap-2"
              >
                <Icon icon="mdi:arrow-left" className="w-4 h-4" />
                <span>Back</span>
              </Button>
            )}
            <Button
              type="submit"
              className="bg-green-600 hover:bg-green-700 flex items-center gap-2 px-6 py-3"
            >
              <Icon icon="mdi:check" className="w-4 h-4" />
              <span>Complete Booking</span>
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default PaymentDetailsStep;
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payment Method *
            </label>
            <select
              value={formData.paymentMethod}
              onChange={(e) =>
                handleInputChange("paymentMethod", e.target.value)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              <option value="">Select payment method</option>
              {paymentMethods.map((method) => (
                <option key={method} value={method}>
                  {method}
                </option>
              ))}
            </select>
          </div>

          {/* Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Consultation Fee *
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">Rs.</span>
              <input
                type="number"
                value={formData.amount}
                onChange={(e) => handleInputChange("amount", e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                min="0"
              />
            </div>
          </div>

          {/* Payment Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payment Status
            </label>
            <select
              value={formData.paymentStatus}
              onChange={(e) =>
                handleInputChange("paymentStatus", e.target.value)
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            >
              {paymentStatuses.map((status) => (
                <option key={status} value={status}>
                  {status}
                </option>
              ))}
            </select>
          </div>

          {/* Bank Name */}
          {(formData.paymentMethod === "Bank Transfer" ||
            formData.paymentMethod === "Cheque") && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bank Name
              </label>
              <select
                value={formData.bankName}
                onChange={(e) => handleInputChange("bankName", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="">Select bank</option>
                {banks.map((bank) => (
                  <option key={bank} value={bank}>
                    {bank}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Transaction ID */}
          {formData.paymentMethod !== "Cash" &&
            formData.paymentMethod !== "Insurance" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Transaction ID
                </label>
                <input
                  type="text"
                  value={formData.transactionId}
                  onChange={(e) =>
                    handleInputChange("transactionId", e.target.value)
                  }
                  placeholder="Enter transaction ID"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
            )}

          {/* Insurance Provider */}
          {formData.paymentMethod === "Insurance" && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Insurance Provider
                </label>
                <input
                  type="text"
                  value={formData.insuranceProvider}
                  onChange={(e) =>
                    handleInputChange("insuranceProvider", e.target.value)
                  }
                  placeholder="Enter insurance provider name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Insurance Number
                </label>
                <input
                  type="text"
                  value={formData.insuranceNumber}
                  onChange={(e) =>
                    handleInputChange("insuranceNumber", e.target.value)
                  }
                  placeholder="Enter insurance policy number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
            </>
          )}

          {/* Discount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Discount Amount
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">Rs.</span>
              <input
                type="number"
                value={formData.discount}
                onChange={(e) => handleInputChange("discount", e.target.value)}
                placeholder="0"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                min="0"
              />
            </div>
          </div>

          {/* Final Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Final Amount
            </label>
            <div className="relative">
              <span className="absolute left-3 top-2 text-gray-500">Rs.</span>
              <input
                type="number"
                value={formData.finalAmount}
                readOnly
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700"
              />
            </div>
          </div>
        </div>

        {/* Payment Summary */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-800 mb-3">
            Payment Summary
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Consultation Fee:</span>
              <span className="font-medium">Rs. {formData.amount}</span>
            </div>
            {formData.discount && (
              <div className="flex justify-between">
                <span className="text-gray-600">Discount:</span>
                <span className="font-medium text-green-600">
                  - Rs. {formData.discount}
                </span>
              </div>
            )}
            <div className="flex justify-between border-t pt-2">
              <span className="font-medium text-gray-800">Total Amount:</span>
              <span className="font-bold text-lg">
                Rs. {formData.finalAmount}
              </span>
            </div>
          </div>
        </div>

        {/* Final Submission Note */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <Icon icon="mdi:information" className="w-5 h-5 text-blue-600" />
            <p className="text-sm text-blue-800">
              <strong>Ready to submit:</strong> Clicking "Complete Booking" will
              finalize your appointment and submit all information.
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            type="button"
            onClick={onBack}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Icon icon="mdi:arrow-left" className="w-4 h-4" />
            <span>Back</span>
          </Button>

          <Button
            type="submit"
            className="bg-green-600 hover:bg-green-700 flex items-center gap-2 px-6 py-3"
          >
            <Icon icon="mdi:check-circle" className="w-5 h-5" />
            <span>Complete Booking</span>
          </Button>
          {/* Action Buttons */}
          <div className="flex justify-between pt-6">
            {showBack && (
              <Button
                type="button"
                onClick={onBack}
                className="bg-gray-500 hover:bg-gray-600 flex items-center gap-2"
              >
                <Icon icon="mdi:arrow-left" className="w-4 h-4" />
                <span>Back</span>
              </Button>
            )}
            <Button
              type="submit"
              className="bg-green-600 hover:bg-green-700 flex items-center gap-2 px-6 py-3"
            >
              <Icon icon="mdi:check" className="w-4 h-4" />
              <span>Complete Booking</span>
            </Button>
          </div>
        </form>
      </div>
    </FormikProvider>
  );
};

export default PaymentDetailsStep;
