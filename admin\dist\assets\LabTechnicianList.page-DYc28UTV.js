import{ad as P,a5 as f,aM as j,bw as r,aw as v,bI as _,a2 as n,af as w,ag as c,ah as F,aQ as H,aj as O}from"./index-ClX9RVH0.js";const B=()=>{var p,u,g,A;const l=P(),[t,i]=f.useState({limit:5,page:1}),[a,o]=f.useState({selectedDepartment:"",selectedSpecialist:"",search:""}),x=a.search,C=j({multiroles:JSON.stringify([r.LAB_TECHNICIAN,r.LAB_TECHNOLOGIST,r.PATHOLOGIST]),search:a.search,"departmentDetails.department":a.selectedDepartment,"departmentDetails.speciality":a.selectedSpecialist,...x?{}:{page:t.page,limit:t.limit}}),{data:s}=v(C),{mutateAsync:L}=_(),E=()=>{l(`/${c.ADDLABTECHNICIAN}`)},d={columns:[{title:"Name",key:"doctorName"},{title:"ID",key:"tokenid"},{title:"Department",key:"department"},{title:"Specialist",key:"specialist"},{title:"Available Status",key:"status"},{title:"Action",key:"action"}],rows:(u=(p=s==null?void 0:s.data)==null?void 0:p.users)==null?void 0:u.map((e,k)=>{var h,D,S,N,y,I,T,b;return{key:k,tokenid:`D-${(D=e==null?void 0:e._id)==null?void 0:D.slice(((h=e==null?void 0:e._id)==null?void 0:h.length)-5,e==null?void 0:e._id.length)}`,department:((N=(S=e==null?void 0:e.departmentDetails)==null?void 0:S.hirachyFirst)==null?void 0:N.name)??"-",doctorName:(I=(y=e==null?void 0:e.commonInfo)==null?void 0:y.personalInfo)==null?void 0:I.fullName,specialist:((b=(T=e==null?void 0:e.departmentDetails)==null?void 0:T.speciality)==null?void 0:b.name)??"-",status:n.jsx(F,{status:e!=null&&e.isActive?"ACTIVE":"INACTIVE"}),treatment:e.treat,action:n.jsx(w,{onShow:()=>{l(`/${c.LABTECHNICIANDETAILS}/${e==null?void 0:e._id}`)},onEdit:()=>{l(`/${c.ADDLABTECHNICIAN}/${e==null?void 0:e._id}`)},onDelete:()=>{L({id:JSON.stringify([e==null?void 0:e._id])})}})}})};return n.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[n.jsx(H,{headerTitle:"Lab Technician / Radiologists List",onSearch:!0,onSpecialist:!0,onDepartment:!0,onFilter:!0,button:!0,buttonText:"Add Medical Staff",onSearchFunc:e=>o({...a,search:e}),buttonAction:E,onDepartmentSelectFunc:e=>o({...a,selectedDepartment:e}),onSpecialistSelectFunc:e=>o({...a,selectedSpecialist:e})}),n.jsx("div",{className:"bg-white rounded-md",children:n.jsx(O,{columns:d.columns,rows:d.rows,loading:!1,pagination:{currentPage:t.page,totalPage:(A=(g=s==null?void 0:s.data)==null?void 0:g.pagination)==null?void 0:A.pages,limit:t.limit,onClick:e=>{e.page&&i({...t,page:e.page}),e.limit&&i({...t,limit:e.limit})}}})})]})};export{B as LabTechnicianListPage};
