import{a5 as a,ba as R,av as s,ar as G,at as L,aw as P,bb as B,a1 as U,bc as z,a2 as n,a7 as H,am as W,a9 as Y,aa as J,bd as K,aB as Q,ao as X,aC as Z,b8 as $,be as D,al as ee}from"./index-ClX9RVH0.js";import{u as se,a as re,b as te,c as oe}from"./operation.api-BRnS95Te.js";const c=e=>e?"yes":"no",C=e=>({patientId:s.get(e,"patientInfo.patientId",""),patientName:s.get(e,"commonInfo.personalInfo.fullName",""),contactNumber:s.get(e,"commonInfo.contactInfo.phone.primaryPhone",""),preCheckups:{preSurgeryChecklistCompleted:"",fastingStatus:"",surgerySiteMarked:"",consentFormSigned:"",emergencyEquipment:"",bloodAvailibility:"",note:"",specialRequest:"",allergies:[]},surgeon:[],anesthesiologists:[],scrubNurses:[],circulatingNurses:[],assignedSurgery:{patient:s.get(e,"_id",""),surgeryType:"",surgeryCost:"",startTime:"",endTime:""},ot:"",patient:"",date:""}),ae=(e,r)=>{const i=C(r);return{...i,preCheckups:{fastingStatus:c(s.get(e,"preCheckups.fastingStatus")),surgerySiteMarked:c(s.get(e,"preCheckups.surgerySiteMarked")),consentFormSigned:c(s.get(e,"preCheckups.consentFormSigned")),emergencyEquipment:c(s.get(e,"preCheckups.emergencyEquipment")),bloodAvailibility:c(s.get(e,"preCheckups.bloodAvailibility")),note:s.get(e,"preCheckups.note",""),specialRequest:s.get(e,"preCheckups.specialRequest",""),allergies:s.get(e,"preCheckups.allergies",[]),preSurgeryChecklistCompleted:c(s.get(e,"preCheckups.preSurgeryChecklistCompleted"))},surgeon:s.get(e,"assignedSurgery.surgeon",[]).map(o=>o._id),anesthesiologists:s.get(e,"assignedSurgery.anesthesiologists",[]).map(o=>o._id),scrubNurses:s.get(e,"assignedSurgery.scrubNurses",[]).map(o=>o._id),circulatingNurses:s.get(e,"assignedSurgery.circulatingNurses",[]).map(o=>o._id),assignedSurgery:{...i.assignedSurgery,surgeryType:s.get(e,"assignedSurgery.surgeryType",""),surgeryCost:s.get(e,"assignedSurgery.surgeryCost",""),startTime:s.get(e,"assignedSurgery.startTime",""),endTime:s.get(e,"assignedSurgery.endTime","")},ot:s.get(e,"ot._id",""),date:s.get(e,"date","")}},ne=e=>{const r=$(e);return{date:r.date,patientName:r.assignedSurgery.patient,ot:r.ot,patient:r.assignedSurgery.patient,contactNumber:r.contactNumber,preCheckups:{...r.preCheckups,fastingStatus:r.preCheckups.fastingStatus==="yes",surgerySiteMarked:r.preCheckups.surgerySiteMarked==="yes",consentFormSigned:r.preCheckups.consentFormSigned==="yes",emergencyEquipment:r.preCheckups.emergencyEquipment==="yes",bloodAvailibility:r.preCheckups.bloodAvailibility==="yes",preSurgeryChecklistCompleted:r.preCheckups.preSurgeryChecklistCompleted==="yes",allergies:r.preCheckups.allergies||[]},assignedSurgery:{...r.assignedSurgery,surgeon:r.surgeon||[],anesthesiologists:r.anesthesiologists||[],scrubNurses:r.scrubNurses||[],circulatingNurses:r.circulatingNurses||[],otherStaff:[]},status:"SCHEDULED"}},ie=()=>{const[e,r]=a.useState(null),[i,o]=a.useState(!0);return a.useEffect(()=>{(async()=>{try{o(!0);const l=await D.get("user",{params:{role:"DOCTOR"}});r(l.data)}catch(l){console.error("Error fetching doctors:",l),ee.error("Failed to fetch doctors")}finally{o(!1)}})()},[]),{doctors:e,loading:i}},ue=(e,r)=>{const[i,o]=a.useState(!1);return{initialValues:a.useMemo(()=>e&&r?ae(e,r):C(r),[e,r]),isInitialized:i,setIsInitialized:o}},me=()=>{const[e,r]=a.useState(1),i=R(),o=s.get(i,"id","add").split("-"),p=o[0]==="surgery"&&o[1],{patientId:l}=G(Z,t=>t),{data:d}=se(p?o[1]:"");console.log("editData",d);const{data:k}=L(l),{data:m}=re({isAvailable:!0}),{data:y}=P({role:"NURSE"}),{doctors:S}=ie(),{mutate:v,isPending:N,isError:ce,isSuccess:h}=te(),{mutate:I,isPending:le,isSuccess:ge}=oe(),{initialValues:F}=ue(d,k),E=a.useMemo(()=>s.get(m,["data","ot"],[]).map(t=>({label:s.get(t,"name",""),value:s.get(t,"_id","")})),[m]),x=a.useMemo(()=>s.get(S,"data.users",[]).map(t=>({label:s.get(t,"commonInfo.personalInfo.fullName",""),value:s.get(t,"_id","")})),[S]),T=a.useMemo(()=>s.get(y,"data.users",[]).map(t=>({label:s.get(t,"commonInfo.personalInfo.fullName",""),value:s.get(t,"_id","")})),[y]),j=a.useMemo(()=>B.map(t=>({...t,isActive:t.step===e})),[e]),g=U({initialValues:F,enableReinitialize:!0,validationSchema:z,onSubmit:async t=>{const u=ne(t);console.log("values, ",u),o[1]?I({_id:o[1],entityData:u}):v(u)}}),{handleSubmit:f,values:M,errors:V,touched:_,getFieldProps:A,setFieldValue:q,resetForm:b}=g;console.log(g,"Formik"),a.useEffect(()=>{h&&b()},[h,b]);const w=a.useCallback(t=>{r(t+1)},[]),O=a.useCallback(()=>{window.history.back()},[]);return console.log("sfsodfiusdf",d),console.log("formik.values",g.values),n.jsxs("div",{children:[n.jsx(H,{listTitle:"Surgery Details Form",hideHeader:!0}),n.jsxs("div",{className:"relative flex w-full gap-6",children:[n.jsx("div",{className:"w-auto h-full",children:n.jsx(W,{steps:j})}),n.jsx("div",{className:"w-full",children:n.jsx(Y,{value:g,children:n.jsxs(J,{onSubmit:f,className:"space-y-4",children:[K({otList:E,doctorList:x,nurseList:T}).map(({fields:t},u)=>n.jsx("div",{onClick:()=>w(u),className:"p-4 bg-white rounded-sm grid grid-cols-1 sm:grid-cols-2 gap-4 md:grid-cols-3 cursor-pointer hover:bg-gray-50 transition-colors",children:n.jsx(Q,{setFieldValue:q,values:M,formDatails:t,getFieldProps:A,errors:V,touched:_},u.toString())},u.toString())),n.jsx(X,{onCancel:O,onSubmit:f,isPending:N})]})})})]})]})};export{me as SurgeryGeneralWard};
