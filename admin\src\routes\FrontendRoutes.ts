export const FrontendRoutes = {
  PROFILE: "/profile",
  HOME: "/",
  UNAUTHORIZED: "/unauthorized",
  LOGIN: "/auth-login",
  FORGOT: "/auth-forgot",
  Hello: "/auth-hello",
  OPT_VERIFICATION: "/auth-verification",
  CH<PERSON><PERSON>_PASSWORD: "/auth-password",
  ADMINDASHBOARD: "/admin-dashboard",
  NURSEDASHBOARD: "/nurse-dashboard",
  DOCTORDASHBOARD: "/dashboard/doctor-dashboard",
  ACCOUNTDASHBOARD: "/account-dashboard",
  LABDASHBOARD: "/lab-dashboard",
  RADIOLOGYDASHBOARD: "/radiology-dashboard",
  PATIENTLIST: "/patient",
  INPATIENTLIST: "/inpatient",
  IPDPATIENTLIST: "/inpatientcustom",
  ADDPATIENT: "/patient/add-patient",
  DoctorsAppointment: "/appointment/doctors",
  PATIENTDETAIL: `/patient/patient-details`,
  DOCTORLIST: "/doctor",
  DOCTORDETAIL: "/doctor",
  ADDDOCTOR: "/doctor/add-doctor",
  DOC<PERSON><PERSON><PERSON>ILABILITY: "/availability",
  DOC<PERSON>RREVIEWS: "/doc-reviews",
  STAFFLIST: "/staff",
  ADDSTAFF: "staff/add-staff",
  EVENTLIST: "events",
  EVENTADD: "/events/add-events",
  STAFFDETAIL: "staff",
  PHARMACISTSLIST: "/pharmacist",
  ADDPHARMACIST: "pharmacist/add-pharmacists",
  VOUCHER: "/expenses/voucher",
  PHARMACISTSDETAILS: "/pharmacist",
  LABTECHNICIANLIST: "/lab_diagnostic",
  ADDLABTECHNICIAN: "lab_diagnostic/add-lab-technician",

  LABTECHNICIANDETAILS: "lab_diagnostic",
  // PHARMACIST
  NURSELIST: "/nurse",
  ADDNURSE: "/nurse/add-nurse",
  NURSEINVENTORY: "/nurse/nurse-inventory",

  //certificate
  CERTIFICATE: "/certificate",
  ADDCERTIFICATE: "certificate/add-certificate",

  // -----------------------------SHIFT------------------------------------------
  NURSEDETAIL: "nurse",
  MEDICALSTAFFDETAIL: "medical-staff/details",

  NURSESHIFTOVERVIEW: "/nurse-shift-overview",

  DAILYSHIFT: "/daily-shift",
  SHIFTLIST: "/shift-mangement/shift-list",
  SHIFTASSIGN: "/shift-mangement/shift-assign",

  // -----------------------------Attendance------------------------------------------
  ATTENDANCELIST: "/attendance-list",
  ADDATTENDANCE: "/add-attendance",
  VIEWATTENDANCE: "/attendance-details",

  ROOMDETAILS: "/room-details",
  GENERALMEDICINE: "/general-medicine",
  CARDIOLOGY: "/cardiology",
  PEDIATRICS: "/pediatrics",

  // -----------------------------------General Ward-----------------------------------
  GENERALWARD: "/general-ward",
  TRANSFER_GENERALWARD: "/general-ward/transfer",
  TREATMENT_GENERALWARD: "/general-ward/treatment",
  SURGERY_GENERALWARD: "/general-ward/surgery",
  DISCHARGE_GENERALWARD: "/general-ward/discharge",

  NICU: "/nicu",
  ICU: "/icu",
  POSTOPERATIVEWARD: "/post-operative-ward",
  GYNEOBSERVATIONWARD: "/gyne-observation-ward",
  HIGHDEPENDENCYUNIT: "/high-dependency-unit",
  DAYCAREUNIT: "/day-care-unit",
  DOCTORPRESCRIPTIONS: "/doctor-prescriptions",
  ADDPRESCRIPTION: "/add-prescription",
  //canteen-------------------
  DIETPLANLIST: "/diet-plan-list",
  ASSIGNDIETPLAN: "/assign-diet-plan",
  ADDASSIGNDIETPLAN: "/assign-diet-plan/add-assign-diet-plan",
  ADDDIETPLAN: "/diet-plan-list/add-diet-plan",
  CANTEENINVENTROY: "/canteen/canteen-inventory",
  CANTEENPRODUCT: "/canteen/canteen-product",
  CANTEENPURCHASE: "canteen-purchase",
  CANTEENMENULIST: "canteen-menu",
  CANTEENDIETCHART: "canteen-diet",
  CANTEENSTOCKLIST: "canteen-stock",
  CANTEENINVOICE: "canteen-invoice",
  CANTEENMEALCONFIG: "canteen-meal-config",

  //Lab-------------
  TESTREQUEST: "/test-requests",
  TESTRESULT: "/test-results",
  ADDTESTRESULT: "/test-results/add-test-result",
  ADDTESTREQUEST: "/test-results/add-test-request",
  ADDEMERGENCYPATIENT: "/add-emergency-patient",
  INVENTORYLIST: "/inventory-list",
  ADDINVENTORY: "/add-inventory",

  // //Radiology-------------
  RADIOLOGYPURCHASE: "/radiology/purchase",
  RADIOLOGYREPORT: "/radiology/report",
  RADIOLOGYTESTREQUEST: "/radiology/test-request",
  ADDRADIOLOGYTESTREQUEST: "/radiology/add-test-request",
  RADIOLOGYINVENTORY: "/radiology/inventory",
  RADIOLOGYPRODUCTLIST: "/radiology/product-list",
  RADIOLOGYDUELIST: "/radiology/due-list",
  RADIOLOGYINVOICE: "/radiology/invoice-list",
  TESTLISTCONFIG: "/radiology/test-list-config",
  SERVICETYPE: "/radiology/sub-department",
  RADIOLOGYEDITREPORT: "/radiology/test-request/add-report",
  RADIOLOGYDEPARTMENT: "/radiology/department-config",

  // -------------------------------- Operation Theatre ----------------------------------------------------------------

  AVAILABILITY: "/surgery-availability",
  AVAILABILITYDETAIL: "/surgery-availability/surgery-availability-details",
  GENERALOT: "/assign-ot",
  GENERALOT_VIEWDETAIL: "/assign-ot/view-detail",
  SURGERY_DETAILS: "/assign-ot/edit",
  MINOROT: "/minor-ot",
  CONFIGURATIONOT: "/configuration-ot",
  OLOT: "/ortho-laproscopic",
  POF: "/pre-operative-form",

  // -------------------------------- Operation Theatre ----------------------------------------------------------------
  PRESCRIPTIONS: "/doctor-prescriptions",
  ADD_PRESCRIPTIONS: "/add-prescription",

  // -------------------------------- Appointment ----------------------------------------------------------------
  APPOINTMENTLIST: "/appointment/appointment-list",
  ADDAPPOINTMENT: "/appointment/add-appointment",
  OPDAPPOINTMENTFORM: "/appointment/opd-appointment-form",
  GENERALCHECKUPLIST: "/appointment/general-check",
  GENERALPRECHECKUPADD: "/appointment/general-check/pre-checkup",
  GENERALCHECKUPADD: "/appointment/general-check/checkup",
  GENERALPATIENTDETAILS: "/appointment/general-check/patient-details",
  GENERALPHYSICIANLIST: "/appointment/general-physician",
  GENERALPHYSICIAN: "/appointment/general-physician/checkup",
  CARDILOGYLIST: "/appointment/cardiology-list",
  CARDILOGY: "/appointment/cardiology/checkup",
  PEDIATRICLIST: "/appointment/pediatric-list",
  ORTHOPEDICSLIST: "/appointment/orthopedics-list",
  NEUROLOGYLIST: "/appointment/neurology-list",
  DERMATOLOGYLIST: "/appointment/dermatology-list",
  DERMATOLOGY: "/appointment/dermatology/checkup",
  ENTLIST: "/appointment/ent-list",
  ENT: "/appointment/ent/checkup",
  NEUROLOGY: "/appointment/neurology/checkup",
  ORTHOPEDICS: "/appointment/orthopedics/checkup",
  DENTALCARELIST: "/appointment/dental-care-list",
  DENTALCARE: "/appointment/dental-care/checkup",
  GYNECOLOGYLIST: "/appointment/gynecology-list",
  GYNECOLOGY: "/appointment/gynecology/checkup",
  ORTHOMOLOGYLIST: "/appointment/orthomology-list",
  ORTHOMOLOGY: "/appointment/orthomology/checkup",

  //--------------------------Ambulance--------------------------------------------------
  AMBULANCELIST: "/hospital-ambulance",
  // AMBULANCETYPE: "/ambulance-management/ambulance-type",
  AMBULANCETYPE: "/ambulance-type",

  ADDAMBULANCETYPE: "/add-ambulance-type",
  AMBULANCEINQUIRY: "/ambulance-inquiry",
  ADDAMBULANCEINQUIRY: "/ambulance-inquiry/add-ambulanceinquiry",
  EDITAMBULANCEINQUIRY: "/ambulance-inquiry/edit-ambulanceinquiry/:id",
  PRICECONFIG: "/price-config",

  // Purcahse Management
  VENDORLIST: "/purchase-management/vendor-list",
  ADDVENDOR: "/purchase-management/vendor-list/add-vendor",
  EDITVENDOR: "/purchase-management/vendor-list/edit-vendor",
  PURCHASERETURN: "/purchase-management/purchase-return",
  ADDPURCHASERETURN: "/purchase-management/purchase-return/add-purchase-return",
  PURCHASELIST: "/purchase-management/purchase-list",
  PRODUCTLIST: "/purchase-management/product-list",

  VENDORORDERLIST: "/purchase-management/order-list",
  VENDORBILLING: "/purchase-management/billing",

  ADDPURCHASE: "/purchase-management/purchase-list/add-purchase",
  PURCHASEORDER: "/purchase-management/purchase-order",
  NEWPURCHASEORDER: `/purchase-management/purchase-list/add`,
  EDITPURCHASEORDER: "/purchase-management/purchase-order/edit-purchase-order",

  //Inventory Management
  INVENTORY: "/inventory-list",
  INVENTORYPRODUCTLIST: "/product-list",
  INVENTORYCATEGORYLIST: "/inventory-category",
  INVENTORYSUBCATEGORYLIST: "/inventory-sub-category",
  ADDCATEGORY: "/add-categories",
  ADDSUBCATEGORY: "/add-subcategories",

  //Emergency
  EMERGENCYROOM: "/emergency-room",
  EMERGENCYADDPATIENT: "/emergency/add-patient",

  // Pricing Config
  PRICINGCONFIGSERVICEITEM: "/pricing-config/service-list",
  PRICINGCONFIGSERVICEITEMADD: "/pricing-config/service-list/add-new-service",
  PRICINGCONFIGCATEGORYLIST: "/pricing-config/category-list",
  PRICINGADDPRICINGCATEGORY: "/pricing-config/add-category",
  PRICINGADDSERVICES: "/pricing-config/add-service",

  // Financial Ops
  FINANCIALOPSINVOICE: "/financial-ops/invoice",
  FINANCIAlPAYMENT: "/financial-ops/payment",
  FINANCEADDNEWINVOICE: "/financial-ops/invoice/add-invoice",
  FINANCEADDNEWPAYMENT: "/financial-ops/payment/add-payment",

  // Payroll
  PAYROLL_LIST: "/payroll-list",
  PAYROLL_CONFIG: "/payroll-config",

  // Expense
  EXPENSE_LIST: "/expense/list",
  EXPENSE_CATEGORY: "/expense/category",
  // EXPENSE_LIST: "/expense-management/expense-list",
  EXPENSE_ADD: "/expense-management/expense-add",
  // EXPENSE_CATEGORY: "/expense-management/expense-category",

  // Settings
  SETTINGSDEPARTMENTCONFIG: "/settings/department-config",
  SETTINGSADDDEPARTMENT: "/settings/department-config/add-department",
  USERMANAGEMENT: "/settings/user-management",
  ROLEMANAGEMENT: "/settings/role-management",
  ROLEMANAGEMENTFORM: "/settings/role-management/form",
  USERMANAGEMENTADDUSER: "/settings/user-management/add-user",
  WARD: "/settings/ward",
  ADDWARD: "/settings/ward/add-ward",

  NOTIFICATIONSETUP: "/settings/notification",
  NOTIFICATIONSETUPADD: "/settings/notification/add-notification",
  REPORT: "/settings/report",

  // Bed Allocation
  BEDALLOCATIONLIST: "/bed-allocation-list",
  EDITBEDALLOCATION: "/bed-allocation/edit-bed-allocation",
  ADDASSIGNBED: "/bed-allocation/assign-bed",
  BEDROOMDETAILS: "/bed-allocation/room-details",
  BEDADDROOMS: "/bed-allocation/room-details/add-rooms",
  BEDLIST: "/bed-allocation/bed-list",
  BEDCATEGORY: "/bed-allocation/bed-category",

  // token management
  ACCOUNTTOKENMANAGEMENT: "/token-management/accounting",
  TOKENCONFIG: "/token-management/token-config",
  APPOINTMENTTOKENMANAGEMENT: "/token-management/appointment",

  // Donor List
  BLOODDONORLIST: "/donor/blood-donor-list",
  ADDDONOR: "/donor/blood-donor-list/custom-form",
  VIEWDETAILSDONOR: "/donor/blood-donor-list/view-details",
  ORGANDONORLIST: "/donor/organ-donor-list",
  BLOODBANK: "/donor/blood-bank",
  CASHEQUIPMENT: "/donor/cash-equipment",
  RECEIVER: "/receiver/receiver-list",
  CUSTOMFORM: "/receiver/receiver-list/custom-form",

  //-------------------------- Setting Commission Mgt--------------------------------------------------

  TYPESERVICE: "/settings/commission-management/type-service",

  RESELLER: "/settings/commission-management/reseller-list",
  ADDRESELLER: "/settings/commission-management/add-reseller-list",
  ADDTYPE: "/settings/commission-management/type-service/custom-form",

  ADDSERVICECOM: "/settings/commission-management/type-service",
  RESELLERDETAIL: "/settings/commission-management/detail-reseller",
  RESELLERDETAILPAGE: "/settings/commission-management/detail-reseller",
  COMMISSIONCONFIG: "/settings/commission-management",
  ASSIGNCOMMISSION: "/settings/commission-management/custom-form",

  //-------------------------- Lab Dashboard --------------------------------------------------
  LABPURCHASE: "/lab-purchase",
  LABTESTRESULT: "/lab-test-result",
  LABTESTREQUESTLIST: "/lab-report-list",
  AddLabTestRequest: "/lab-report/add-test-request",
  ADDREPORT: "lab-report-list/add-report",
  LABSAMPLECOLLECTION: "lab-sample-collection",
  LABINVENTORY: "/lab-inventory",
  LABPRODUCTLIST: "/lab-product-list",
  LABINVOICELIST: "/lab-invoice-list",
  // LABDUELIST: 'lab-due-list',
  LABDEPARTMENTCONFIG: "/lab-department-config",
  LABSUBCATEGORYCONFIG: "/lab-subcategory-config",
  LABTESTTYPECONFIG: "/lab-test-type-config",
  LABPARAMETERCONFIG: "/lab-parameter-config",

  //----------------PHarmacy
  POS: "/pos",
  MEDICINEREQUEST: "/medicine-request",
  PHARMACYINVENTORY: "/pharmacy-inventory",
  PHARMACYINVENTORYDetails: "/pharmacy-inventory/details",

  PHARMACYPRODUCT: "/pharmacy-product",
  MEDICINERQUESTDETAILS: "/medicine-request/medicine-request-details",
  PHARMACYEXPENSE: "/pharmacy-expense",
  PHARMACYSALES: "/pharmacy-sales",
  PHARMACYREPORT: "/pharmacy-reports",

  //------------bank
  BANK: "/bank",
  TRANSACTIONDETAILS: "/transaction-details",

  //-------------------------- Pharmacy Dashboard --------------------------------------------------
  PHARMACYDASHBOARD: "/pharmacy-dashboard",

  // website
  WEBSITE_BLOG_MANAGEMENT: "/website/blog-management",
  WEBSITE_TEAM_MANAGEMENT: "/website/team-management",

  WEBSITE_CARRER: "/website/career",
  WEBSITE_CARRER_APPLICATION: "/website/career/application",

  WEBSITE_COMPAIGN_MANAGEMENT: "/website/campaign-management",
  WEBSITE_GALLERY: "/website/gallery",
  WEBSITE_CONTACT: "/website/contact",
  WEBSITE_TRAINING: "/website/training",

  //Accounting:
  BALANCESHEET: "/accouting/balance-sheet",
  PAYMENT_VOUCHER: "/accouting/payment-voucher",
  PURCHASE_RETURN: "/accouting/purchase-return",
  SALES_VOUCHER: "/accouting/sales-voucher",
  SALES_RETURN: "/accouting/sales-return",
  TRANSACTION_LIST: "/accouting/transaction-list",
  GENERAL_LEDGER_BALANCE: "/accouting/general-ledger-balance",
  INCOME_STATEMENT: "/accouting/income-statement",
};
