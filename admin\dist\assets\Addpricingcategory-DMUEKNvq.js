import{aV as N,aX as l,ba as u,a2 as e,a7 as b,a5 as m,a1 as C,a9 as v,aa as E,ac as g,ao as S}from"./index-ClX9RVH0.js";import{b as w,c as A,d as D}from"./categoryApi-75124ePN.js";const B=N({categoryName:l().min(5,"Category name must be at least 2 characters").required("Category name is required"),description:l().min(5,"Description must be at least 5 characters").required("Description is required")}),I=()=>{const{id:a}=u(),t=!!a?"Edit Category":"Add New Category";return e.jsxs("div",{children:[e.jsx(b,{title:t,hideHeader:!0,listTitle:t}),e.jsx("div",{className:"mb-6",children:e.jsx(F,{})})]})},F=()=>{const{id:a}=u(),i=!!a,t=w(),x=A(),{data:s,isLoading:p}=D(a),[y,h]=m.useState({categoryName:"",description:""});m.useEffect(()=>{s&&h({categoryName:s.categoryName||"",description:s.description||""})},[s]);const o=C({initialValues:y,enableReinitialize:!0,validationSchema:B,onSubmit:async d=>{try{i?await x.mutateAsync({entityData:d,_id:a}):await t.mutateAsync(d),history.back()}catch(j){console.error("Error submitting category:",j)}}}),{handleSubmit:c,resetForm:f,errors:r,touched:n}=o;return i&&p?e.jsx("p",{className:"p-5",children:"Loading category data..."}):e.jsx(v,{value:o,children:e.jsx(E,{onSubmit:c,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsx("div",{className:"w-full h-full bg-white",children:e.jsx("div",{className:"w-[70%] h-full",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-5 py-8 px-5",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx(g,{type:"text",label:"Category",placeholder:"Enter Category",name:"categoryName"}),r.categoryName&&n.categoryName&&e.jsx("span",{className:"text-red text-sm",children:r.categoryName})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx(g,{type:"text",label:"Description",placeholder:"Enter Description",name:"description"}),r.description&&n.description&&e.jsx("span",{className:"text-red text-sm",children:r.description})]})]})})}),e.jsx(S,{onCancel:()=>f(),onSubmit:c})]})})})};export{F as BasicInformation,I as default};
