// import type React from "react";
// import { useState } from "react";

// export interface VerticalTabItem {
//   id: string;
//   label: string;
//   content: React.ReactNode;
// }

// interface VerticalTabsProps {
//   tabs: VerticalTabItem[];
//   defaultActiveTab?: string;
//   onTabChange?: (tabId: string) => void;
//   className?: string;
// }

// const VerticalTabs: React.FC<VerticalTabsProps> = ({
//   tabs,
//   defaultActiveTab,
//   onTabChange,
//   className = "",
// }) => {
//   const [activeTab, setActiveTab] = useState(defaultActiveTab || tabs[0]?.id);

//   const handleTabClick = (tabId: string) => {
//     setActiveTab(tabId);
//     onTabChange?.(tabId);
//   };

//   const activeTabContent = tabs.find((tab) => tab.id === activeTab)?.content;
//   const activeTabLabel = tabs.find((tab) => tab.id === activeTab)?.label;

//   return (
//     <div className={`flex h-full bg-white ${className}`}>
//       {/* Left Sidebar - Vertical Tabs */}
//       <div className="w-64 bg-gray-50 border-r border-gray-200">
//         <nav className="flex flex-col" aria-label="Vertical Tabs">
//           {tabs.map((tab) => (
//             <button
//               key={tab.id}
//               onClick={() => handleTabClick(tab.id)}
//               className={`
//                 px-4 py-3 text-left text-sm font-medium transition-colors duration-200 border-b border-gray-200
//                 ${
//                   activeTab === tab.id
//                     ? "bg-[#E9F2FF] text-primary border-l-4 border-l-primary"
//                     : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
//                 }
//               `}
//               aria-current={activeTab === tab.id ? "page" : undefined}
//             >
//               {tab.label}
//             </button>
//           ))}
//         </nav>
//       </div>

//       {/* Right Content Area */}
//       <div className="flex-1 flex flex-col">
//         {/* Content Header */}
//         <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
//           <div className="flex items-center justify-between">
//             <h2 className="text-lg font-semibold text-gray-900">
//               {activeTabLabel}
//             </h2>
//             <input
//               type="date"
//               className="px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
//               placeholder="mm/dd/yyyy"
//             />
//           </div>
//         </div>

//         {/* Content Body */}
//         <div className="flex-1 p-6 overflow-auto">{activeTabContent}</div>
//       </div>
//     </div>
//   );
// };

// // Sample Vital Signs Table Component
// const VitalSignsTable: React.FC = () => {
//   const vitalSignsData = [
//     {
//       date: "01/02/2025",
//       time: "4:30 PM",
//       user: "Rita",
//       temp: "98°C",
//       bpm: "90",
//       sysbp: "100",
//       disbp: "120",
//       weight: "90 kg",
//       bmi: "24",
//     },
//     {
//       date: "01/02/2025",
//       time: "4:30 PM",
//       user: "Rita",
//       temp: "98°C",
//       bpm: "90",
//       sysbp: "100",
//       disbp: "120",
//       weight: "90 kg",
//       bmi: "24",
//     },
//     {
//       date: "01/02/2025",
//       time: "4:30 PM",
//       user: "Rita",
//       temp: "98°C",
//       bpm: "90",
//       sysbp: "100",
//       disbp: "120",
//       weight: "90 kg",
//       bmi: "24",
//     },
//     {
//       date: "01/02/2025",
//       time: "4:30 PM",
//       user: "Rita",
//       temp: "98°C",
//       bpm: "90",
//       sysbp: "100",
//       disbp: "120",
//       weight: "90 kg",
//       bmi: "24",
//     },
//   ];

//   return (
//     <div className="overflow-x-auto">
//       <table className="min-w-full bg-white">
//         <thead className="bg-gray-100">
//           <tr>
//             <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
//               DATE
//             </th>
//             <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
//               TIME
//             </th>
//             <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
//               USER
//             </th>
//             <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
//               TEMP
//             </th>
//             <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
//               BPM
//             </th>
//             <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
//               SYSBP
//             </th>
//             <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
//               DISBP
//             </th>
//             <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
//               WEIGHT
//             </th>
//             <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
//               BMI
//             </th>
//           </tr>
//         </thead>
//         <tbody className="divide-y divide-gray-200">
//           {vitalSignsData.map((row, index) => (
//             <tr key={index} className="hover:bg-gray-50">
//               <td className="px-4 py-2 text-sm text-gray-900">{row.date}</td>
//               <td className="px-4 py-2 text-sm text-gray-900">{row.time}</td>
//               <td className="px-4 py-2 text-sm text-gray-900">{row.user}</td>
//               <td className="px-4 py-2 text-sm text-gray-900">{row.temp}</td>
//               <td className="px-4 py-2 text-sm text-gray-900">{row.bpm}</td>
//               <td className="px-4 py-2 text-sm text-gray-900">{row.sysbp}</td>
//               <td className="px-4 py-2 text-sm text-gray-900">{row.disbp}</td>
//               <td className="px-4 py-2 text-sm text-gray-900">{row.weight}</td>
//               <td className="px-4 py-2 text-sm text-gray-900">{row.bmi}</td>
//             </tr>
//           ))}
//         </tbody>
//       </table>
//     </div>
//   );
// };

// // Example usage component
// const MedicalRecordsApp: React.FC = () => {
//   const medicalTabs: VerticalTabItem[] = [
//     {
//       id: "vital-signs",
//       label: "Vital Signs",
//       content: <VitalSignsTable />,
//     },
//     {
//       id: "medical-administration",
//       label: "Medical Administration",
//       content: (
//         <div className="p-4 bg-gray-50 rounded-lg">
//           <h3 className="text-lg font-semibold mb-2">Medical Administration</h3>
//           <p className="text-gray-600">
//             Medical administration records will be displayed here.
//           </p>
//         </div>
//       ),
//     },
//     {
//       id: "allergies",
//       label: "Allergies",
//       content: (
//         <div className="p-4 bg-gray-50 rounded-lg">
//           <h3 className="text-lg font-semibold mb-2">Patient Allergies</h3>
//           <p className="text-gray-600">
//             Patient allergy information will be displayed here.
//           </p>
//         </div>
//       ),
//     },
//     {
//       id: "immunizations",
//       label: "Immunizations",
//       content: (
//         <div className="p-4 bg-gray-50 rounded-lg">
//           <h3 className="text-lg font-semibold mb-2">Immunization Records</h3>
//           <p className="text-gray-600">
//             Patient immunization history will be displayed here.
//           </p>
//         </div>
//       ),
//     },
//     {
//       id: "chronic-conditions",
//       label: "Chronic Conditions",
//       content: (
//         <div className="p-4 bg-gray-50 rounded-lg">
//           <h3 className="text-lg font-semibold mb-2">Chronic Conditions</h3>
//           <p className="text-gray-600">
//             Patient chronic conditions will be displayed here.
//           </p>
//         </div>
//       ),
//     },
//     {
//       id: "surgical-history",
//       label: "Surgical History",
//       content: (
//         <div className="p-4 bg-gray-50 rounded-lg">
//           <h3 className="text-lg font-semibold mb-2">Surgical History</h3>
//           <p className="text-gray-600">
//             Patient surgical history will be displayed here.
//           </p>
//         </div>
//       ),
//     },
//     {
//       id: "family-history",
//       label: "Family History",
//       content: (
//         <div className="p-4 bg-gray-50 rounded-lg">
//           <h3 className="text-lg font-semibold mb-2">Family Medical History</h3>
//           <p className="text-gray-600">
//             Patient family medical history will be displayed here.
//           </p>
//         </div>
//       ),
//     },
//     {
//       id: "social-history",
//       label: "Social History",
//       content: (
//         <div className="p-4 bg-gray-50 rounded-lg">
//           <h3 className="text-lg font-semibold mb-2">Social History</h3>
//           <p className="text-gray-600">
//             Patient social history will be displayed here.
//           </p>
//         </div>
//       ),
//     },
//     {
//       id: "visit-history",
//       label: "Visit History",
//       content: (
//         <div className="p-4 bg-gray-50 rounded-lg">
//           <h3 className="text-lg font-semibold mb-2">Visit History</h3>
//           <p className="text-gray-600">
//             Patient visit history will be displayed here.
//           </p>
//         </div>
//       ),
//     },
//   ];

//   return (
//     <div className="h-screen">
//       <VerticalTabs
//         tabs={medicalTabs}
//         defaultActiveTab="vital-signs"
//         onTabChange={(tabId) => console.log("Active tab:", tabId)}
//         className="h-full"
//       />
//     </div>
//   );
// };

// export default MedicalRecordsApp;

// Second

"use client";

import type React from "react";
import { useState } from "react";

export interface VerticalTabItem {
  id: string;
  label: string;
  content: React.ReactNode;
}

interface VerticalTabsProps {
  tabs: VerticalTabItem[];
  defaultActiveTab?: string;
  onTabChange?: (tabId: string) => void;
  className?: string;
}

const VerticalTabs: React.FC<VerticalTabsProps> = ({
  tabs,
  defaultActiveTab,
  onTabChange,
  className = "",
}) => {
  const [activeTab, setActiveTab] = useState(defaultActiveTab || tabs[0]?.id);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    onTabChange?.(tabId);
  };

  const activeTabContent = tabs.find((tab) => tab.id === activeTab)?.content;
  const activeTabLabel = tabs.find((tab) => tab.id === activeTab)?.label;

  return (
    <div className={`flex h-full bg-white ${className}`}>
      {/* Left Sidebar - Vertical Tabs */}
      <div className="w-64 bg-gray-50 border-r border-gray-200">
        <nav className="flex flex-col" aria-label="Vertical Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.id)}
              className={`
                px-4 py-3 text-left text-sm font-medium transition-colors duration-200 border-b border-gray-200
                ${
                  activeTab === tab.id
                    ? "bg-[#E9F2FF]  text-blue-800 border-l-4 border-l-primary"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                }
              `}
              aria-current={activeTab === tab.id ? "page" : undefined}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Right Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Content Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              {activeTabLabel}
            </h2>
            <input
              type="date"
              className="px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="mm/dd/yyyy"
            />
          </div>
        </div>

        {/* Content Body */}
        <div className="flex-1 p-6 overflow-auto">{activeTabContent}</div>
      </div>
    </div>
  );
};

// Vital Signs Table Component
const VitalSignsTable: React.FC = () => {
  const vitalSignsData = [
    {
      date: "01/02/2025",
      time: "4:30 PM",
      user: "Rita",
      temp: "98°C",
      bpm: "90",
      sysbp: "100",
      disbp: "120",
      weight: "90 kg",
      bmi: "24",
    },
    {
      date: "01/01/2025",
      time: "2:15 PM",
      user: "John",
      temp: "97°C",
      bpm: "85",
      sysbp: "110",
      disbp: "70",
      weight: "88 kg",
      bmi: "23.5",
    },
    {
      date: "12/30/2024",
      time: "10:00 AM",
      user: "Sarah",
      temp: "98.5°C",
      bpm: "92",
      sysbp: "105",
      disbp: "75",
      weight: "91 kg",
      bmi: "24.2",
    },
    {
      date: "12/28/2024",
      time: "3:45 PM",
      user: "Mike",
      temp: "97.8°C",
      bpm: "88",
      sysbp: "115",
      disbp: "80",
      weight: "89 kg",
      bmi: "23.8",
    },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              DATE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              TIME
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              USER
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              TEMP
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              BPM
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              SYSBP
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              DISBP
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              WEIGHT
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              BMI
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {vitalSignsData.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-4 py-2 text-sm text-gray-900">{row.date}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.time}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.user}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.temp}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.bpm}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.sysbp}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.disbp}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.weight}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.bmi}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Medical Administration Table Component
const MedicalAdministrationTable: React.FC = () => {
  const medicationData = [
    {
      medication: "Lisinopril",
      dosage: "10mg",
      frequency: "Once daily",
      route: "Oral",
      prescriber: "Dr. Smith",
      startDate: "12/15/2024",
      status: "Active",
    },
    {
      medication: "Metformin",
      dosage: "500mg",
      frequency: "Twice daily",
      route: "Oral",
      prescriber: "Dr. Johnson",
      startDate: "11/20/2024",
      status: "Active",
    },
    {
      medication: "Atorvastatin",
      dosage: "20mg",
      frequency: "Once daily",
      route: "Oral",
      prescriber: "Dr. Smith",
      startDate: "10/05/2024",
      status: "Active",
    },
    {
      medication: "Aspirin",
      dosage: "81mg",
      frequency: "Once daily",
      route: "Oral",
      prescriber: "Dr. Wilson",
      startDate: "09/12/2024",
      status: "Discontinued",
    },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              MEDICATION
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              DOSAGE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              FREQUENCY
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              ROUTE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              PRESCRIBER
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              START DATE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              STATUS
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {medicationData.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-4 py-2 text-sm text-gray-900 font-medium">
                {row.medication}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.dosage}</td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.frequency}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.route}</td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.prescriber}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.startDate}
              </td>
              <td className="px-4 py-2 text-sm">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    row.status === "Active"
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {row.status}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Allergies Table Component
const AllergiesTable: React.FC = () => {
  const allergiesData = [
    {
      allergen: "Penicillin",
      reaction: "Skin rash, hives",
      severity: "Moderate",
      onsetDate: "03/15/2020",
      notes: "Developed after antibiotic treatment",
    },
    {
      allergen: "Shellfish",
      reaction: "Swelling, difficulty breathing",
      severity: "Severe",
      onsetDate: "07/22/2018",
      notes: "Anaphylactic reaction to shrimp",
    },
    {
      allergen: "Pollen",
      reaction: "Sneezing, watery eyes",
      severity: "Mild",
      onsetDate: "04/10/2015",
      notes: "Seasonal allergies, spring months",
    },
    {
      allergen: "Latex",
      reaction: "Contact dermatitis",
      severity: "Moderate",
      onsetDate: "11/05/2019",
      notes: "Reaction to medical gloves",
    },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              ALLERGEN
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              REACTION
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              SEVERITY
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              ONSET DATE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              NOTES
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {allergiesData.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-4 py-2 text-sm text-gray-900 font-medium">
                {row.allergen}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.reaction}
              </td>
              <td className="px-4 py-2 text-sm">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    row.severity === "Severe"
                      ? "bg-red-100 text-red-800"
                      : row.severity === "Moderate"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-green-100 text-green-800"
                  }`}
                >
                  {row.severity}
                </span>
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.onsetDate}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.notes}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Immunizations Table Component
const ImmunizationsTable: React.FC = () => {
  const immunizationData = [
    {
      vaccine: "COVID-19 (Pfizer)",
      dateGiven: "12/15/2024",
      provider: "CVS Pharmacy",
      lotNumber: "EW0182",
      nextDue: "12/15/2025",
      status: "Complete",
    },
    {
      vaccine: "Influenza",
      dateGiven: "10/20/2024",
      provider: "Dr. Smith's Office",
      lotNumber: "FL2024",
      nextDue: "10/20/2025",
      status: "Complete",
    },
    {
      vaccine: "Tdap",
      dateGiven: "08/12/2021",
      provider: "Community Health Center",
      lotNumber: "TD2021",
      nextDue: "08/12/2031",
      status: "Complete",
    },
    {
      vaccine: "Hepatitis B",
      dateGiven: "06/05/2020",
      provider: "Travel Clinic",
      lotNumber: "HB2020",
      nextDue: "N/A",
      status: "Complete",
    },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              VACCINE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              DATE GIVEN
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              PROVIDER
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              LOT NUMBER
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              NEXT DUE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              STATUS
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {immunizationData.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-4 py-2 text-sm text-gray-900 font-medium">
                {row.vaccine}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.dateGiven}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.provider}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.lotNumber}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.nextDue}</td>
              <td className="px-4 py-2 text-sm">
                <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                  {row.status}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Chronic Conditions Table Component
const ChronicConditionsTable: React.FC = () => {
  const conditionsData = [
    {
      condition: "Type 2 Diabetes",
      diagnosisDate: "03/15/2020",
      status: "Active",
      severity: "Moderate",
      managingPhysician: "Dr. Johnson",
    },
    {
      condition: "Hypertension",
      diagnosisDate: "01/22/2019",
      status: "Active",
      severity: "Mild",
      managingPhysician: "Dr. Smith",
    },
    {
      condition: "Hyperlipidemia",
      diagnosisDate: "06/10/2021",
      status: "Active",
      severity: "Mild",
      managingPhysician: "Dr. Smith",
    },
    {
      condition: "Osteoarthritis",
      diagnosisDate: "09/05/2022",
      status: "Active",
      severity: "Moderate",
      managingPhysician: "Dr. Wilson",
    },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              CONDITION
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              DIAGNOSIS DATE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              STATUS
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              SEVERITY
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              MANAGING PHYSICIAN
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {conditionsData.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-4 py-2 text-sm text-gray-900 font-medium">
                {row.condition}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.diagnosisDate}
              </td>
              <td className="px-4 py-2 text-sm">
                <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                  {row.status}
                </span>
              </td>
              <td className="px-4 py-2 text-sm">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    row.severity === "Severe"
                      ? "bg-red-100 text-red-800"
                      : row.severity === "Moderate"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-green-100 text-green-800"
                  }`}
                >
                  {row.severity}
                </span>
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.managingPhysician}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Surgical History Table Component
const SurgicalHistoryTable: React.FC = () => {
  const surgeryData = [
    {
      procedure: "Appendectomy",
      date: "08/15/2018",
      surgeon: "Dr. Martinez",
      hospital: "General Hospital",
      outcome: "Successful",
      complications: "None",
    },
    {
      procedure: "Cataract Surgery (Right Eye)",
      date: "03/22/2021",
      surgeon: "Dr. Lee",
      hospital: "Eye Care Center",
      outcome: "Successful",
      complications: "None",
    },
    {
      procedure: "Knee Arthroscopy",
      date: "11/10/2022",
      surgeon: "Dr. Wilson",
      hospital: "Orthopedic Clinic",
      outcome: "Successful",
      complications: "Minor swelling",
    },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              PROCEDURE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              DATE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              SURGEON
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              HOSPITAL
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              OUTCOME
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              COMPLICATIONS
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {surgeryData.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-4 py-2 text-sm text-gray-900 font-medium">
                {row.procedure}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.date}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.surgeon}</td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.hospital}
              </td>
              <td className="px-4 py-2 text-sm">
                <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                  {row.outcome}
                </span>
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.complications}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Family History Table Component
const FamilyHistoryTable: React.FC = () => {
  const familyData = [
    {
      relative: "Father",
      condition: "Hypertension",
      ageOfOnset: "45",
      status: "Deceased",
      ageAtDeath: "72",
      causeOfDeath: "Heart attack",
    },
    {
      relative: "Mother",
      condition: "Type 2 Diabetes",
      ageOfOnset: "52",
      status: "Living",
      ageAtDeath: "N/A",
      causeOfDeath: "N/A",
    },
    {
      relative: "Brother",
      condition: "High Cholesterol",
      ageOfOnset: "38",
      status: "Living",
      ageAtDeath: "N/A",
      causeOfDeath: "N/A",
    },
    {
      relative: "Maternal Grandmother",
      condition: "Breast Cancer",
      ageOfOnset: "65",
      status: "Deceased",
      ageAtDeath: "78",
      causeOfDeath: "Cancer",
    },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              RELATIVE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              CONDITION
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              AGE OF ONSET
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              STATUS
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              AGE AT DEATH
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              CAUSE OF DEATH
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {familyData.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-4 py-2 text-sm text-gray-900 font-medium">
                {row.relative}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.condition}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.ageOfOnset}
              </td>
              <td className="px-4 py-2 text-sm">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    row.status === "Living"
                      ? "bg-green-100 text-green-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  {row.status}
                </span>
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.ageAtDeath}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.causeOfDeath}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Social History Component
const SocialHistoryContent: React.FC = () => {
  const socialData = [
    {
      category: "Smoking",
      status: "Former smoker",
      details: "Quit 5 years ago, 10 pack-year history",
      lastUpdated: "01/15/2024",
    },
    {
      category: "Alcohol",
      status: "Occasional",
      details: "1-2 drinks per week, wine with dinner",
      lastUpdated: "01/15/2024",
    },
    {
      category: "Exercise",
      status: "Regular",
      details: "Walking 30 minutes, 4-5 times per week",
      lastUpdated: "01/15/2024",
    },
    {
      category: "Diet",
      status: "Balanced",
      details: "Mediterranean diet, low sodium",
      lastUpdated: "01/15/2024",
    },
    {
      category: "Occupation",
      status: "Active",
      details: "Office worker, sedentary job",
      lastUpdated: "01/15/2024",
    },
    {
      category: "Marital Status",
      status: "Married",
      details: "Married for 25 years, supportive spouse",
      lastUpdated: "01/15/2024",
    },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              CATEGORY
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              STATUS
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              DETAILS
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              LAST UPDATED
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {socialData.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-4 py-2 text-sm text-gray-900 font-medium">
                {row.category}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.status}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.details}</td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.lastUpdated}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Visit History Table Component
const VisitHistoryTable: React.FC = () => {
  const visitData = [
    {
      date: "01/02/2025",
      time: "2:30 PM",
      provider: "Dr. Smith",
      reason: "Annual Physical",
      type: "Routine",
      duration: "45 min",
      notes: "Normal examination, labs ordered",
    },
    {
      date: "12/15/2024",
      time: "10:00 AM",
      provider: "Dr. Johnson",
      reason: "Diabetes Follow-up",
      type: "Follow-up",
      duration: "30 min",
      notes: "A1C improved, medication adjusted",
    },
    {
      date: "11/20/2024",
      time: "3:15 PM",
      provider: "Dr. Wilson",
      reason: "Knee Pain",
      type: "Consultation",
      duration: "20 min",
      notes: "Referred to orthopedic specialist",
    },
    {
      date: "10/05/2024",
      time: "9:00 AM",
      provider: "Nurse Rita",
      reason: "Flu Vaccination",
      type: "Immunization",
      duration: "15 min",
      notes: "No adverse reactions",
    },
  ];

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead className="bg-gray-100">
          <tr>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              DATE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              TIME
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              PROVIDER
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              REASON
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              TYPE
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              DURATION
            </th>
            <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              NOTES
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {visitData.map((row, index) => (
            <tr key={index} className="hover:bg-gray-50">
              <td className="px-4 py-2 text-sm text-gray-900">{row.date}</td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.time}</td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.provider}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.reason}</td>
              <td className="px-4 py-2 text-sm">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    row.type === "Routine"
                      ? "bg-blue-100 text-blue-800"
                      : row.type === "Follow-up"
                      ? "bg-green-100 text-green-800"
                      : row.type === "Consultation"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-purple-100 text-purple-800"
                  }`}
                >
                  {row.type}
                </span>
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">
                {row.duration}
              </td>
              <td className="px-4 py-2 text-sm text-gray-900">{row.notes}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Main App Component
const MedicalRecordsApp: React.FC = () => {
  const medicalTabs: VerticalTabItem[] = [
    {
      id: "vital-signs",
      label: "Vital Signs",
      content: <VitalSignsTable />,
    },
    {
      id: "medical-administration",
      label: "Medical Administration",
      content: <MedicalAdministrationTable />,
    },
    {
      id: "allergies",
      label: "Allergies",
      content: <AllergiesTable />,
    },
    {
      id: "immunizations",
      label: "Immunizations",
      content: <ImmunizationsTable />,
    },
    {
      id: "chronic-conditions",
      label: "Chronic Conditions",
      content: <ChronicConditionsTable />,
    },
    {
      id: "surgical-history",
      label: "Surgical History",
      content: <SurgicalHistoryTable />,
    },
    {
      id: "family-history",
      label: "Family History",
      content: <FamilyHistoryTable />,
    },
    {
      id: "social-history",
      label: "Social History",
      content: <SocialHistoryContent />,
    },
    {
      id: "visit-history",
      label: "Visit History",
      content: <VisitHistoryTable />,
    },
  ];

  return (
    <div className="h-screen">
      <VerticalTabs
        tabs={medicalTabs}
        defaultActiveTab="vital-signs"
        onTabChange={(tabId) => console.log("Active tab:", tabId)}
        className="h-full"
      />
    </div>
  );
};

export default MedicalRecordsApp;
