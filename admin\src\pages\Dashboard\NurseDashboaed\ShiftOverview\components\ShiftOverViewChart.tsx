import React from "react";
import { Icon } from "@iconify/react";

const ShiftOverViewChart = () => {
  const shifts = [
    {
      day: "Sunday",
      time: "Morning",
      ward: "ICU",
      patients: 6,
      nurse: "Nurse <PERSON><PERSON>",
    },
    {
      day: "Monday",
      time: "Night",
      ward: "General Ward",
      patients: 12,
      nurse: "Nurse <PERSON><PERSON>",
    },
    {
      day: "Tuesday",
      time: "Morning",
      ward: "Emergency",
      patients: 4,
      nurse: "Nurse <PERSON>",
    },
    {
      day: "Wednesday",
      time: "Afternoon",
      ward: "Pediatrics",
      patients: 8,
      nurse: "Nurse <PERSON><PERSON>",
    },
    {
      day: "Thursday",
      time: "Morning",
      ward: "ICU",
      patients: 7,
      nurse: "Nurse <PERSON>",
    },
    {
      day: "Friday",
      time: "Night",
      ward: "General Ward",
      patients: 11,
      nurse: "Nurse <PERSON><PERSON>",
    },
    {
      day: "Saturday",
      time: "Morning",
      ward: "Maternity",
      patients: 5,
      nurse: "Nurse <PERSON>",
    },
  ];

  const days = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  const times = [
    { label: "Morning", hours: "7:00 AM - 3:00 PM" },
    { label: "Afternoon", hours: "3:00 PM - 11:00 PM" },
    { label: "Night", hours: "11:00 PM - 7:00 AM" },
  ];

  return (
    <div className='overflow-auto rounded-lg shadow-md '>
      <div className='min-w-[800px] bg-white shadow-lg p-2 rounded'>
        <h2 className='mb-2 text-xl font-semibold text-left'>
          My Shift Overview
        </h2>
        <div className='grid grid-cols-[150px_repeat(7,1fr)] border  '>
          {/* Header Row */}
          <div className='p-3 font-semibold bg-[#b3b3b3] border '></div>
          {days.map((day) => (
            <div
              key={day}
              className='p-3 font-semibold text-center bg-[#b3b3b3] border '
            >
              {day}
            </div>
          ))}

          {/* Time Slots */}
          {times.map((timeSlot) => (
            <React.Fragment key={timeSlot.label}>
              <div className='flex flex-col justify-center p-2 border bg-[#e9f2ff] border-[#b3b3b3]'>
                <div className='font-semibold text-center'>
                  {timeSlot.label}
                </div>
                <div className='text-sm text-center'>{timeSlot.hours}</div>
              </div>
              {days.map((day) => {
                const shift = shifts.find(
                  (s) => s.day === day && s.time === timeSlot.label
                );
                return (
                  <div
                    key={day + timeSlot.label}
                    className='relative h-24 border bg-[#e9f2ff] border-[#b3b3b3]'
                  >
                    {shift ? (
                      <div
                        className='absolute inset-0 flex flex-col p-3 text-xs text-black justify-evenly'
                        style={{ backgroundColor: "#8cc4ff" }}
                      >
                        <div>
                          <div className='mb-1 font-semibold'>{shift.ward}</div>
                          <div className='flex items-center gap-1'>
                            <Icon icon='ic:baseline-people' />
                            {shift.patients} Patients
                          </div>
                          <div className='flex items-center gap-1'>
                            <Icon icon='healthicons:nurse' />
                            {shift.nurse}
                          </div>
                        </div>
                        <div className='flex items-center gap-1'>
                          <Icon icon='mdi:clock' />
                          10–10:30 AM
                        </div>
                      </div>
                    ) : null}
                  </div>
                );
              })}
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ShiftOverViewChart;
