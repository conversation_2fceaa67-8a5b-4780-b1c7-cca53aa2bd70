import { useParams } from "react-router-dom";
import { useMemo, useCallback } from "react";
import { useGetUserById } from "../../../../../server-action/api/user";
import { useGetAllAppointments } from "../../../../../server-action/api/appointmentApi";
import { useGetShiftAssign } from "../../../../../server-action/api/shiftAssignApi";
import { HospitalLoader } from "../../../../Loader";
import dayjs from "dayjs";
import DoctorHeader from "./Components/DoctorHeader";
import PersonalProfessionalDetails from "../components/PersonalProfessionalDetails";
import ContactInfo from "../components/ContactInfo";
import AboutSection from "../components/AboutSection";
import Documents from "../components/Documents";
import BankDetails from "../components/BankDetails";
import MonthlySchedule from "../components/MonthlySchedule";
import Appointments from "./Components/Appointments";
import PatientReviews from "../components/PatientReviews";

const DoctorDetailsPage = () => {
  const { id } = useParams();
  const { data: userData, isLoading } = useGetUserById(id as string);
  const { data: appointmentsApiData, isLoading: appointmentsLoading } =
    useGetAllAppointments({
      limit: 100,
    });

  const { data: shiftApiData } = useGetShiftAssign();

  const { data: doctorShiftData } = useGetShiftAssign(
    id ? { user: id } : undefined,
    {
      enabled: Boolean(id),
      refetchOnMount: true,
      refetchOnWindowFocus: true,
    }
  );

  const memoizedUserData = useMemo(() => userData, [userData]);

  const documents = useMemo(() => {
    if (!userData) return [];

    const allDocuments: any[] = [];

    if (userData?.identityInformation?.identityDocuments) {
      userData.identityInformation.identityDocuments.forEach((doc: any) => {
        if (doc.documentImages && doc.documentImages.length > 0) {
          doc.documentImages.forEach((image: string, index: number) => {
            if (image && image.trim() !== "") {
              allDocuments.push({
                name: `${doc.documentType || "Identity Document"} `,
                type: "identity",
                hasView: Boolean(image),
                hasDownload: Boolean(image),
                url: image,
                id: `${doc._id || doc.documentNumber}-${index}`,
              });
            }
          });
        }
      });
    }

    if (
      userData?.professionalDetails?.education &&
      Array.isArray(userData.professionalDetails.education)
    ) {
      userData.professionalDetails.education.forEach(
        (edu: any, eduIndex: number) => {
          if (edu.documentImages && edu.documentImages.length > 0) {
            edu.documentImages.forEach((image: string, imageIndex: number) => {
              if (image && image.trim() !== "") {
                allDocuments.push({
                  name: `${`Academic Document (${
                    edu.qualification || "Unknown"
                  })`}`,
                  type: "education",
                  hasView: Boolean(image),
                  hasDownload: Boolean(image),
                  url: image,
                  id: `${edu._id || `edu-${eduIndex}`}-${imageIndex}`,
                  details: {
                    degree: edu.qualification,
                    institution: edu.qualificationInstitution,
                    year: edu.startDate || edu.qualifiedDate,
                    grade: edu.licenceNo,
                  },
                });
              }
            });
          }
        }
      );
    }

    return allDocuments;
  }, [userData]);

  const calculateAge = useCallback((dateOfBirth: string | Date) => {
    if (!dateOfBirth) return "N/A";

    const today = new Date();
    const birthDate = new Date(dateOfBirth);

    if (isNaN(birthDate.getTime())) return "N/A";

    if (birthDate > today) {
      return "Future Date";
    }

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age >= 0 ? `${age} Yrs` : "N/A";
  }, []);

  const doctorData = useMemo(
    () => ({
      name: userData?.commonInfo?.personalInfo?.fullName || "N/A",
      specialization: `${
        userData?.departmentDetails?.speciality?.name || "N/A"
      }, ${userData?.professionalDetails?.academicDegree || ""}`,
      doctorId: `D-${
        userData?._id?.slice(userData?._id?.length - 5, userData?._id.length) ||
        "N/A"
      }`,
      image:
        userData?.identityInformation?.profileImage ||
        "https://via.placeholder.com/150",
      isAvailable: userData?.isActive || false,
      phoneNumber:
        userData?.commonInfo?.contactInfo?.phone?.primaryPhone || "N/A",
      email: userData?.email || "N/A",
    }),
    [userData]
  );

  const personalDetails = useMemo(
    () => ({
      fullName: userData?.commonInfo?.personalInfo?.fullName || "N/A",
      age: calculateAge(userData?.commonInfo?.personalInfo?.dob),
      gender: userData?.commonInfo?.personalInfo?.gender || "N/A",
      dateOfBirth: userData?.commonInfo?.personalInfo?.dob
        ? new Date(userData.commonInfo.personalInfo.dob).toLocaleDateString()
        : "N/A",
      maritalStatus: userData?.commonInfo?.personalInfo?.maritalStatus || "N/A",
      bloodGroup: userData?.commonInfo?.personalInfo?.bloodGroup || "N/A",
      religion: userData?.commonInfo?.personalInfo?.religion || "N/A",
      language: userData?.commonInfo?.personalInfo?.language || "N/A",
      yearsofExperience: userData?.experienceDetails?.yearOfExperience || "N/A",
      joinedDate: userData?.experienceDetails?.joinedDate
        ? new Date(userData.experienceDetails.joinedDate).toLocaleDateString()
        : "N/A",
    }),
    [userData]
  );

  const professionalDetails = useMemo(
    () => ({
      doctorId: `D-${userData?._id?.slice(-5)}` || "N/A",
      academicDegree:
        userData?.professionalDetails?.education[0]?.fieldOfStudy || "N/A",
      designation: userData?.professionalDetails?.designation || "N/A",
      specialization: userData?.departmentDetails?.speciality?.name || "N/A",
      department: userData?.departmentDetails?.hirachyFirst?.name || "N/A",
      yearsOfExperience: userData?.experienceDetails?.totalExperience || "N/A",
      licenseNumber: userData?.professionalDetails?.licenseNumber || "N/A",
      joiningDate: userData?.experienceDetails?.joinedDate
        ? new Date(userData.experienceDetails.joinedDate).toLocaleDateString()
        : "N/A",
      institution:
        userData?.professionalDetails?.education[0]?.institution || "N/A",
      fieldOfStudy:
        userData?.professionalDetails?.education[0]?.fieldOfStudy || "N/A",
    }),
    [userData]
  );

  const contactInfo = useMemo(
    () => ({
      phoneNumber:
        userData?.commonInfo?.contactInfo?.phone?.primaryPhone || "N/A",
      secondaryPhone:
        userData?.commonInfo?.contactInfo?.phone?.secondaryPhone || "N/A",
      email: userData?.email || "N/A",
      address:
        `${userData?.commonInfo?.contactInfo?.address.currentAddress || ""}, ${
          userData?.commonInfo?.contactInfo?.address?.city || ""
        }`
          .trim()
          .replace(/^,|,$/, "") || "N/A",
      emergencyContact:
        userData?.commonInfo?.contactInfo?.phone?.emergency ||
        userData?.commonInfo?.contactInfo?.phone?.secondary ||
        "N/A",
    }),
    [userData]
  );

  const aboutData = useMemo(
    () => ({
      description:
        userData?.commonInfo?.generalDescription ||
        userData?.professionalDetails?.description ||
        "No description available.",
      specializedIn:
        userData?.professionalDetails?.specializations ||
        userData?.professionalDetails?.skills ||
        [
          userData?.departmentDetails?.speciality?.name || "General Practice",
        ].filter(Boolean),
    }),
    [userData]
  );

  const bankDetails = useMemo(
    () => ({
      accountHolderName:
        userData?.professionalDetails?.accountDetails?.accountHolderName ||
        userData?.commonInfo?.personalInfo?.fullName ||
        "N/A",
      bankName:
        userData?.professionalDetails?.accountDetails?.bankName || "N/A",
      accountNumber:
        userData?.professionalDetails?.accountDetails?.accountNumber || "N/A",
      taxId:
        userData?.bankDetails?.taxId ||
        userData?.professionalDetails?.pan ||
        "N/A",
      basicSalary:
        userData?.professionalDetails?.salary ||
        userData?.bankDetails?.basicSalary ||
        "N/A",
    }),
    [userData]
  );

  const scheduleData = {
    currentMonth: "July",
    currentYear: 2025,
    days: [
      { date: 30, isDisabled: true },
      { date: 1 },
      { date: 2 },
      { date: 3 },
      { date: 4 },
      { date: 5 },
      { date: 6 },
      { date: 7 },
      { date: 8 },
      { date: 9 },
      { date: 10 },
      { date: 11 },
      { date: 12 },
      { date: 13 },
      { date: 14 },
      { date: 15 },
      { date: 16 },
      { date: 17, isToday: true },
      { date: 18 },
      { date: 19 },
      { date: 20 },
      { date: 21 },
      { date: 22 },
      { date: 23 },
      { date: 24 },
      { date: 25 },
      { date: 26 },
      { date: 27 },
      { date: 28 },
      { date: 29 },
      { date: 30 },
      { date: 31 },
      { date: 1, isDisabled: true },
      { date: 2, isDisabled: true },
      { date: 3, isDisabled: true },
    ],
  };

  const appointmentsData = useMemo(() => {
    const allAppointments =
      (appointmentsApiData as any)?.data?.appointments || [];
    const today = dayjs();

    if (!Array.isArray(allAppointments)) {
      return { upcoming: [], history: [] };
    }

    const doctorAppointments = allAppointments.filter((appointment: any) => {
      const appointmentDoctorId =
        appointment.doctor?._id || appointment.doctor?.id;
      return appointmentDoctorId === id;
    });

    if (doctorAppointments.length === 0) {
      return { upcoming: [], history: [] };
    }

    const upcoming = doctorAppointments
      .filter((appointment: any) => {
        const appointmentDate = dayjs(appointment.date);
        const appointmentTime = appointment.timeSlot || "00:00";

        const appointmentDateTime = appointmentDate
          .hour(parseInt(appointmentTime.split(":")[0]))
          .minute(parseInt(appointmentTime.split(":")[1]));

        return (
          appointmentDateTime.isAfter(today) ||
          appointmentDateTime.isSame(today, "hour")
        );
      })
      .map((appointment: any) => ({
        id: appointment._id,
        type: appointment.treatment || "General Consultation",
        patientName:
          appointment.user?.commonInfo?.personalInfo?.fullName || "N/A",
        date: dayjs(appointment.date).format("ddd, MMM D YYYY"),
        time: appointment.timeSlot || "N/A",
        status: (appointment.status === "CONFIRMED"
          ? "Confirmed"
          : appointment.status === "COMPLETED"
          ? "Completed"
          : "Upcoming") as "Confirmed" | "Completed" | "Upcoming",
      }));

    const history = doctorAppointments
      .filter((appointment: any) => {
        const appointmentDate = dayjs(appointment.date);
        const appointmentTime = appointment.timeSlot || "00:00";

        const appointmentDateTime = appointmentDate
          .hour(parseInt(appointmentTime.split(":")[0]))
          .minute(parseInt(appointmentTime.split(":")[1]));

        return (
          appointmentDateTime.isBefore(today) &&
          !appointmentDateTime.isSame(today, "hour")
        );
      })
      .map((appointment: any) => ({
        id: appointment._id,
        type: appointment.treatment || "General Consultation",
        patientName:
          appointment.user?.commonInfo?.personalInfo?.fullName || "N/A",
        date: dayjs(appointment.date).format("ddd, MMM D YYYY"),
        time: appointment.timeSlot || "N/A",
        status: "Completed" as "Completed",
      }));

    return { upcoming, history };
  }, [appointmentsApiData, id]);

  const todayShiftSlots = useMemo(() => {
    if (!(doctorShiftData as any)?.data?.shiftassigned) return [];

    const today = dayjs().format("YYYY-MM-DD");
    const timeSlotMap = new Map();

    const todayUpcomingAppointments = appointmentsData.upcoming.filter(
      (apt: any) => {
        const appointmentDate = dayjs(apt.date).format("YYYY-MM-DD");
        return appointmentDate === today;
      }
    );

    const todayHistoryAppointments = appointmentsData.history.filter(
      (apt: any) => {
        const appointmentDate = dayjs(apt.date).format("YYYY-MM-DD");
        return appointmentDate === today;
      }
    );

    (doctorShiftData as any).data.shiftassigned.forEach((entry: any) => {
      if (entry?.shiftAssignment) {
        entry.shiftAssignment.forEach((sa: any) => {
          if (sa?.date && sa?.shifts) {
            const shiftDate = dayjs(sa.date).format("YYYY-MM-DD");
            if (shiftDate === today) {
              sa.shifts.forEach((shiftItem: any) => {
                const slots = shiftItem.slots || [];

                slots.forEach((slot: any) => {
                  if (slot.isAvailiable && slot.timeRange) {
                    const [startTime, endTime] = slot.timeRange.split("-");
                    const slotStartTime = startTime.trim();
                    const slotEndTime = endTime.trim();
                    const timeKey = `${slotStartTime} - ${slotEndTime}`;

                    const upcomingInSlot = todayUpcomingAppointments.filter(
                      (apt: any) => {
                        const appointmentTime = apt.time;
                        return (
                          appointmentTime >= slotStartTime &&
                          appointmentTime < slotEndTime
                        );
                      }
                    );

                    const historyInSlot = todayHistoryAppointments.filter(
                      (apt: any) => {
                        const appointmentTime = apt.time;
                        return (
                          appointmentTime >= slotStartTime &&
                          appointmentTime < slotEndTime
                        );
                      }
                    );

                    if (
                      (upcomingInSlot.length > 0 || historyInSlot.length > 0) &&
                      !timeSlotMap.has(timeKey)
                    ) {
                      const totalAppointments =
                        upcomingInSlot.length + historyInSlot.length;
                      const allAppointments = [
                        ...upcomingInSlot,
                        ...historyInSlot,
                      ];

                      timeSlotMap.set(timeKey, {
                        id: slot._id || `slot-${Date.now()}`,
                        time: timeKey,
                        appointmentCount: totalAppointments,
                        appointments: allAppointments,
                        type: "combined",
                      });
                    }
                  }
                });
              });
            }
          }
        });
      }
    });

    const todaySlots = Array.from(timeSlotMap.values());

    if (todaySlots.length === 0) {
      return [
        {
          id: "no-shifts",
          time: "No shifts scheduled for today",
          appointmentCount: 0,
        },
      ];
    }

    return todaySlots;
  }, [doctorShiftData, appointmentsData]);

  const todayScheduleData = useMemo(() => {
    const shiftAssignments = (shiftApiData as any)?.data?.shiftassigned || [];
    const doctorShift = shiftAssignments.find(
      (shift: any) => shift.user?._id === id
    );

    if (!doctorShift?.shiftAssignment) return [];

    const today = dayjs().format("YYYY-MM-DD");
    const todayShift = doctorShift.shiftAssignment.find(
      (assignment: any) => dayjs(assignment.date).format("YYYY-MM-DD") === today
    );

    if (!todayShift?.shifts) return [];

    return todayShift.shifts.map((shift: any, index: number) => ({
      id: `shift-${index}`,
      time: `${shift.startTime} - ${shift.endTime}`,
      appointmentCount: appointmentsData.upcoming.length,
    }));
  }, [shiftApiData, id, appointmentsData.upcoming.length]);

  const reviewsData = useMemo(() => {
    if (!userData?.reviews || userData.reviews.length === 0) {
      return [];
    }

    return userData.reviews.map((review: any, index: number) => ({
      id: review._id || `review-${index}`,
      patientName: review.reviewerName || "Anonymous",
      rating: review.reviewerRating || 0,
      comment: review.reviewerComment || "No comment provided",
      isRecommended: review.reviewerRating > 3,
      date: review.reviewerDate || new Date().toISOString(),
      designation: review.reviewerDesignation || "",
      institution: review.reviewerInstitution || "",
      email: review.reviewerEmail || "",
    }));
  }, [userData?.reviews]);

  if (isLoading || appointmentsLoading || !userData) {
    return <HospitalLoader />;
  }

  return (
    <div className="mb-2" style={{ fontFamily: "roboto" }}>
      <div className=" mx-auto space-y-2">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-2">
          <div className="lg:col-span-3 flex flex-col gap-2">
            <DoctorHeader doctor={doctorData} contactInfo={contactInfo} />
            <div className="grid grid-cols-3 gap-2">
              <div className="col-span-2">
                <PersonalProfessionalDetails
                  personalDetails={personalDetails}
                  professionalDetails={professionalDetails}
                  staffType="Doctor"
                />
              </div>
              <ContactInfo contactInfo={contactInfo} />
            </div>
            <AboutSection about={aboutData} />
            <div className="grid gap-2 grid-cols-2">
              <Documents documents={documents} />
              <BankDetails bankDetails={bankDetails} />
            </div>
            <PatientReviews reviews={reviewsData} />
          </div>
          <div className="flex flex-col gap-2">
            <MonthlySchedule
              schedule={scheduleData}
              todaySchedule={todayShiftSlots}
              shiftData={doctorShiftData}
              doctorId={id}
              doctorData={memoizedUserData}
              staffType="Doctor"
            />
            <Appointments appointments={appointmentsData} />
          </div>
        </div>
        <div></div>
      </div>
    </div>
  );
};

export default DoctorDetailsPage;
