import{a5 as a,cs as $,av as g,cr as G,a2 as e,d3 as K,ct as W,aM as q,bK as z,ah as Q,a7 as V,b3 as J,aj as O}from"./index-ClX9RVH0.js";const X=a.forwardRef(({data:r,type:j,status:o},x)=>{var u,b,m,f;const{data:n,isSuccess:y}=$();console.log(n,"hospitalInfo"),g.get(n,"data.hospital"),console.log(o,"status"),console.log(r,"data");const i=(b=(u=r==null?void 0:r.data)==null?void 0:u.invoices)==null?void 0:b.reduce((s,h)=>s+(h.totalAmount||0),0),v=G(Math.abs(i??0),{currency:"NRS",locale:"IN"});return e.jsxs("div",{className:" p-2 bg-white",ref:x,children:[e.jsx(K,{}),e.jsx("div",{className:"text-center mb-6",children:e.jsx("h2",{className:"text-lg font-semibold text-gray-800",children:j})}),e.jsx("div",{className:"overflow-x-auto border border-gray-800 mb-4 rounded-md",children:e.jsxs("table",{className:"min-w-full text-sm text-left text-gray-700",children:[e.jsx("thead",{className:"bg-gray-50 border-b border-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Date"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Invoice No"}),e.jsx("th",{className:"px-4 py-2 text-center border-r border-gray-800 font-medium",children:"Inventory"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Payment Method"}),e.jsx("th",{className:"px-4 py-2 border-r border-gray-800 text-center font-medium",children:"Total Amount"})]})}),e.jsx("tbody",{children:g.get(r,"data.invoices",[]).map((s,h)=>{var l;return e.jsxs("tr",{className:"border-b border-gray-200 hover:bg-gray-100 break-inside-avoid-page",children:[e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center max-w-[200px]",children:(s==null?void 0:s.date)||"-"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center whitespace-nowrap w-[100px]",children:(s==null?void 0:s.invoiceNo)||"-"}),e.jsx("td",{className:"px-4 py-2 w-[120px] border-r border-gray-800 text-xs text-center",children:(s==null?void 0:s.inventoryFor)||"-"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center w-fit whitespace-nowrap",children:(s==null?void 0:s.paymentMethod)||"-"}),e.jsx("td",{className:"px-4 py-2 border-r border-gray-800 text-xs text-center w-fit whitespace-nowrap",children:((l=s==null?void 0:s.totalAmount)==null?void 0:l.toFixed(2))||"0.00"})]},h)})})]})}),e.jsxs("div",{className:`flex justify-center py-2 items-center my-4 rounded-md border ${((f=(m=r==null?void 0:r.data)==null?void 0:m.invoices)==null?void 0:f.length)>17?"mt-24":""} border-gray-800 bg-gray-50 text-sm font-semibold`,children:[e.jsx("div",{className:"text-right",children:"Total Amount: Rs."}),e.jsx("div",{className:"text-right",children:i})]}),e.jsx("div",{className:"border border-gray-800 mb-4",children:e.jsxs("div",{className:"p-2 text-sm flex gap-4",children:[e.jsx("span",{className:"font-medium",children:"Amount in Words: "}),e.jsx("span",{className:"border-b border-gray-300 inline-block ",children:v})]})})]})}),Z=()=>{const[r,j]=a.useState(""),[o,x]=a.useState(""),[n,y]=a.useState(""),[i,v]=a.useState(""),[u,b]=a.useState(""),[m,f]=a.useState(""),[s,h]=a.useState(!1),[l,d]=a.useState([]),k=a.useRef(null),E=W.useReactToPrint({contentRef:k,pageStyle:`
        @page {
          size: A4;
          margin: 0.5in;
        }
        @media print {
          body {
            -webkit-print-color-adjust: exact;
          }
        }
      `}),N=o||n||r,M=N?q({page:1,limit:1e3,category:"PURCHASERETURN",startDate:o||void 0,endDate:n||void 0}):null,{data:A,isLoading:D}=z(M),S=N?g.get(A,"data",{}):{};console.log(A,"data"),a.useEffect(()=>{d([])},[]),a.useEffect(()=>{d([])},[o,n,r]);const H=()=>{const t=u||m?"ALL":"";x(u),y(m),v(t),j(i),d([]),h(!0)},L=()=>{x(""),y(""),j(""),b(""),f(""),v(""),d([]),h(!0)},B=(t,c)=>{d(t?c:[])},_=(t,c)=>{d(c?p=>[...p,t]:p=>p.filter(w=>w!==t))},F=()=>{const t=g.get(S,"invoices",[]);l.length>0&&t.filter(c=>l.includes(c._id)),setTimeout(()=>{E()},100)},R={columns:[{title:"Date",key:"date"},{title:"Vendor Name",key:"fullName"},{title:"Invoice No.",key:"invoiceNo"},{title:"Inventory",key:"inventoryFor"},{title:"Payment Method",key:"paymentMethod"},{title:"Payable Amount",key:"payableAmount"}],rows:g.get(S,"invoices",[]).map(t=>{var c,p,w,C,P,T;return{_id:t._id,date:new Date(t.date).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),invoiceNo:t==null?void 0:t.invoiceNo,fullName:((w=(p=(c=t==null?void 0:t.createdBy)==null?void 0:c.commonInfo)==null?void 0:p.personalInfo)==null?void 0:w.fullName)||((T=(P=(C=t==null?void 0:t.vendor)==null?void 0:C.commonInfo)==null?void 0:P.personalInfo)==null?void 0:T.fullName)||"N/A",predefinedBillNo:t==null?void 0:t.predefinedBillNo,inventoryFor:t==null?void 0:t.inventoryFor,paymentMethod:t.paymentMethod||"N/A",payableAmount:t==null?void 0:t.payableAmount,category:e.jsx(Q,{status:t.category,className:"px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"})}})},U=N?g.get(S,"invoices",[]).length:0,I=o||n||r;return e.jsxs("div",{className:"h-screen overflow-y-auto",children:[e.jsx(V,{listTitle:"Purchase Return",hideHeader:!0}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-end md:space-x-4 space-y-4 md:space-y-0",children:[e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),e.jsx("input",{type:"date",value:u,onChange:t=>b(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"w-full md:w-1/5",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),e.jsx("input",{type:"date",value:m,onChange:t=>f(t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"w-full md:w-auto flex gap-4 mt-2 md:mt-0",children:[e.jsx("button",{onClick:H,className:"w-full md:w-auto px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 transition-colors font-medium",children:"Apply"}),e.jsx("button",{onClick:L,className:"w-full md:w-auto px-4 py-2 bg-rose-500 text-white rounded-md hover:bg-rose-600 transition-colors font-medium",children:"Clear"}),e.jsxs("button",{onClick:F,disabled:!s||!i,className:`w-full md:w-auto px-4 py-2  text-white rounded-md  transition-colors font-medium
              ${s&&i?"bg-sky-500 text-white hover:bg-sky-700":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:[e.jsx(J,{icon:"material-symbols-light:print-outline",width:"24",height:"24"})," "]})]})]}),I&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex flex-wrap gap-2 items-center",children:[e.jsx("span",{className:"text-sm text-gray-600 font-medium",children:"Active filters:"}),o&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["From: ",o,e.jsx("button",{onClick:()=>{x(""),x("")},className:"ml-1 text-green-600 hover:text-green-800",children:"×"})]}),n&&e.jsxs("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:["To: ",n,e.jsx("button",{onClick:()=>{y(""),y("")},className:"ml-1 text-green-600 hover:text-green-800",children:"×"})]})]})})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-sm",children:[e.jsx("div",{className:"p-6 border-b border-gray-200",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Transaction Details"}),e.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:[N?D?"Loading...":`Showing ${U} transactions`:"Apply filters to view transactions",I&&N&&" (filtered)"]})]}),l.length>0&&e.jsxs("div",{className:"text-sm text-gray-600",children:[l.length," transaction(s) selected"]})]})}),e.jsx(O,{columns:R.columns,rows:R.rows,loading:D,color:"bg-gray-50",textcolor:"text-gray-600",selectedIds:l,onSelectAll:B,onSelectRow:_,primaryKey:"_id",onBulkAction:F,bulkActionLabel:"Print Selected",showBulkActions:!0})]}),e.jsx("section",{className:"hidden",children:e.jsx(X,{ref:k,data:A,type:"Purchase Return",status:i})})]})};export{Z as default};
