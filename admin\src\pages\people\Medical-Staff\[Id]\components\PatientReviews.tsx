import React from "react";
import { Icon } from "@iconify/react";

interface Review {
  id: string;
  patientName: string;
  rating: number;
  comment: string;
  isRecommended: boolean;
}

interface PatientReviewsProps {
  reviews: Review[];
  staffType?: "Doctor" | "Nurse" | "Staff";
  showEmptyMessage?: boolean;
}

const PatientReviews: React.FC<PatientReviewsProps> = ({
  reviews,
  staffType = "Doctor",
  showEmptyMessage = true,
}) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Icon
        key={index}
        icon="material-symbols:star"
        className={`w-4 h-4 ${
          index < rating ? "text-orange-400" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
      {/* Header */}
      <div className="bg-blue-50 px-4 py-3 border-b flex items-center justify-between">
        <h3 className="text-base font-semibold text-blue">Patient's Reviews</h3>
      </div>

      <div className="p-2">
        {reviews.length === 0 ? (
          showEmptyMessage && (
            <div className="text-center py-8">
              <Icon
                icon="material-symbols:rate-review"
                className="w-12 h-12 text-gray-600 mx-auto mb-3"
              />
              <p className="text-gray-500 text-sm">
                {staffType === "Nurse"
                  ? "Patient reviews not available"
                  : "No reviews available"}
              </p>
            </div>
          )
        ) : (
          <div
            className={`grid grid-cols-1 gap-4 ${
              staffType === "Nurse" ? "md:grid-cols-1" : "md:grid-cols-2"
            }  `}
          >
            {reviews.map((review) => (
              <div
                key={review.id}
                className="bg-blue/5 border border-blue/20 rounded-lg p-4"
              >
                {/* Review Text */}
                <p className="text-gray-700 text-sm mb-4 leading-relaxed">
                  {review.comment}
                </p>

                {/* Bottom Section */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <Icon
                        icon="material-symbols:person"
                        className="w-4 h-4 text-gray-600"
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {review.patientName}
                      </p>
                      {review.rating > 3 && (
                        <p className="text-xs text-gray-600 capitalize flex items-center gap-1">
                          <Icon
                            icon="material-symbols:thumb-up"
                            className="w-3 h-3"
                          />
                          {staffType === "Nurse"
                            ? "Recommend "
                            : `I recommend this ${staffType.toLowerCase()} `}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-0.5">
                    {renderStars(review.rating)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PatientReviews;
