import { Icon } from "@iconify/react/dist/iconify.js";
import { useGetDashboardStat } from "../../../../server-action/api/dasboard.api";
type DashboardCard = {
  title: string;
  value: string;
  change: string;
  iconColor: string;
  topIcon: string;
  color: string;
  status: string;
  bgColor: string;
};

const HeadNurseCard = () => {
  const { data: dashboardData } = useGetDashboardStat();
  console.log(dashboardData, "dashboard");
  const dashboardCards: DashboardCard[] = [
    {
      title: "Nurse on Duty",
      value: " 100 / 130",
      change: "+1.5%",
      iconColor: "#fb6262",
      topIcon: "fa6-solid:user-doctor",
      bgColor: "bg-[#faecec]",
      color: "#faecec",
      status: "Available",
    },
    {
      title: "No. of Patients",
      value: "30",
      change: "+8.5%",

      iconColor: "#3cd4ca",
      topIcon: "ph:money-fill",

      bgColor: "bg-[#cef9f6]",
      color: "#cef9f6",
      status: "Patients",
    },
    {
      title: "No. of Beds",
      value: "150",
      change: "+102",
      iconColor: "#44d2ac",
      topIcon: "mdi:bed-outline",
      bgColor: "bg-[#d8f7de]",
      color: "#d8f7de",
      status: "Available",
    },
    {
      title: "Bed Occupied",
      value: "130",
      change: "+8.5%",
      iconColor: "#b4b4b4",
      topIcon: "mdi:bed-outline",
      bgColor: "bg-[#efefef]",
      color: "#efefef",
      status: "Available",
    },
    {
      title: "Vacant Bed",
      value: "20",
      change: "+8.5%",

      iconColor: "#44d2ac",
      topIcon: "mdi:bed-outline",
      bgColor: "bg-[#9df2db]",
      color: "#9df2db",
      status: "Treated",
    },
  ];
  return (
    <div className="flex w-full justify-center items-center gap-5 overflow-hidden py-2    ">
      {dashboardCards?.map((card, index) => (
        <div
          className={` p-2 w-full rounded-lg shadow-md shadow-gray-300 ${card?.bgColor} `}
        >
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-sm text-gray-600">{card?.title}</h1>
            <div className="bg-white rounded-md p-0.5 ">
              <Icon
                icon={card?.topIcon}
                width="24"
                height="24"
                color={card?.iconColor}
              />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <p className="text-md font-semibold">{card?.value}</p>
            <div className="flex text-xs">
              <span className="flex gap-1 items-center text-green">
                {" "}
                <Icon icon="carbon:growth" width="16" height="16" />{" "}
                {card?.change}
              </span>
              {/* {card?.status} */}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default HeadNurseCard;
