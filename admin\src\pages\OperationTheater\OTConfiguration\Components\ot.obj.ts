import { get } from "lodash";
import { useGetAllSurgeryDepartment } from "../../../../server-action/api/OTAssignment";
import { useGetAllOperationTreature } from "../../../../server-action/api/operationTreature";
import { useGetUser } from "../../../../server-action/api/user";
import { IOptions } from "../../../IPD/GeneralWard/generalWardObj";
import { useGetBloodInventry } from "../../../../server-action/api/DonorInventryApi";
import dayjs from "dayjs";

export const surgerConfigColumns = [
  {
    title: "S.No",
    key: "sno",
  },
  {
    title: "Department",
    key: "department",
  },
  {
    title: "Sub Category",
    key: "subCategory",
  },
  {
    title: "Action",
    key: "action",
  },
];

export const otConfigColumns = [
  {
    title: "S.No",
    key: "sno",
  },
  {
    title: "OT Name",
    key: "otName",
  },
  {
    title: "Floor",
    key: "floor",
  },
  {
    title: "Department",
    key: "department",
  },
  { title: "Sub-Category", key: "subDepartment" },
  {
    title: "Action",
    key: "action",
  },
];

export const SurgeryFields = ({
  otList,
  doctorList,
  nurseList,
}: Record<string, IOptions[]>) => [
  {
    fields: [
      {
        label: "Patient Id",
        disabled: true,
        field: "patientId",
        type: "text",
      },
      {
        label: "Patient Name",
        disabled: true,
        field: "patientName",
        type: "text",
      },
      {
        label: "Contact Number",
        field: "contactNumber",
        type: "text",
        required: true,
      },
    ],
  },
  {
    fields: [
      {
        label: "Types of Surgery",
        field: "assignedSurgery.surgeryType",
        type: "text",
        required: true,
      },
      {
        label: "Surgery Cost",
        field: "assignedSurgery.surgeryCost",
        type: "text",
        required: true,
      },
      { label: "Surgery Date", field: "date", type: "date", required: true },
      {
        label: "Operation Theatre",
        field: "ot",
        type: "select",
        options: otList,
        required: true,
      },
    ],
  },
  {
    fields: [
      {
        label: "Fasting Status Confirmed",
        field: "preCheckups.fastingStatus",
        type: "radio",
        required: true,
        options: [
          { label: "Yes", value: "yes" },
          { label: "No", value: "no" },
        ],
      },
      {
        label: "Consent Firm Signed",
        field: "preCheckups.consentFormSigned",
        required: true,
        type: "radio",
        options: [
          { label: "Yes", value: "yes" },
          { label: "No", value: "no" },
        ],
      },
      {
        label: "Pre Surgery Checklist Completed",
        field: "preCheckups.preSurgeryChecklistCompleted",
        required: true,
        type: "radio",
        options: [
          { label: "Yes", value: "yes" },
          { label: "No", value: "no" },
        ],
      },
    ],
  },
  {
    fields: [
      {
        label: "Surgeons",
        field: "surgeon",
        type: "multiSelectWithChips",
        required: true,
        options: doctorList,
      },

      {
        label: "Anesthetist",
        field: "anesthesiologists",
        type: "multiSelectWithChips",
        required: true,
        options: doctorList,
      },
      {
        label: "Scrub Nurses",
        field: "scrubNurses",
        type: "multiSelectWithChips",
        required: true,
        options: nurseList,
      },
      {
        label: "Circulating Nurses",
        field: "circulatingNurses",
        type: "multiSelectWithChips",
        required: true,
        options: nurseList,
      },
      {
        label: "Start Time",
        field: "assignedSurgery.startTime",
        required: true,
        type: "time",
      },
      {
        label: "End Time",
        field: "assignedSurgery.endTime",
        required: true,
        type: "time",
      },
    ],
  },
  {
    fields: [
      {
        label: "Blood Availability",
        field: "preCheckups.bloodAvailibility",
        required: true,
        type: "radio",
        options: [
          { label: "Yes", value: "yes" },
          { label: "No", value: "no" },
        ],
      },
      {
        label: "Emergency Equipment Checked",
        field: "preCheckups.emergencyEquipment",
        required: true,
        type: "radio",
        options: [
          { label: "Yes", value: "yes" },
          { label: "No", value: "no" },
        ],
      },
      {
        label: "Allergies",
        field: "preCheckups.allergies",
        type: "freeMultiSelect",
        placeholder: "type and enterss",
      },
    ],
  },
  {
    fields: [
      { label: "Additional Notes", field: "preCheckups.note", type: "text" },
      {
        label: "Concerns or special Requests",
        field: "specialRequest",
        type: "text",
      },
    ],
  },
];

export const surgerPatientField = [
  {
    label: "Patient Id",
    disabled: true,
    field: "patientId",
    type: "text",
  },
  {
    label: "Patient Name",
    disabled: true,
    field: "patientName",
    type: "text",
  },
  {
    label: "Ward No",
    field: "wardNo",
    type: "text",
    required: true,
  },
  {
    label: "Bed No",
    field: "bedNo",
    type: "text",
    required: true,
  },
];

export const surgeryDetails = [
  { type: "date", label: "Date", field: "date" },
  {
    type: "searchableSelect",
    label: "Department",
    field: "department",
    options: [],
  },
  {
    type: "searchableSelect",
    label: "Sub Department",
    field: "subDepartment",
    options: [],
  },
  {
    type: "dropdown",
    label: "Operation Type",
    field: "operationType",
    options: [],
  },
  {
    type: "searchableSelect",
    label: "Assigned OT",
    field: "ot",
    options: [],
  },
  {
    type: "text",
    label: "floor Number",
    field: "floor",
    disabled: true,
  },
  {
    type: "text",
    label: "Surgery Duration Time",
    field: "time",
  },
  {
    type: "number",
    label: "Surgery Cost",
    field: "cost",
  },
];

export const surgeryTeams = [
  {
    label: "Surgeons",
    field: "surgeon",
    type: "multiSelectWithChips",
    required: true,
    options: [],
  },
  {
    label: "Assistant Surgeons",
    field: "assistantSurgeons",
    type: "multiSelectWithChips",
    required: true,
    options: [],
  },

  {
    label: "Anesthetist",
    field: "anesthesiologists",
    type: "multiSelectWithChips",
    required: true,
    options: [],
  },
  {
    label: "Peri Operating Nurses",
    field: "periOperatingNurses",
    type: "multiSelectWithChips",
    required: true,
    options: [],
  },
  {
    label: "Scrub Nurses",
    field: "scrubNurses",
    type: "multiSelectWithChips",
    required: true,
    options: [],
  },
  {
    label: "Circulating Nurses",
    field: "circulatingNurses",
    type: "multiSelectWithChips",
    required: true,
    options: [],
  },
];

export const useGetAllApisData = () => {
  const { data: bloodInventory } = useGetBloodInventry();
  const { data: usersList, isSuccess } = useGetUser({ role: "PATIENT" });
  const { data: doctors, isSuccess: doctorSuccess } = useGetUser(
    { role: "DOCTOR" },
    { enabled: isSuccess }
  );
  const { data: nurses, isSuccess: nurseSuccess } = useGetUser(
    { role: "NURSE" },
    { enabled: doctorSuccess }
  );
  const { data: anesthesist } = useGetUser(
    { role: "ANESTHESIOLOGIST" },
    {
      enabled: nurseSuccess,
    }
  );

  const { data: surgeryDepartments } = useGetAllSurgeryDepartment();
  const { data: operationTheature } = useGetAllOperationTreature();

  const doctorList = get(doctors, "data.users", []).map((item) => ({
    label: get(item, "commonInfo.personalInfo.fullName"),
    value: get(item, "_id"),
  }));
  const nurseList = get(nurses, "data.users", []).map((item) => ({
    label: get(item, "commonInfo.personalInfo.fullName"),
    value: get(item, "_id"),
  }));
  const anesthesistList = get(anesthesist, "data.users", []).map((item) => ({
    label: get(item, "commonInfo.personalInfo.fullName"),
    value: get(item, "_id"),
  }));

  const patientList = get(usersList, "data.users", []).map((item) => ({
    label: get(item, "commonInfo.personalInfo.fullName"),
    value: get(item, "_id"),
  }));
  const patientIdList = get(usersList, "data.users", []).map((item) => ({
    label: get(item, "patientInfo.patientId"),
    value: get(item, "_id"),
  }));

  const theatres = get(operationTheature, "data.ot", []).map((item) => ({
    label: get(item, "name"),
    value: get(item, "_id"),
    floor: get(item, "floor", ""),
  }));
  const bloodInventry = get(bloodInventory, "data.bloodInventory", []).map(
    (item: { bloodType: string; quantityInUnits: number; _id: string }) => ({
      ...item,
      label: get(item, "bloodGroup", ""),
      value: get(item, "bloodGroup", ""),
    })
  );
  const surgeryDepartment = get(
    surgeryDepartments,
    "data.surgeryDepartments",
    []
  ).map((item) => ({
    label: get(item, "name"),
    value: get(item, "_id"),
    subDepartments: get(item, "subDepartments", []).map(
      (subDept: { name: string; _id: string; severity: string }) => ({
        label: get(subDept, "name"),
        value: get(subDept, "_id"),
        severity: get(subDept, "severity"),
      })
    ),
  }));
  return {
    patientList,
    patientIdList,
    surgeryDepartment,
    theatres,
    doctorList,
    nurseList,
    anesthesistList,
    bloodInventry,
  };
};

export interface BloodData {
  batchNo: string;
  bloodGroup: string;
  expiryDate: string;
  quantityInUnits: number;
  // other fields can exist, but we only care about these
}

export interface BloodUnit {
  type: string;
  unit: number;
}

export function getBloodUnitsByType(
  data: BloodData[],
  targetType: string
): BloodUnit[] {
  const today = dayjs();

  return data
    .filter(
      (item) =>
        dayjs(item.expiryDate).isAfter(today) && item.bloodGroup === targetType
    )
    .map((item) => ({
      type: item.bloodGroup,
      unit: item.quantityInUnits,
    }));
}

export const stepperList = [
  { step: 1, title: "Patient Information" },
  { step: 2, title: "Surgery Details" },
  { step: 3, title: "Medical Staff" },
  { step: 5, title: "Surgical Team" },
];

export const checklist = [
  {
    type: "searchableSelect",
    label: "Fasting Status",
    field: "preCheckups.fastingStatus",
    options: [
      { label: "Pending", value: "pending" },
      { label: "Confirmed", value: "confirmed" },
    ],
  },
  {
    type: "searchableSelect",
    label: "Presurgery Checklist",
    field: "preCheckups.presurgery",
    options: [
      { label: "Pending", value: "pending" },
      { label: "Confirmed", value: "confirmed" },
    ],
  },
  {
    type: "searchableSelect",
    label: "Emergency Equipment",
    field: "preCheckups.emergencyEquipment",
    options: [
      { label: "Pending", value: "pending" },
      { label: "Confirmed", value: "confirmed" },
    ],
  },
];
