"use client";
import {
  Icon,
  InlineIcon,
  _api,
  addAPIProvider,
  addCollection,
  addIcon,
  calculateSize,
  disableCache,
  enableCache,
  getIcon,
  iconLoaded,
  iconToSVG,
  listIcons,
  loadIcon,
  loadIcons,
  replaceIDs,
  setCustomIconLoader,
  setCustomIconsLoader
} from "./chunk-K7QL45TD.js";
import "./chunk-CMM6OKGN.js";
import "./chunk-OL46QLBJ.js";
export {
  Icon,
  InlineIcon,
  _api,
  addAPIProvider,
  addCollection,
  addIcon,
  iconToSVG as buildIcon,
  calculateSize,
  disableCache,
  enableCache,
  getIcon,
  iconLoaded as iconExists,
  iconLoaded,
  listIcons,
  loadIcon,
  loadIcons,
  replaceIDs,
  setCustomIconLoader,
  setCustomIconsLoader
};
