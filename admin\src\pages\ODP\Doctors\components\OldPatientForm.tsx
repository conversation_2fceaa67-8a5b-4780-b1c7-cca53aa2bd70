import { useF<PERSON><PERSON>, FormikProvider, Form } from "formik";
import * as yup from "yup";
import { useGetUser } from "../../../../server-action/api/user";
import { useCreateAppointment } from "../../../../server-action/api/appointmentApi";
import { FormField } from "../../../LabDashboard/components/FormField";
import { buildQueryParams } from "../../../../hooks/useBuildQuery";
import { userRole } from "../../../../constant/constant";

type Props = {
  EditData?: any;
  token?: string;
  onClose: () => void;
  //   onBack: () => void;
};

const OldPatientForm = ({ EditData, token, onClose }: Props) => {
  const queryParams = buildQueryParams({
    role: userRole.PATIENT,
    "commonInfo.ipdOpd": "OPD",
  });

  const { data: userPatientDetails } = useGetUser(queryParams);

  const oldPatientOptions = (userPatientDetails as any)?.data?.users?.map(
    (item: any) => ({
      label: item?.patientInfo?.patientId,
      value: item?._id,
    })
  );

  const createAppointment = useCreateAppointment().mutate;
  const oldPatientValidationSchema = yup.object().shape({
    patientId: yup.string().required("Patient ID is required"),
    appointmentType: yup.string().required("Appointment type is required"),
    appointmentDate: yup.string().required("Appointment date is required"),
    appointmentTime: yup.string().required("Appointment time is required"),
    status: yup
      .string()
      .oneOf(["PENDING", "CONFIRMED", "inprogress", "RESCHEDULED", "CANCELLED"])
      .required("Status is required"),
    paymentMethod: yup
      .string()
      .oneOf(["CASH", "BANK"])
      .required("Payment method is required"),
    paymentStatus: yup
      .string()
      .oneOf(["PAID", "PENDING", "PARTIALLY-PAID"])
      .required("Payment status is required"),
  });

  const formik = useFormik({
    initialValues: {
      patientId: "",
      appointmentType: "",
      appointmentDate: "",
      appointmentTime: "",
      status: "",
      symptoms: "",
      paymentMethod: "",
      paymentStatus: "",
      bankName: "",
      transactionId: "",
    },
    validationSchema: oldPatientValidationSchema,
    onSubmit: async (values) => {
      const toSendOldPatient = {
        user: values.patientId,
        date: values.appointmentDate,
        timeSlot: values.appointmentTime,
        doctor: EditData?._id,
        status: values.status,
        department: EditData?.departmentDetails?.department?._id,
        firstHirachy: EditData?.departmentDetails?.hirachyFirst?._id,
        specialist: EditData?.departmentDetails?.speciality?._id,
        remark: values.symptoms,
        paymentMethod: values.paymentMethod,
        paymentStatus: values.paymentStatus,
      };
      await createAppointment(toSendOldPatient);
      onClose();
    },
  });

  return (
    <FormikProvider value={formik}>
      <Form onSubmit={formik.handleSubmit} className="space-y-4">
        <div className="grid grid-cols-3 gap-x-4 gap-y-2">
          <FormField
            type="searchable-dropdown"
            name="patientId"
            label="Patient ID"
            placeholder="Search patient ID"
            options={oldPatientOptions}
            formik={formik}
            required
          />
          <FormField
            readonly
            name="patientName"
            label="Patient Full Name"
            type="text"
            value={
              (userPatientDetails as any)?.data?.users?.find(
                (user: any) => user._id === formik.values.patientId
              )?.commonInfo?.personalInfo?.fullName || ""
            }
            formik={formik}
          />
          <FormField
            readonly
            name="number"
            label="Contact Number"
            type="text"
            value={
              (userPatientDetails as any)?.data?.users?.find(
                (user: any) => user._id === formik.values.patientId
              )?.commonInfo?.contactInfo?.phone?.primaryPhone || ""
            }
            formik={formik}
          />

          <FormField
            name="appointmentType"
            label="Appointment Type"
            type="dropdown"
            options={[
              { value: "Online-Booking", label: "Online-Booking" },
              { value: "Walk-in", label: "Walk-in" },
              { value: "Tele-Booking", label: "Tele-Booking" },
            ]}
            placeholder="Select Appointment Type"
            formik={formik}
            required
          />
          <FormField
            name="appointmentDate"
            label="Appointment Date"
            type="date"
            placeholder="Select Date"
            formik={formik}
            required
          />
          <FormField
            name="appointmentTime"
            label="Appointment Time"
            type="dropdown"
            placeholder="Select Time"
            options={[
              { value: "10:00-10:30", label: "10:00-10:30" },
              { value: "10:30-11:00", label: "10:30-11:00" },
              { value: "11:00-11:30", label: "11:00-11:30" },
              { value: "11:30-12:00", label: "11:30-12:00" },
              { value: "12:00-12:30", label: "12:00-12:30" },
            ]}
            formik={formik}
            required
          />
          <FormField
            name="status"
            label="Status"
            type="dropdown"
            placeholder="Status"
            options={[
              { value: "PENDING", label: "Pending" },
              { value: "CONFIRMED", label: "Confirmed" },
              { value: "inprogress", label: "In Progress" },
              { value: "RESCHEDULED", label: "Re-Scheduled" },
              { value: "CANCELLED", label: "Cancelled" },
            ]}
            formik={formik}
            required
          />
          <FormField
            name="symptoms"
            label="Reason for Visit/ Symptoms"
            type="textarea"
            placeholder="Describe your symptoms or reason for appointment..."
            formik={formik}
          />
        </div>
        <div className="grid grid-cols-4 gap-x-4 gap-y-2">
          <FormField
            name="paymentMethod"
            label="Payment Method"
            type="dropdown"
            options={[
              { value: "CASH", label: "Cash" },
              { value: "BANK", label: "Bank" },
            ]}
            placeholder="Select"
            formik={formik}
            required
          />
          {formik.values.paymentMethod === "BANK" && (
            <>
              <FormField
                name="bankName"
                label="Bank Name"
                type="dropdown"
                placeholder="Select"
                options={[
                  { value: "SBI", label: "SBI" },
                  { value: "HDFC", label: "HDFC" },
                  { value: "ICICI", label: "ICICI" },
                  { value: "AXIS", label: "AXIS" },
                ]}
                formik={formik}
              />
              <FormField
                name="transactionId"
                label="Transaction ID"
                type="text"
                placeholder="Transaction ID"
                formik={formik}
              />
            </>
          )}

          <FormField
            name="paymentStatus"
            label="Payment Status"
            type="dropdown"
            placeholder="Select"
            options={[
              { value: "PAID", label: "Paid" },
              { value: "PENDING", label: "Pending" },
              { value: "PARTIALLY-PAID", label: "Partially Paid" },
            ]}
            formik={formik}
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-x-4 gap-y-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-1 text-gray-900 border border-gray-300 rounded"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-1 bg-primary text-white rounded"
          >
            Confirm Appointment
          </button>
        </div>
      </Form>
    </FormikProvider>
  );
};

export default OldPatientForm;
