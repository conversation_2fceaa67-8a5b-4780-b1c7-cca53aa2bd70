import{a5 as p,ae as u,a2 as e,af as x,ah as g,ai as h,aj as b}from"./index-ClX9RVH0.js";import{C as f,D as j}from"./Svg-BMTGOzwv.js";import{D as y}from"./DepartmentHeader-Aj6XBXn4.js";const S=()=>{const[i,s]=p.useState("Patient"),a={columns:[{title:"Patient Id",key:"tokenid"},{title:"Patient Name",key:"patientName"},{title:"Age",key:"date"},{title:"Condition",key:"treatment"},{title:"Bed No.",key:"date"},{title:"Addmission Date",key:"date"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:u.map(({tokenId:t,patientName:o,date:r,doctorName:l,status:c,treatment:d},m)=>({key:m,tokenid:t,patientName:o,date:r,doctorName:l,status:e.jsx(g,{status:c}),treatment:d,action:e.jsx(x,{onEdit:()=>{},onMore:()=>{}})}))},n=t=>{console.log(t,"onSearch")};return e.jsxs("div",{children:[e.jsx(y,{title:"High Dependency Unit (HDU)",doctorName:"Dr. Shishir Thapa",services:["Intermediate Care","Continuous Monitoring","Specialized Treatment","Advanced Equipment","24/7 Medical Supervision"],totalPatients:200,criticalCases:50,bedAvailability:50,dischargedPatients:10,doctorImage:e.jsx(j,{}),headerTitle:"Inpatient Department",icon:e.jsx(f,{})}),e.jsxs("div",{className:"bg-white",children:[e.jsxs("div",{className:"flex items-center justify-between pt-2 pb-1 pr-4 mt-5",children:[e.jsx(h,{tabs:["Patient","Doctor","Nurse","Staff"],defaultTab:i,onTabChange:t=>s(t)}),e.jsx("div",{className:"flex flex-row gap-10",children:e.jsx("div",{className:"relative flex items-center",children:e.jsx("input",{type:"text",placeholder:"Search name, id",onChange:t=>n(t.target.value),className:"py-2 pl-10 pr-4 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})})})]}),e.jsx(b,{columns:a.columns,rows:a.rows,loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}})]})]})};export{S as HighDependencyUnit};
