import{ad as A,bB as N,a5 as o,aP as y,bC as p,av as t,a2 as e,af as x,ah as b,a7 as v,ab as l,ag as D,aj as I,bA as f}from"./index-ClX9RVH0.js";const h=()=>{const i=A(),{data:c,isLoading:j}=N(),[k,u]=o.useState(!1);y(()=>{u(!1)});const[d,r]=o.useState(""),{mutateAsync:g}=p({id:JSON.stringify([d])}),[m,s]=o.useState(!1),n={columns:[{title:"Inv no.",key:"invoiceNo"},{title:"Patient Name",key:"patientName"},{title:"Date Issued",key:"dateIssued"},{title:"Total Amt (Rs.)",key:"TotalAmount"},{title:"Amt Paid (Rs.)",key:"amountPaid"},{title:"Balance Due",key:"balanceDue"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:t.get(c,"data.invoices",[]).map(a=>({invoiceNo:t.get(a,"invoiceNo","N/A"),patientName:t.get(a,"inventoryFor","N/A"),dateIssued:t.get(a,"date","N/A"),TotalAmount:t.get(a,"totalAmount","N/A"),amountPaid:t.get(a,"payableAmount","N/A"),balanceDue:t.get(a,"dueAmount","N/A"),status:e.jsx(b,{status:t.get(a,"paymentStatus","N/A")}),action:e.jsx(x,{onDelete:()=>{r(t.get(a,"_id")),s(!0)},onEdit:()=>console.log("Edit"),onPrint:()=>console.log("Print")})}))};return e.jsxs("div",{children:[e.jsx(v,{title:"Invoice",onSearch:()=>{},onAddClick:()=>{i(D.FINANCEADDNEWINVOICE)},listTitle:"Invoice",FilterSection:()=>e.jsxs("div",{className:"flex gap-5",children:[e.jsx(l,{label:"",options:[{value:"All",label:"Category"}]}),e.jsx(l,{label:"",options:[{value:"All",label:"Due Date"}]}),e.jsx(l,{label:"",options:[{value:"All",label:"Status"}]})]})})," ",e.jsxs("div",{className:"py-4",children:[e.jsx(I,{columns:n.columns,rows:n.rows,color:"bg-white ",textcolor:"text-gray-400",loading:!1,pagination:{currentPage:1,totalPage:300,limit:20,onClick:()=>{}}}),e.jsx(f,{confirmAction:m,title:"Do you want to delete this invoice?",des:"This action cannot be undone.",onClose:()=>s(!1),onConfirm:async()=>{await g(),s(!1)}})]})]})};export{h as default};
