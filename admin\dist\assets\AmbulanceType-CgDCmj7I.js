import{a2 as e,c8 as _,a5 as t,aP as f,aM as $,av as c,aV as z,aW as G,aX as r,af as H,aQ as J,aj as X,aR as N,bn as Y,aa as K,a4 as Z,ac as x,bl as ee,aY as ae}from"./index-ClX9RVH0.js";import{u as se,f as te,b as ie,g as ne}from"./ambulanceApi-C42c0mRe.js";const le=({viewData:n,className:d})=>e.jsxs("div",{className:_("mx-6 my-6",d),children:[e.jsx("div",{children:e.jsx("h1",{className:"text-xl font-semibold",children:n.ambulanceType})}),e.jsx("div",{className:"border-b-2 border-gray-300 my-4"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Capacity"}),e.jsx("p",{className:"text-gray-600",children:n.capacity})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Usage"}),e.jsx("p",{className:"text-gray-600",children:n.usage})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Equipment"}),e.jsx("p",{className:"text-gray-600",children:n.equipment.join(", ")})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold",children:"Description"}),e.jsx("p",{className:"text-gray-600",children:n.description})]})]})]}),xe=()=>{var b;const[n,d]=t.useState("create"),[T,ce]=t.useState(""),[s,p]=t.useState(null),[w,l]=t.useState(!1),[S,m]=t.useState(!1),q=f(()=>{l(!1)}),A=f(()=>{m(!1)}),{mutate:k,isPending:C}=se(),{mutate:P,isPending:M}=te(),[h,D]=t.useState({search:""}),[u,E]=t.useState({page:1,limit:10}),R=T,U=$({search:h.search,...R?{}:{page:u.page,limit:u.limit}}),[re,de]=t.useState(""),{data:o,isLoading:F}=ie(U),{mutate:O}=ne(),[oe,pe]=t.useState(!1),I=()=>{d("create"),p(null),l(!0)},V=a=>{d("edit"),p(a),l(!0)},B=a=>{p(a),m(!0)},y=c.get(o,"data.pagination",{}),g=({page:a,limit:i})=>{E(v=>({...v,page:a??1,limit:i??v.limit}))};t.useEffect(()=>{g({page:1})},[]);const L=z().shape({ambulanceType:r().required("Ambulance Type is required"),capacity:r().required("Capacity is required"),usage:r().required("Usage is required"),description:r().required("Description is required"),equipment:G().of(r()).min(1,"At least one equipment is required")}),Q=a=>{n==="edit"&&s?P({_id:s._id,entityData:a},{onSuccess:()=>{l(!1)}}):k(a,{onSuccess:()=>{l(!1)}})},W=()=>e.jsx(Y,{initialValues:{ambulanceType:(s==null?void 0:s.ambulanceType)||"",capacity:(s==null?void 0:s.capacity)||"",usage:(s==null?void 0:s.usage)||"",description:s==null?void 0:s.description,equipment:(s==null?void 0:s.equipment)||[]},validationSchema:L,onSubmit:Q,children:({errors:a,touched:i})=>e.jsx(K,{children:e.jsxs("div",{className:"p-4 relative",children:[e.jsx(Z,{as:"h3",text:"Add Ambulance Type",size:"body-lg-lg",variant:"primary-blue",className:"mb-4 text-primary flex justify-center items-center"}),e.jsxs("div",{className:"border-2 border-[#e8e6e7] px-4 py-3 rounded-xl mb-3",children:[e.jsx("div",{className:"absolute cursor-pointer hover:text-red top-5 right-10",onClick:()=>l(!1),children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",children:e.jsx("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeWidth:2,d:"m8 8l4 4m0 0l4 4m-4-4l4-4m-4 4l-4 4"})})}),e.jsx("div",{children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[e.jsxs("div",{className:"w-full",children:[e.jsx(x,{label:"Ambulance Type",type:"text",name:"ambulanceType",placeholder:"Basic Life Support"}),a.ambulanceType&&i.ambulanceType&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.ambulanceType})]}),e.jsxs("div",{className:"w-full",children:[e.jsx(x,{label:"Capacity",type:"text",name:"capacity",placeholder:"2-3 patients"}),a.capacity&&i.capacity&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.capacity})]})]})}),e.jsx("div",{children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 my-2",children:[e.jsxs("div",{className:"w-full",children:[e.jsx(x,{label:"Usage",type:"text",name:"usage",placeholder:"Routine patient transfers"}),a.usage&&i.usage&&e.jsx("div",{className:"text-red text-xs",children:a.usage})]}),e.jsxs("div",{children:[e.jsx(ee,{label:"Equipments",name:"equipment"}),a.equipment&&i.equipment&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.equipment})]})]})}),e.jsxs("div",{children:[e.jsx(ae,{label:"Description",name:"description"}),a.description&&i.description&&e.jsx("div",{className:"text-red text-xs mb-2",children:a.description})]})]}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx("button",{type:"button",className:"px-4 py-2 bg-gray-300 text-black rounded",onClick:()=>l(!1),children:"Cancel"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary text-white rounded",children:n==="edit"?M?"Updating...":"Update":C?"Creating...":"Create"})]})]})})}),j={columns:[{title:"Ambulance Type",key:"ambulanceType"},{title:"Capacity",key:"capacity"},{title:"Usage",key:"usage"},{title:"Action",key:"action"}],rows:((b=o==null?void 0:o.data.ambulanceConfiguration)==null?void 0:b.map((a,i)=>({key:i,ambulanceType:c.get(a,"ambulanceType","N/A"),capacity:e.jsx("div",{className:"text-nowrap",children:c.get(a,"capacity","N/A")}),usage:e.jsx("div",{className:"line-clamp-2",children:c.get(a,"usage","N/A")}),action:e.jsx(H,{onEdit:()=>V(a),onDelete:()=>{O({id:JSON.stringify([c.get(a,"_id","-")])})},onShow:()=>B(a)})})))??[]};return e.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[e.jsx(J,{headerTitle:"Ambulance Type",toSearch:"Search by ambulance type",onSearch:!0,onSearchFunc:a=>D({...h,search:a}),button:!0,buttonText:"Add Ambulance Type",buttonAction:I}),e.jsx("div",{className:"bg-white rounded-md",children:e.jsx(X,{columns:j.columns,rows:j.rows,loading:F,color:"bg-white",textcolor:"text-gray-400",pagination:{totalPage:y.pages||1,currentPage:y.page||1,limit:u.limit||10,onClick:g}})}),w&&e.jsx(N,{ref:q,children:W()}),S&&s&&e.jsx(N,{ref:A,classname:"w-[30rem] h-[90%] overflow-y-scroll",children:e.jsxs("div",{className:"relative p-6",children:[e.jsx("div",{className:"absolute cursor-pointer hover:text-red top-5 right-10",onClick:()=>m(!1),children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",children:e.jsx("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeWidth:2,d:"m8 8l4 4m0 0l4 4m-4-4l4-4m-4 4l-4 4"})})}),e.jsx(le,{viewData:s})]})})]})};export{xe as AmbulanceType};
