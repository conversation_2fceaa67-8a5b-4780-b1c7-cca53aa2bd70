import{a5 as l,a2 as t,aQ as o,ai as i,aj as r}from"./index-ClX9RVH0.js";const c=()=>{const[n,s]=l.useState("All"),e={columns:[{title:"S.N.",key:"serialNumber"},{title:"User Name",key:"userName"},{title:"Start Time",key:"startTime"},{title:"End Time",key:"endTime"},{title:"Action",key:"action"}],rows:[]};return t.jsxs("div",{children:[t.jsx(o,{button:!0,buttonAction:()=>{},buttonText:"Shift Assign"}),t.jsx(i,{tabs:["All","Doctor","Nurse"],defaultTab:"All",onTabChange:a=>s(a)}),t.jsx(r,{className:"px-4",columns:e.columns,rows:e.rows,loading:!1})]})};export{c as default};
