import{a5 as d,a1 as r,al as m,a2 as e,a7 as p,am as u,a9 as x,aa as j,ac as o,ab as a,ao as b}from"./index-ClX9RVH0.js";const v=()=>{const[i,s]=d.useState(1),n=[{step:1,title:"Basic Information",isActive:i===1},{step:2,title:"Test Information",isActive:i===2}],t=r({initialValues:{id:"",patientName:"",room:"",bed:"",medicalCondition:"",dietType:"",mealTiming:"",startDate:"",endDate:""},enableReinitialize:!0,onSubmit:c=>{m.success("Form submitted successfully!"),history.back(),console.log(c)}}),{handleSubmit:l}=t;return e.jsxs("div",{children:[e.jsx(p,{listTitle:"Add Test Result",hideHeader:!0}),e.jsxs("div",{className:"relative flex w-full mt-4 gap-6",children:[e.jsx("div",{className:"w-auto h-full",children:e.jsx(u,{steps:n})}),e.jsx("div",{className:"w-full",children:e.jsx(x,{value:t,children:e.jsx(j,{onSubmit:l,children:e.jsxs("div",{className:"flex flex-col w-full gap-5 pb-4",children:[e.jsx("div",{className:"flex flex-col gap-4 p-4 bg-white rounded-sm",onClick:()=>s(1),children:e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(o,{label:"Patient Name",name:"id",onFocus:()=>s(1)}),e.jsx(a,{label:"ID",options:[],name:"pateintName",onFocus:()=>s(1)}),e.jsx(a,{label:"Doctor Name",options:[],name:"pateintName",onFocus:()=>s(1)})]})}),e.jsx("section",{className:"bg-white p-4",children:e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsx(a,{options:[],label:"Test Type",name:"dietType",onClick:()=>s(2)}),e.jsx(o,{label:"Requested Date",type:"date",name:"dietType",onClick:()=>s(2)}),e.jsx(a,{options:[],label:"Priority",name:"mealTiming",onClick:()=>s(2)})]})}),e.jsx(b,{onCancel:()=>history.back(),onSubmit:l})]})})})})]})]})};export{v as AddTestRequest};
