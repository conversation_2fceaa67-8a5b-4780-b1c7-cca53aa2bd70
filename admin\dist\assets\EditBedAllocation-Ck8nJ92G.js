import{a2 as e,a7 as d,a8 as t,a1 as m,al as c,a9 as p,aa as b,ac as l,ab as a,aZ as u,ao as x}from"./index-ClX9RVH0.js";const n=[{label:"Male",value:"male"},{label:"<PERSON>Male",value:"female"}],s=[{label:"General",value:"General"},{label:"Cardiology",value:"Cardiology"},{label:"Neurology",value:"Neurology"},{label:"Orthopedics",value:"Orthopedics"},{label:"Gynecology",value:"Gynecology"}],j=()=>e.jsxs("div",{className:"mb-6 rounded-lg",children:[e.jsx(d,{title:"Edit Assigned Bed",hideHeader:!0,listTitle:"Edit Assigned Bed"}),e.jsxs("div",{className:"flex w-full gap-10",children:[e.jsx("div",{className:"h-auto",children:e.jsxs("div",{className:"flex flex-col gap-4 bg-white px-4 py-2",children:[e.jsx(t,{step:1,title:"Patient Details",isActive:!0}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(t,{step:2,title:"Bed Information",isActive:!1}),e.jsx("div",{className:"h-5 w-0.5 bg-dotted border-l border-dashed border-primary ml-5"}),e.jsx(t,{step:3,title:"Additional Details",isActive:!1})]})}),e.jsx("div",{className:"w-full h-full",children:e.jsx(g,{})})]})]}),g=()=>{const i=m({initialValues:{patientname:"",gender:"",patientstatus:"",admissiondate:"",department:"",bedno:""},enableReinitialize:!0,onSubmit:r=>{c.success("Form submitted successfully!"),history.back(),console.log(r)}}),{handleSubmit:o}=i;return e.jsx(e.Fragment,{children:e.jsx(p,{value:i,children:e.jsx(b,{onSubmit:o,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5 rounded-lg",children:[e.jsx(l,{type:"text",label:"Patient Name",placeholder:"Haleigh, Kuhlman",name:"patientname"}),e.jsx(a,{label:"Gender",options:n,name:"gender"}),e.jsx(l,{label:"Patient ID",name:"1001"}),e.jsx(a,{label:"Patient Status",options:n,name:"patientstatus"}),e.jsx(l,{type:"date",label:"Admission Date",placeholder:"Enter Date",name:"admissiondate"})]}),e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5 rounded-lg",children:[e.jsx(a,{label:"Room Type",options:s,name:"roomtype"}),e.jsx(a,{label:"Department",options:s,name:"department"}),e.jsx(a,{label:"Floor",options:s,name:"floor"}),e.jsx(a,{label:"Room Number",options:s,name:"roomnumber"}),e.jsx(a,{label:"Bed Number",options:s,name:"bednumber"}),e.jsx(l,{type:"text",label:"Current Bed Status",placeholder:"Enter Phone",name:"currentbedstatus"})]}),e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5 rounded-lg",children:[e.jsx("div",{className:"col-span-3",children:e.jsx(u,{label:"Reason for Admission",placeholder:"Enter reason for admission (e.g. surgery, observation)",name:"admissionreason"})}),e.jsx(l,{type:"text",label:"Attending Doctor",placeholder:"Enter Doctor Name",name:"attendingdoctor"})]}),e.jsx(x,{onCancel:()=>{history.back()},onSubmit:o})]})})})})};export{g as BasicInformation,j as default};
