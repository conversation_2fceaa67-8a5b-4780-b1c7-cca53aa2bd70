import { useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { AiOutlinePlus } from "react-icons/ai";
import { Icon } from "@iconify/react/dist/iconify.js";

export default function PresentingComplaints() {
  const [complaints, setComplaints] = useState([
    {
      id: 1,
      title: "Write presenting Complaints",
      text: "Patient reports a 3-day history of persistent dysuria associated with suprapubic burning pain, exacerbated postprandially, and accompanied by increased urinary frequency and low-grade fever. Also reports intermittent nausea. Differential diagnosis includes urinary tract infection or possible cystitis. Further evaluation warranted.",
      date: "21 August 2023",
    },
    {
      id: 2,
      text: "Patient reports a 3-day history of persistent dysuria associated with suprapubic burning pain, exacerbated postprandially, and accompanied by increased urinary frequency and low-grade fever. Also reports intermittent nausea. Differential diagnosis includes urinary tract infection or possible cystitis. Further evaluation warranted.",
      date: "21 August 2023",
    },
    {
      id: 3,
      text: "Patient reports a 3-day history of persistent dysuria associated with suprapubic burning pain, exacerbated postprandially, and accompanied by increased urinary frequency and low-grade fever. Also reports intermittent nausea. Differential diagnosis includes urinary tract infection or possible cystitis. Further evaluation warranted.",
      date: "21 August 2023",
    },
    {
      id: 4,
      text: "Patient reports a 3-day history of persistent dysuria associated with suprapubic burning pain, exacerbated postprandially, and accompanied by increased urinary frequency and low-grade fever. Also reports intermittent nausea. Differential diagnosis includes urinary tract infection or possible cystitis. Further evaluation warranted.",
      date: "21 August 2023",
    },
  ]);

  const formik = useFormik({
    initialValues: {
      title: "",
      details: "",
    },
    validationSchema: Yup.object({
      title: Yup.string().required("Presenting complaint title is required"),
      details: Yup.string()
        .required("Details are required")
        .min(20, "Details should be at least 20 characters"),
    }),
    onSubmit: (values, { resetForm }) => {
      const newComplaint = {
        id: complaints.length + 1,
        title: values.title,
        text: values.details,
        date: new Date().toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "long",
          year: "numeric",
        }),
      };
      setComplaints([newComplaint, ...complaints]);
      resetForm();
    },
  });

  return (
    <div className='w-full min-h-screen p-4 mx-auto'>
      {/* Header */}
      <h2 className='mb-4 font-semibold text-center'>Presenting Complaints</h2>

      {/* Complaint Form */}
      <form onSubmit={formik.handleSubmit} className='space-y-4'>
        {/* Title Input with Plus Button */}
        <div className='flex items-center gap-2'>
          <input
            name='title'
            placeholder='Write presenting Complaints'
            className='flex-1 border border-gray-300 px-3 py-1.5 rounded-md text-sm'
            onChange={formik.handleChange}
            value={formik.values.title}
          />
          <button
            type='button'
            className='p-1.5 bg-gray-100 border border-gray-300 rounded-md'
            title='Add'
            onClick={() => formik.submitForm()}
          >
            <AiOutlinePlus className='text-xl' />
          </button>
        </div>
        {formik.touched.title && formik.errors.title && (
          <p className='text-sm text-red-600'>{formik.errors.title}</p>
        )}

        {/* Details Textarea */}
        <div>
          <textarea
            name='details'
            placeholder='Details of the complain'
            rows={5}
            className='w-full px-3 py-2 text-sm border border-gray-300 rounded-md'
            onChange={formik.handleChange}
            value={formik.values.details}
          />
          {formik.touched.details && formik.errors.details && (
            <p className='mt-1 text-sm text-red-600'>{formik.errors.details}</p>
          )}
        </div>

        {/* Save Button */}
        <div className='flex justify-end col-span-2'>
          <button
            type='submit'
            className='bg-blue-600 hover:bg-blue-700 text-white bg-[#116aef] px-4 py-1.5 rounded-md flex items-center gap-1'
          >
            <Icon
              icon='fa6-solid:floppy-disk'
              width='18'
              height='18'
              color='white'
            />
            Save
          </button>
        </div>
      </form>

      {/* Complaints History */}
      <h2 className='mb-4 text-lg font-medium text-center '>
        Complaints History
      </h2>
      <div className='space-y-1'>
        {complaints.map((complaint) => (
          <div
            key={complaint.id}
            className='p-3 bg-white border border-gray-300 rounded-md shadow-sm'
          >
            <p className='mb-2 text-sm leading-relaxed text-gray-800'>
              {complaint.text}
            </p>
            <div className='flex justify-end text-sm font-medium text-[#116aef]'>
              {complaint.date}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
