import { Icon } from "@iconify/react/dist/iconify.js";
import React from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { useFormik } from "formik";
import * as Yup from "yup";

// Data for the "Recently Taken Medicine" table
const vaccineTableData = {
  columns: [
    { title: "Vaccine Name", key: "vaccineName" },
    { title: "Batch No.", key: "batchNo" },
    { title: "Dose No.", key: "doseNo" },
    { title: "Route", key: "route" },
    { title: "Site", key: "site" },
    { title: "Status", key: "status" },
    { title: "Remarks", key: "remarks" },
  ],
  rows: [
    {
      vaccineName: "Covid-19",
      batchNo: "CVX-12345",
      doseNo: "1st dose",
      route: "Intramuscular",
      site: "Left Deltoid",
      status: "Given",
      remarks: "Mild soreness reported at site",
    },
    {
      vaccineName: "Hepatitis B",
      batchNo: "HBV-22311",
      doseNo: "2nd dose",
      route: "Intramuscular",
      site: "Right Deltoid",
      status: "Given",
      remarks: "No adverse effects",
    },
    {
      vaccineName: "Influenza",
      batchNo: "FLU-99881",
      doseNo: "Annual dose",
      route: "Intramuscular",
      site: "Left Thigh",
      status: "Given",
      remarks: "Slight redness observed",
    },
    {
      vaccineName: "Tetanus",
      batchNo: "TT-55678",
      doseNo: "Booster",
      route: "Intramuscular",
      site: "Right Arm",
      status: "Not Given",
      remarks: "Patient refused",
    },
    {
      vaccineName: "MMR",
      batchNo: "MMR-33902",
      doseNo: "1st dose",
      route: "Subcutaneous",
      site: "Left Upper Arm",
      status: "Missed",
      remarks: "Patient absent",
    },
  ],
};

const AdministerVaccination: React.FC = () => {
  const initialValues = {
    vaccineName: "",
    batch: "",
    dose: "",
    route: "",
    site: "",
    status: "",
    remarks: "",
  };

  const medicationValidationSchema = Yup.object({
    vaccineName: Yup.string().required("Medicine Name is required"),
    batch: Yup.string().required("Batch Number required"),
    dose: Yup.string().required("Dose is required"),
    route: Yup.string().required("Route is required"),
    site: Yup.string().required("site is required"),
    status: Yup.string().required("Status is required"),
    remarks: Yup.string(),
  });

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: medicationValidationSchema,
    onSubmit: (values, { resetForm }) => {
      console.log("Medication Administration Form submitted", values);
      resetForm();
    },
  });

  return (
    <div className='max-w-full p-4 mx-auto'>
      <h2 className='mb-4 text-lg font-semibold text-center'>
        Administer Vaccination
      </h2>

      <form
        onSubmit={formik.handleSubmit}
        className='w-full p-4 mb-8 space-y-4 border rounded-md'
      >
        {/* First Row: Vaccine Name, Batch no, Dose No */}
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Vaccine Name */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='medicineName' className='whitespace-nowrap'>
                Vaccine Name
              </label>
              <input
                type='text'
                id='vaccineName'
                name='vaccineName'
                placeholder='Vaccine name'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.vaccineName}
              />
            </div>
            {formik.touched.vaccineName && formik.errors.vaccineName && (
              <p className='text-xs text-red-500'>
                {formik.errors.vaccineName}
              </p>
            )}
          </div>

          {/* Batch No */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='scheduledTime' className='whitespace-nowrap'>
                Batch Number
              </label>
              <input
                type='text'
                id='batch'
                name='batch'
                placeholder='Batch Number'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.batch}
              />
            </div>
            {formik.touched.batch && formik.errors.batch && (
              <p className='text-xs text-red-500'>{formik.errors.batch}</p>
            )}
          </div>

          {/* Dose No */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='givenTime' className='whitespace-nowrap'>
                Dose No.
              </label>
              <input
                type='text'
                id='dose'
                name='dose'
                placeholder='Dose'
                className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.dose}
              />
            </div>
            {formik.touched.dose && formik.errors.dose && (
              <p className='text-xs text-red-500'>{formik.errors.dose}</p>
            )}
          </div>
        </div>

        {/* Second Row: Route,Site, Status */}
        <div className='grid items-start grid-cols-12 gap-4'>
          {/* Route */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='route' className='whitespace-nowrap'>
                Route
              </label>
              <select
                id='route'
                name='route'
                className='w-full p-1.5 text-[#3a3a3a] border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.route}
              >
                <option value='' className='text-[#3a3a3a]'>
                  Status
                </option>
                <option value='Oral'>Oral</option>
                <option value='Injection'>Injection</option>
                <option value='IV'>IV</option>
                <option value='Topical'>Topical</option>
                <option value='Other'>Other</option>
              </select>
            </div>
            {formik.touched.route && formik.errors.route && (
              <p className='text-xs text-red-500'>{formik.errors.route}</p>
            )}
          </div>

          {/* site  */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='route' className='whitespace-nowrap'>
                Site
              </label>
              <select
                id='site'
                name='site'
                className='w-full p-1.5 text-[#3a3a3a]  border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.site}
              >
                <option value='' className='text-[#3a3a3a]'>
                  Site
                </option>
                <option value='Oral'>Oral</option>
                <option value='Injection'>Injection</option>
              </select>
            </div>
            {formik.touched.site && formik.errors.site && (
              <p className='text-xs text-red-500'>{formik.errors.site}</p>
            )}
          </div>

          {/* Status */}
          <div className='flex flex-col col-span-4 gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='status' className='whitespace-nowrap'>
                Status
              </label>
              <select
                id='status'
                name='status'
                className='w-full p-1.5  border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.status}
              >
                <option value=''>Status</option>
                <option value='Given'>Given</option>
                <option value='Not Given'>Not Given</option>
              </select>
            </div>
            {formik.touched.status && formik.errors.status && (
              <p className='text-xs text-red-500'>{formik.errors.status}</p>
            )}
          </div>
        </div>

        {/* Third Row: Remarks, Save Button */}
        <div className='grid items-end grid-cols-12 gap-4'>
          {/* Remarks */}
          <div className='col-span-9 flex flex-col gap-1'>
            <div className='flex items-center gap-2'>
              <label htmlFor='remarks' className='whitespace-nowrap'>
                Remarks
              </label>

              <div className='flex flex-col w-full'>
                <input
                  id='remarks'
                  name='remarks'
                  placeholder='Write remarks'
                  className='w-full p-2 border border-gray-300 rounded focus:outline-none focus:border-blue-400'
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  value={formik.values.remarks}
                />

                {formik.touched.remarks && formik.errors.remarks && (
                  <p className='text-xs text-red-500 mt-1'>
                    {formik.errors.remarks}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className='flex items-end justify-end h-full col-span-3'>
            <button
              type='submit'
              className='flex items-center justify-center px-6 py-2 gap-3 text-white text-md bg-[#116aef] rounded-lg hover:bg-blue-700 h-fit'
            >
              <Icon
                icon='fa6-solid:floppy-disk'
                width='18'
                height='18'
                color='white'
              />
              Save
            </button>
          </div>
        </div>
      </form>

      {/* Recently Taken Medicine Table */}
      <h2 className='mb-4 text-lg font-semibold text-center'>
        Recently Taken Vaccine
      </h2>
      <div className='overflow-x-auto'>
        <MasterTable
          columns={vaccineTableData.columns}
          rows={vaccineTableData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default AdministerVaccination;
