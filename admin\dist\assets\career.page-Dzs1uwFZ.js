import{bR as b,bV as f,a5 as h,aM as P,ad as v,a2 as a,ag as J,dg as C,a1 as N,a9 as E,bx as A,aa as j,b3 as S,dt as i,ao as w,aP as T,du as q,aR as F}from"./index-ClX9RVH0.js";import{G as R}from"./GenericCard.components-BAGnoJGb.js";const u=b(f.JOB,"Job"),k=u.useCreate,B=u.useGetAll,_=u.useUpdate,I=u.useDeleteWithQuery,L=()=>({getTitle:e=>e==null?void 0:e.jobTitle,getDescription:e=>e==null?void 0:e.requirement,getImage:e=>(e==null?void 0:e.thumbnail)||"/placeholder.svg?height=200&width=400",getDate:e=>e==null?void 0:e.date,getMetadata:e=>[...e!=null&&e.experience?[{icon:"mdi:briefcase",label:"Experience",value:e==null?void 0:e.experience}]:[],...e!=null&&e.salary?[{icon:"circum:money-bill",label:"Salary",value:e.salary}]:[]],cardStyle:"job",maxDescriptionLength:150}),G=({setCareerProps:e})=>{var d,m,x,g;const[s,t]=h.useState({limit:5,page:1}),c=P({page:s.page,limit:s.limit}),{data:n}=B(c),{mutateAsync:o}=I(),r=L(),p=v();return a.jsxs("div",{className:"-mx-4 flex flex-col gap-4",children:[a.jsx("section",{className:"grid grid-cols-3 gap-6",children:(m=(d=n==null?void 0:n.data)==null?void 0:d.jobs)==null?void 0:m.map(l=>a.jsx(R,{item:l,config:r,onEdit:y=>{e({modal:!0,selectedData:y})},onView:()=>{p(`${J.WEBSITE_CARRER_APPLICATION}/${l==null?void 0:l._id}`)},onDelete:async()=>{await o({id:JSON.stringify([l==null?void 0:l._id])})}}))}),a.jsx(C,{currentPage:s.page,limit:s.limit,onClick:l=>{l.page&&t({...s,page:l.page}),l.limit&&t({...s,limit:l.limit})},totalPage:(g=(x=n==null?void 0:n.data)==null?void 0:x.pagination)==null?void 0:g.pages})]})},M=({onCancel:e,editData:s})=>{const{mutateAsync:t,isPending:c}=k(),{mutateAsync:n,isPending:o}=_(),r=N({initialValues:{date:(s==null?void 0:s.date)??"",jobTitle:(s==null?void 0:s.jobTitle)??"",experience:(s==null?void 0:s.experience)??"",requirement:(s==null?void 0:s.requirement)??"",thumbnail:(s==null?void 0:s.thumbnail)??"",salary:(s==null?void 0:s.salary)??""},onSubmit:async()=>{s?(await n({_id:s._id??"",entityData:r.values}),e()):(await t(r.values),e())}}),{handleSubmit:p}=r;return a.jsxs(E,{value:r,children:[(c||o)&&a.jsx(A,{isLoading:c||o}),a.jsxs(j,{className:"grid grid-cols-2 gap-4 p-6",children:[a.jsxs("div",{className:"flex col-span-2 justify-between items-center border-b pb-4 mb-2",children:[a.jsxs("div",{children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:s?"Edit Job":"Create Job"}),a.jsx("p",{className:"text-sm text-gray-500",children:s?"Update the details of your posted job.":"Fill out the form to publish a new job vaccancy."})]}),a.jsx("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:a.jsx(S,{icon:"mdi:close",className:"w-6 h-6"})})]}),a.jsx(i,{name:"jobTitle",label:"Job Title",type:"text",placeholder:"Enter job title"}),a.jsx(i,{name:"experience",label:"Required experience",type:"text",placeholder:"Enter required experience"}),a.jsx(i,{name:"salary",label:"Job Salary",type:"text",placeholder:"Enter job Salary"}),a.jsx(i,{name:"date",label:"Expiry Date",type:"date"}),a.jsx("section",{className:"col-span-2",children:a.jsx(i,{name:"thumbnail",label:"Thumbnail",type:"file",accept:"image/*"})}),a.jsx("section",{className:"col-span-2",children:a.jsx(i,{name:"requirement",label:"Description",type:"quill",placeholder:"Enter job description..."})}),a.jsx("section",{className:"col-span-2",children:a.jsx(w,{onCancel:e,onSubmit:p})})]})]})},U=()=>{const[e,s]=h.useState({modal:!1,selectedData:{}}),t=T(()=>s({...e,modal:!1}));return a.jsxs("div",{children:[a.jsx(q,{title:"Career",subtitle:"Manage your job posts",buttonText:"Post A Job",onButtonClick:()=>{s({modal:!0,selectedData:void 0})}}),a.jsx("section",{className:"px-8 ",children:a.jsx(G,{setCareerProps:s})}),e.modal&&a.jsx(F,{ref:t,classname:"h-[650px] overflow-scroll w-[700px]",children:a.jsx(M,{onCancel:()=>s({selectedData:void 0,modal:!1}),editData:e.selectedData})})]})};export{U as CareerPage};
