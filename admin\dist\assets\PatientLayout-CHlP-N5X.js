import{ad as V,a2 as s,ah as U,b3 as Q,aj as p,ba as D,a7 as ee}from"./index-ClX9RVH0.js";import{i as se}from"./OTAssignment-BHmGwNc9.js";const le=({_id:i,address:e,gender:r,image:a,name:o})=>(V(),s.jsx("div",{className:"bg-white rounded-xl relative",children:s.jsxs("div",{className:"flex flex-col lg:flex-row lg:place-items-center gap-10 p-5",children:[s.jsx("img",{className:"rounded-xl w-28 h-28 object-cover mx-auto lg:mx-0",src:a??"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D",alt:"patient images"}),s.jsxs("div",{className:"flex flex-col ",children:[s.jsxs("div",{className:"flex flex-row gap-4 lg:gap-10",children:[s.jsx("h4",{className:"font-bold text-2xl capitalize",children:o}),s.jsx(U,{status:"In Patient"})]}),s.jsxs("div",{className:"flex flex-col lg:flex-row text-lg text-gray-600 gap-4 mt-4",children:[s.jsxs("div",{className:"flex gap-1 place-items-center",children:[s.jsx(Q,{icon:"iconamoon:profile-fill",width:"18",height:"18",style:{color:"#525252"}}),s.jsx("p",{className:"text-xs font-medium",children:r})]}),s.jsxs("div",{className:"flex gap-1 place-items-center",children:[s.jsx(Q,{icon:"mdi:location",width:"18",height:"18",style:{color:"#525252"}}),s.jsx("p",{className:"text-xs font-medium",children:e})]})]}),s.jsx("div",{className:"mt-3 text-lg",children:i})]})]})})),ie=({surgeryType:i,surgeon:e,date:r,startTime:a,anesthesiologists:o,scrubNurses:d,circulatingNurses:f,otherStaff:h})=>{const x={columns:[{title:"Surgery Type",key:"surgeryType"},{title:"Referring Doctor",key:"surgeon"},{title:"Date",key:"date"},{title:"Time",key:"startTime"}],rows:[{surgeryType:i,surgeon:e,date:r,startTime:a}]},c=m=>Array.isArray(m)?m.join(", "):m||"",g={columns:[{title:"Surgeon Name",key:"surgeon"},{title:"Anethesiologist ",key:"anesthesiologists"},{title:"Scrub Nurse",key:"scrubNurses"},{title:"Circulating Nurse",key:"circulatingNurses"}],rows:[{surgeon:c(e),anesthesiologists:c(o),scrubNurses:c(d),circulatingNurses:c(f),otherStaff:c(h)}]};return s.jsx(s.Fragment,{children:s.jsxs("div",{className:"mt-9 bg-white rounded-xl relative",children:[s.jsx("div",{className:" w-full",children:s.jsx("h2",{className:"text-xl font-semibold",children:"Surgery Details"})}),s.jsxs("div",{children:[s.jsx(p,{columns:x.columns,rows:x.rows,loading:!1}),s.jsx(p,{columns:g.columns,rows:g.rows,loading:!1})]})]})})},ne=({fastingStatus:i,bloodAvailibility:e,allergies:r,emergencyEquipment:a})=>s.jsx("div",{className:"p-4 bg-white rounded-xl relative",children:s.jsxs("div",{className:"w-full",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Emergency Preparedness"}),s.jsxs("div",{className:"bg-[#f1fcfb] border-[#cef9f6] border rounded-lg p-4",children:[s.jsxs("div",{className:"flex justify-between py-1",children:[s.jsx("span",{className:"text-gray-700",children:"Fasting Status"}),s.jsx("span",{className:"font-medium text-gray-800",children:i?"Yes ":"No"})]}),s.jsxs("div",{className:"flex justify-between py-1",children:[s.jsx("span",{className:"text-gray-700",children:"Blood Availibility"}),s.jsx("span",{className:"font-medium text-gray-800",children:e?"Yes":"No"})]}),s.jsxs("div",{className:"flex justify-between py-1",children:[s.jsx("span",{className:"text-gray-700",children:"Emergency Equipment"}),s.jsx("span",{className:"font-medium text-gray-800",children:a?"Yes":"No"})]}),s.jsxs("div",{className:"flex justify-between py-1",children:[s.jsx("span",{className:"text-gray-700",children:"Allergies"}),s.jsx("span",{className:"font-medium text-gray-800",children:r?"Yes":"No"})]})]})]})}),te=({note:i})=>s.jsx("div",{className:"p-4  rounded-xl relative",children:s.jsxs("div",{className:"w-full",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"More Information"}),i&&s.jsxs("div",{className:"bg-gray-100 rounded-lg p-4 mb-4",children:[s.jsx("h3",{className:"text-gray-500 font-semibold mb-1",children:"Additional Notes"}),typeof i=="object"&&Object.keys(i).length>0?s.jsx("ul",{className:"list-disc list-inside text-gray-800 space-y-1",children:Object.entries(i).map(([e,r])=>s.jsxs("li",{children:[s.jsxs("span",{className:"font-medium capitalize",children:[e,":"]})," ",String(r)]},e))}):s.jsx("p",{className:"text-gray-800",children:i})]})]})}),re=({surgerySiteMarked:i,fastingStatus:e,consentFormSigned:r,specialRequest:a})=>s.jsxs("div",{className:"p-6 bg-white rounded-xl shadow-md",children:[s.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Additional Surgery Details"}),s.jsxs("div",{className:"flex flex-col lg:flex-row gap-5",children:[s.jsxs("div",{className:"lg:w-1/3 bg-[#f1fcfb] border-[#cef9f6] border rounded-md p-4 space-y-3",children:[s.jsxs("div",{className:"flex justify-between py-1",children:[s.jsx("span",{className:"text-gray-700",children:"Surgery Site Marked"}),s.jsx("span",{className:"font-semibold text-gray-800",children:i?"yes":"No"})]}),s.jsxs("div",{className:"flex justify-between py-1",children:[s.jsx("span",{className:"text-gray-700",children:"Fasting Status"}),s.jsx("span",{className:"font-semibold text-gray-800",children:e?"Yes":"No"})]}),s.jsxs("div",{className:"flex justify-between py-1",children:[s.jsx("span",{className:"text-gray-700",children:"Consent Form Signed"}),s.jsx("span",{className:"font-semibold text-gray-800",children:r?"Yes":"No"})]})]}),s.jsx("div",{className:"lg:w-2/3 text-sm space-y-4",children:s.jsxs("div",{className:"bg-gray-100 h-auto text-gray-700 p-3 rounded-md",children:[s.jsx("strong",{className:"block mb-1",children:"Special Instructions"}),s.jsx("p",{className:"text-gray-800",children:a})]})})]})]}),ae=({surgeryType:i,surgeon:e,date:r,startTime:a,status:o})=>{const d={columns:[{title:"Surgery Type",key:"surgeryType"},{title:"Referring Doctor",key:"surgeon"},{title:"Date",key:"date"},{title:"Time",key:"startTime"},{title:"Status",key:"status"}],rows:[{surgeryType:i,surgeon:e,date:r,startTime:a,status:s.jsx(U,{status:o??"N/A"})}]};return s.jsxs("div",{className:"p-4 mt-4 bg-white rounded-xl relative",children:[s.jsx("div",{className:" w-full",children:s.jsx("h2",{className:"text-xl font-semibold",children:"History Surgery"})}),s.jsx("div",{children:s.jsx(p,{columns:d.columns,rows:d.rows,loading:!1})})]})},de=()=>{var r,a,o,d,f,h,x,c,g,m,y,j,u,N,b,v,w,S,I,k,T,A,C,M,P,E,F,Y,H,q,_,z,B,O,R,G,L,W,X,Z,$,J,K;const{id:i}=D(),{data:e}=se(i);return console.log(e,"user"),s.jsxs("div",{className:"gap-2 pb-8",children:[s.jsx(ee,{listTitle:"Surgery Details",hideHeader:!0}),s.jsxs("section",{className:"flex gap-x-5 flex-row w-full",children:[s.jsxs("div",{className:"w-full lg:w-[70%]",children:[s.jsx(le,{_id:`P-${(a=e==null?void 0:e._id)==null?void 0:a.slice(((r=e==null?void 0:e._id)==null?void 0:r.length)-5,e==null?void 0:e._id.length)}`,address:((h=(f=(d=(o=e==null?void 0:e.patient)==null?void 0:o.commonInfo)==null?void 0:d.contactInfo)==null?void 0:f.address)==null?void 0:h.currentAddress)??((m=(g=(c=(x=e==null?void 0:e.patient)==null?void 0:x.commonInfo)==null?void 0:c.contactInfo)==null?void 0:g.address)==null?void 0:m.permanentAddress),gender:(u=(j=(y=e==null?void 0:e.patient)==null?void 0:y.commonInfo)==null?void 0:j.personalInfo)==null?void 0:u.gender,image:(v=(b=(N=e==null?void 0:e.patient)==null?void 0:N.commonInfo)==null?void 0:b.personalInfo)==null?void 0:v.image,name:(I=(S=(w=e==null?void 0:e.patient)==null?void 0:w.commonInfo)==null?void 0:S.personalInfo)==null?void 0:I.fullName}),s.jsx(ie,{surgeryType:(k=e==null?void 0:e.assignedSurgery)==null?void 0:k.surgeryType,surgeon:(A=(T=e==null?void 0:e.assignedSurgery)==null?void 0:T.surgeon)==null?void 0:A.map(l=>{var n,t;return(t=(n=l==null?void 0:l.commonInfo)==null?void 0:n.personalInfo)==null?void 0:t.fullName}),date:e==null?void 0:e.date,startTime:(C=e==null?void 0:e.assignedSurgery)==null?void 0:C.startTime,anesthesiologists:(P=(M=e==null?void 0:e.assignedSurgery)==null?void 0:M.anesthesiologists)==null?void 0:P.map(l=>{var n,t;return(t=(n=l==null?void 0:l.commonInfo)==null?void 0:n.personalInfo)==null?void 0:t.fullName}),scrubNurses:(F=(E=e==null?void 0:e.assignedSurgery)==null?void 0:E.scrubNurses)==null?void 0:F.map(l=>{var n,t;return(t=(n=l==null?void 0:l.commonInfo)==null?void 0:n.personalInfo)==null?void 0:t.fullName}),circulatingNurses:(H=(Y=e==null?void 0:e.assignedSurgery)==null?void 0:Y.circulatingNurses)==null?void 0:H.map(l=>{var n,t;return(t=(n=l==null?void 0:l.commonInfo)==null?void 0:n.personalInfo)==null?void 0:t.fullName}),otherStaff:(_=(q=e==null?void 0:e.assignedSurgery)==null?void 0:q.otherStaff)==null?void 0:_.map(l=>{var n,t;return(t=(n=l==null?void 0:l.commonInfo)==null?void 0:n.personalInfo)==null?void 0:t.fullName})})]}),s.jsxs("div",{className:"w-full lg:w-[30%] space-y-2  h-[32rem] overflow-y-scroll",children:[s.jsx("div",{className:" bg-emerald-50 border-[#c5f0e1] border h-[15rem] overflow-y-auto",children:s.jsx(ne,{fastingStatus:(z=e==null?void 0:e.preCheckups)==null?void 0:z.fastingStatus,bloodAvailibility:(B=e==null?void 0:e.preCheckups)==null?void 0:B.bloodAvailibility,allergies:(O=e==null?void 0:e.preCheckups)==null?void 0:O.allergies,emergencyEquipment:(R=e==null?void 0:e.preCheckups)==null?void 0:R.emergencyEquipment})}),s.jsx("div",{className:"bg-white border h-[15rem] overflow-y-auto",children:s.jsx(te,{note:(G=e==null?void 0:e.preCheckups)==null?void 0:G.note})})]})]}),s.jsx("section",{children:s.jsx(re,{surgerySiteMarked:(L=e==null?void 0:e.preCheckups)==null?void 0:L.surgerySiteMarked,fastingStatus:(W=e==null?void 0:e.preCheckups)==null?void 0:W.fastingStatus,consentFormSigned:(X=e==null?void 0:e.preCheckups)==null?void 0:X.consentFormSigned})}),s.jsx("section",{children:s.jsx(ae,{surgeryType:(Z=e==null?void 0:e.assignedSurgery)==null?void 0:Z.surgeryType,surgeon:(J=($=e==null?void 0:e.assignedSurgery)==null?void 0:$.surgeon)==null?void 0:J.map(l=>{var n,t;return(t=(n=l==null?void 0:l.commonInfo)==null?void 0:n.personalInfo)==null?void 0:t.fullName}),date:e==null?void 0:e.date,startTime:(K=e==null?void 0:e.assignedSurgery)==null?void 0:K.startTime,status:e==null?void 0:e.status})})]})};export{de as default};
