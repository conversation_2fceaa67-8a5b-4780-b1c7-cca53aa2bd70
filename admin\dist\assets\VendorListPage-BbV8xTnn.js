import{ad as c,a5 as p,bI as m,aw as u,av as e,a2 as t,af as f,a7 as v,ab as i,aj as I}from"./index-ClX9RVH0.js";import{v as h}from"./vendorObj-Dry546GQ.js";const b=()=>{const o=c(),[s,l]=p.useState({page:1,limit:5,search:"",status:"",categories:""}),{mutate:d}=m(),{data:n}=u({role:"VENDOR",page:s.page,limit:s.limit}),g=e.get(n,"data.users",[]).map((a,r)=>({sn:(s.page-1)*s.limit+r+1,name:e.get(a,"commonInfo.personalInfo.fullName","-"),vendorId:e.get(a,"vendorInfo.vendorId","-"),contactPerson:e.get(a,"vendorInfo.contactPerson","-"),email:e.get(a,"email","-"),phoneNo:e.get(a,"commonInfo.contactInfo.phone.primaryPhone","-"),address:e.get(a,"commonInfo.contactInfo.address.currentAddress","-"),category:e.get(a,"vendorInfo.category","-"),action:t.jsx(f,{onEdit:()=>o(`/purchase-management/vendor-list/edit-${e.get(a,"_id","")}`),onShow:()=>{sessionStorage.setItem("vendorId",JSON.stringify(e.get(a,"_id",""))),o("/purchase-management/vendor-list/view")},onDelete:()=>d({id:JSON.stringify([e.get(a,"_id","")])})})}));return t.jsxs("div",{className:"space-y-4",children:[t.jsx(v,{title:"Vendor",onSearch:()=>{},onAddClick:()=>{o("/purchase-management/vendor-list/add")},listTitle:"Vendor List",FilterSection:()=>t.jsxs("div",{className:"flex gap-5",children:[t.jsx(i,{label:"",options:[{value:"All",label:"Categories"}]}),t.jsx(i,{label:"",options:[{value:"All",label:"Status"}]})]})}),t.jsx(I,{columns:h,rows:g,loading:!1,pagination:{currentPage:e.get(n,"data.pagination.page",1),totalPage:e.get(n,"data.pagination.pages",1),limit:10,onClick:({page:a})=>l(r=>({...r,page:a||1}))}})]})};export{b as default};
