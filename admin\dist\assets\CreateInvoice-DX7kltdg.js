import{a2 as e,ad as C,dv as T,a1 as m,al as r,ag as S,af as u,ac as l,a9 as x,aa as h,ab as s,aj as p,a5 as A,a7 as F,ai as q}from"./index-ClX9RVH0.js";const b=[{label:"medicine",value:"Electronics"},{label:"Clothing",value:"Clothing"},{label:"Furniture",value:"Furniture"}],E=[{label:"1500",value:"1500"}],f=[{itemName:"Paracetamol",unitprice:"11,220",amount:"22,400",status:"ACTIVE"}],$=()=>e.jsx("div",{children:e.jsx("div",{className:"flex flex-col gap-6",children:e.jsx(R,{})})}),R=()=>{const a=C(),{mutate:c}=T(),i=m({initialValues:{name:"",contactNumber:"",paitentId:"",department:"",date:"",doctor:"",items:f.map(o=>({quantity:0,item:o}))},enableReinitialize:!0,onSubmit:(o,{resetForm:d})=>{c(o,{onSuccess:()=>{r.success("Invoice created successfully"),d(),setTimeout(()=>a(S.FINANCIALOPSINVOICE),1e3)},onError:P=>{r.error("Failed to create invoice:"+P.message)}})}}),{handleSubmit:t,values:n,handleChange:X}=i,y={columns:[{title:"S.N",key:"sn"},{title:"Service / Item name",key:"serviceItem"},{title:"",key:"space"},{title:"Quantity",key:"quantity"},{title:"Unit Price",key:"unitprice"},{title:"Amount",key:"amount"},{title:"Action",key:"action"}],rows:f.map((o,d)=>({sn:d,serviceItem:o.itemName,quantity:e.jsx(l,{label:"",type:"number",name:`items.${d}.quantity`,value:i.values.items[d].quantity,onChange:i.handleChange,placeholder:"0"}),space:e.jsx("div",{className:"w-[20rem] bg-black"}),amount:o.unitprice,unitprice:o.amount,action:e.jsx(u,{onDelete:()=>{}})}))};return e.jsx(e.Fragment,{children:e.jsx(x,{value:i,children:e.jsx(h,{onSubmit:t,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-5 py-8 px-5 w-full bg-white",children:[e.jsx(l,{type:"text",label:"Invoice no.",placeholder:"Auto",name:"invoiceno"}),e.jsx(l,{type:"date",label:"Invoice Date",placeholder:"Auto",name:"invoicedate"}),e.jsx(l,{type:"text",label:"Patient ID",placeholder:"Enter ID, Name",name:"patientid"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-5 py-8 px-5 w-full bg-white",children:[e.jsx(s,{label:"Category Name",name:"categoryname",options:b}),e.jsx(s,{label:"Service/Item Name",name:"servicename",options:b}),e.jsx(s,{label:"Refered By",name:"referedby",options:b})]}),e.jsxs("div",{className:"p-6 bg-white",children:[e.jsx(p,{columns:y.columns,rows:y.rows,loading:!1,color:"bg-[#f2f2f2] text-black",textcolor:"text-black"}),e.jsx("div",{className:"flex justify-end w-full h-full",children:e.jsxs("div",{className:"flex flex-col gap-2 w-1/5 h-full ",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{children:"Sub Total with Vat (13%)"}),e.jsx("h1",{className:"font-semibold",children:"51,000"})]}),e.jsxs("div",{className:"flex justify-between items-center ",children:[e.jsx("h1",{children:"Discount"}),e.jsx("div",{className:"",children:e.jsx(s,{label:"",name:"discount",options:E})})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{children:"Total"}),e.jsx("h1",{className:"font-semibold",children:"51,000"})]})]})})]}),e.jsxs("div",{className:"flex gap-2 justify-end items-center",children:[e.jsx("button",{className:"ml-2 bg-gray-500 text-white font-bold py-2 px-4 rounded",children:"Cancel"}),e.jsx("button",{className:"bg-primary text-white font-bold py-2 px-4 rounded",children:"Save & Print"})]})]})})})})},j=[{label:"medicine",value:"Electronics"},{label:"Clothing",value:"Clothing"},{label:"Furniture",value:"Furniture"}],V=[{label:"1500",value:"1500"}],g=[{itemName:"Paracetamol",unitprice:"11,220",amount:"22,400",status:"ACTIVE"}],B=()=>e.jsx("div",{children:e.jsx("div",{className:"flex flex-col gap-6",children:e.jsx(z,{})})}),z=()=>{const a=m({initialValues:{name:"",contactNumber:"",paitentId:"",department:"",date:"",doctor:"",items:g.map(t=>({quantity:0,item:t}))},enableReinitialize:!0,onSubmit:t=>{r.success("Form submitted successfully!"),history.back(),console.log(t)}}),{handleSubmit:c}=a,i={columns:[{title:"S.N",key:"sn"},{title:"Service / Item name",key:"serviceItem"},{title:"",key:"space"},{title:"Quantity",key:"quantity"},{title:"Unit Price",key:"unitprice"},{title:"Amount",key:"amount"},{title:"",key:"action"}],rows:g.map((t,n)=>({sn:n,serviceItem:t.itemName,quantity:e.jsx(l,{label:"",type:"number",name:`items.${n}.quantity`,value:a.values.items[n].quantity,onChange:a.handleChange,placeholder:"0"}),space:e.jsx("div",{className:"w-[36rem] bg-black"}),amount:t.unitprice,unitprice:t.amount,action:e.jsx(u,{onDelete:()=>{}})}))};return e.jsx(e.Fragment,{children:e.jsx(x,{value:a,children:e.jsx(h,{onSubmit:c,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-5 py-8 px-5 w-full bg-white",children:[e.jsx(l,{type:"text",label:"Receipt Number",placeholder:"Auto",name:"receiptnumber"}),e.jsx(l,{type:"date",label:"Receipt Date",placeholder:"Auto",name:"invoicedate"}),e.jsx(l,{type:"number",label:"Patient ID",placeholder:"Enter ID, Name",name:"patientid"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-5 py-8 px-5 w-full bg-white",children:[e.jsx(s,{label:"Category Name",name:"categoryname",options:j}),e.jsx(s,{label:"Service/Item Name",name:"servicename",options:j}),e.jsx(l,{type:"number",label:"Diagnostic Tests (If any)",placeholder:"Enter price per unit",name:"diagnostictests"}),e.jsx(s,{label:"Paitent Type",name:"patienttype",options:j})]}),e.jsxs("div",{className:"p-6 bg-white",children:[e.jsx(p,{columns:i.columns,rows:i.rows,loading:!1,color:"bg-[#f2f2f2] text-black",textcolor:"text-black"}),e.jsx("div",{className:"flex justify-end w-full h-full",children:e.jsxs("div",{className:"flex flex-col gap-2 w-1/5 h-full ",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{children:"Sub Total with Vat (13%)"}),e.jsx("h1",{className:"font-semibold",children:"51,000"})]}),e.jsxs("div",{className:"flex justify-between items-center ",children:[e.jsx("h1",{children:"Discount"}),e.jsx("div",{className:"",children:e.jsx(s,{label:"",name:"discount",options:V})})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{children:"Total"}),e.jsx("h1",{className:"font-semibold",children:"51,000"})]})]})})]}),e.jsxs("div",{className:"flex gap-2 justify-end items-center",children:[e.jsx("button",{className:"ml-2 bg-gray-500 text-white font-bold py-2 px-4 rounded",children:"Cancel"}),e.jsx("button",{className:"bg-primary text-white font-bold py-2 px-4 rounded",children:"Save & Print"})]})]})})})})},v=[{label:"medicine",value:"Electronics"},{label:"Clothing",value:"Clothing"},{label:"Furniture",value:"Furniture"}],O=[{label:"1500",value:"1500"}],N=[{itemName:"Paracetamol",unitprice:"11,220",amount:"22,400",status:"ACTIVE"}],Q=()=>e.jsx("div",{children:e.jsx("div",{className:"flex flex-col gap-6",children:e.jsx(U,{})})}),U=()=>{const a=m({initialValues:{name:"",contactNumber:"",paitentId:"",department:"",date:"",doctor:"",items:N.map(t=>({quantity:0,item:t}))},enableReinitialize:!0,onSubmit:t=>{r.success("Form submitted successfully!"),history.back(),console.log(t)}}),{handleSubmit:c}=a,i={columns:[{title:"S.N",key:"sn"},{title:"Service / Item name",key:"serviceItem"},{title:"",key:"space"},{title:"Quantity",key:"quantity"},{title:"Unit Price",key:"unitprice"},{title:"Amount",key:"amount"},{title:"",key:"action"}],rows:N.map((t,n)=>({sn:n,serviceItem:t.itemName,quantity:e.jsx(l,{label:"",type:"number",name:`items.${n}.quantity`,value:a.values.items[n].quantity,onChange:a.handleChange,placeholder:"0"}),space:e.jsx("div",{className:"w-[36rem] bg-black"}),amount:t.unitprice,unitprice:t.amount,action:e.jsx(u,{onDelete:()=>{}})}))};return e.jsx(e.Fragment,{children:e.jsx(x,{value:a,children:e.jsx(h,{onSubmit:c,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-5 py-8 px-5 w-full bg-white",children:[e.jsx(l,{type:"text",label:"Receipt Number",placeholder:"Auto",name:"receiptno"}),e.jsx(l,{type:"date",label:"Receipt Date",placeholder:"Auto",name:"receiptdate"}),e.jsx(l,{type:"text",label:"Patient ID",placeholder:"Enter ID, Name",name:"patientid"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-5 py-8 px-5 w-full bg-white",children:[e.jsx(s,{label:"Category Name",name:"categoryname",options:v}),e.jsx(s,{label:"Food Item",name:"fooditem",options:v})]}),e.jsxs("div",{className:"p-6 bg-white",children:[e.jsx(p,{columns:i.columns,rows:i.rows,loading:!1,color:"bg-[#f2f2f2] text-black",textcolor:"text-black"}),e.jsx("div",{className:"flex justify-end w-full h-full",children:e.jsxs("div",{className:"flex flex-col gap-2 w-1/5 h-full ",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{children:"Sub Total with Vat (13%)"}),e.jsx("h1",{className:"font-semibold",children:"51,000"})]}),e.jsxs("div",{className:"flex justify-between items-center ",children:[e.jsx("h1",{children:"Discount"}),e.jsx("div",{className:"",children:e.jsx(s,{label:"",name:"discount",options:O})})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{children:"Total"}),e.jsx("h1",{className:"font-semibold",children:"51,000"})]})]})})]}),e.jsxs("div",{className:"flex gap-2 justify-end items-center",children:[e.jsx("button",{className:"ml-2 bg-gray-500 text-white font-bold py-2 px-4 rounded",children:"Cancel"}),e.jsx("button",{className:"bg-primary text-white font-bold py-2 px-4 rounded",children:"Save & Print"})]})]})})})})},J=[{label:"medicine",value:"Electronics"},{label:"Clothing",value:"Clothing"},{label:"Furniture",value:"Furniture"}],L=[{label:"1500",value:"1500"}],w=[{label:"Dr. John Doe",value:"Dr. John Doe"}],I=[{itemName:"Paracetamol",unitprice:"11,220",amount:"22,400",status:"ACTIVE"}],H=()=>e.jsx("div",{children:e.jsx("div",{className:"flex flex-col gap-6",children:e.jsx(M,{})})}),M=()=>{const a=m({initialValues:{name:"",contactNumber:"",paitentId:"",department:"",date:"",doctor:"",items:I.map(t=>({quantity:0,item:t}))},enableReinitialize:!0,onSubmit:t=>{r.success("Form submitted successfully!"),history.back(),console.log(t)}}),{handleSubmit:c}=a,i={columns:[{title:"S.N",key:"sn"},{title:"Service / Item name",key:"serviceItem"},{title:"",key:"space"},{title:"Quantity",key:"quantity"},{title:"Unit Price",key:"unitprice"},{title:"Amount",key:"amount"},{title:"",key:"action"}],rows:I.map((t,n)=>({sn:n,serviceItem:t.itemName,quantity:e.jsx(l,{label:"",type:"number",name:`items.${n}.quantity`,value:a.values.items[n].quantity,onChange:a.handleChange,placeholder:"0"}),space:e.jsx("div",{className:"w-[36rem] bg-black"}),amount:t.unitprice,unitprice:t.amount,action:e.jsx(u,{onDelete:()=>{}})}))};return e.jsx(e.Fragment,{children:e.jsx(x,{value:a,children:e.jsx(h,{onSubmit:c,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-5 py-8 px-5 w-full bg-white",children:[e.jsx(l,{type:"text",label:"Receipt Number",placeholder:"Auto",name:"receiptno"}),e.jsx(l,{type:"date",label:"Receipt Date",placeholder:"Auto",name:"receiptdate"}),e.jsx(l,{type:"text",label:"Patient ID",placeholder:"Enter ID, Name",name:"patientid"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-5 py-8 px-5 w-full bg-white",children:[e.jsx(s,{label:"Product Name",name:"productname",options:J}),e.jsx(s,{label:"Refered By",name:"referedby",options:w}),e.jsx(s,{label:"Paitent Type",name:"paitenttype",options:w})]}),e.jsxs("div",{className:"p-6 bg-white",children:[e.jsx(p,{columns:i.columns,rows:i.rows,loading:!1,color:"bg-[#f2f2f2] text-black",textcolor:"text-black"}),e.jsx("div",{className:"flex justify-end w-full h-full",children:e.jsxs("div",{className:"flex flex-col gap-2 w-1/5 h-full ",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{children:"Sub Total with Vat (13%)"}),e.jsx("h1",{className:"font-semibold",children:"51,000"})]}),e.jsxs("div",{className:"flex justify-between items-center ",children:[e.jsx("h1",{children:"Discount"}),e.jsx("div",{className:"",children:e.jsx(s,{label:"",name:"discount",options:L})})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{children:"Total"}),e.jsx("h1",{className:"font-semibold",children:"51,000"})]})]})})]}),e.jsxs("div",{className:"flex gap-2 justify-end items-center",children:[e.jsx("button",{className:"ml-2 bg-gray-500 text-white font-bold py-2 px-4 rounded",children:"Cancel"}),e.jsx("button",{className:"bg-primary text-white font-bold py-2 px-4 rounded",children:"Save & Print"})]})]})})})})},G=[{label:"medicine",value:"Electronics"},{label:"Clothing",value:"Clothing"},{label:"Furniture",value:"Furniture"}],Y=[{label:"1500",value:"1500"}],k=[{label:"Dr. John Doe",value:"Dr. John Doe"}],D=[{itemName:"Paracetamol",unitprice:"11,220",amount:"22,400",status:"ACTIVE"},{itemName:"Paracetamol",unitprice:"11,220",amount:"22,400",status:"ACTIVE"}],K=()=>e.jsx("div",{children:e.jsx("div",{className:"flex flex-col gap-6",children:e.jsx(W,{})})}),W=()=>{const a=m({initialValues:{name:"",contactNumber:"",paitentId:"",department:"",date:"",doctor:"",items:D.map(t=>({quantity:0,item:t}))},enableReinitialize:!0,onSubmit:t=>{r.success("Form submitted successfully!"),history.back(),console.log(t)}}),{handleSubmit:c}=a,i={columns:[{title:"S.N",key:"sn"},{title:"Service / Item name",key:"serviceItem"},{title:"",key:"space"},{title:"Quantity",key:"quantity"},{title:"Unit Price",key:"unitprice"},{title:"Amount",key:"amount"},{title:"",key:"action"}],rows:D.map((t,n)=>({sn:n,serviceItem:t.itemName,quantity:e.jsx(l,{label:"",type:"number",name:`items.${n}.quantity`,value:a.values.items[n].quantity,onChange:a.handleChange,placeholder:"0"}),space:e.jsx("div",{className:"w-[36rem] bg-black"}),amount:t.unitprice,unitprice:t.amount,action:e.jsx(u,{onDelete:()=>{}})}))};return e.jsx(e.Fragment,{children:e.jsx(x,{value:a,children:e.jsx(h,{onSubmit:c,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-5 py-8 px-5 w-full bg-white",children:[e.jsx(l,{type:"text",label:"Receipt Number",placeholder:"Auto",name:"receiptno"}),e.jsx(l,{type:"date",label:"Receipt Date",placeholder:"Auto",name:"receiptdate"}),e.jsx(l,{type:"text",label:"Patient ID",placeholder:"Enter ID, Name",name:"patientid"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-5 py-8 px-5 w-full bg-white",children:[e.jsx(s,{label:"Product Name",name:"productname",options:G}),e.jsx(s,{label:"Refered By",name:"referedby",options:k}),e.jsx(s,{label:"Paitent Type",name:"paitenttype",options:k})]}),e.jsxs("div",{className:"p-6 bg-white",children:[e.jsx(p,{columns:i.columns,rows:i.rows,loading:!1,color:"bg-[#f2f2f2] text-black",textcolor:"text-black"}),e.jsx("div",{className:"flex justify-end w-full h-full",children:e.jsxs("div",{className:"flex flex-col gap-2 w-1/5 h-full ",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{children:"Sub Total with Vat (13%)"}),e.jsx("h1",{className:"font-semibold",children:"51,000"})]}),e.jsxs("div",{className:"flex justify-between items-center ",children:[e.jsx("h1",{children:"Discount"}),e.jsx("div",{className:"",children:e.jsx(s,{label:"",name:"discount",options:Y})})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{children:"Total"}),e.jsx("h1",{className:"font-semibold",children:"51,000"})]})]})})]}),e.jsxs("div",{className:"flex gap-2 justify-end items-center",children:[e.jsx("button",{className:"ml-2 bg-gray-500 text-white font-bold py-2 px-4 rounded",children:"Cancel"}),e.jsx("button",{className:"bg-primary text-white font-bold py-2 px-4 rounded",children:"Save & Print"})]})]})})})})},_=()=>{const[a,c]=A.useState("IPD"),i=()=>{switch(a){case"IPD":return e.jsx($,{});case"OPD":return e.jsx(B,{});case"Canteen":return e.jsx(Q,{});case"Pharmacy":return e.jsx(H,{});case"Lab":return e.jsx(K,{})}},t=C();return e.jsxs("div",{children:[e.jsx(F,{title:"Create Invoice",onSearch:()=>{},onAddClick:()=>{t(S.EMERGENCYADDPATIENT)},listTitle:"Create Invoice",hideHeader:!0,FilterSection:()=>e.jsxs("div",{className:"flex gap-5",children:[e.jsx(s,{label:"",options:[{value:"All",label:"Bed Allocated Time"}]}),e.jsx(s,{label:"",options:[{value:"All",label:"Status"}]})]})}),e.jsx("div",{className:"py-2 bg-white",children:e.jsx(q,{tabs:["IPD","OPD","Canteen","Pharmacy","Lab"],defaultTab:a,onTabChange:n=>c(n)})}),e.jsx("div",{className:"flex-1",children:i()})]})};export{_ as default};
