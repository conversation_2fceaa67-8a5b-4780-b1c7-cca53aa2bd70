import React from "react";
import { Icon } from "@iconify/react";
import dayjs from "dayjs";

interface Appointment {
  id: string;
  type: string;
  patientName: string;
  date: string;
  time: string;
  status: "Confirmed" | "Completed" | "Upcoming";
}

interface AppointmentsProps {
  appointments: {
    upcoming: Appointment[];
    history: Appointment[];
  };
}

const Appointments: React.FC<AppointmentsProps> = ({ appointments }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Confirmed":
        return "bg-blue/10 text-blue border border-blue/20";
      case "Completed":
        return "bg-green/10 text-green border border-green/20";
      case "Upcoming":
        return "bg-blue/10 text-blue border border-blue/20";
      default:
        return "bg-gray/10 text-gray-600 border border-gray/20";
    }
  };

  const AppointmentCard = ({ appointment }: { appointment: Appointment }) => (
    <div className="border-l-4 border-l-blue bg-blue/5 rounded-r-lg p-3 mb-3">
      <div className="flex items-start justify-between mb-2">
        <div>
          <h4 className="text-sm text-gray-600 mb-1">{appointment.type}</h4>
          <p className="text-sm font-medium text-gray-900">
            {appointment.patientName}
          </p>
        </div>
        <span
          className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(
            appointment.status
          )}`}
        >
          {appointment.status}
        </span>
      </div>
      <div className="flex items-center gap-1 text-xs text-gray-600">
        <Icon icon="material-symbols:calendar-today" className="w-3 h-3" />
        <span>
          {appointment.date} - {appointment.time}
        </span>
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
      {/* Header */}
      <div className="bg-blue-50 px-4 py-3 border-b flex items-center justify-between">
        <h3 className="text-base font-semibold text-blue">Appointments</h3>
        {/* <button className="text-blue text-xs hover:text-blue-700">
          <Icon icon="material-symbols:more-horiz" className="w-4 h-4" />
        </button> */}
      </div>

      <div className="p-4">
        {/* Today Section */}
        {(appointments?.upcoming?.filter((apt) => {
          const appointmentDate = dayjs(apt.date).format("YYYY-MM-DD");
          const today = dayjs().format("YYYY-MM-DD");
          return appointmentDate === today;
        }).length > 0 ||
          appointments.history.filter((apt) => {
            const appointmentDate = dayjs(apt.date).format("YYYY-MM-DD");
            const today = dayjs().format("YYYY-MM-DD");
            return appointmentDate === today;
          }).length > 0) && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-blue mb-3">Today</h4>
            <div>
              {/* Today's Upcoming Appointments */}
              {appointments?.upcoming
                ?.filter((apt) => {
                  const appointmentDate = dayjs(apt.date).format("YYYY-MM-DD");
                  const today = dayjs().format("YYYY-MM-DD");
                  return appointmentDate === today;
                })
                .map((appointment) => (
                  <AppointmentCard
                    key={appointment.id}
                    appointment={appointment}
                  />
                ))}

              {/* Today's History Appointments */}
              {appointments.history
                .filter((apt) => {
                  const appointmentDate = dayjs(apt.date).format("YYYY-MM-DD");
                  const today = dayjs().format("YYYY-MM-DD");
                  return appointmentDate === today;
                })
                .map((appointment) => (
                  <AppointmentCard
                    key={appointment.id}
                    appointment={appointment}
                  />
                ))}
            </div>
          </div>
        )}

        {/* Upcoming Section */}
        {appointments?.upcoming?.filter((apt) => {
          const appointmentDate = dayjs(apt.date).format("YYYY-MM-DD");
          const today = dayjs().format("YYYY-MM-DD");
          return appointmentDate > today;
        }).length > 0 && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-blue mb-3">Upcoming</h4>
            <div>
              {appointments?.upcoming
                .filter((apt) => {
                  const appointmentDate = dayjs(apt.date).format("YYYY-MM-DD");
                  const today = dayjs().format("YYYY-MM-DD");
                  return appointmentDate > today;
                })
                .map((appointment) => (
                  <AppointmentCard
                    key={appointment.id}
                    appointment={appointment}
                  />
                ))}
            </div>
          </div>
        )}

        {/* History Section */}
        {appointments.history.filter((apt) => {
          const appointmentDate = dayjs(apt.date).format("YYYY-MM-DD");
          const today = dayjs().format("YYYY-MM-DD");
          return appointmentDate < today;
        }).length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-blue mb-3">History</h4>
            <div>
              {appointments.history
                .filter((apt) => {
                  const appointmentDate = dayjs(apt.date).format("YYYY-MM-DD");
                  const today = dayjs().format("YYYY-MM-DD");
                  return appointmentDate < today;
                })
                .map((appointment) => (
                  <AppointmentCard
                    key={appointment.id}
                    appointment={appointment}
                  />
                ))}
            </div>
          </div>
        )}

        {/* No appointments message */}
        {appointments.upcoming.length === 0 &&
          appointments.history.length === 0 && (
            <div className="text-center py-8">
              <Icon
                icon="material-symbols:event-busy"
                className="w-12 h-12 text-gray-300 mx-auto mb-3"
              />
              <p className="text-gray-500 text-sm">No appointments found</p>
            </div>
          )}
      </div>
    </div>
  );
};

export default Appointments;
