import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../../../layouts/Table/MasterTable";
import classNames from "classnames";

const Equipments = () => {
  const tableData = {
    columns: [
      { title: "Item Name", key: "itemName" },
      { title: "Unit", key: "unit" },
      { title: "Quantity", key: "quantity" },
      { title: "Minimum Stock", key: "minimumStock" },
      { title: "Expiration Date", key: "expirationDate" },
      { title: "Batch", key: "batch" },
      { title: "Supplier", key: "supplier" },
      { title: "Order", key: "orderQuantity" },
    ],
    rows: [
      {
        itemName: "Thermometer ",
        unit: "Pieces",
        quantity: 3,
        minimumStock: 10,
        expirationDate: "N/A",
        batch: "TH-2023-011",
        supplier: "TechHealth Ltd.",
        orderQuantity: 10,
      },
      {
        itemName: "Oxygen Mask",
        unit: "Units",
        quantity: 4,
        minimumStock: 10,
        expirationDate: "Sep 9, 2025",
        batch: "OXM-2025-021",
        supplier: "LifeLine Equipment",
        orderQuantity: 10,
      },
      {
        itemName: "IV Stand",
        unit: "Units",
        quantity: 6,
        minimumStock: 8,
        expirationDate: "N/A",
        batch: "IVS-2023-010",
        supplier: "MediEquip Supplies",
        orderQuantity: 5,
      },
      {
        itemName: "Wheelchair",
        unit: "Units",
        quantity: 2,
        minimumStock: 5,
        expirationDate: "N/A",
        batch: "WHC-2023-008",
        supplier: "CareMobility Ltd.",
        orderQuantity: 3,
      },
      {
        itemName: "BP Monitor",
        unit: "Pieces",
        quantity: 4,
        minimumStock: 6,
        expirationDate: "N/A",
        batch: "BPM-2024-002",
        supplier: "HealthTech Inc.",
        orderQuantity: 2,
      },
      {
        itemName: "Stethoscope",
        unit: "Pieces",
        quantity: 10,
        minimumStock: 15,
        expirationDate: "N/A",
        batch: "STH-2023-007",
        supplier: "MediHear Ltd.",
        orderQuantity: 5,
      },
      {
        itemName: "Defibrillator",
        unit: "Units",
        quantity: 1,
        minimumStock: 2,
        expirationDate: "N/A",
        batch: "DEF-2024-009",
        supplier: "LifeSaver Equipments",
        orderQuantity: 1,
      },
      {
        itemName: "Suction Machine",
        unit: "Units",
        quantity: 2,
        minimumStock: 4,
        expirationDate: "N/A",
        batch: "SUC-2023-005",
        supplier: "MediVac Systems",
        orderQuantity: 2,
      },
      {
        itemName: "Pulse Oximeter",
        unit: "Pieces",
        quantity: 6,
        minimumStock: 10,
        expirationDate: "N/A",
        batch: "PUL-2024-003",
        supplier: "VitalSense Corp.",
        orderQuantity: 4,
      },
      {
        itemName: "Infusion Pump",
        unit: "Units",
        quantity: 3,
        minimumStock: 5,
        expirationDate: "N/A",
        batch: "INF-2024-006",
        supplier: "FlowMed Devices",
        orderQuantity: 2,
      },
    ],
  };

  const statCards = [
    {
      title: "Total Items",
      count: 96,
      subtitle: "Active inventory items",
      icon: "pajamas:work-item-epic",
      iconBg: "text-gray-9000",
      textColor: "text-gray-900",
      bgColor: "bg-[#9DF2DB]",
    },
    {
      title: "Low Stock",
      count: 8,
      subtitle: "Items below minimum",
      icon: "tdesign:error-triangle",
      iconBg: "text-rose-600",
      textColor: "text-rose-600",
      bgColor: "bg-[#FAECEC]",
    },
    {
      title: "Expiring Soon",
      count: 18,
      subtitle: "within 10 days",
      icon: "lets-icons:date-fill",
      iconBg: "text-amber-500",
      textColor: "text-amber-500",
      bgColor: "bg-[#CEF9F6]",
    },
    {
      title: "Today's Reorders",
      count: 32,
      subtitle: "Active inventory items",
      icon: "solar:restart-circle-bold",
      iconBg: "text-lime-600",
      textColor: "text-lime-600",
      bgColor: "bg-[#D8F7DE]",
    },
  ];

  return (
    <div className='p-2 mt-4'>
      {/* Header */}

      {/* Metrics Cards */}
      <div className='grid grid-cols-4 gap-4 mb-6'>
        {statCards.map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgColor} rounded-lg shadow-sm border border-gray-200 p-4`}
          >
            <div className='flex items-center justify-between gap-2'>
              <div className='text-sm font-semibold text-gray-800'>
                {stat.title}
              </div>
              <div>
                <Icon
                  icon={stat.icon}
                  width='24'
                  height='24'
                  className={`${stat.iconBg}`}
                />
              </div>
            </div>
            <div className={`text-2xl font-bold mt-1 ${stat.textColor}`}>
              {stat.count}
            </div>
            <div className='text-xs text-gray-500'>{stat.subtitle}</div>
          </div>
        ))}
      </div>

      <div className='bg-white p-6'>
        <div className='flex items-center justify-between mb-4'>
          <h2 className='text-lg font-semibold text-gray-900'>
            Stock Inventory
          </h2>
          <div className='flex items-center gap-3'>
            <div className='relative'>
              <svg
                className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
                />
              </svg>
              <input
                placeholder='Search items'
                className='pl-10 w-64 border border-gray-300 rounded-md px-3 py-2'
              />
            </div>
            <button className='flex items-center gap-2 bg-transparent border border-gray-300 rounded-md px-4 py-2 hover:bg-gray-50'>
              Sort
              <svg
                className='w-4 h-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M19 9l-7 7-7-7'
                />
              </svg>
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Add New Item
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Reorder Selected
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Download Report
            </button>
          </div>
        </div>
        <div>
          <MasterTable
            columns={tableData.columns}
            rows={tableData.rows}
            loading={false}
          />
        </div>
      </div>
    </div>
  );
};

export default Equipments;
