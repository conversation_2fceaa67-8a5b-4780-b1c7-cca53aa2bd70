import { ISidebarDashboardRoutes } from "../Interface/global.interface";
import { FrontendRoutes } from "./FrontendRoutes";

export const SidebarDashboardRoutes: ISidebarDashboardRoutes = {
  title: "Dashboard",
  routes: [
    // {
    //   id: 10001,
    //   path: "#",
    //   title: "Dashboard",
    //   icon: "mage:dashboard-2",
    //   children: [
    //     {
    //       path: FrontendRoutes.ADMINDASHBOARD,
    //       title: "Admin Dashboard",
    //       id: 10002,
    //     },
    //     {
    //       path: FrontendRoutes.DOCTORDASHBOARD,
    //       title: "Doctor Dashboard",
    //       id: 10003,
    //     },
    //     {
    //       path: FrontendRoutes.NURSEDASHBOARD,
    //       title: "Nurse Dashboard",
    //       id: 10135,
    //     },
    //     {
    //       path: FrontendRoutes.ACCOUNTDASHBOARD,
    //       title: "Account Dashboard",
    //       id: 10004,
    //     },

    //     {
    //       id: 10005,
    //       path: FrontendRoutes.LABDASHBOARD,
    //       title: "Lab Dashboard",
    //     },

    //     {
    //       id: 10006,
    //       path: FrontendRoutes.RADIOLOGYDASHBOARD,
    //       title: "Radiology Dashboard",
    //     },

    //     {
    //       id: 10121, //toconvert into 10121
    //       path: FrontendRoutes.PHARMACYDASHBOARD,
    //       title: "Pharmacy Dashboard",
    //     },
    //   ],
    // },
    {
      id: 10001,
      path: "/dashboard",
      title: "Dashboard",
      icon: "mage:dashboard-2",
    },
  ],
};

export const SidebarPeoplesRoutes: ISidebarDashboardRoutes = {
  title: "Peoples",
  routes: [
    {
      id: 10007,
      path: "#",
      title: "Patient",
      icon: "uil:wheelchair",
      children: [
        { path: FrontendRoutes.PATIENTLIST, title: "Patient", id: 10009 },
        // { path: FrontendRoutes.INPATIENTLIST, title: "In Patient", id: 10009 },
      ],
    },
    {
      id: 10008,
      path: "#",
      title: "Medical-Staff",
      icon: "fa6-solid:user-doctor",
      children: [
        { path: FrontendRoutes.DOCTORLIST, title: "Doctor", id: 10009 },
        // {
        //   path: FrontendRoutes.DOCTORAVAILABILITY,
        //   title: 'Doctor Availability',
        //   id: 10133,
        // },

        {
          id: 10010,
          path: FrontendRoutes.NURSELIST,
          title: "Nurse",
        },

        {
          id: 10011,
          path: FrontendRoutes.PHARMACISTSLIST,
          title: "Pharmacist",
        },

        {
          id: 10012,
          path: FrontendRoutes.LABTECHNICIANLIST,
          title: "Lab Technician / Radiologists",
        },
        { path: FrontendRoutes.DOCTORREVIEWS, title: "Reviews", id: 10013 },
      ],
    },
    {
      id: 10014,
      path: FrontendRoutes.STAFFLIST,
      title: "Non Medical-Staff",
      icon: "healthicons:doctor-male-outline",
    },
  ],
};

export const SidebarTokenManagement: ISidebarDashboardRoutes = {
  title: "Token Management",
  routes: [
    {
      id: 10015,
      path: FrontendRoutes.ACCOUNTTOKENMANAGEMENT,
      title: "Token List",
      icon: "fa6-solid:user-doctor",
    },
    {
      id: 10016,
      path: FrontendRoutes.TOKENCONFIG,
      title: " Token Configuration",
      icon: "fa6-solid:user-doctor",
    },
  ],
};

export const SidebarDailyScheduleRoutes: ISidebarDashboardRoutes = {
  title: "Daily Schedule",
  routes: [
    {
      id: 10017,
      path: "#",
      title: "Shift Management",
      icon: "tdesign:task-time",
      children: [
        { path: FrontendRoutes.DAILYSHIFT, title: "Daily List", id: 10018 },
        { path: FrontendRoutes.SHIFTLIST, title: "Shift Config", id: 10019 },
        { path: FrontendRoutes.SHIFTASSIGN, title: "Shift Assign", id: 10020 },
      ],
    },

    {
      id: 10021,
      path: FrontendRoutes.ATTENDANCELIST,
      title: "Attendance",
      icon: "ix:user-success-filled",
    },

    {
      id: 10022,
      path: "#",
      title: "Bed Allocation",
      icon: "mdi:bed-outline",
      children: [
        {
          path: FrontendRoutes.BEDALLOCATIONLIST,
          title: "Bed Allocation List",
          id: 10023,
        },

        {
          path: FrontendRoutes.BEDROOMDETAILS,
          title: "Rooms Details",
          id: 10024,
        },
      ],
    },
  ],
};

export const SidebarServiceDepartmentsRoutes: ISidebarDashboardRoutes = {
  title: "Service Departments",
  routes: [
    {
      id: 10025,
      path: "#",
      title: "OPD",
      icon: "mdi:walk",
      children: [
        {
          path: FrontendRoutes.APPOINTMENTLIST,
          title: "Appointment List",
          id: 10026,
        },
        {
          path: FrontendRoutes.OPDAPPOINTMENTFORM,
          title: "OPD Appointment Form",
          id: 10137,
        },
        {
          path: FrontendRoutes.GENERALCHECKUPLIST,
          title: "General Checkup",
          id: 10027,
        },
        {
          path: FrontendRoutes.DoctorsAppointment,
          title: "Doctors Appointment",
          id: 10136,
        },
      ],
    },
    {
      id: 10028,
      path: FrontendRoutes.GENERALWARD,
      title: "IPD",
      icon: "material-symbols:ward-outline",
    },
    {
      id: 10029,
      path: FrontendRoutes.EMERGENCYROOM,
      title: "Emergency",
      icon: "material-symbols-light:e911-emergency-outline",
    },

    {
      id: 10030,
      path: "#",
      title: "Operation Theater",
      icon: "material-symbols-light:ward-outline-sharp",
      children: [
        {
          path: FrontendRoutes.AVAILABILITY,
          title: "Surgery Availability",
          id: 10031,
        },
        {
          path: FrontendRoutes.GENERALOT,
          title: "Assign OT",
          id: 10031,
        },
        {
          path: FrontendRoutes.CONFIGURATIONOT,
          title: "OT Configuration",
          id: 10032,
        },
      ],
    },
    {
      id: 10033,
      path: FrontendRoutes.CERTIFICATE,
      title: "Certificate",
      icon: "ph:certificate-bold",
    },

    {
      id: 10034,
      path: "#",
      title: "Ambulance",
      icon: "ph:ambulance-light",
      children: [
        {
          path: FrontendRoutes.AMBULANCETYPE,
          title: "Ambulance Type",
          id: 10035,
        },
        {
          path: FrontendRoutes.AMBULANCELIST,
          title: "Ambulance List",
          id: 10036,
        },
        { path: FrontendRoutes.PRICECONFIG, title: "Price Config", id: 10037 },
        {
          path: FrontendRoutes.AMBULANCEINQUIRY,
          title: "Ambulance Inquiry",
          id: 10038,
        },
      ],
    },

    {
      id: 10039,
      path: "#",
      title: "Inventory",
      icon: "tabler:brand-databricks",
      children: [
        { path: FrontendRoutes.INVENTORY, title: "Inventory List", id: 10040 },
        {
          path: FrontendRoutes.INVENTORYPRODUCTLIST,
          title: "Product List",
          id: 10041,
        },
        {
          path: FrontendRoutes.INVENTORYCATEGORYLIST,
          title: "Category List",
          id: 10042,
        },
      ],
    },

    {
      id: 10043,
      path: "#",
      title: "Donation Management",
      icon: "arcticons:blood-donor",
      children: [
        {
          path: FrontendRoutes.BLOODDONORLIST,
          title: "Donor List",
          id: 10044,
        },
        {
          path: FrontendRoutes.RECEIVER,
          title: "Receiver List",
          id: 10134,
        },
        {
          path: FrontendRoutes.BLOODBANK,
          title: "Blood Bank",
          id: 10045,
        },
        {
          path: FrontendRoutes.CASHEQUIPMENT,
          title: "Cash & Equipment",
          id: 10046,
        },
      ],
    },
  ],
};

export const SidebarLaboratoryRoutes = {
  title: "Laboratory",
  routes: [
    {
      id: 10046,
      path: FrontendRoutes.LABTESTREQUESTLIST,
      title: "Test Request",
      icon: "carbon:result-draft",
    },
    {
      id: 10047,
      path: FrontendRoutes.LABTESTRESULT,
      title: "Test Result",
      icon: "carbon:result",
    },
    {
      id: 10048,
      path: "#",
      title: "Lab Inventory",
      icon: "akar-icons:shipping-box-02",
      children: [
        {
          path: FrontendRoutes.LABINVENTORY,
          title: "Inventory",
          id: 10049,
        },
        {
          path: FrontendRoutes.LABPRODUCTLIST,
          title: "Product List",
          id: 10050,
        },
      ],
    },
    {
      id: 10051,
      path: FrontendRoutes.LABPURCHASE,
      title: "Purchase",
      icon: "famicons:bag-check-outline",
    },
    {
      id: 10052,
      path: "#",
      title: "Billing And Payments",
      icon: "ph:money-duotone",
      children: [
        {
          path: FrontendRoutes.LABINVOICELIST,
          title: "Invoice List",
          id: 10053,
        },
        // {
        //   path: FrontendRoutes.LABDUELIST,
        //   title: "Due List",
        //   id: 10054,
        // },
      ],
    },
    {
      id: 10055,
      path: "#",
      title: "Test Config",
      icon: "clarity:settings-line",
      children: [
        {
          path: FrontendRoutes.LABDEPARTMENTCONFIG,
          title: "Department Config",
          id: 10056,
        },
        {
          path: FrontendRoutes.LABSUBCATEGORYCONFIG,
          title: "Sub-Category Config",
          id: 10057,
        },
        {
          path: FrontendRoutes.LABTESTTYPECONFIG,
          title: "Test Type Config",
          id: 10058,
        },
      ],
    },
  ],
};

export const SidebarRadiologyRoutes = {
  title: "Radiology",
  id: "",
  routes: [
    {
      id: 10060,
      path: FrontendRoutes.RADIOLOGYTESTREQUEST,
      title: "Test Request",
      icon: "carbon:result",
    },
    {
      id: 10061,
      path: FrontendRoutes.RADIOLOGYREPORT,
      title: "Test Result",
      icon: "carbon:result",
    },
    {
      id: 10062,
      path: "#",
      title: "Radiology Inventory",
      icon: "carbon:result-draft",
      children: [
        {
          path: FrontendRoutes.RADIOLOGYINVENTORY,
          title: "Inventory",
          id: 10063,
        },
        {
          path: FrontendRoutes.RADIOLOGYPRODUCTLIST,
          title: "Product List",
          id: 10064,
        },
      ],
    },
    {
      path: FrontendRoutes.RADIOLOGYPURCHASE,
      title: "Purchase",
      id: 10059,
      icon: "clarity:settings-line",
    },
    {
      id: 10065,
      path: "#",
      title: "Billing And Payments",
      icon: "ph:money-duotone",
      children: [
        {
          path: FrontendRoutes.RADIOLOGYINVOICE,
          title: "Invoice List",
          id: 10066,
        },
        // {
        //   path: FrontendRoutes.RADIOLOGYDUELIST,
        //   title: "Due List",
        //   id: 10067,
        // },
      ],
    },

    {
      id: 10068,
      path: "#",
      title: "Test Config",
      icon: "clarity:settings-line",
      children: [
        {
          path: FrontendRoutes.RADIOLOGYDEPARTMENT,
          title: "Department ",
          id: 10070,
        },
        {
          path: FrontendRoutes.SERVICETYPE,
          title: "Sub Department",
          id: 10069,
        },
        {
          path: FrontendRoutes.TESTLISTCONFIG,
          title: "Test List Config",
          id: 10070,
        },
      ],
    },
  ],
};

export const SidebarCanteenRoutes = {
  title: "Canteen",
  routes: [
    {
      id: 10071,
      path: FrontendRoutes.CANTEENPURCHASE,
      title: "Purchase",
      icon: "famicons:bag-check-outline",
    },
    {
      id: 10072,
      path: FrontendRoutes.CANTEENMENULIST,
      title: "Menu List",
      icon: "carbon:result",
    },
    {
      id: 10073,
      path: FrontendRoutes.CANTEENDIETCHART,
      title: "Diet Chart",
      icon: "carbon:result",
    },
    {
      id: 10074,
      path: "#",
      title: "Inventory",
      icon: "carbon:result-draft",
      children: [
        {
          path: FrontendRoutes.CANTEENSTOCKLIST,
          title: "Stock List",
          id: 10075,
        },

        {
          path: FrontendRoutes.CANTEENPRODUCT,
          title: "Product List",
          id: 10076,
        },
      ],
    },
    {
      id: 10077,
      path: "#",
      title: "Billing & Finance",
      icon: "akar-icons:shipping-box-02",
      children: [
        {
          path: FrontendRoutes.CANTEENINVOICE,
          title: "Invoice",
          id: 10078,
        },
        {
          path: FrontendRoutes.CANTEENMEALCONFIG,
          title: "Meal Price Config",
          id: 10079,
        },
      ],
    },
  ],
};

export const SidebarBillingAndPaymentsRoutes = {
  title: "Billing and Payment",
  routes: [
    {
      id: 10080,
      path: "#",
      title: "Financial Ops",
      icon: "ph:money-duotone",
      children: [
        {
          path: FrontendRoutes.FINANCIALOPSINVOICE,
          title: "Invoice",
          id: 10081,
        },
        {
          path: FrontendRoutes.FINANCIAlPAYMENT,
          title: "Advance Payment",
          id: 10082,
        },
      ],
    },
    {
      id: 10083,
      path: "#",
      title: "Payroll",
      icon: "mdi:account-hard-hat",
      children: [
        {
          path: FrontendRoutes.PAYROLL_LIST,
          title: "Payroll List",
          id: 10084,
        },
        {
          path: FrontendRoutes.PAYROLL_CONFIG,
          title: "Payroll Config",
          id: 10085,
        },
      ],
    },

    {
      id: 10086,
      path: "#",
      title: "Exp Management",
      icon: "arcticons:expense-register",
      children: [
        {
          path: FrontendRoutes.EXPENSE_LIST,
          title: "Expense List",
          id: 10087,
        },
        {
          path: FrontendRoutes.EXPENSE_CATEGORY,
          title: "Expense Category",
          id: 10088,
        },
        {
          path: FrontendRoutes.VOUCHER,
          title: "Voucher",
          id: 10122,
        },
      ],
    },

    {
      id: 10089,
      path: "#",
      title: "Vendor Management",
      icon: "tabler:brand-databricks",
      children: [
        {
          path: FrontendRoutes.PURCHASELIST,
          title: "Purchase List",
          id: 10090,
        },

        {
          path: FrontendRoutes.PRODUCTLIST,
          title: "Product List",
          id: 10136,
        },

        {
          path: FrontendRoutes.VENDORORDERLIST,
          title: "Order List",
          id: 10136, //to be 37
        },

        {
          path: FrontendRoutes.VENDORBILLING,
          title: "Billing",
          id: 10136, //to be 38
        },
        // {
        //   path: FrontendRoutes.PURCHASEORDER,
        //   title: "Purchase Order",
        //   id: 10091,
        // },
        {
          path: FrontendRoutes.PURCHASERETURN,
          title: "Purchase Return",
          id: 10092,
        },
        { path: FrontendRoutes.VENDORLIST, title: "Vendor List", id: 10093 },
      ],
    },
    {
      id: 10094,
      path: "#",
      title: "Banking Details",
      icon: "hugeicons:bank",
      children: [
        {
          path: FrontendRoutes.BANK,
          title: "Bank Details",
          id: 10095,
        },
        {
          path: FrontendRoutes.TRANSACTIONDETAILS,
          title: "Transaction Details",
          id: 10096,
        },
      ],
    },

    {
      id: 10123,
      path: "#",
      title: "Accounting",
      icon: "map:accounting",
      children: [
        {
          path: FrontendRoutes.BALANCESHEET,
          title: "Balance Sheet",
          id: 10124,
        },
        // {
        //   path: FrontendRoutes.TRANSACTIONDETAILS,
        //   title: 'Payment voucher',
        //   id: 10125,
        // },
        {
          path: FrontendRoutes.PAYMENT_VOUCHER,
          title: "Payment Voucher",
          id: 10126,
        },
        {
          path: FrontendRoutes.PURCHASE_RETURN,
          title: "Purchase Return",
          id: 10127,
        },
        {
          path: FrontendRoutes.SALES_VOUCHER,
          title: "Sales Voucher",
          id: 10128,
        },
        {
          path: FrontendRoutes.SALES_RETURN,
          title: "Sales Return",
          id: 10129,
        },
        {
          path: FrontendRoutes.TRANSACTION_LIST,
          title: "Transaction List",
          id: 10130,
        },
        {
          path: FrontendRoutes.GENERAL_LEDGER_BALANCE,
          title: "General Ledger Balance",
          id: 10131,
        },
        {
          path: FrontendRoutes.INCOME_STATEMENT,
          title: "Income Statement",
          id: 10132,
        },
      ],
    },
  ],
};

export const SidebarSettingsRoutes = {
  title: "Settings",
  routes: [
    {
      id: 10097,
      path: "#",
      title: "Settings",
      icon: "lets-icons:setting-line",
      children: [
        {
          path: FrontendRoutes.ROLEMANAGEMENT,
          title: "Role Management",
          id: 10098,
        },
        // {
        //   path: FrontendRoutes.USERMANAGEMENT,
        //   title: "User Management",
        //   id: 10099,
        // },
        {
          path: FrontendRoutes.SETTINGSDEPARTMENTCONFIG,
          title: "Department Config",
          id: 10100,
        },

        {
          path: FrontendRoutes.WARD,
          title: "Ward Config",
          id: 10101,
        },

        {
          path: FrontendRoutes.NOTIFICATIONSETUP,
          title: "Notification",
          id: 10102,
        },
      ],
    },

    {
      id: 10103,
      path: "#",
      title: "Commission Mgt",
      icon: "lets-icons:setting-line",
      children: [
        // {
        //   path: FrontendRoutes.RESELLER,
        //   title: 'Reseller Type',
        //   id: 10104,
        // },
        // {
        //   path: FrontendRoutes.TYPESERVICE,
        //   title: 'Types & Services',
        //   id: 10105,
        // },
        {
          path: FrontendRoutes.COMMISSIONCONFIG,
          title: "Commission Config",
          id: 10106,
        },
      ],
    },

    {
      id: 10107,
      path: FrontendRoutes.REPORT,
      title: "Report",
      icon: "lets-icons:setting-line",
    },

    {
      id: 10108,
      path: "#",
      title: "Pricing Config",
      icon: "lets-icons:setting-line-light",
      children: [
        {
          path: FrontendRoutes.PRICINGCONFIGCATEGORYLIST,
          title: "Category List",
          id: 10109,
        },
        {
          path: FrontendRoutes.PRICINGCONFIGSERVICEITEM,
          title: "Service/Item List",
          id: 10110,
        },
      ],
    },
  ],
};

export const SidebarPharmacyRoutes = {
  title: "Pharmacy",
  routes: [
    {
      id: 10112,
      path: FrontendRoutes.POS,
      title: "POS",
      icon: "ph:money-fill",
    },

    {
      id: 10113,
      path: FrontendRoutes.MEDICINEREQUEST,
      title: "Medicine Request",
      icon: "uit:check-square",
    },

    {
      id: 10114,
      path: "#",
      title: "Inventory",
      icon: "akar-icons:shipping-box-02",
      children: [
        {
          path: FrontendRoutes.PHARMACYINVENTORY,
          title: "Inventory List",
          id: 10115,
        },
        {
          path: FrontendRoutes.PHARMACYPRODUCT,
          title: "Product List",
          id: 10116,
        },
      ],
    },

    {
      id: 10117,
      path: "#",
      title: "Finance",
      icon: "la:money-bill",
      children: [
        {
          path: FrontendRoutes.PHARMACYEXPENSE,
          title: "Expenses",
          id: 10118,
        },
        {
          path: FrontendRoutes.PHARMACYSALES,
          title: "Sales",
          id: 10119,
        },
        {
          path: FrontendRoutes.PHARMACYREPORT,
          title: "Reports",
          id: 10120,
        },
      ],
    },
  ],
};
