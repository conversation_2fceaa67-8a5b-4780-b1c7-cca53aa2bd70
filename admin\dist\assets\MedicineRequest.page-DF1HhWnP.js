import{a5 as f,aM as j,bK as k,ad as M,bL as v,a2 as s,af as R,ag as T,ah as h,aQ as w,aj as C,bM as q,bN as D}from"./index-ClX9RVH0.js";const Q=()=>{var c,u,i;const[e,l]=f.useState({limit:5,page:1}),[n,E]=f.useState({selectedDepartment:"",selectedSpecialist:"",search:""}),I=j({category:D.SALE,inventoryFor:q.PHARMACY,search:n.search,...n.search?{}:{page:e.page,limit:e.limit}}),{data:t}=k(I),o=(c=t==null?void 0:t.data)==null?void 0:c.invoices,x=M(),{mutateAsync:P}=v(),r={columns:[{title:"S.N.",key:"sn"},{title:"Date",key:"date"},{title:"Requested By",key:"requestedBy"},{title:"Patient",key:"patient"},{title:"Payment Status",key:"paymentStatus"},{title:"Status",key:"status"},{title:"Action",key:"action"}],rows:o==null?void 0:o.map((a,b)=>{var d,p,g,y,S,A,N;return{sn:b+1,date:a==null?void 0:a.date,requestedBy:((g=(p=(d=a==null?void 0:a.supervisor)==null?void 0:d.commonInfo)==null?void 0:p.personalInfo)==null?void 0:g.fullName)??"N/A",patient:((y=a==null?void 0:a.walkInCustomer)==null?void 0:y.name)??((N=(A=(S=a==null?void 0:a.billingAgainst)==null?void 0:S.commonInfo)==null?void 0:A.personalInfo)==null?void 0:N.fullName)??"Outside Patient",paymentStatus:s.jsx(h,{status:a==null?void 0:a.paymentStatus}),status:s.jsx(h,{status:a!=null&&a.isActive?"ACTIVE":"INVACTIVE"}),action:s.jsx(R,{onShow:()=>x(`${T.MEDICINERQUESTDETAILS}/${a==null?void 0:a._id}`),onDelete:async()=>{await P({id:JSON.stringify([a==null?void 0:a._id])})}})}})};return s.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[s.jsx(w,{headerTitle:"Medicine Request",onSearch:!0,onSearchFunc:a=>{E({...n,search:a})}}),s.jsx("div",{className:"bg-white rounded-md",children:s.jsx(C,{columns:r.columns,rows:r.rows,loading:!1,pagination:{currentPage:e.page,totalPage:(i=(u=t==null?void 0:t.data)==null?void 0:u.pagination)==null?void 0:i.pages,limit:e.limit,onClick:a=>{a.page&&l({...e,page:a.page}),a.limit&&l({...e,limit:a.limit})}}})})]})};export{Q as MedicineRequestPage};
