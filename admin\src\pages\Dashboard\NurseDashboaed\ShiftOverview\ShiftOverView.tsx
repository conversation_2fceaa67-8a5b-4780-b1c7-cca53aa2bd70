import MasterTable from "../../../../layouts/Table/MasterTable";
import { TableAction } from "../../../../layouts/Table/TableAction";
import Alerts from "./components/Alerts";
import ShiftOverViewChart from "./components/ShiftOverViewChart";
const patientOverviewTableData = {
  columns: [
    { title: "Patient Name", key: "patientName" },
    { title: "Room No.", key: "roomNo" },
    { title: "Bed No.", key: "bedNo" },
    { title: "Tasks", key: "tasks" },
    { title: "Action", key: "action" },
  ],
  rows: [
    {
      patientName: "Ram Prasad Shrestha",
      roomNo: "224",
      bedNo: "69",
      tasks: "Check Vitals regularly, 20 minutes",
      action: <TableAction onShow={() => {}} />,
    },
    {
      patientName: "Sita Lama",
      roomNo: "310",
      bedNo: "15",
      tasks: "Pantop D ( 11 PM, 11:20 PM )",
      action: <TableAction onShow={() => {}} />,
    },
    {
      patientName: "<PERSON><PERSON><PERSON> Thapa",
      roomNo: "412",
      bedNo: "33",
      tasks: "Blood Test Scheduled at 2:00 PM",
      action: <TableAction onShow={() => {}} />,
    },
    {
      patientName: "Kiran Rai",
      roomNo: "205",
      bedNo: "12",
      tasks: "MRI Scan at 4:00 PM",
      action: <TableAction onShow={() => {}} />,
    },
    {
      patientName: "Sunita Gurung",
      roomNo: "120",
      bedNo: "5",
      tasks: "Physiotherapy Session at 9:00 AM",
      action: <TableAction onShow={() => {}} />,
    },
  ],
};

const ShiftOverView = () => {
  return (
    <div className='flex flex-col w-full gap-4 p-2 mx-auto'>
      {/* shift overview chart  */}
      <div className='w-full h-1/2'>
        <ShiftOverViewChart />
      </div>
      <div className='flex w-full gap-3 h-1/2'>
        {/* patient overview */}
        <div className='w-[70%] rounded-lg shadow-md border border-gray-200'>
          <div className='px-4 py-2 bg-white rounded-lg'>
            <h2 className='px-4 pt-2.5 mb-1 text-lg font-semibold text-left '>
              Patients Overview
            </h2>

            <div className='overflow-x-auto'>
              <MasterTable
                columns={patientOverviewTableData.columns}
                rows={patientOverviewTableData.rows}
                loading={false}
              />
            </div>
          </div>
        </div>

        <div className='w-[30%]'>
          <Alerts />
        </div>
      </div>
    </div>
  );
};

export default ShiftOverView;
