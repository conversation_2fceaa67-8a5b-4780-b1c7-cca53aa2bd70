import { useState } from "react";
import MasterTable from "../../../../../../../layouts/Table/MasterTable";
import { MedicationDataMedicalRecord } from "../../Consultations/pages/sampleMasterTableData";
import { ClientHistory } from "../../../../../../../server-action/api/patienthistory.api";
import { get } from "lodash";

interface VitalSignsProps {
  data: {
    _id: string;
    data: ClientHistory;
  };
}
const MedicalAdministration: React.FC<VitalSignsProps> = ({ data }) => {
  const [statusFilter, setStatusFilter] = useState("");
  const [dateFilter, setDateFilter] = useState("");

  // const tableData = {
  //   columns: [
  //     { title: "Medicine Name", key: "medicineName" },
  //     { title: "Dose", key: "dose" },
  //     { title: "Route", key: "route" },
  //     { title: "Scheduled Time", key: "scheduledTime" },
  //     { title: "Given Time", key: "givenTime" },
  //     { title: "Nurse", key: "nurse" },
  //     { title: "Status", key: "status" },
  //     { title: "Reason", key: "reason" },
  //   ],
  //   rows: get(data, "medicationAdministration", []).map(
  //     (item: any, index: number) => ({
  //       key: index,
  //       medicineName: item.medicineName,
  //       dose: item.dose,
  //       route: item.route,
  //       scheduledTime: item.scheduledTime,
  //       givenTime: item.givenTime,
  //       nurse: item.nurse,
  //       status: item.status,
  //       reason: item.reason,
  //     })
  //   ),
  // };
  const tableData = {
    columns: [
      { title: "Medicine Name", key: "medicineName" },
      { title: "Dose", key: "dose" },
      { title: "Route", key: "route" },
      { title: "Scheduled Time", key: "scheduledTime" },
      { title: "Given Time", key: "givenTime" },
      { title: "Status", key: "status" },
      { title: "Reason", key: "reason" },
    ],
    rows: [
      {
        medicineName: "Paracetamol",
        dose: "500 mg",
        route: "Oral",
        scheduledTime: "2025-07-04 08:00 AM",
        givenTime: "2025-07-04 08:05 AM",
        status: "Given",
        reason: "-",
      },
      {
        medicineName: "Amoxicillin",
        dose: "250 mg",
        route: "Oral",
        scheduledTime: "2025-07-04 12:00 PM",
        givenTime: "2025-07-04 12:10 PM",
        status: "Given",
        reason: "-",
      },
      {
        medicineName: "Insulin",
        dose: "10 units",
        route: "Subcutaneous",
        scheduledTime: "2025-07-04 06:00 AM",
        givenTime: "-",
        status: "Not Given",
        reason: "Patient refused",
      },
      {
        medicineName: "Omeprazole",
        dose: "20 mg",
        route: "Oral",
        scheduledTime: "2025-07-04 07:30 AM",
        givenTime: "2025-07-04 07:32 AM",
        status: "Given",
        reason: "-",
      },
    ],
  };

  return (
    <div className='w-full p-4 mx-auto'>
      <h2 className='mb-4 text-lg font-semibold text-center'>
        Medication Administration
      </h2>

      {/* Filters */}
      <div className='flex justify-end gap-2 mb-2'>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className='w-full max-w-[100px] border border-gray-300 p-1 rounded focus:outline-none'
        >
          <option value=''>Status</option>
          <option value='Given'>Given</option>
          <option value='Not Given'>Not Given</option>
        </select>

        <input
          type='date'
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className='w-full max-w-[150px] border border-gray-300 p-1 rounded focus:outline-none'
        />
      </div>

      {/* Medication Table */}
      <div className='overflow-x-auto'>
        <MasterTable
          // color="bg-[#b3b3b3]"
          // textcolor="text-[#000000]"
          columns={tableData.columns}
          rows={tableData.rows}
          loading={false}
        />
      </div>
    </div>
  );
};

export default MedicalAdministration;
