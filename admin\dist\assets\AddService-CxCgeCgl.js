import{aV as k,aX as u,bm as w,ba as g,a2 as e,a7 as E,a5 as v,a1 as A,a9 as C,aa as F,ab as I,ac as o,aZ as P,ao as B}from"./index-ClX9RVH0.js";import{b as q,c as V,d as O}from"./serviceApi-DTA9RlDi.js";import{u as R}from"./categoryApi-75124ePN.js";const T=k({categoryName:u().required("select the category name"),serviceName:u().required("Service name is required"),rate:w().min(1,"Price must be greater than 1").required("Price is required"),reamrk:u()}),H=()=>{const{id:n}=g(),s=!!n?"Edit Old Service/Item":"Add New Service/Item";return e.jsxs("div",{children:[e.jsx(E,{hideHeader:!0,listTitle:s}),e.jsx("div",{className:"mb-6",children:e.jsx(_,{})})]})},_=()=>{var h;const n=q(),x=V(),{id:s}=g(),y=!!s,{data:m}=R({page:1,limit:100}),b=((h=m==null?void 0:m.data)==null?void 0:h.serviceCategory)||[],{data:r}=O(s),[j,f]=v.useState({categoryName:"",serviceName:"",rate:0,vat:"13",reamrk:"",price:0});v.useEffect(()=>{var a,l;r&&f({categoryName:((a=r.categoryName)==null?void 0:a._id)||"",serviceName:r.serviceName||"",rate:r.rate||0,vat:((l=r.vat)==null?void 0:l.toString())||"13",reamrk:r.reamrk||"",price:r.price||0})},[r]);const p=A({initialValues:j,validationSchema:T,enableReinitialize:!0,onSubmit:async a=>{try{y?await x.mutateAsync({entityData:a,_id:s}):await n.mutateAsync(a),history.back()}catch(l){console.log(l)}},validateOnChange:!0,validateOnBlur:!0}),{handleSubmit:N,errors:t,touched:c,values:i,resetForm:S,setFieldValue:d}=p;return v.useEffect(()=>{const a=(Number(i.rate)*1.13).toFixed(2);d("price",a,!1)},[i.rate]),e.jsx(C,{value:p,children:e.jsx(F,{onSubmit:N,children:e.jsxs("div",{className:"flex flex-col gap-5",children:[e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-5 py-8 px-5 w-full bg-white",children:[e.jsxs("div",{className:"w-full",children:[e.jsx(I,{label:"Category",name:"categoryName",options:b.map(a=>({value:a._id,label:a.categoryName})),value:i.categoryName,onChange:a=>d("categoryName",a.target.value)}),t.categoryName&&c.categoryName&&e.jsx("span",{className:"text-red text-sm",children:t.categoryName})]}),e.jsxs("div",{className:"w-full",children:[e.jsx(o,{type:"text",label:"Service/Item",placeholder:"Enter",name:"serviceName"}),t.serviceName&&c.serviceName&&e.jsx("span",{className:"text-red text-sm",children:t.serviceName})]}),e.jsxs("div",{className:"w-full",children:[e.jsx(o,{type:"number",label:"Price (Rs.)",placeholder:"Enter",name:"rate"}),t.rate&&c.rate&&e.jsx("span",{className:"text-red text-sm",children:t.rate})]}),e.jsx("div",{className:"w-full",children:e.jsx(o,{type:"text",label:"VAT (13%)",placeholder:"Auto",name:"vat",disabled:!0})}),e.jsx("div",{className:"w-full",children:e.jsx(o,{type:"number",label:"Total Price",placeholder:"Auto",name:"price",disabled:!0,value:i.price})}),e.jsxs("div",{className:"col-span-3 w-full",children:[e.jsx(P,{name:"reamrk",label:"Notes",placeholder:"Enter Short Notes",value:i.reamrk,onChange:a=>d("reamrk",a.target.value)}),t.reamrk&&c.reamrk&&e.jsx("span",{className:"text-red text-sm",children:t.reamrk})]})]}),e.jsx(B,{onCancel:()=>S(),onSubmit:N})]})})})};export{H as default};
