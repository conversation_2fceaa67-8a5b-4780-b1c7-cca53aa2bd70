import{a5 as o,aM as f,bB as N,av as m,a2 as e,af as p,b3 as r,aj as v}from"./index-ClX9RVH0.js";const k=()=>{const[n,i]=o.useState({page:1,limit:10,search:"",status:""}),[l,c]=o.useState([]),x=f(n),{data:d,isLoading:u}=N({...x,category:"PAYMENT"});m.get(d,"data.invoices",[]);const s={allPayments:{count:36,amount:1380},succeeded:{count:224,amount:2380},pending:{count:4,amount:380},failed:{count:4,amount:590},incomplete:{count:4,amount:590}},g=t=>{switch(t.toLowerCase()){case"succeeded":case"completed":return"bg-green-100 text-green-800 border-green-200";case"pending":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"failed":return"bg-red-100 text-red-800 border-red-200";case"incomplete":return"bg-gray-100 text-gray-800 border-gray-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},b=[{title:"",key:"checkbox"},{title:"CODE",key:"code"},{title:"STATUS",key:"status"},{title:"DESCRIPTION",key:"description"},{title:"TIME",key:"time"},{title:"DATE",key:"date"},{title:"CUSTOMER",key:"customer"},{title:"AMOUNT",key:"amount"},{title:"",key:"action"}],h=[{id:"#29359",status:"Incomplete",description:"Payment for invoice",time:"03:09 AM",date:"Feb 15, 2023",customer:{name:"Ryan Young",avatar:"👤"},amount:"$89"},{id:"#29359",status:"Succeeded",description:"Payment for invoice",time:"02:26 AM",date:"Feb 14, 2023",customer:{name:"Matthew Martinez",avatar:"👤"},amount:"$73"},{id:"#29359",status:"Succeeded",description:"Interest",time:"07:04 AM",date:"Feb 14, 2023",customer:{name:"Emily Johnson",avatar:"👤"},amount:"$9"},{id:"#29359",status:"Pending",description:"Interest",time:"09:50 PM",date:"Feb 14, 2023",customer:{name:"Ryan Brown",avatar:"👤"},amount:"$4"},{id:"#29359",status:"Succeeded",description:"Payment for invoice",time:"12:09 AM",date:"Feb 14, 2023",customer:{name:"Brian Hall",avatar:"👤"},amount:"$49"},{id:"#29359",status:"Failed",description:"Interest",time:"01:21 PM",date:"Feb 14, 2023",customer:{name:"Layla Phillips",avatar:"👤"},amount:"$91"},{id:"#29359",status:"Pending",description:"Payment for invoice",time:"01:05 PM",date:"Feb 14, 2023",customer:{name:"Emma Wilson",avatar:"👤"},amount:"$15"},{id:"#29359",status:"Succeeded",description:"Service fee",time:"09:35 PM",date:"Feb 12, 2023",customer:{name:"Brian White",avatar:"👤"},amount:"$25"}].map((t,a)=>({key:t.id+a,checkbox:e.jsx("input",{type:"checkbox",className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500",checked:l.includes(t.id+a),onChange:y=>{y.target.checked?c([...l,t.id+a]):c(l.filter(j=>j!==t.id+a))}}),code:e.jsx("span",{className:"text-blue-600 font-medium cursor-pointer hover:underline",children:t.id}),status:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium border ${g(t.status)}`,children:t.status}),description:t.description,time:t.time,date:t.date,customer:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-sm",children:t.customer.avatar}),e.jsx("span",{className:"font-medium",children:t.customer.name})]}),amount:e.jsx("span",{className:"font-semibold",children:t.amount}),action:e.jsx(p,{onShow:()=>console.log("View payment:",t.id),onEdit:()=>console.log("Edit payment:",t.id),onDelete:()=>console.log("Delete payment:",t.id)})}));return e.jsxs("div",{className:"p-6 bg-gray-50 min-h-screen",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Payments"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 transition-colors text-sm",children:[e.jsx(r,{icon:"material-symbols:download",className:"text-sm"}),"Export"]}),e.jsxs("button",{className:"flex items-center gap-2 px-4 py-2 bg-blue text-white rounded hover:bg-blue-700 transition-colors text-sm",children:[e.jsx(r,{icon:"material-symbols:add",className:"text-sm"}),"New payment"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"All payments"}),e.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-blue text-xs font-bold",children:s.allPayments.count})})]}),e.jsxs("div",{className:"text-2xl font-bold text-gray-900",children:["$",s.allPayments.amount]}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:[s.allPayments.count," records"]})]}),e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Succeeded"}),e.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:e.jsx(r,{icon:"material-symbols:check-circle",className:"text-green-600 text-sm"})})]}),e.jsxs("div",{className:"text-2xl font-bold text-gray-900",children:["$",s.succeeded.amount]}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:[s.succeeded.count," records"]})]}),e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Pending"}),e.jsx("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:e.jsx(r,{icon:"material-symbols:schedule",className:"text-yellow-600 text-sm"})})]}),e.jsxs("div",{className:"text-2xl font-bold text-gray-900",children:["$",s.pending.amount]}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:[s.pending.count," records"]})]}),e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Failed"}),e.jsx("div",{className:"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(r,{icon:"material-symbols:error",className:"text-red-600 text-sm"})})]}),e.jsxs("div",{className:"text-2xl font-bold text-gray-900",children:["$",s.failed.amount]}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:[s.failed.count," records"]})]}),e.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Incomplete"}),e.jsx("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:e.jsx(r,{icon:"material-symbols:info",className:"text-gray-600 text-sm"})})]}),e.jsxs("div",{className:"text-2xl font-bold text-gray-900",children:["$",s.incomplete.amount]}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:[s.incomplete.count," records"]})]})]}),l.length>0&&e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"text-sm text-blue-800",children:[l.length," selected payments | $",s.allPayments.amount," total amount"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"Action"}),e.jsx(r,{icon:"material-symbols:keyboard-arrow-down",className:"text-blue-600"})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm",children:e.jsx(v,{columns:b,rows:h,loading:u,pagination:{currentPage:n.page,totalPage:m.get(d,"data.pagination.pages",1),limit:n.limit,onClick:({page:t,limit:a})=>{t&&i({...n,page:t}),a&&i({...n,limit:a})}}})})]})};export{k as VendorBillingPage};
