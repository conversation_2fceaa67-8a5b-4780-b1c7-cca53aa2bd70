import{bT as q,bU as A,aN as S,aU as k,aV as F,aW as _,aX as c,a1 as I,b8 as B,a2 as r,a4 as H,a9 as L,aa as V,ac as R,ab as T,aY as U,ao as $}from"./index-ClX9RVH0.js";import{P as z}from"./ProductMultiSelect-DbxM3Xg6.js";const E=({editData:t,onClose:s})=>{var p,u,x,h,b;const{mutate:v}=q(),{mutate:g}=A(),{data:d}=S(),{data:i}=k(),y=[{label:"Category",value:"category"},...((u=(p=d==null?void 0:d.data)==null?void 0:p.productscategory)==null?void 0:u.map(e=>({label:e==null?void 0:e.categoryName,value:e==null?void 0:e._id})))||[]],m=[{label:"All",value:""},...((h=(x=i==null?void 0:i.data)==null?void 0:x.departments)==null?void 0:h.map(e=>({label:e==null?void 0:e.name,value:e==null?void 0:e._id})))||[]],j=m.filter(e=>e.value!=="").map(e=>e.value),f=F().shape({name:c().required("Product name is required"),category:c().required("Category is required"),description:c().required("Description is required"),department:_().of(c().min(1,"Department items must be non-empty strings")).min(1,"At least one department must be selected").required("Department field is required")}),o=I({initialValues:{name:(t==null?void 0:t.name)??"",category:(t==null?void 0:t.category._id)??"",description:(t==null?void 0:t.description)??"",department:((b=t==null?void 0:t.department)==null?void 0:b.map(e=>e._id))||[]},validationSchema:f,enableReinitialize:!0,onSubmit:e=>{let l={department:e.department.includes("")?j:e.department,name:e.name,category:e.category,description:e.description};l=B(l),t&&t._id?g({entityData:l,_id:t._id}):v(l),s==null||s()}}),{handleSubmit:N,handleChange:P,errors:a,touched:n,values:w}=o;return console.log(w,"val"),r.jsxs("div",{children:[r.jsx(H,{as:"h3",text:"Add Product",size:"body-lg-lg",variant:"primary-blue",className:"my-4 text-primary flex justify-center items-center"}),r.jsx(L,{value:o,children:r.jsxs(V,{children:[r.jsxs("div",{className:"my-3 mx-4 border-2 border-[#e6e6e6] rounded-xl",children:[r.jsx("div",{className:"absolute cursor-pointer hover:text-red top-5 right-10",onClick:()=>{s==null||s()},children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",children:r.jsx("path",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeWidth:2,d:"m8 8l4 4m0 0l4 4m-4-4l4-4m-4 4l-4 4"})})}),r.jsxs("div",{className:"flex px-2 pt-4 pb-1 mx-2 items-center gap-4",children:[r.jsxs("div",{className:"w-full",children:[r.jsx(R,{label:"Product Name",type:"text",name:"name",placeholder:"eg.Hospital Bed"}),a.name&&n.name&&r.jsx("div",{className:"text-red text-xs ",children:a.name})]}),r.jsxs("div",{className:"w-full ",children:[r.jsx(T,{firstInput:"Select",label:"Product Category",name:"category",value:o.values.category,onChange:P,options:y}),a.category&&n.category&&r.jsx("div",{className:"text-red text-xs mb-2",children:a.category})]}),r.jsxs("div",{className:"w-full",children:[r.jsx(z,{name:"department",label:"Department",options:m}),a.department&&n.department&&r.jsx("div",{className:"text-red text-xs ",children:a.department})]})]}),r.jsxs("div",{className:"px-4 w-full items-center pt-1 pb-4 ",children:[r.jsx(U,{label:"Short Description",name:"description",placeholder:"Routine patient transfers"}),a.description&&n.description&&r.jsx("div",{className:"text-red text-xs ",children:a.description})]})]}),r.jsx("div",{className:" mr-4 mb-4",children:r.jsx($,{onCancel:()=>{s==null||s()},submitLabel:"Save",submitlabelButton:!0,onSubmit:N})})]})})]})};export{E as A};
