import { Icon } from "@iconify/react/dist/iconify.js";
import MasterTable from "../../../../../layouts/Table/MasterTable";
import classNames from "classnames";

const Consumables = () => {
  const tableData = {
    columns: [
      { title: "Item Name", key: "itemName" },
      { title: "Unit", key: "unit" },
      { title: "Quantity", key: "quantity" },
      { title: "Minimum Stock", key: "minimumStock" },
      { title: "Expiration Date", key: "expirationDate" },
      { title: "Batch", key: "batch" },
      { title: "Supplier", key: "supplier" },
      { title: "Order", key: "orderQuantity" },
    ],
    rows: [
      {
        itemName: "Surgical Gloves",
        unit: "Boxes",
        quantity: 5,
        minimumStock: 10,
        expirationDate: "Dec 15, 2024",
        batch: "SG-2024-001",
        supplier: "MedSupply Co.",
        orderQuantity: 10,
      },
      {
        itemName: "Face Masks",
        unit: "Packs",
        quantity: 20,
        minimumStock: 30,
        expirationDate: "Jan 10, 2025",
        batch: "FM-2025-002",
        supplier: "SafeAir Ltd.",
        orderQuantity: 20,
      },
      {
        itemName: "IV Sets",
        unit: "Units",
        quantity: 12,
        minimumStock: 20,
        expirationDate: "Aug 30, 2025",
        batch: "IVS-2025-007",
        supplier: "HealthFlow Supplies",
        orderQuantity: 15,
      },
      {
        itemName: "Syringes",
        unit: "Boxes",
        quantity: 6,
        minimumStock: 15,
        expirationDate: "Oct 18, 2024",
        batch: "SYR-2024-004",
        supplier: "MediTools Pvt. Ltd.",
        orderQuantity: 20,
      },
      {
        itemName: "Bandages",
        unit: "Boxes",
        quantity: 9,
        minimumStock: 20,
        expirationDate: "Jul 15, 2025",
        batch: "BDG-2025-006",
        supplier: "MediWrap Inc.",
        orderQuantity: 15,
      },
      {
        itemName: "Gauze Pads",
        unit: "Packs",
        quantity: 10,
        minimumStock: 25,
        expirationDate: "Dec 25, 2024",
        batch: "GP-2024-014",
        supplier: "SterilCare Ltd.",
        orderQuantity: 20,
      },
      {
        itemName: "Cotton Rolls",
        unit: "Rolls",
        quantity: 15,
        minimumStock: 30,
        expirationDate: "Jan 2, 2026",
        batch: "CR-2026-003",
        supplier: "PureCare Medicals",
        orderQuantity: 20,
      },
      {
        itemName: "Alcohol Swabs",
        unit: "Boxes",
        quantity: 7,
        minimumStock: 15,
        expirationDate: "Nov 20, 2024",
        batch: "ASW-2024-009",
        supplier: "CleanTouch Pvt. Ltd.",
        orderQuantity: 10,
      },
      {
        itemName: "Catheters",
        unit: "Units",
        quantity: 5,
        minimumStock: 10,
        expirationDate: "Oct 10, 2025",
        batch: "CT-2025-008",
        supplier: "MedRoute Corp.",
        orderQuantity: 10,
      },
      {
        itemName: "Disinfectant Wipes",
        unit: "Canisters",
        quantity: 6,
        minimumStock: 12,
        expirationDate: "Sep 1, 2025",
        batch: "DW-2025-011",
        supplier: "SanitiCo",
        orderQuantity: 12,
      },
    ],
  };

  const statCards = [
    {
      title: "Total Items",
      count: 200,
      subtitle: "Active inventory items",
      icon: "pajamas:work-item-epic",
      iconBg: "text-gray-9000",
      textColor: "text-gray-900",
      bgColor: "bg-[#9DF2DB]",
    },
    {
      title: "Low Stock",
      count: 10,
      subtitle: "Items below minimum",
      icon: "tdesign:error-triangle",
      iconBg: "text-rose-600",
      textColor: "text-rose-600",
      bgColor: "bg-[#FAECEC]",
    },
    {
      title: "Expiring Soon",
      count: 5,
      subtitle: "within 10 days",
      icon: "lets-icons:date-fill",
      iconBg: "text-amber-500",
      textColor: "text-amber-500",
      bgColor: "bg-[#CEF9F6]",
    },
    {
      title: "Today's Reorders",
      count: 8,
      subtitle: "Active inventory items",
      icon: "solar:restart-circle-bold",
      iconBg: "text-lime-600",
      textColor: "text-lime-600",
      bgColor: "bg-[#D8F7DE]",
    },
  ];

  return (
    <div className='p-2 mt-4 '>
      {/* Header */}

      {/* Metrics Cards */}
      <div className='grid grid-cols-4 gap-4 mb-6'>
        {statCards.map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgColor} rounded-lg shadow-sm border border-gray-200 p-4`}
          >
            <div className='flex items-center justify-between gap-2'>
              <div className='text-sm font-semibold text-gray-800'>
                {stat.title}
              </div>
              <div
              // className={`w-8 h-8  flex items-center p-2 justify-center ${stat.iconBg}`}
              >
                <Icon
                  icon={stat.icon}
                  width='24'
                  height='24'
                  className={`${stat.iconBg}`}
                />
              </div>
            </div>
            <div className={`text-2xl font-bold mt-1 ${stat.textColor}`}>
              {stat.count}
            </div>
            <div className='text-xs text-gray-500'>{stat.subtitle}</div>
          </div>
        ))}
      </div>

      <div className='bg-white p-6'>
        <div className='flex items-center justify-between mb-4'>
          <h2 className='text-lg font-semibold text-gray-900'>
            Stock Inventory
          </h2>
          <div className='flex items-center gap-3'>
            <div className='relative'>
              <svg
                className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z'
                />
              </svg>
              <input
                placeholder='Search items'
                className='pl-10 w-64 border border-gray-300 rounded-md px-3 py-2'
              />
            </div>
            <button className='flex items-center gap-2 bg-transparent border border-gray-300 rounded-md px-4 py-2 hover:bg-gray-50'>
              Sort
              <svg
                className='w-4 h-4'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M19 9l-7 7-7-7'
                />
              </svg>
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Add New Item
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Reorder Selected
            </button>
            <button className='bg-primary hover:bg-light_primary text-white px-4 py-2 rounded-md'>
              Download Report
            </button>
          </div>
        </div>

        {/* <table className="w-full">
          <thead>
            <tr className="bg-gray-50">
              <th className="text-left font-medium text-gray-700 px-4 py-3">
                Item Name
              </th>
              <th className="text-left font-medium text-gray-700 px-4 py-3">
                Unit
              </th>
              <th className="text-left font-medium text-gray-700 px-4 py-3">
                Quantity
              </th>
              <th className="text-left font-medium text-gray-700 px-4 py-3">
                Minimum Stock
              </th>
              <th className="text-left font-medium text-gray-700 px-4 py-3">
                Expiration Date
              </th>
              <th className="text-left font-medium text-gray-700 px-4 py-3">
                Batch#
              </th>
              <th className="text-left font-medium text-gray-700 px-4 py-3">
                Supplier
              </th>
              <th className="text-left font-medium text-gray-700 px-4 py-3">
                Order
              </th>
            </tr>
          </thead>
          <tbody>
            {inventoryData.map((item, index) => (
              <tr key={index} className="border-b border-gray-200">
                <td className="font-medium px-4 py-3">{item.itemName}</td>
                <td className="px-4 py-3">{item.unit}</td>
                <td className="px-4 py-3">{item.quantity}</td>
                <td className="px-4 py-3">{item.minimumStock}</td>
                <td className="text-orange-600 px-4 py-3">
                  {item.expirationDate}
                </td>
                <td className="px-4 py-3">{item.batch}</td>
                <td className="px-4 py-3">{item.supplier}</td>
                <td className="px-4 py-3">
                  <div className="flex items-center gap-2">
                    <button className="w-8 h-8 border border-gray-300 rounded bg-transparent hover:bg-gray-50 flex items-center justify-center">
                      <svg
                        className="w-3 h-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M20 12H4"
                        />
                      </svg>
                    </button>
                    <span className="bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium">
                      {item.orderQuantity}
                    </span>
                    <button className="w-8 h-8 border border-gray-300 rounded bg-transparent hover:bg-gray-50 flex items-center justify-center">
                      <svg
                        className="w-3 h-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 4v16m8-8H4"
                        />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table> */}
        <div>
          <MasterTable
            // color="bg-[#b3b3b3]"
            // textcolor="text-[#000000]"
            columns={tableData.columns}
            rows={tableData.rows}
            loading={false}
          />
        </div>
      </div>
    </div>
  );
};

export default Consumables;
