import { useState } from 'react';
import { Icon } from '@iconify/react/dist/iconify.js';
import MasterTable from '../../../layouts/Table/MasterTable';
import { TableAction } from '../../../layouts/Table/TableAction';
import { useGetAllInvoice } from '../../../server-action/api/financialOpsApi';
import { buildQueryParams } from '../../../hooks/useBuildQuery';
import { get } from 'lodash';

export const VendorBillingPage = () => {
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    search: '',
    status: '',
  });

  const [selectedPayments, setSelectedPayments] = useState<string[]>([]);

  const queryParams = buildQueryParams(filters);
  const { data: paymentData, isLoading } = useGetAllInvoice({
    ...queryParams,
    category: 'PAYMENT',
  });

  const payments = get(paymentData, 'data.invoices', []);

  // Mock summary data - replace with real calculations
  const summaryData = {
    allPayments: { count: 36, amount: 1380 },
    succeeded: { count: 224, amount: 2380 },
    pending: { count: 4, amount: 380 },
    failed: { count: 4, amount: 590 },
    incomplete: { count: 4, amount: 590 },
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'incomplete':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
      case 'completed':
        return 'material-symbols:check-circle';
      case 'pending':
        return 'material-symbols:schedule';
      case 'failed':
        return 'material-symbols:error';
      case 'incomplete':
        return 'material-symbols:info';
      default:
        return 'material-symbols:help';
    }
  };

  const columns = [
    { title: '', key: 'checkbox' },
    { title: 'CODE', key: 'code' },
    { title: 'STATUS', key: 'status' },
    { title: 'DESCRIPTION', key: 'description' },
    { title: 'TIME', key: 'time' },
    { title: 'DATE', key: 'date' },
    { title: 'CUSTOMER', key: 'customer' },
    { title: 'AMOUNT', key: 'amount' },
    { title: '', key: 'action' },
  ];

  // Mock payment data - replace with real data mapping
  const mockPayments = [
    {
      id: '#29359',
      status: 'Incomplete',
      description: 'Payment for invoice',
      time: '03:09 AM',
      date: 'Feb 15, 2023',
      customer: { name: 'Ryan Young', avatar: '👤' },
      amount: '$89',
    },
    {
      id: '#29359',
      status: 'Succeeded',
      description: 'Payment for invoice',
      time: '02:26 AM',
      date: 'Feb 14, 2023',
      customer: { name: 'Matthew Martinez', avatar: '👤' },
      amount: '$73',
    },
    {
      id: '#29359',
      status: 'Succeeded',
      description: 'Interest',
      time: '07:04 AM',
      date: 'Feb 14, 2023',
      customer: { name: 'Emily Johnson', avatar: '👤' },
      amount: '$9',
    },
    {
      id: '#29359',
      status: 'Pending',
      description: 'Interest',
      time: '09:50 PM',
      date: 'Feb 14, 2023',
      customer: { name: 'Ryan Brown', avatar: '👤' },
      amount: '$4',
    },
    {
      id: '#29359',
      status: 'Succeeded',
      description: 'Payment for invoice',
      time: '12:09 AM',
      date: 'Feb 14, 2023',
      customer: { name: 'Brian Hall', avatar: '👤' },
      amount: '$49',
    },
    {
      id: '#29359',
      status: 'Failed',
      description: 'Interest',
      time: '01:21 PM',
      date: 'Feb 14, 2023',
      customer: { name: 'Layla Phillips', avatar: '👤' },
      amount: '$91',
    },
    {
      id: '#29359',
      status: 'Pending',
      description: 'Payment for invoice',
      time: '01:05 PM',
      date: 'Feb 14, 2023',
      customer: { name: 'Emma Wilson', avatar: '👤' },
      amount: '$15',
    },
    {
      id: '#29359',
      status: 'Succeeded',
      description: 'Service fee',
      time: '09:35 PM',
      date: 'Feb 12, 2023',
      customer: { name: 'Brian White', avatar: '👤' },
      amount: '$25',
    },
  ];

  const rows = mockPayments.map((payment, index) => ({
    key: payment.id + index,
    checkbox: (
      <input
        type="checkbox"
        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        checked={selectedPayments.includes(payment.id + index)}
        onChange={(e) => {
          if (e.target.checked) {
            setSelectedPayments([...selectedPayments, payment.id + index]);
          } else {
            setSelectedPayments(
              selectedPayments.filter((id) => id !== payment.id + index)
            );
          }
        }}
      />
    ),
    code: (
      <span className="text-blue-600 font-medium cursor-pointer hover:underline">
        {payment.id}
      </span>
    ),
    status: (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(
          payment.status
        )}`}
      >
        {payment.status}
      </span>
    ),
    description: payment.description,
    time: payment.time,
    date: payment.date,
    customer: (
      <div className="flex items-center gap-2">
        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-sm">
          {payment.customer.avatar}
        </div>
        <span className="font-medium">{payment.customer.name}</span>
      </div>
    ),
    amount: <span className="font-semibold">{payment.amount}</span>,
    action: (
      <TableAction
        onShow={() => console.log('View payment:', payment.id)}
        onEdit={() => console.log('Edit payment:', payment.id)}
        onDelete={() => console.log('Delete payment:', payment.id)}
      />
    ),
  }));

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-xl font-bold text-gray-900">Payments</h1>
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 transition-colors text-sm">
            <Icon icon="material-symbols:download" className="text-sm" />
            Export
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-blue text-white rounded hover:bg-blue-700 transition-colors text-sm">
            <Icon icon="material-symbols:add" className="text-sm" />
            New payment
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        {/* All Payments */}
        <div className="bg-white rounded-lg border border-gray-200 p-2">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-600">All payments</span>
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue text-xs font-bold">
                {summaryData.allPayments.count}
              </span>
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">
            ${summaryData.allPayments.amount}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {summaryData.allPayments.count} records
          </div>
        </div>

        {/* Succeeded */}
        <div className="bg-white rounded-lg border border-gray-200 p-2">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-600">Succeeded</span>
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <Icon
                icon="material-symbols:check-circle"
                className="text-green-600 text-sm"
              />
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">
            ${summaryData.succeeded.amount}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {summaryData.succeeded.count} records
          </div>
        </div>

        {/* Pending */}
        <div className="bg-white rounded-lg border border-gray-200 p-2">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-600">Pending</span>
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <Icon
                icon="material-symbols:schedule"
                className="text-yellow-600 text-sm"
              />
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">
            ${summaryData.pending.amount}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {summaryData.pending.count} records
          </div>
        </div>

        {/* Failed */}
        <div className="bg-white rounded-lg border border-gray-200 p-2">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-600">Failed</span>
            <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
              <Icon
                icon="material-symbols:error"
                className="text-red-600 text-sm"
              />
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">
            ${summaryData.failed.amount}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {summaryData.failed.count} records
          </div>
        </div>

        {/* Incomplete */}
        <div className="bg-white rounded-lg border border-gray-200 p-2">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-600">Incomplete</span>
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <Icon
                icon="material-symbols:info"
                className="text-gray-600 text-sm"
              />
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">
            ${summaryData.incomplete.amount}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            {summaryData.incomplete.count} records
          </div>
        </div>
      </div>

      {/* Selection Info */}
      {selectedPayments.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              {selectedPayments.length} selected payments | $
              {summaryData.allPayments.amount} total amount
            </span>
            <div className="flex items-center gap-2">
              <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                Action
              </button>
              <Icon
                icon="material-symbols:keyboard-arrow-down"
                className="text-blue-600"
              />
            </div>
          </div>
        </div>
      )}

      {/* Payments Table */}
      <div className="bg-white rounded-lg shadow-sm">
        <MasterTable
          columns={columns}
          rows={rows}
          loading={isLoading}
          pagination={{
            currentPage: filters.page,
            totalPage: get(paymentData, 'data.pagination.pages', 1),
            limit: filters.limit,
            onClick: ({ page, limit }: { page?: number; limit?: number }) => {
              if (page) setFilters({ ...filters, page });
              if (limit) setFilters({ ...filters, limit });
            },
          }}
        />
      </div>
    </div>
  );
};
