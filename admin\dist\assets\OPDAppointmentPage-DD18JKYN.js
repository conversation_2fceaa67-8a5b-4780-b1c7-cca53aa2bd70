import{a1 as S,aV as w,a2 as e,a9 as I,b3 as x,ab as f,ac as b,bF as j,a5 as v}from"./index-ClX9RVH0.js";const M=[{value:"cardiology",label:"Cardiology"},{value:"neurology",label:"Neurology"},{value:"orthopedics",label:"Orthopedics"},{value:"pediatrics",label:"Pediatrics"},{value:"general",label:"General Medicine"},{value:"emergency",label:"Emergency"},{value:"surgery",label:"Surgery"},{value:"dermatology",label:"Dermatology"}],T=[{value:"dr-smith",label:"Dr. <PERSON>"},{value:"dr-johnson",label:"<PERSON>. <PERSON>"},{value:"dr-brown",label:"Dr. <PERSON>"},{value:"dr-davis",label:"<PERSON>. <PERSON>"},{value:"dr-w<PERSON>on",label:"<PERSON>. <PERSON>"},{value:"dr-<PERSON><PERSON>",label:"<PERSON>. <PERSON>"},{value:"dr-miller",label:"<PERSON>. <PERSON>"},{value:"dr-taylor",label:"<PERSON>. <PERSON>"}],O=["11:00 <PERSON>","11:30 AM","12:00 PM","12:30 PM","01:00 PM","01:30 PM","02:00 PM","02:30 PM","03:00 PM","03:30 PM","04:00 PM","04:30 PM","05:00 PM","05:30 PM","06:00 PM"],F=w().shape({}),E=()=>new Date().toISOString().split("T")[0],R=({onNext:i,onBack:u,initialData:s,showBack:g=!0})=>{const l=S({initialValues:{department:(s==null?void 0:s.department)||"",doctor:(s==null?void 0:s.doctor)||"",appointmentDate:(s==null?void 0:s.appointmentDate)||"",availableTimeSlot:(s==null?void 0:s.availableTimeSlot)||"",doctorDutyTime:(s==null?void 0:s.doctorDutyTime)||"",availablestatus:(s==null?void 0:s.availablestatus)||"None",totalCharge:(s==null?void 0:s.totalCharge)||"500",resellerName:(s==null?void 0:s.resellerName)||"none",remark:(s==null?void 0:s.remark)||""},enableReinitialize:!0,validationSchema:F,onSubmit:r=>{i(r)}}),{values:o,setFieldValue:m,getFieldProps:h}=l;return e.jsx(I,{value:l,children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-red-100 rounded-full flex items-center justify-center",children:e.jsx(x,{icon:"mdi:doctor",className:"w-5 h-5 text-red-600"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Doctor Selection"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Choose your preferred department, doctor, and appointment time"})]})]}),e.jsxs("form",{onSubmit:l.handleSubmit,children:[e.jsxs("div",{className:"grid grid-cols-3 lg:grid-cols-3 gap-5 bg-white py-8 px-5",children:[e.jsx("div",{children:e.jsx(f,{required:!0,label:"Department",options:M,firstInput:"Select Department",name:"department",value:o.department,onChange:r=>m("department",r.target.value)})}),e.jsx("div",{children:e.jsx(f,{required:!0,label:"Choose Doctor",options:T,firstInput:"Select Doctor",name:"doctor",value:o.doctor,onChange:r=>m("doctor",r.target.value),disabled:!o.appointmentDate})}),e.jsx("div",{children:e.jsx(b,{type:"date",required:!0,label:"Appointment Date",placeholder:"yy/mm/dd",name:"appointmentDate",min:E(),onChange:r=>{m("appointmentDate",r.target.value),m("doctor","")},value:o.appointmentDate})}),e.jsx("div",{children:e.jsx(f,{label:"Available Time Slot",options:O.map(r=>({value:r,label:r})),firstInput:"Select Time Slot",name:"availableTimeSlot",value:o.availableTimeSlot,onChange:r=>{const t=r.target.value;m("availableTimeSlot",t),m("doctorDutyTime",t)},disabled:!o.doctor})}),e.jsx("div",{children:e.jsx(b,{type:"text",label:"Available Status",placeholder:"None",value:o.availablestatus||"None",disabled:!0})}),e.jsx("div",{children:e.jsx(b,{type:"time",label:"Doctor Duty Time",placeholder:"Time",value:o.doctorDutyTime,disabled:!0})}),e.jsx("div",{children:e.jsx(f,{label:"Referral Name",options:[{value:"none",label:"None"},{value:"referral1",label:"Referral 1"},{value:"referral2",label:"Referral 2"}],firstInput:"Select Referral",name:"resellerName",value:o.resellerName,onChange:r=>m("resellerName",r.target.value)})}),e.jsx("div",{children:e.jsx(b,{type:"text",label:"Total Charge",placeholder:"Auto",value:o.totalCharge||"500",disabled:!0})}),e.jsx("div",{children:e.jsx(b,{type:"text",label:"Remark",placeholder:"Remarks",value:o.remark,onChange:r=>m("remark",r.target.value)})})]}),e.jsxs("div",{className:"flex justify-between mt-8",children:[e.jsxs(j,{type:"button",onClick:u,variant:"outline",className:"flex items-center gap-2",children:[e.jsx(x,{icon:"mdi:arrow-left",className:"w-4 h-4"}),e.jsx("span",{children:"Back"})]}),e.jsxs(j,{type:"submit",className:"bg-blue-600 hover:bg-blue-700 flex items-center gap-2",children:[e.jsx("span",{children:"Next"}),e.jsx(x,{icon:"mdi:arrow-right",className:"w-4 h-4"})]})]})]})]})})},$=[{value:"consultation",label:"General Consultation"},{value:"follow-up",label:"Follow-up Visit"},{value:"emergency",label:"Emergency"},{value:"routine",label:"Routine Check-up"},{value:"specialist",label:"Specialist Consultation"},{value:"surgery",label:"Surgery Consultation"}],G=[{value:"low",label:"Low"},{value:"normal",label:"Normal"},{value:"high",label:"High"},{value:"urgent",label:"Urgent"},{value:"emergency",label:"Emergency"}],z=w().shape({}),q=({onNext:i,onBack:u,initialData:s,showBack:g=!0})=>{const l=S({initialValues:{appointmentType:(s==null?void 0:s.appointmentType)||"",priority:(s==null?void 0:s.priority)||"normal",reason:(s==null?void 0:s.reason)||"",symptoms:(s==null?void 0:s.symptoms)||"",notes:(s==null?void 0:s.notes)||"",specialInstructions:(s==null?void 0:s.specialInstructions)||"",duration:(s==null?void 0:s.duration)||"",followUpRequired:(s==null?void 0:s.followUpRequired)||!1,emergencyContact:(s==null?void 0:s.emergencyContact)||""},enableReinitialize:!0,validationSchema:z,onSubmit:r=>{i(r)}}),{values:o,setFieldValue:m,getFieldProps:h}=l;return e.jsx(I,{value:l,children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:e.jsx(x,{icon:"mdi:calendar",className:"w-5 h-5 text-blue-600"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Appointment Details"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Choose date, reason & priority"})]})]}),e.jsxs("form",{onSubmit:l.handleSubmit,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Appointment Type *"}),e.jsxs("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",value:o.appointmentType,onChange:r=>m("appointmentType",r.target.value),children:[e.jsx("option",{value:"",children:"Select appointment type"}),$.map(r=>e.jsx("option",{value:r.value,children:r.label},r.value))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Reason for Visit *"}),e.jsx("textarea",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:4,placeholder:"Please describe the reason for your visit",...h("reason")})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Additional notes"}),e.jsx("textarea",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",rows:4,placeholder:"Any additional information you would like to share",...h("notes")})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Priority Level *"}),e.jsxs("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",value:o.priority,onChange:r=>m("priority",r.target.value),children:[e.jsx("option",{value:"",children:"Select Priority level"}),G.map(r=>e.jsx("option",{value:r.value,children:r.label},r.value))]})]})]}),e.jsxs("div",{className:"flex justify-between mt-8",children:[e.jsxs(j,{type:"button",onClick:u,variant:"outline",className:"flex items-center gap-2",children:[e.jsx(x,{icon:"mdi:arrow-left",className:"w-4 h-4"}),e.jsx("span",{children:"Back"})]}),e.jsxs(j,{type:"submit",className:"bg-blue-600 hover:bg-blue-700 flex items-center gap-2",children:[e.jsx("span",{children:"Next"}),e.jsx(x,{icon:"mdi:arrow-right",className:"w-4 h-4"})]})]})]})]})})},V=({onNext:i,onBack:u,initialData:s,showBack:g=!0})=>{const[l,o]=v.useState({paymentMethod:"",amount:"500",paymentStatus:"Pending",bankName:"",transactionId:"",insuranceProvider:"",insuranceNumber:"",discount:"",finalAmount:"500"});v.useEffect(()=>{s&&o(s)},[s]);const m=(a,d)=>{o(c=>{const p={...c,[a]:d};if(a==="discount"){const N=parseFloat(p.amount)||0,y=parseFloat(d)||0;p.finalAmount=(N-y).toString()}return p})},h=a=>{a.preventDefault(),i(l)},r=["Cash","Credit Card","Debit Card","Bank Transfer","Digital Wallet","Insurance","Cheque"],t=["Pending","Paid","Partially Paid","Failed","Refunded"],n=["Nepal Bank Limited","Rastriya Banijya Bank","Agricultural Development Bank","Nepal Investment Bank","Standard Chartered Bank","Himalayan Bank","Nepal SBI Bank","Nepal Bangladesh Bank","Everest Bank","Bank of Kathmandu"];return e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center",children:e.jsx(x,{icon:"mdi:credit-card",className:"w-5 h-5 text-purple-600"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Payment Details"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Billing & Insurance information"})]})]}),e.jsxs("form",{onSubmit:h,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payment Method *"}),e.jsxs("select",{value:l.paymentMethod,onChange:a=>m("paymentMethod",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select payment method"}),r.map(a=>e.jsx("option",{value:a,children:a},a))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Consultation Fee *"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-2 text-gray-500",children:"Rs."}),e.jsx("input",{type:"number",value:l.amount,onChange:a=>m("amount",a.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",min:"0"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payment Status"}),e.jsx("select",{value:l.paymentStatus,onChange:a=>m("paymentStatus",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:t.map(a=>e.jsx("option",{value:a,children:a},a))})]}),(l.paymentMethod==="Bank Transfer"||l.paymentMethod==="Cheque")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Bank Name"}),e.jsxs("select",{value:l.bankName,onChange:a=>m("bankName",a.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[e.jsx("option",{value:"",children:"Select bank"}),n.map(a=>e.jsx("option",{value:a,children:a},a))]})]}),l.paymentMethod!=="Cash"&&l.paymentMethod!=="Insurance"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Transaction ID"}),e.jsx("input",{type:"text",value:l.transactionId,onChange:a=>m("transactionId",a.target.value),placeholder:"Enter transaction ID",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]}),l.paymentMethod==="Insurance"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Insurance Provider"}),e.jsx("input",{type:"text",value:l.insuranceProvider,onChange:a=>m("insuranceProvider",a.target.value),placeholder:"Enter insurance provider name",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Insurance Number"}),e.jsx("input",{type:"text",value:l.insuranceNumber,onChange:a=>m("insuranceNumber",a.target.value),placeholder:"Enter insurance policy number",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Discount Amount"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-2 text-gray-500",children:"Rs."}),e.jsx("input",{type:"number",value:l.discount,onChange:a=>m("discount",a.target.value),placeholder:"0",className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",min:"0"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Final Amount"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{className:"absolute left-3 top-2 text-gray-500",children:"Rs."}),e.jsx("input",{type:"number",value:l.finalAmount,readOnly:!0,className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700"})]})]})]}),e.jsxs("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-800 mb-3",children:"Payment Summary"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Consultation Fee:"}),e.jsxs("span",{className:"font-medium",children:["Rs. ",l.amount]})]}),l.discount&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Discount:"}),e.jsxs("span",{className:"font-medium text-green-600",children:["- Rs. ",l.discount]})]}),e.jsxs("div",{className:"flex justify-between border-t pt-2",children:[e.jsx("span",{className:"font-medium text-gray-800",children:"Total Amount:"}),e.jsxs("span",{className:"font-bold text-lg",children:["Rs. ",l.finalAmount]})]})]})]}),e.jsx("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(x,{icon:"mdi:information",className:"w-5 h-5 text-blue-600"}),e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("strong",{children:"Ready to submit:"}),' Clicking "Complete Booking" will finalize your appointment and submit all information.']})]})}),e.jsxs("div",{className:"flex justify-between mt-8",children:[e.jsxs(j,{type:"button",onClick:u,variant:"outline",className:"flex items-center gap-2",children:[e.jsx(x,{icon:"mdi:arrow-left",className:"w-4 h-4"}),e.jsx("span",{children:"Back"})]}),e.jsxs(j,{type:"submit",className:"bg-green-600 hover:bg-green-700 flex items-center gap-2 px-6 py-3",children:[e.jsx(x,{icon:"mdi:check-circle",className:"w-5 h-5"}),e.jsx("span",{children:"Complete Booking"})]})]})]})]})},L=({steps:i,currentStep:u,stepData:s,onEdit:g,isStepCompleted:l})=>{const o=t=>({teal:"border-teal-500",red:"border-red",blue:"border-blue",purple:"border-purple"})[t.color]||"border-teal-500",m=t=>({teal:"text-teal-600",red:"text-red-600",blue:"text-blue",purple:"bg-purple-50 text-purple-600"})[t.color]||"bg-red text-teal-600",h=t=>{switch(t.id){case 1:{const n=s.patientInfo;return n?e.jsxs("div",{className:"grid grid-cols-2 gap-1 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Patient ID:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.patientId||"Auto Generated"})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Name:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.fullName})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Blood Group:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.bloodGroup})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"DOB:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.dateOfBirth})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Phone:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.phoneNumber})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Email:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.emailAddress})]})]}):null}case 2:{const n=s.doctorInfo;return n?e.jsxs("div",{className:"grid grid-cols-1 gap-1 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Department:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.department})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Doctor:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.doctorName})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Available Status:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.availableStatus})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Appointment Date:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.appointmentDate})]})]}):null}case 3:{const n=s.appointmentInfo;return n?e.jsxs("div",{className:"grid grid-cols-1 gap-1 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Date:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.date})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Time:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.timeSlot})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Type:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.appointmentType})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Priority:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.priority})]})]}):null}case 4:{const n=s.paymentInfo;return n?e.jsxs("div",{className:"grid grid-cols-1 gap-1 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Payment Method:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.paymentMethod})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Amount:"}),e.jsxs("span",{className:"ml-2 font-medium",children:["Rs. ",n.amount]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Status:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.paymentStatus})]}),n.transactionId&&e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-600",children:"Transaction ID:"}),e.jsx("span",{className:"ml-2 font-medium",children:n.transactionId})]})]}):null}default:return null}},r=t=>{const n=l(t.id),a=t.id===u,d=h(t);return e.jsxs("div",{className:`h-full flex flex-col bg-white rounded-lg border-2 transition-all duration-300 
        ${n?`${o(t)} shadow-md`:a?"border-gray-300 shadow-sm":"border-gray-200"}
      `,children:[e.jsx("div",{className:"p-1 border-b border-gray-100",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${m(t)}`,children:e.jsx(x,{icon:t.icon,className:"w-4 h-4"})}),e.jsx("h3",{className:"text-sm font-medium text-gray-900",children:t.title})]}),n&&e.jsx("button",{onClick:()=>g(t.id),className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",title:"Edit",children:e.jsx(x,{icon:"mdi:pencil",className:"w-4 h-4"})})]})}),e.jsx("div",{className:"p-4 flex-1",children:n&&d?d:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(x,{icon:"mdi:dots-horizontal",className:"w-8 h-8 text-gray-300 mx-auto"}),e.jsx("p",{className:"text-xs text-gray-400 mt-2",children:a?"In Progress":"Pending"})]})})]})};return e.jsxs("div",{className:"flex   gap-4",children:[e.jsx("div",{className:"w-full lg:w-1/3",children:r(i[0])}),e.jsx("div",{className:"w-full lg:w-2/3 flex gap-4",children:i.slice(1).map(t=>e.jsx("div",{className:"w-1/3",children:r(t)},t.id))})]})},W=({steps:i,currentStep:u,isStepCompleted:s})=>{const g=o=>o.id===u?"active":o.id<u||s(o.id)?"completed":"inactive",l=()=>Math.max(0,u-1)/(i.length-1)*100;return e.jsx("div",{className:"w-full py-8 ",children:e.jsxs("div",{className:"relative  mx-auto",children:[e.jsx("div",{className:"absolute top-6 left-0 right-0 h-0.5 bg-gray-300 mx-6"}),e.jsx("div",{className:"absolute top-6 left-6 h-0.5 bg-blue transition-all duration-500 ease-in-out",style:{width:`calc(${l()}% - 24px)`}}),e.jsx("div",{className:"flex items-start justify-between relative",children:i.map(o=>{const m=g(o),h=m==="completed",t=h||m==="active";return e.jsxs("div",{className:"flex flex-col items-center relative z-10 flex-1",style:{maxWidth:`${100/i.length}%`},children:[e.jsx("div",{className:`w-12 h-12 rounded-full  border-2 flex items-center justify-center transition-all duration-300 ${t?"bg-blue border-blue text-white":"bg-white border-gray-300 text-gray-400"}`,children:h?e.jsx(x,{icon:o.icon,className:"w-5 h-5"}):e.jsx(x,{icon:o.icon,className:"w-5 h-5"})}),e.jsxs("div",{className:"mt-3 text-center",children:[e.jsx("div",{className:`text-sm font-semibold leading-tight ${t?"text-blue  ":"text-gray-500"}`,children:o.title}),e.jsx("div",{className:`text-xs  leading-tight ${t?"text-blue":"text-gray-400"}`,children:o.subtitle})]})]},o.id)})})]})})},K=[{value:"Male",label:"Male"},{value:"Female",label:"Female"},{value:"Other",label:"Other"}],Y=[{value:"A+",label:"A+"},{value:"A-",label:"A-"},{value:"B+",label:"B+"},{value:"B-",label:"B-"},{value:"AB+",label:"AB+"},{value:"AB-",label:"AB-"},{value:"O+",label:"O+"},{value:"O-",label:"O-"}],J=[{value:"Single",label:"Single"},{value:"Married",label:"Married"},{value:"Divorced",label:"Divorced"},{value:"Widowed",label:"Widowed"}],U=w().shape({}),B=({onNext:i,onBack:u,initialData:s,showBack:g=!1})=>{const[l,o]=v.useState(!1),m=S({initialValues:{patientId:(s==null?void 0:s.patientId)||"",fullName:(s==null?void 0:s.fullName)||"",gender:(s==null?void 0:s.gender)||"",bloodGroup:(s==null?void 0:s.bloodGroup)||"",dateOfBirth:(s==null?void 0:s.dateOfBirth)||"",maritalStatus:(s==null?void 0:s.maritalStatus)||"",language:(s==null?void 0:s.language)||"",currentAddress:(s==null?void 0:s.currentAddress)||"",permanentAddress:(s==null?void 0:s.permanentAddress)||"",contactNumber:(s==null?void 0:s.contactNumber)||"",alternatePhoneNumber:(s==null?void 0:s.alternatePhoneNumber)||"",email:(s==null?void 0:s.email)||"",citizenNumber:(s==null?void 0:s.citizenNumber)||"",tokenNumber:(s==null?void 0:s.tokenNumber)||""},enableReinitialize:!0,validationSchema:U,onSubmit:p=>{i(p)}}),{values:h,errors:r,touched:t,handleChange:n,handleBlur:a,setFieldValue:d,getFieldProps:c}=m;return v.useEffect(()=>{s&&o(!!s.patientId)},[s]),e.jsx(I,{value:m,children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[e.jsx("div",{className:"w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center",children:e.jsx(x,{icon:"mdi:account",className:"w-5 h-5 text-teal-600"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Patient Information"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Please provide your personal and medical details"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("label",{className:"flex items-center space-x-2 cursor-pointer",children:[e.jsx("input",{type:"radio",name:"patientType",checked:!l,onChange:()=>o(!1),className:"w-4 h-4 text-teal-600"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"I am a new patient"})]}),e.jsxs("label",{className:"flex items-center space-x-2 cursor-pointer",children:[e.jsx("input",{type:"radio",name:"patientType",checked:l,onChange:()=>o(!0),className:"w-4 h-4 text-teal-600"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"I am an old patient"})]})]}),!l&&e.jsxs("div",{className:"mt-2 flex items-center space-x-2 text-sm text-gray-600",children:[e.jsx("span",{children:"Token Number:"}),e.jsx("span",{className:"font-medium text-gray-800",children:"Token Number"})]})]}),e.jsxs("form",{onSubmit:m.handleSubmit,children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-5",children:[e.jsxs("div",{children:[e.jsx(b,{required:!0,type:"text",label:"Patient ID",placeholder:"Patient identity number",disabled:!l,...c("patientId")}),r.patientId&&t.patientId&&e.jsx("p",{className:"text-red-500 text-sm",children:String(r.patientId)})]}),e.jsxs("div",{children:[e.jsx(f,{label:"Gender",options:K,name:"gender",required:!0,value:h.gender,onChange:p=>d("gender",p.target.value)}),r.gender&&t.gender&&e.jsx("p",{className:"text-red-500 text-sm",children:String(r.gender)})]}),e.jsxs("div",{children:[e.jsx(f,{label:"Blood Group",options:Y,name:"bloodGroup",required:!0,value:h.bloodGroup,onChange:p=>d("bloodGroup",p.target.value)}),r.bloodGroup&&t.bloodGroup&&e.jsx("p",{className:"text-red-500 text-sm",children:String(r.bloodGroup)})]}),e.jsxs("div",{children:[e.jsx(b,{required:!0,type:"text",label:"Full Name",placeholder:"Enter full name",...c("fullName")}),r.fullName&&t.fullName&&e.jsx("p",{className:"text-red-500 text-sm",children:String(r.fullName)})]}),e.jsxs("div",{children:[e.jsx(b,{required:!0,type:"date",label:"Date of Birth",placeholder:"DD/MM/YYYY",...c("dateOfBirth")}),r.dateOfBirth&&t.dateOfBirth&&e.jsx("p",{className:"text-red-500 text-sm",children:String(r.dateOfBirth)})]}),e.jsx("div",{children:e.jsx(f,{label:"Marital Status",options:J,name:"maritalStatus",value:h.maritalStatus,onChange:p=>d("maritalStatus",p.target.value)})}),e.jsxs("div",{children:[e.jsx(b,{type:"text",label:"Language",placeholder:"Enter preferred language",...c("language")}),r.language&&t.language&&e.jsx("p",{className:"text-red-500 text-sm",children:String(r.language)})]}),e.jsxs("div",{children:[e.jsx(b,{required:!0,type:"tel",label:"Phone Number",placeholder:"Phone Number",...c("contactNumber")}),r.contactNumber&&t.contactNumber&&e.jsx("p",{className:"text-red-500 text-sm",children:String(r.contactNumber)})]}),e.jsxs("div",{children:[e.jsx(b,{type:"tel",label:"Alternate Phone Number",placeholder:"Alternate Phone Number",...c("alternatePhoneNumber")}),r.alternatePhoneNumber&&t.alternatePhoneNumber&&e.jsx("p",{className:"text-red-500 text-sm",children:String(r.alternatePhoneNumber)})]}),e.jsxs("div",{children:[e.jsx(b,{required:!0,type:"email",label:"Email Address",placeholder:"Email Address",...c("email")}),r.email&&t.email&&e.jsx("p",{className:"text-red-500 text-sm",children:String(r.email)})]}),e.jsx("div",{children:e.jsx(b,{type:"text",label:"Current Address",placeholder:"Enter current address",...c("currentAddress")})}),e.jsx("div",{children:e.jsx(b,{type:"text",label:"Permanent Address",placeholder:"Enter permanent address",...c("permanentAddress")})}),e.jsxs("div",{children:[e.jsx(b,{type:"text",label:"Citizenship No",placeholder:"Enter Citizenship number",...c("citizenNumber")}),r.citizenNumber&&t.citizenNumber&&e.jsx("p",{className:"text-red-500 text-sm",children:String(r.citizenNumber)})]})]}),e.jsxs("div",{className:"flex justify-between mt-8",children:[g?e.jsxs(j,{type:"button",onClick:u,variant:"outline",className:"flex items-center gap-2",children:[e.jsx(x,{icon:"mdi:arrow-left",className:"w-4 h-4"}),e.jsx("span",{children:"Back"})]}):e.jsx("div",{}),e.jsxs(j,{type:"submit",className:"bg-blue-600 hover:bg-blue-700 flex items-center gap-2",children:[e.jsx("span",{children:"Next"}),e.jsx(x,{icon:"mdi:arrow-right",className:"w-4 h-4"})]})]})]})]})})},H=()=>{const[i,u]=v.useState(1),[s,g]=v.useState({}),l=[{id:1,title:"Patient Information",subtitle:"Personal & Medical Details",icon:"mdi:account",color:"teal"},{id:2,title:"Doctor Info",subtitle:"Choose Department & Doctor",icon:"mdi:doctor",color:"red"},{id:3,title:"Appointment",subtitle:"Choose Date, Reason & Priority",icon:"mdi:calendar",color:"blue"},{id:4,title:"Payment",subtitle:"Billing & Insurance",icon:"mdi:credit-card",color:"purple"}],o=(d,c)=>{console.log(`Step ${d} completed with data:`,c);const p={...s,[`${t(d)}`]:c};g(p),d<4?(u(d+1),console.log(`Moving to step ${d+1}`)):(console.log("All steps completed, submitting final form..."),m(p))},m=d=>{var p,N,y,C,P,k,A;console.log("Final form submission:",d);const c={patientInfo:d.patientInfo,doctorInfo:d.doctorInfo,appointmentInfo:d.appointmentInfo,paymentInfo:d.paymentInfo,submittedAt:new Date().toISOString(),status:"PENDING"};console.log("=== APPOINTMENT BOOKING COMPLETED ==="),console.log("Step 1 - Patient Information:",c.patientInfo),console.log("Step 2 - Doctor Selection:",c.doctorInfo),console.log("Step 3 - Appointment Details:",c.appointmentInfo),console.log("Step 4 - Payment Details:",c.paymentInfo),console.log("Complete Final Data:",c),console.log("====================================="),alert(`Appointment Booking Completed Successfully!

Patient: ${((p=c.patientInfo)==null?void 0:p.firstName)||"N/A"} ${((N=c.patientInfo)==null?void 0:N.lastName)||""}
Doctor: ${((y=c.doctorInfo)==null?void 0:y.doctor)||"N/A"}
Date: ${((C=c.doctorInfo)==null?void 0:C.appointmentDate)||"N/A"}
Time: ${((P=c.doctorInfo)==null?void 0:P.availableTimeSlot)||"N/A"}
Type: ${((k=c.appointmentInfo)==null?void 0:k.appointmentType)||"N/A"}
Priority: ${((A=c.appointmentInfo)==null?void 0:A.priority)||"N/A"}

Check console for complete data!`),u(1),g({patientInfo:null,doctorInfo:null,appointmentInfo:null,paymentInfo:null})},h=d=>{u(d)},r=()=>{i>1&&u(i-1)},t=d=>{switch(d){case 1:return"patientInfo";case 2:return"doctorInfo";case 3:return"appointmentInfo";case 4:return"paymentInfo";default:return"patientInfo"}},n=d=>{const c=t(d);return!!s[c]},a=()=>{const d={onNext:c=>o(i,c),onBack:r,initialData:s[t(i)],showBack:i>1};switch(i){case 1:return e.jsx(B,{...d});case 2:return e.jsx(R,{...d});case 3:return e.jsx(q,{...d});case 4:return e.jsx(V,{...d});default:return e.jsx(B,{...d})}};return e.jsx("div",{className:"bg-[#EFF7F9]",children:e.jsxs("div",{className:"mx-auto",children:[e.jsx(L,{steps:l,currentStep:i,stepData:s,onEdit:h,isStepCompleted:n}),e.jsx(W,{steps:l,currentStep:i,isStepCompleted:n}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:a()})]})})},X=()=>e.jsx("div",{className:"min-h-screen",children:e.jsx(H,{})});export{X as default};
