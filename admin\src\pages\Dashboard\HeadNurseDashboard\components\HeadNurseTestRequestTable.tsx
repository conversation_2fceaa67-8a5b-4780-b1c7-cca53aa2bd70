/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from "react";
import SearchableSelect from "../../../../components/SearchableSelect";
import Button from "../../../../components/Button";
import MasterTable from "../../../../layouts/Table/MasterTable";

const HeadNurseTestRequestTable: React.FC = () => {
  const [timeFilter, setTimeFilter] = useState("today");
  const [patientTypeFilter, setPatientTypeFilter] = useState("all");

  const testRequestData = {
    today: {
      all: [
        {
          patientID: "P-001",
          patientName: "<PERSON>",
          department: "Hospital Patient",
          nurse: "<PERSON>",
          bedNo: "Blood Test",
          time: "09:30 AM",
          medicine: "Paracetamol",
          priority: "Normal",
        },
        {
          patientID: "P-002",
          patientName: "<PERSON>",
          department: "Emergency",
          nurse: "<PERSON>",
          bedNo: "ICU-04",
          time: "10:15 AM",
          medicine: "Amoxicillin",
          priority: "Urgent",
        },
      ],
      hospital: [
        {
          patientID: "P-003",
          patientName: "<PERSON>",
          department: "Cardiology",
          nurse: "<PERSON>",
          bedNo: "Ward-12B",
          time: "11:00 AM",
          medicine: "Atenolol",
          priority: "Normal",
        },
        {
          patientID: "P-004",
          patientName: "Sophia Williams",
          department: "Maternity",
          nurse: "James Anderson",
          bedNo: "Room-205",
          time: "08:45 AM",
          medicine: "Iron Supplement",
          priority: "Urgent",
        },
      ],
      outpatient: [
        {
          patientID: "P-005",
          patientName: "Daniel Garcia",
          department: "Orthopedics",
          nurse: "Emma Thompson",
          bedNo: "Ortho-03",
          time: "01:30 PM",
          medicine: "Ibuprofen",
          priority: "Urgent",
        },
      ],
    },
  };

  const currentData =
    testRequestData[timeFilter as keyof typeof testRequestData][
      patientTypeFilter as keyof typeof testRequestData.today
    ] || [];

  const totalRequests = currentData.length;
  const urgentRequests = currentData.filter(
    (req) => req.priority === "Urgent"
  ).length;
  const normalRequests = currentData.filter(
    (req) => req.priority === "Normal"
  ).length;

  const getPriorityBadge = (priority: string) => {
    const baseClasses = "px-2 py-1 rounded-md text-xs font-medium";
    const priorityClasses =
      priority === "Urgent"
        ? "bg-red text-white"
        : priority === "Normal"
        ? "bg-[#10B981] text-white"
        : "bg-gray text-white";

    return (
      <span className={`${baseClasses} ${priorityClasses}`}>{priority}</span>
    );
  };

  const tableData = {
    columns: [
      { title: "Patient ID", key: "patientID" },
      { title: "Time", key: "time" },
      { title: "Patient Name", key: "patientName" },
      { title: "Department", key: "department" },
      { title: "Nurse", key: "nurse" },
      { title: "Bed No", key: "bedNo" },
      { title: "Medicine", key: "medicine" },
      { title: "Priority", key: "priority" },
    ],
    rows: currentData.map((request) => ({
      patientID: request.patientID,
      patientName: request.patientName,
      department: request.department,
      nurse: request.nurse || "N/A",
      bedNo: request.bedNo,
      time: request.time,
      medicine: request.medicine,
      priority: getPriorityBadge(request.priority),
    })),
  };

  return (
    <div className="w-full bg-white shadow-sm rounded-xl p-4 border border-purple-100 transition-all duration-300">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <div>
            <h3 className="text-xl font-bold text-gray-800">
              Medication & Treatment Logs
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {totalRequests} total • {urgentRequests} urgent • {normalRequests}{" "}
              normal
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            title="View All"
            className="!text-blue-600 !border-blue-500 hover:!bg-blue-50 !px-4 !py-2 !text-sm !font-medium !rounded-lg transition-all duration-200"
          />
        </div>
      </div>

      <div className="flex flex-wrap gap-3 mb-6">
        <div className="bg-white flex gap-1 text-[#2FC0A1] border border-[#2FC0A1] rounded-md shadow-sm hover:shadow-md transition-all duration-200">
          <SearchableSelect
            value={timeFilter}
            options={[
              { label: "Today", value: "today" },
              { label: "Weekly", value: "week" },
              { label: "Monthly", value: "month" },
            ]}
            onChange={(value) => setTimeFilter(value)}
          />
        </div>

        <div className="bg-white flex gap-1 text-[#2FC0A1] border border-[#2FC0A1] rounded-md shadow-sm hover:shadow-md transition-all duration-200">
          <SearchableSelect
            value={patientTypeFilter}
            options={[
              { label: "All Patients", value: "all" },
              { label: "Hospital Patients", value: "hospital" },
              { label: "Out Patients", value: "outpatient" },
            ]}
            onChange={(value) => setPatientTypeFilter(value)}
          />
        </div>
      </div>
      <div className="overflow-x-auto">
        <MasterTable
          className="p-0"
          rows={tableData.rows}
          columns={tableData.columns}
          loading={false}
        />
      </div>
    </div>
  );
};

export default HeadNurseTestRequestTable;
