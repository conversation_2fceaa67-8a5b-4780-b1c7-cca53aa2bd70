import{aM as U,a5 as h,cY as q,ct as J,a2 as e,b3 as Q,cZ as V,c_ as W,c$ as X,a1 as Z,a9 as z,aa as K,ac as B}from"./index-ClX9RVH0.js";import{u as ee}from"./analytics.api-DOA8_WvB.js";const te=({dateFrom:i,dateTo:m})=>{var j,f;const G=U({startDate:i,endDate:m}),{data:p}=ee(G),t=p==null?void 0:p.data,[c,H]=h.useState([]);h.useEffect(()=>{var o,a,n,d,l,x,b,N,S,P,_,C,T,D,E,I,w,F,R,$,A,y,k,Y,L,O;if(!t)return;const s=[{title:"REVENUE",id:"A.",details:[{title:"Core Service",children:[{title:"OPD Consultations",amount:((a=(o=t==null?void 0:t.coreServices)==null?void 0:o.opdConsultation)==null?void 0:a.totalAmount)||0,percentage:0,notes:`${((d=(n=t==null?void 0:t.coreServices)==null?void 0:n.opdConsultation)==null?void 0:d.appointmentCount)||0} patients/day`},{title:"IPD Admissions",amount:((x=(l=t==null?void 0:t.coreServices)==null?void 0:l.IPDAdmission)==null?void 0:x.totalCharges)||0,percentage:0,notes:`${((N=(b=t==null?void 0:t.coreServices)==null?void 0:b.IPDAdmission)==null?void 0:N.totalBedAssigned)||0} beds/day`}]},{title:"Ancillary Services",children:[{title:"Laboratory Tests",amount:((P=(S=t==null?void 0:t.coreServices)==null?void 0:S.laboratory)==null?void 0:P.totalLaboratoryInvoice)||0,percentage:0,notes:`${((C=(_=t==null?void 0:t.coreServices)==null?void 0:_.laboratory)==null?void 0:C.labInvoiceCount)||0} tests/day`},{title:"Pharmacy Sales",amount:((D=(T=t==null?void 0:t.coreServices)==null?void 0:T.pharmacy)==null?void 0:D.totalPharmacyInvoice)||0,percentage:0,notes:`${((I=(E=t==null?void 0:t.coreServices)==null?void 0:E.pharmacy)==null?void 0:I.totalPharmacyInvoiceCount)||0} invoice/day`}]}]},{title:"OPERATING EXPENSES",id:"B.",details:[{title:"Direct Costs",children:[{title:"Staff Salary",amount:125e5,percentage:0,notes:"Notes"},{title:"Medical Supplies",amount:182e5,percentage:0,notes:"Notes"}]}]},{title:"Hospital Overall Invoice",id:"C.",details:[{title:"Invoices",children:[{title:"Paid invoice",amount:((F=(w=t==null?void 0:t.coreServices)==null?void 0:w.invoices)==null?void 0:F.paidInvoiceTotal)||0,percentage:0,notes:`${(($=(R=t==null?void 0:t.coreServices)==null?void 0:R.invoices)==null?void 0:$.paidInvoiceCount)||0}/day`},{title:"Pending invoice",amount:((y=(A=t==null?void 0:t.coreServices)==null?void 0:A.invoices)==null?void 0:y.pendingInvoiceTotal)||0,percentage:0,notes:`${((Y=(k=t==null?void 0:t.coreServices)==null?void 0:k.invoices)==null?void 0:Y.pendingInvoiceCount)||0}/day`},{title:"Total invoice",amount:((O=(L=t==null?void 0:t.coreServices)==null?void 0:L.invoices)==null?void 0:O.allInvoiceTotal)||0,percentage:0,notes:"-"}]}]}],r=q(s);H(r)},[t]);const v=((j=c[0])==null?void 0:j.details.reduce((s,r)=>s+r.children.reduce((o,a)=>o+(a.amount||0),0),0))||0,u=((f=c[1])==null?void 0:f.details.reduce((s,r)=>s+r.children.reduce((o,a)=>o+(a.amount||0),0),0))||0,g=h.useRef(null),M=J.useReactToPrint({contentRef:g});return e.jsxs("div",{className:"flex flex-col p-6 gap-6 bg-white",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("section",{className:"flex flex-col",children:[e.jsx("p",{className:"font-semibold text-[#464646] text-xl",children:"Profit & Loss Statement"}),e.jsxs("p",{className:"font-semibold text-base text-[#464646]",children:["Fiscal Year: 2024 (Jan 1- Dec 31)"," "]})]}),e.jsx("section",{className:"flex place-items-center gap-3",children:e.jsxs("button",{className:"flex bg-[#146C71] place-items-center px-3 py-2 gap-2 text-white rounded-lg",onClick:M,children:[e.jsx("p",{children:"Print"}),e.jsx(Q,{icon:"mingcute:print-line"})]})})]}),e.jsxs("div",{className:"grid grid-cols-[80px_1fr_200px_120px_1fr] bg-gray-50 p-4 border-b font-medium text-gray-600",children:[e.jsx("div",{children:"#"}),e.jsx("div",{children:"Category"}),e.jsx("div",{className:"text-right",children:"Amount (NPR)"}),e.jsx("div",{className:"text-right",children:"% of Total"}),e.jsx("div",{className:"pl-8",children:"Notes"})]}),e.jsx("div",{className:"flex flex-col",children:c==null?void 0:c.map((s,r)=>{var o;return e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"grid grid-cols-[80px_1fr_200px_120px_1fr] p-4 border-b bg-white",children:[e.jsx("div",{className:"font-medium text-teal-600",children:s.id}),e.jsx("div",{className:"font-medium text-teal-600",children:s.title}),e.jsx("div",{}),e.jsx("div",{}),e.jsx("div",{})]}),(o=s==null?void 0:s.details)==null?void 0:o.map((a,n)=>{var d;return e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"grid grid-cols-[80px_1fr_200px_120px_1fr] p-4 border-b bg-white",children:[e.jsxs("div",{className:"font-medium",children:[n+1,"."]}),e.jsx("div",{className:"font-medium",children:a.title}),e.jsx("div",{}),e.jsx("div",{}),e.jsx("div",{})]}),(d=a==null?void 0:a.children)==null?void 0:d.map((l,x)=>e.jsxs("div",{className:"grid grid-cols-[80px_1fr_200px_120px_1fr] p-4 border-b bg-white",children:[e.jsx("div",{}),e.jsx("div",{className:"pl-4",children:l.title}),e.jsx("div",{className:"text-right",children:V(l.amount)}),e.jsx("div",{className:"text-right",children:W(l.percentage)}),e.jsx("div",{className:"pl-8",children:l.notes})]},`child-${r}-${n}-${x}`))]},`detail-${r}-${n}`)}),s.title==="REVENUE"&&e.jsxs("div",{className:"grid grid-cols-[80px_1fr_200px_120px_1fr] p-4 border-b bg-white font-medium",children:[e.jsx("div",{}),e.jsx("div",{children:"Total Revenue"}),e.jsx("div",{className:"text-right",children:V(v)}),e.jsx("div",{className:"text-right",children:"100%"}),e.jsx("div",{className:"pl-8",children:"9% YoY"})]})]},`section-${r}`)})}),e.jsx("div",{style:{display:"none"},children:e.jsx(X,{ref:g,reportData:t,tableData:c,totalRevenue:v,totalExpenses:u,netIncome:v-u,endDate:m,startDate:i})})]})},ie=()=>{const i=Z({initialValues:{dateFrom:"",dateTo:""},onSubmit:m=>{}});return e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsx("div",{className:"w-full bg-white",children:e.jsx(z,{value:i,children:e.jsxs(K,{className:"flex items-center justify-between p-4 bg-white gap-8 w-[850px] ",children:[e.jsx(B,{label:"Date From",placeholder:"Select Date",type:"date",name:"dateFrom",value:i.values.dateFrom,onChange:i.handleChange}),e.jsx(B,{label:"Date To",placeholder:"Select Date",type:"date",name:"dateTo",value:i.values.dateTo,onChange:i.handleChange})]})})}),e.jsx(te,{dateFrom:i.values.dateFrom,dateTo:i.values.dateTo})]})};export{ie as PharmacyFinanceReportsPage};
