import{ad as L,a5 as D,bw as a,aM as w,aw as U,bI as M,a2 as t,af as $,ag as r,ah as G,aQ as Q,aj as H}from"./index-ClX9RVH0.js";const q=()=>{var d,A,g,S;const l=L(),[o,i]=D.useState({limit:5,page:1}),[s,c]=D.useState({selectedRole:"all",selectedDesgination:"all",search:""}),P=s.search,C=[a.STAFF,a.OTHER,a.DRIVER,a.ASSISTANT_MANAGER,a.CLEANER,a.IT_OFFICER,a.SECURITY_GUARD,a.OFFICE_BOY,a.ELECTRICIAN,a.HOSPITAL_MANAGER,a.FINANCE_MANAGER,a.RECEPTIONIST,a.PLUMBER,a.ASSISTANT_ACCOUNTANT,a.SOCIAL_WORKER],_={search:s.search,"professionalDetails.designation":s.selectedDesgination,...P?{}:{page:o.page,limit:o.limit}},x=s.selectedRole==="all"?{multiroles:JSON.stringify(C)}:{role:s.selectedRole},k=w({..._,...x}),{data:n}=U(k),{mutateAsync:O}=M(),j=()=>{l(`/${r.ADDSTAFF}`)},u={columns:[{title:"Name",key:"doctorName"},{title:"ID",key:"tokenid"},{title:"Designation",key:"specialist"},{title:"Role",key:"role"},{title:"Phone Number",key:"contactNumber"},{title:"Available status",key:"status"},{title:"Action",key:"action"}],rows:(A=(d=n==null?void 0:n.data)==null?void 0:d.users)==null?void 0:A.map((e,v)=>{var I,R,p,N,T,E,h,f,F,y,b;return{key:v,tokenid:`S-${(R=e==null?void 0:e._id)==null?void 0:R.slice(((I=e==null?void 0:e._id)==null?void 0:I.length)-5,e==null?void 0:e._id.length)}`,contactNumber:((T=(N=(p=e==null?void 0:e.commonInfo)==null?void 0:p.contactInfo)==null?void 0:N.phone)==null?void 0:T.primaryPhone)??((f=(h=(E=e==null?void 0:e.commonInfo)==null?void 0:E.contactInfo)==null?void 0:h.phone)==null?void 0:f.secondaryPhone),role:e==null?void 0:e.role,doctorName:(y=(F=e==null?void 0:e.commonInfo)==null?void 0:F.personalInfo)==null?void 0:y.fullName,specialist:((b=e==null?void 0:e.professionalDetails)==null?void 0:b.designation)??"-",status:t.jsx(G,{status:e!=null&&e.isActive?"ACTIVE":"INACTIVE"}),action:t.jsx($,{onShow:()=>{l(`/${r.STAFFDETAIL}/${e==null?void 0:e._id}`)},onEdit:()=>{l(`/${r.ADDSTAFF}/${e==null?void 0:e._id}`)},onDelete:()=>{O({id:JSON.stringify([e==null?void 0:e._id])})}})}})};return t.jsxs("div",{className:"flex flex-col gap-2 pb-8",children:[t.jsx(Q,{headerTitle:"Staff List",onSearch:!0,onDesignation:!0,onFilter:!0,button:!0,buttonText:"Add Staff",buttonAction:j,onSearchFunc:e=>c({...s,search:e}),onRoleFunc:e=>c({...s,selectedRole:e}),onDesignationSelectFunc:e=>c({...s,selectedDesgination:e}),onRole:!0}),t.jsx("div",{className:"bg-white rounded-md",children:t.jsx(H,{columns:u.columns,rows:u.rows,loading:!1,pagination:{currentPage:o.page,totalPage:(S=(g=n==null?void 0:n.data)==null?void 0:g.pagination)==null?void 0:S.pages,limit:o.limit,onClick:e=>{e.page&&i({...o,page:e.page}),e.limit&&i({...o,limit:e.limit})}}})})]})};export{q as default};
