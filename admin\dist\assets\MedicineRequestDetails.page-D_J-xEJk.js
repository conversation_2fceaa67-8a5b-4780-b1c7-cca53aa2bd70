import{a1 as q,av as e,a5 as m,ct as D,a2 as s,a9 as A,aa as P,c7 as w,aj as F,bF as R,cU as S,ba as T,cV as V,aQ as C}from"./index-ClX9RVH0.js";const E=({invoice:t})=>{var f,x,c,b,g;console.log(t,"invoice");const u=[{type:"date",field:"requestDate",label:"Requested Date",disabled:!0},{type:"text",field:"patientName",label:"Patient Name",disabled:!0},{type:"text",field:"doctorName",label:"Doctor Name",isVisible:!!(t!=null&&t.supervisor),disabled:!0},{type:"text",field:"department",label:"Department",disabled:!0,isVisible:!!(t!=null&&t.supervisor)},{type:"text",field:"status",label:"Status",disabled:!0},{type:"text",field:"note",label:"Note"}],o=q({initialValues:{requestDate:e.get(t,"date"),patientName:((f=t==null?void 0:t.walkInCustomer)==null?void 0:f.name)??((b=(c=(x=t==null?void 0:t.billingAgainst)==null?void 0:x.commonInfo)==null?void 0:c.personalInfo)==null?void 0:b.fullName)??"",doctorName:e.get(t,"supervisor.commonInfo.personalInfo.fullName")??"",department:e.get(t,"billingAgainst.commonInfo.ipdOpd")??"",status:t!=null&&t.isActive?"Active":"InActive",note:""},onSubmit:()=>{}}),n=m.useRef(null),I=D.useReactToPrint({contentRef:n,documentTitle:`Invoice-${(t==null?void 0:t.invoiceNo)??""}`,pageStyle:`
      @page {
        size: A4;
        margin: 0.5in;
      }
      @media print {
        body {
          -webkit-print-color-adjust: exact;
        }
      }
    `});m.useEffect(()=>{var a,d,l,r;t&&o.setValues({requestDate:e.get(t,"date")||"",patientName:((a=t==null?void 0:t.walkInCustomer)==null?void 0:a.name)??((r=(l=(d=t==null?void 0:t.billingAgainst)==null?void 0:d.commonInfo)==null?void 0:l.personalInfo)==null?void 0:r.fullName)??"",doctorName:e.get(t,"supervisor.commonInfo.personalInfo.fullName")||"",department:e.get(t,"billingAgainst.commonInfo.ipdOpd")||"",status:t!=null&&t.isActive?"Active":"InActive",note:""})},[t]);const p={columns:[{title:"S.N.",key:"sn"},{title:"Product Name",key:"product"},{title:"Dose",key:"dose"},{title:"Frequency",key:"frequency"},{title:"Duration",key:"duration"},{title:"Quantity",key:"quantity"},{title:"Unit Price",key:"price"},{title:"Tax (%)",key:"tax"},{title:"Discount",key:"discount"},{title:"Total Price",key:"total"}],rows:(g=t==null?void 0:t.productList)==null?void 0:g.map((a,d)=>{var l,r,y,N;return{sn:d+1,product:((l=a==null?void 0:a.pProduct)==null?void 0:l.name)??"-",quantity:(a==null?void 0:a.quantity)??"-",price:(a==null?void 0:a.totalAmount)/(a==null?void 0:a.quantity),total:((r=a==null?void 0:a.payableAmount)==null?void 0:r.toFixed(2))??"-",dose:(a==null?void 0:a.doses)??"-",tax:((y=a==null?void 0:a.tax)==null?void 0:y.toFixed(2))??"-",discount:((N=a==null?void 0:a.discount)==null?void 0:N.toFixed(2))??"-",frequency:(a==null?void 0:a.frequency)??"-",duration:(a==null?void 0:a.duration)??"-"}})},{handleSubmit:k,errors:h,touched:j}=o;return s.jsxs("div",{className:"flex flex-col gap-3 ",children:[s.jsxs("section",{className:"flex flex-col gap-3 bg-white p-6",children:[s.jsx("p",{className:"text-darkish-black font-semibold text-base",children:"Customer Information"}),s.jsx(A,{value:o,children:s.jsx(P,{onSubmit:k,className:"grid grid-cols-4 gap-5 w-full ",children:s.jsx(w,{formDatails:u,getFieldProps:o.getFieldProps,errors:h,touched:j})})})]}),s.jsx(F,{rows:p.rows,columns:p.columns,loading:!1,color:"bg-whites-100",textcolor:"#353537"}),s.jsx("section",{className:"flex justify-end",children:s.jsx(R,{title:"Print Request",onClick:I})}),s.jsx("section",{className:"hidden",children:s.jsx(S,{ref:n,invoiceData:t})})]})},M=()=>{const{id:t}=T(),{data:u}=V(t);return s.jsxs("div",{className:"flex flex-col gap-2 ",children:[s.jsx(C,{headerTitle:"View Medicine Request",showBelow:!1}),s.jsx(E,{invoice:u})]})};export{M as MedicineRequestDetailsPage};
